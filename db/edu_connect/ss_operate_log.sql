-- 新增 操作记录表
CREATE TABLE `ss_operate_log`  (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `object_id` bigint NOT NULL COMMENT '业务ID',
                                   `operate_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作记录名称',
                                   `category` tinyint(1) NOT NULL COMMENT '类别: 1-店长红包自定义;',
                                   `type` tinyint(1) NOT NULL COMMENT '类型: 1-新增; 2-修改; 3-删除;',
                                   `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '日志详情',
                                   `operate_id` bigint NULL DEFAULT NULL COMMENT '操作人ID',
                                   `operate_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
                                   `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人用户名',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
                                   `del_flag` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   INDEX `idx_category`(`category`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作记录表' ROW_FORMAT = Dynamic;
