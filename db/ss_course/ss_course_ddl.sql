create table device_info
(
    id          bigint       not null
        primary key,
    school_name varchar(255) null,
    device_name varchar(255) null,
    brand       varchar(255) null,
    cpu         text         null,
    memory      varchar(255) null,
    os          varchar(255) null,
    os_type     varchar(255) null,
    os_version  varchar(255) null,
    net         varchar(255) null
);

create table franchisee
(
    id            int auto_increment comment '加盟商id'
        primary key,
    name          varchar(64)      null comment '加盟商名称',
    phone         varchar(255)     null comment '联系电话',
    agreement_num varchar(255)     null comment '合同编号',
    head_name     varchar(255)     null comment '负责人',
    qrcode        varchar(255)     null comment '二维码图片',
    disable       char default '0' not null comment '禁用状态，0不禁用，1禁用',
    create_by     varchar(255)     not null comment '创建者',
    create_time   datetime         null,
    update_time   datetime         null,
    deleted       char default '0' not null,
    sms_phone     varchar(20)      null
)
    comment '加盟商表' collate = utf8mb4_general_ci
                       row_format = DYNAMIC;

create table school
(
    id                      int auto_increment comment '主键ID'
        primary key,
    franchisee_id           int                         null comment '加盟商id',
    name                    varchar(255)                null comment '加盟校名称',
    management_fee          decimal(10, 2)              null comment '管理费',
    cost_time               datetime                    null comment '缴费时间',
    nickname                varchar(255)                null comment '签约智慧约读时的乙方名称（会有公司或者个人姓名）',
    no                      varchar(32)                 null comment '校区编号',
    standard                decimal(10, 2)              null comment '收费标准',
    phone                   varchar(255)                null comment '联系电话',
    last_end_date           datetime                    null comment '上期结算终止时间',
    qrcode                  varchar(255)                null comment '二维码',
    disable                 char           default '0'  not null comment '禁用状态，0不禁用，1禁用',
    create_by               varchar(255)                not null comment '创建者',
    create_time             datetime                    null comment '创建时间',
    update_time             datetime                    null comment '更新时间',
    deleted                 char           default '0'  not null comment '逻辑删除: 0-正常; 1-删除;',
    management_fee_discount decimal(10, 2) default 1.00 null comment '管理费用折扣'
)
    collate = utf8mb4_general_ci
    row_format = DYNAMIC;

create table school_device
(
    id                 bigint auto_increment comment '主键ID'
        primary key,
    create_time        datetime          null comment '创建时间',
    update_time        datetime          null comment '更新时间',
    remak              varchar(255)      null comment '备注信息',
    del_flag           tinyint default 0 null comment '逻辑删除: 0-正常; 1-删除;',
    franchisee_name    varchar(50)       null comment '加盟商名称',
    franchisee_id      bigint            null comment '加盟商ID',
    school_name        varchar(50)       null comment '校区名称',
    school_id          bigint            null comment '校区ID',
    device_id          bigint            null comment '设备ID',
    device_name        varchar(50)       null comment '设备名称',
    device_unique      varchar(50)       null comment '设备唯一编号',
    device_address     varchar(200)      null comment '设备安装地址',
    device_expire_date datetime          null comment '设备到期时间',
    device_purpose     tinyint           null comment '设备类型: 0-正式; 1-试用; 2-弃用; 3-内部;',
    device_status      tinyint default 0 null comment '设备状态: 0-正常;1-禁用;',
    school_no          varchar(20)       null comment '校区编号',
    room_id            bigint            null comment '教室ID',
    room_name          varchar(50)       null comment '教室名称',
    device_type        tinyint default 0 null comment '设备类型: 0-百家云; 1-双师设备;'
)
    charset = utf8mb3
    row_format = DYNAMIC;

create table ss_appointment_class_log
(
    id                      bigint auto_increment comment '主键ID'
        primary key,
    source                  bigint       null comment '所属门店',
    class_id                bigint       not null comment '班级ID',
    campus_id               bigint       not null comment '约课校区ID',
    class_room_id           bigint       not null comment '约课教室ID',
    device_id               bigint       not null comment '约课设备ID',
    appointment_time        datetime     null comment '预约时间',
    cancel_appointment_time datetime     null comment '取消预约时间',
    xgj_campus_id           varchar(255) null comment '校管家校区ID',
    xgj_class_room_id       varchar(255) null comment '校管家教室ID',
    ctime                   datetime     null comment '创建时间',
    creator                 varchar(64)  null comment '创建者',
    mtime                   datetime     null comment '编辑时间',
    modifer                 varchar(64)  null comment '编辑者'
)
    comment '门店预约班级记录表' collate = utf8mb4_general_ci
                                 row_format = DYNAMIC;

create index idx_campus_id
    on ss_appointment_class_log (campus_id);

create index idx_class_id
    on ss_appointment_class_log (class_id);

create index idx_class_room_id
    on ss_appointment_class_log (class_room_id);

create index idx_device_id
    on ss_appointment_class_log (device_id);

create index idx_source
    on ss_appointment_class_log (source);

create index idx_xgj_campus_id
    on ss_appointment_class_log (xgj_campus_id);

create index idx_xgj_class_room_id
    on ss_appointment_class_log (xgj_class_room_id);

create table ss_auth_room_log
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    business_id    bigint      not null comment '业务表ID',
    edit_auth_type tinyint     not null comment '修改授权类型: 0-班级授权; 1-课次授权;',
    old_value      text        null comment '旧授权设备ID值(多个教室以英文逗号分割)',
    new_value      text        null comment '新授权设备ID值(多个教室以英文逗号分割)',
    ctime          datetime    null comment '授权修改时间',
    creator        varchar(64) null comment '创建者',
    mtime          datetime    null comment '编辑时间',
    modifer        varchar(64) null comment '编辑者'
)
    comment '班级/课次授权修改记录表' collate = utf8mb4_general_ci
                                      row_format = DYNAMIC;

create index idx_business_id
    on ss_auth_room_log (business_id);

create index idx_edit_auth_type
    on ss_auth_room_log (edit_auth_type);

create table ss_campus
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    xgj_campus_id varchar(255)      null comment '校管家校区ID(类型为主讲端时为空)',
    region_name   varchar(255)      null comment '大区名称',
    campus_no     varchar(255)      null comment '校区编号',
    campus_name   varchar(255)      not null comment '校区名称',
    campus_state  tinyint default 0 not null comment '校区状态: 0-启用; 1-禁用;',
    campus_type   tinyint           not null comment '校区类型: 1-主讲端; 2-教室端;',
    ctime         datetime          null comment '创建时间',
    creator       varchar(64)       null comment '创建者',
    mtime         datetime          null comment '编辑时间',
    modifer       varchar(64)       null comment '编辑者'
)
    comment '校区表' collate = utf8mb4_general_ci
                     row_format = DYNAMIC;

create index idx_campus_no
    on ss_campus (campus_no);

create index idx_campus_state
    on ss_campus (campus_state);

create index idx_campus_type
    on ss_campus (campus_type);

create index idx_xgj_campus_id
    on ss_campus (xgj_campus_id);

create table ss_class
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    class_name     varchar(255)      not null comment '班级名称',
    grade          tinyint           not null comment '年级(字典类型: ss_level)',
    class_state    tinyint default 0 not null comment '班级状态: 0-正常; 1-已结业;',
    is_sync_xiaogj tinyint default 1 not null comment '是否同步校管家: 0-否; 1-是;',
    class_type     tinyint           null comment '班级类型: 0-读书会; 1-点播班;',
    ctime          datetime          null comment '创建时间',
    creator        varchar(64)       null comment '创建者',
    mtime          datetime          null comment '编辑时间',
    modifer        varchar(64)       null comment '编辑者'
)
    comment '班级信息表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_class_state
    on ss_class (class_state);

create index idx_class_type
    on ss_class (class_type);

create index idx_is_sync_xiaogj
    on ss_class (is_sync_xiaogj);

create table ss_class_auth_room
(
    id                 bigint auto_increment comment '主键ID'
        primary key,
    source             bigint       null comment '所属门店',
    class_id           bigint       not null comment '班级ID',
    xgj_class_id       varchar(255) null comment '校管家班级ID',
    campus_id          bigint       not null comment '校区ID',
    class_room_id      bigint       null comment '教室ID',
    device_id          bigint       null comment '教室端设备ID',
    appointment_time   datetime     null comment '预约时间/授权时间',
    xgj_campus_id      varchar(255) not null comment '校管家校区ID',
    xgj_class_room_id  varchar(255) null comment '校管家教室ID',
    class_time_ids     text         null comment '课次IDS(代表临时授权课次,多个以英文逗号分隔)',
    appointment_status tinyint      not null comment '预约状态:0-未预约;1-已预约',
    ctime              datetime     null comment '创建时间',
    creator            varchar(64)  null comment '创建者',
    mtime              datetime     null comment '编辑时间',
    modifer            varchar(64)  null comment '编辑者'
)
    comment '班级授权教室表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

create index idx_appointment_status
    on ss_class_auth_room (appointment_status);

create index idx_campus_id
    on ss_class_auth_room (campus_id);

create index idx_class_id
    on ss_class_auth_room (class_id);

create index idx_class_room_id
    on ss_class_auth_room (class_room_id);

create index idx_device_id
    on ss_class_auth_room (device_id);

create index idx_source
    on ss_class_auth_room (source);

create index idx_xgj_campus_id
    on ss_class_auth_room (xgj_campus_id);

create index idx_xgj_class_room_id
    on ss_class_auth_room (xgj_class_room_id);

create index idx_xiaogj_class_id
    on ss_class_auth_room (xgj_class_id);

create table ss_class_auth_room_student
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    class_id       bigint       not null comment '班级ID',
    campus_id      bigint       not null comment '校区ID',
    student_id     varchar(255) not null comment '校管家学生ID',
    student_mobile varchar(255) not null comment '校管家学生手机号',
    student_name   varchar(255) not null comment '校管家学生名称',
    ctime          datetime     null comment '创建时间',
    creator        varchar(64)  null comment '创建者',
    mtime          datetime     null comment '编辑时间',
    modifer        varchar(64)  null comment '编辑者'
)
    comment '班级授权校区学生表' collate = utf8mb4_general_ci
                                 row_format = DYNAMIC;

create index idx_campus_id
    on ss_class_auth_room_student (campus_id);

create index idx_class_id
    on ss_class_auth_room_student (class_id);

create table ss_class_room
(
    id                bigint auto_increment comment '主键ID'
        primary key,
    xgj_class_room_id varchar(255)      null comment '校管家教室ID(类型为主讲端时为空)',
    campus_id         bigint            not null comment '校区ID',
    class_room_name   varchar(255)      not null comment '教室名称',
    class_room_state  tinyint default 0 not null comment '教室状态: 0-启用; 1-禁用;',
    class_room_type   tinyint           not null comment '教室类型: 1-主讲端; 2-教室端;',
    ctime             datetime          null comment '创建时间',
    creator           varchar(64)       null comment '创建者',
    mtime             datetime          null comment '编辑时间',
    modifer           varchar(64)       null comment '编辑者'
)
    comment '教室信息表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_campus_id
    on ss_class_room (campus_id);

create index idx_class_room_state
    on ss_class_room (class_room_state);

create index idx_class_room_type
    on ss_class_room (class_room_type);

create index idx_xgj_class_room_id
    on ss_class_room (xgj_class_room_id);

create table ss_class_time
(
    id                           bigint auto_increment comment '主键ID'
        primary key,
    room_uuid                    varchar(255)      null comment '声网UUID',
    class_id                     bigint            not null comment '班级ID',
    course_schedule_id           bigint            null comment '排课ID',
    course_schedule_books_id     bigint            null comment '排课书籍ID',
    course_schedule_rule_id      bigint            null comment '排课规则ID',
    attend_class_date            date              not null comment '上课日期（yyyy-MM-dd）',
    attend_class_start_time      time              not null comment '上课开始时间（HH:mm）',
    attend_class_end_time        time              not null comment '上课结束时间（HH:mm）',
    is_sync_agora                tinyint default 0 not null comment '是否已同步声网创建课堂: 0-否; 1-是;',
    attend_class_type            tinyint           null comment '上课类型: 0-直播课; 1-点播课;',
    supervision_class_url        varchar(1000)     null comment '监课链接url路径',
    supervision_class_start_time datetime          null comment '监课开始时间(yyyy-MM-dd HH:mm:ss）',
    supervision_class_end_time   datetime          null comment '监课结束时间(yyyy-MM-dd HH:mm:ss）',
    lecturer_id                  bigint            null comment '主讲老师ID(ss_lecturer主键ID)',
    device_id                    bigint            null comment '主讲设备ID',
    class_room_id                bigint            null comment '主讲教室ID',
    books_id                     varchar(255)      null comment '书籍ID',
    books_name                   varchar(255)      null comment '书籍名称',
    recording_id                 bigint            null comment '课程库ID(录播课资源ID)',
    lecturer_room_code           varchar(10)       null comment '主讲端上课码(上课端标识1 + 5位随机数  例:115329)',
    class_room_code              varchar(10)       null comment '教室端上课码(教室端标识2 + 5位随机数  例:235329)',
    ctime                        datetime          null comment '创建时间',
    creator                      varchar(64)       null comment '创建者',
    mtime                        datetime          null comment '编辑时间',
    modifer                      varchar(64)       null comment '编辑者',
    constraint unx_class_room_code
        unique (class_room_code),
    constraint unx_lecturer_room_code
        unique (lecturer_room_code)
)
    comment '课次信息表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_attend_class_type
    on ss_class_time (attend_class_type);

create index idx_class_id
    on ss_class_time (class_id);

create index idx_class_room_id
    on ss_class_time (class_room_id);

create index idx_course_schedule_books_id
    on ss_class_time (course_schedule_books_id);

create index idx_course_schedule_id
    on ss_class_time (course_schedule_id);

create index idx_device_id
    on ss_class_time (device_id);

create index idx_is_sync_agora
    on ss_class_time (is_sync_agora);

create index idx_lecturer_id
    on ss_class_time (lecturer_id);

create index idx_recording_id
    on ss_class_time (recording_id);

create table ss_class_time_0705
(
    id                           bigint auto_increment comment '主键ID'
        primary key,
    room_uuid                    varchar(255)      null comment '声网UUID',
    class_id                     bigint            not null comment '班级ID',
    course_schedule_id           bigint            null comment '排课ID',
    course_schedule_books_id     bigint            null comment '排课书籍ID',
    course_schedule_rule_id      bigint            null comment '排课规则ID',
    attend_class_date            date              not null comment '上课日期（yyyy-MM-dd）',
    attend_class_start_time      time              not null comment '上课开始时间（HH:mm）',
    attend_class_end_time        time              not null comment '上课结束时间（HH:mm）',
    is_sync_agora                tinyint default 0 not null comment '是否已同步声网创建课堂: 0-否; 1-是;',
    attend_class_type            tinyint           null comment '上课类型: 0-直播课; 1-点播课;',
    supervision_class_url        varchar(1000)     null comment '监课链接url路径',
    supervision_class_start_time datetime          null comment '监课开始时间(yyyy-MM-dd HH:mm:ss）',
    supervision_class_end_time   datetime          null comment '监课结束时间(yyyy-MM-dd HH:mm:ss）',
    lecturer_id                  bigint            null comment '主讲老师ID(ss_lecturer主键ID)',
    device_id                    bigint            null comment '主讲设备ID',
    class_room_id                bigint            null comment '主讲教室ID',
    books_id                     varchar(255)      null comment '书籍ID',
    books_name                   varchar(255)      null comment '书籍名称',
    recording_id                 bigint            null comment '课程库ID(录播课资源ID)',
    lecturer_room_code           varchar(10)       null comment '主讲端上课码(上课端标识1 + 5位随机数  例:115329)',
    class_room_code              varchar(10)       null comment '教室端上课码(教室端标识2 + 5位随机数  例:235329)',
    ctime                        datetime          null comment '创建时间',
    creator                      varchar(64)       null comment '创建者',
    mtime                        datetime          null comment '编辑时间',
    modifer                      varchar(64)       null comment '编辑者',
    constraint unx_class_room_code
        unique (class_room_code),
    constraint unx_lecturer_room_code
        unique (lecturer_room_code)
)
    comment '课次信息表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_attend_class_type
    on ss_class_time_0705 (attend_class_type);

create index idx_class_id
    on ss_class_time_0705 (class_id);

create index idx_class_room_id
    on ss_class_time_0705 (class_room_id);

create index idx_course_schedule_books_id
    on ss_class_time_0705 (course_schedule_books_id);

create index idx_course_schedule_id
    on ss_class_time_0705 (course_schedule_id);

create index idx_device_id
    on ss_class_time_0705 (device_id);

create index idx_is_sync_agora
    on ss_class_time_0705 (is_sync_agora);

create index idx_lecturer_id
    on ss_class_time_0705 (lecturer_id);

create index idx_recording_id
    on ss_class_time_0705 (recording_id);

create table ss_class_time_auth_room
(
    id                bigint auto_increment comment '主键ID'
        primary key,
    class_id          bigint       not null comment '班级ID',
    class_time_id     bigint       not null comment '课次ID',
    xgj_class_time_id varchar(255) null comment '校管家课次ID',
    campus_id         bigint       not null comment '校区ID',
    class_room_id     bigint       not null comment '教室ID',
    device_id         bigint       not null comment '教室端设备ID',
    xgj_campus_id     varchar(255) not null comment '校管家校区ID',
    xgj_class_room_id varchar(255) not null comment '校管家教室ID',
    xiaogj_delete_log varchar(255) null comment '校管家排课删除状态',
    ctime             datetime     null comment '创建时间',
    creator           varchar(64)  null comment '创建者',
    mtime             datetime     null comment '编辑时间',
    modifer           varchar(64)  null comment '编辑者'
)
    comment '课次授权教室表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

create index idx_campus_id
    on ss_class_time_auth_room (campus_id);

create index idx_class_id
    on ss_class_time_auth_room (class_id);

create index idx_class_room_id
    on ss_class_time_auth_room (class_room_id);

create index idx_class_time_id
    on ss_class_time_auth_room (class_time_id);

create index idx_device_id
    on ss_class_time_auth_room (device_id);

create index idx_xgj_campus_id
    on ss_class_time_auth_room (xgj_campus_id);

create index idx_xgj_class_room_id
    on ss_class_time_auth_room (xgj_class_room_id);

create index idx_xgj_class_time_id
    on ss_class_time_auth_room (xgj_class_time_id);

create table ss_class_time_auth_room_0705
(
    id                bigint auto_increment comment '主键ID'
        primary key,
    class_id          bigint       not null comment '班级ID',
    class_time_id     bigint       not null comment '课次ID',
    xgj_class_time_id varchar(255) null comment '校管家课次ID',
    campus_id         bigint       not null comment '校区ID',
    class_room_id     bigint       not null comment '教室ID',
    device_id         bigint       not null comment '教室端设备ID',
    xgj_campus_id     varchar(255) not null comment '校管家校区ID',
    xgj_class_room_id varchar(255) not null comment '校管家教室ID',
    ctime             datetime     null comment '创建时间',
    creator           varchar(64)  null comment '创建者',
    mtime             datetime     null comment '编辑时间',
    modifer           varchar(64)  null comment '编辑者'
)
    comment '课次授权教室表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

create index idx_campus_id
    on ss_class_time_auth_room_0705 (campus_id);

create index idx_class_id
    on ss_class_time_auth_room_0705 (class_id);

create index idx_class_room_id
    on ss_class_time_auth_room_0705 (class_room_id);

create index idx_class_time_id
    on ss_class_time_auth_room_0705 (class_time_id);

create index idx_device_id
    on ss_class_time_auth_room_0705 (device_id);

create index idx_xgj_campus_id
    on ss_class_time_auth_room_0705 (xgj_campus_id);

create index idx_xgj_class_room_id
    on ss_class_time_auth_room_0705 (xgj_class_room_id);

create index idx_xgj_class_time_id
    on ss_class_time_auth_room_0705 (xgj_class_time_id);

create table ss_class_time_auth_room_history
(
    id                bigint auto_increment comment '主键ID'
        primary key,
    class_id          bigint                              not null comment '班级ID',
    class_time_id     bigint                              not null comment '课次ID',
    xgj_class_time_id varchar(255)                        null comment '校管家课次ID',
    campus_id         bigint                              not null comment '校区ID',
    class_room_id     bigint                              not null comment '教室ID',
    device_id         bigint                              not null comment '教室端设备ID',
    xgj_campus_id     varchar(255)                        not null comment '校管家校区ID',
    xgj_class_room_id varchar(255)                        not null comment '校管家教室ID',
    ctime             datetime                            null comment '创建时间',
    creator           varchar(64)                         null comment '创建者',
    mtime             datetime                            null comment '编辑时间',
    modifer           varchar(64)                         null comment '编辑者',
    migrated_at       timestamp default CURRENT_TIMESTAMP not null comment '迁移时间'
)
    comment '课次授权教室历史表' collate = utf8mb4_general_ci
                                 row_format = DYNAMIC;

create index idx_campus_id
    on ss_class_time_auth_room_history (campus_id);

create index idx_class_id
    on ss_class_time_auth_room_history (class_id);

create index idx_class_room_id
    on ss_class_time_auth_room_history (class_room_id);

create index idx_class_time_id
    on ss_class_time_auth_room_history (class_time_id);

create index idx_device_id
    on ss_class_time_auth_room_history (device_id);

create index idx_xgj_campus_id
    on ss_class_time_auth_room_history (xgj_campus_id);

create index idx_xgj_class_room_id
    on ss_class_time_auth_room_history (xgj_class_room_id);

create index idx_xgj_class_time_id
    on ss_class_time_auth_room_history (xgj_class_time_id);

create table ss_class_time_student
(
    id                      bigint auto_increment comment '主键ID'
        primary key,
    class_id                bigint            not null comment '班级ID',
    class_time_id           bigint            not null comment '课次ID',
    class_time_auth_room_id bigint            not null comment '课次授权教室表ID',
    campus_id               bigint            not null comment '校区ID',
    class_room_id           bigint            not null comment '教室ID',
    device_id               bigint            not null comment '教室设备ID',
    student_id              varchar(255)      not null comment '校管家学生ID',
    student_mobile          varchar(255)      not null comment '校管家学生手机号',
    student_name            varchar(255)      not null comment '校管家学生名称',
    interactor_bind_status  tinyint default 0 not null comment '互动题绑定状态: 0-未绑定; 1-已绑定;(废弃)',
    ctime                   datetime          null comment '创建时间',
    creator                 varchar(64)       null comment '创建者',
    mtime                   datetime          null comment '编辑时间',
    modifer                 varchar(64)       null comment '编辑者'
)
    comment '校区上课学生表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

create index idx_campus_id
    on ss_class_time_student (campus_id);

create index idx_class_id
    on ss_class_time_student (class_id);

create index idx_class_room_id
    on ss_class_time_student (class_room_id);

create index idx_class_time_auth_room_id
    on ss_class_time_student (class_time_auth_room_id);

create index idx_class_time_id
    on ss_class_time_student (class_time_id);

create index idx_device_id
    on ss_class_time_student (device_id);

create table ss_class_time_student_history
(
    id                      bigint auto_increment comment '主键ID'
        primary key,
    class_id                bigint                              not null comment '班级ID',
    class_time_id           bigint                              not null comment '课次ID',
    class_time_auth_room_id bigint                              not null comment '课次授权教室表ID',
    campus_id               bigint                              not null comment '校区ID',
    class_room_id           bigint                              not null comment '教室ID',
    device_id               bigint                              not null comment '教室设备ID',
    student_id              varchar(255)                        not null comment '校管家学生ID',
    student_mobile          varchar(255)                        not null comment '校管家学生手机号',
    student_name            varchar(255)                        not null comment '校管家学生名称',
    interactor_bind_status  tinyint   default 0                 not null comment '互动题绑定状态: 0-未绑定; 1-已绑定;(废弃)',
    ctime                   datetime                            null comment '创建时间',
    creator                 varchar(64)                         null comment '创建者',
    mtime                   datetime                            null comment '编辑时间',
    modifer                 varchar(64)                         null comment '编辑者',
    migrated_at             timestamp default CURRENT_TIMESTAMP not null comment '迁移时间'
)
    comment '校区上课学生历史表' collate = utf8mb4_general_ci
                                 row_format = DYNAMIC;

create index idx_campus_id
    on ss_class_time_student_history (campus_id);

create index idx_class_id
    on ss_class_time_student_history (class_id);

create index idx_class_room_id
    on ss_class_time_student_history (class_room_id);

create index idx_class_time_auth_room_id
    on ss_class_time_student_history (class_time_auth_room_id);

create index idx_class_time_id
    on ss_class_time_student_history (class_time_id);

create index idx_device_id
    on ss_class_time_student_history (device_id);

create table ss_course_schedule
(
    id                      bigint auto_increment comment '主键ID'
        primary key,
    class_id                bigint      not null comment '班级ID',
    lecturer_id             bigint      not null comment '主讲老师ID(ss_lecturer主键ID)',
    class_time_method       tinyint     not null comment '排课方式: 0-按周排课; 1-按日历排课;',
    attend_class_start_date date        null comment '上课开始日期（yyyy-MM-dd）',
    attend_class_end_date   date        null comment '上课结束日期（yyyy-MM-dd）',
    schedule_cap            int         null comment '排课上限几次',
    attend_class_type       tinyint     not null comment '上课类型: 0-直播课; 1-点播课;',
    ctime                   datetime    null comment '创建时间',
    creator                 varchar(64) null comment '创建者',
    mtime                   datetime    null comment '编辑时间',
    modifer                 varchar(64) null comment '编辑者'
)
    comment '排课表' collate = utf8mb4_general_ci
                     row_format = DYNAMIC;

create index idx_attend_class_type
    on ss_course_schedule (attend_class_type);

create index idx_class_id
    on ss_course_schedule (class_id);

create index idx_lecturer_id
    on ss_course_schedule (lecturer_id);

create table ss_course_schedule_books
(
    id                 bigint auto_increment comment '主键ID'
        primary key,
    class_id           bigint       not null comment '班级ID',
    course_schedule_id bigint       not null comment '排课ID',
    how_many_times     int          null comment '第几次上课书籍',
    books_id           varchar(255) null comment '书籍ID',
    books_name         varchar(255) null comment '书籍名称',
    ctime              datetime     null comment '创建时间',
    creator            varchar(64)  null comment '创建者',
    mtime              datetime     null comment '编辑时间',
    modifer            varchar(64)  null comment '编辑者'
)
    comment '排课书籍表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_class_id
    on ss_course_schedule_books (class_id);

create index idx_course_schedule_id
    on ss_course_schedule_books (course_schedule_id);

create table ss_course_schedule_rule
(
    id                      bigint auto_increment comment '主键ID'
        primary key,
    class_id                bigint      not null comment '班级ID',
    device_id               bigint      null comment '直播设备ID',
    course_schedule_id      bigint      null comment '排课ID',
    attend_class_week       tinyint     null comment '上课周几(字典类型:week_type)',
    attend_class_start_time time        null comment '上课开始时间（HH:mm）',
    attend_class_end_time   time        null comment '上课结束时间（HH:mm）',
    ctime                   datetime    null comment '创建时间',
    creator                 varchar(64) null comment '创建者',
    mtime                   datetime    null comment '编辑时间',
    modifer                 varchar(64) null comment '编辑者'
)
    comment '排课规则表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_class_id
    on ss_course_schedule_rule (class_id);

create index idx_course_schedule_id
    on ss_course_schedule_rule (course_schedule_id);

create index idx_device_id
    on ss_course_schedule_rule (device_id);

create table ss_current_stream
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    room_uuid     varchar(255) null comment '声网UUID',
    class_time_id bigint       not null comment '课次ID',
    streams       text         null comment '当前九屏的流相关',
    ctime         datetime     null comment '创建时间',
    creator       varchar(64)  null comment '创建者',
    mtime         datetime     null comment '编辑时间',
    modifer       varchar(64)  null comment '编辑者'
)
    comment '当前九宫格监听流';

create table ss_device
(
    id                   bigint auto_increment comment '主键ID'
        primary key,
    campus_id            bigint            null comment '校区ID',
    class_room_id        bigint            null comment '教室ID',
    device_name          varchar(255)      null comment '设备名称',
    device_no            varchar(255)      not null comment '设备号',
    device_type          tinyint           null comment '设备类型: 1-主讲端; 2-教室端;',
    device_state         tinyint default 0 null comment '设备状态: 0-启用; 1-禁用;',
    device_active        int     default 0 null comment '设备是否激活:0-未激活;1-已激活:',
    device_arrears       int     default 0 null comment '设备欠费状态:0-正常; 其他状态为欠费(读书会欠费;管理费欠费;设备费欠费;合同欠费;)',
    is_on_line           tinyint default 0 null comment '是否在线: 0-否; 1-是;',
    indate_forever       int     default 1 null comment '设备是否永久:0-否;1-是;',
    indate_start         datetime          null comment '有效期开始时间',
    indate_end           datetime          null comment '有效期结束时间',
    config_id            bigint  default 1 null comment '设备配置表ID',
    audio_config_id      bigint            null comment '音频配置ID',
    agora_recording_type tinyint           null comment '主讲端录课方式:0-页面录制;1-云端录制;',
    live_background      varchar(500)      null comment '直播背景图路径',
    sdk_type             tinyint default 1 null comment '终端SDK版本:0-webSDK;1-Electron',
    ctime                datetime          null comment '创建时间',
    creator              varchar(64)       null comment '创建者',
    mtime                datetime          null comment '编辑时间',
    modifer              varchar(64)       null comment '编辑者',
    device_uuid          varchar(255)      null comment '设备是否是由UUID生成不为空代表注册设备码是由uuid生成',
    constraint uni_device_no
        unique (device_no)
)
    comment '设备表' collate = utf8mb4_general_ci
                     row_format = DYNAMIC;

create index idx_campus_id
    on ss_device (campus_id);

create index idx_class_room_id
    on ss_device (class_room_id);

create index idx_device_active
    on ss_device (device_active);

create index idx_device_state
    on ss_device (device_state);

create index idx_device_type
    on ss_device (device_type);

create index idx_indate_forever
    on ss_device (indate_forever);

create index idx_is_on_line
    on ss_device (is_on_line);

create table ss_device_audio_config
(
    id                             bigint auto_increment comment '主键ID'
        primary key,
    parameters                     text         null comment '参数配置类:
che.audio.start_debug_recording-本地生成音频文件
che.audio.codec.name-打开OPUS和百家云相似,关闭OPUS是声网独特音质
che.audio.mute.input.channel-忽略右声道杂音
che.audio.current.recording.boostMode-关闭系统音量调整
che.audio.enable.agc-关闭增益调整
che.audio.input.volume-输入音量',
    in_class_parameters            varchar(500) null comment '加入频道之后的参数',
    adjust_recording_signal_volume int          null comment '音量增益',
    ctime                          datetime     null comment '创建时间',
    creator                        varchar(64)  null comment '创建者',
    mtime                          datetime     null comment '编辑时间',
    modifer                        varchar(64)  null comment '编辑者',
    remark                         varchar(500) null comment '备注'
)
    comment '设备音频相关配置';

create table ss_device_config
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    t_bw          int               null comment '教师端大流宽度',
    t_bh          int               null comment '教师端大流高度',
    t_bb          int               null comment '教师端大码率',
    t_bf          int               null comment '教师端大帧率',
    t_sw          int               null comment '教师端小流宽度',
    t_sh          int               null comment '教师端小流高度',
    t_sb          int               null comment '教师端小码率',
    t_sf          int               null comment '教师端小帧率',
    s_bw          int               null comment '学生端大流宽度',
    s_bh          int               null comment '学生端大流高度',
    s_bb          int               null comment '学生端大码率',
    s_bf          int               null comment '学生端大帧率',
    s_sw          int               null comment '学生端小流宽度',
    s_sh          int               null comment '学生端小流高度',
    s_sb          int               null comment '学生端小码率',
    s_sf          int               null comment '学生端小帧率',
    t_hd          tinyint           null comment '教师是否订阅大流:0-是;1-否;',
    s_hd          tinyint           null comment '学生端是否订阅大流:0-是;1-否;',
    s_show_number int     default 8 null comment '九空格展示学生数量',
    remark        varchar(500)      null comment '备注',
    ctime         datetime          null comment '创建时间',
    creator       varchar(64)       null comment '创建者',
    mtime         datetime          null comment '编辑时间',
    modifer       varchar(64)       null comment '编辑者',
    log_enable    tinyint default 0 null comment '日志开关是否开启:0-关闭;1-开启;'
)
    comment '设备配置参数表' row_format = DYNAMIC;

create table ss_in_out_log
(
    id                bigint auto_increment comment '主键ID'
        primary key,
    class_time_id     bigint      not null comment '课次ID',
    device_id         bigint      not null comment '教室端设备ID',
    operate_room_type tinyint     not null comment '点播课操作类型: 0-进入课程; 1-离开课程;',
    ctime             datetime    null comment '创建时间',
    creator           varchar(64) null comment '创建者',
    mtime             datetime    null comment '编辑时间',
    modifer           varchar(64) null comment '编辑者'
)
    comment '点播课教室端进出课程记录' collate = utf8mb4_general_ci
                                       row_format = DYNAMIC;

create index idx_class_time_id
    on ss_in_out_log (class_time_id);

create index idx_device_id
    on ss_in_out_log (device_id);

create table ss_interaction_consequence
(
    id                     bigint auto_increment comment '主键ID'
        primary key,
    interaction_setting_id bigint       not null comment '互动设置ID',
    class_time_id          bigint       not null comment '课次ID',
    device_id              bigint       not null comment '设备ID',
    campus_id              bigint       not null comment '校区ID',
    class_room_id          bigint       not null comment '教室ID',
    student_id             varchar(255) not null comment '校管家学生ID',
    student_no             varchar(255) not null comment '校管家学号',
    student_name           varchar(255) not null comment '校管家学生名称',
    integral_number        int          null comment '抢红包-抢到积分数量',
    answer_option          varchar(10)  null comment '答题抢票器-选项',
    ctime                  datetime     null comment '创建时间',
    creator                varchar(64)  null comment '创建者',
    mtime                  datetime     null comment '编辑时间',
    modifer                varchar(64)  null comment '编辑者'
)
    comment '互动结果表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_campus_id
    on ss_interaction_consequence (campus_id);

create index idx_class_room_id
    on ss_interaction_consequence (class_room_id);

create index idx_class_time_id
    on ss_interaction_consequence (class_time_id);

create index idx_device_id
    on ss_interaction_consequence (device_id);

create index idx_interaction_setting_id
    on ss_interaction_consequence (interaction_setting_id);

create table ss_interaction_consequence_history
(
    id                     bigint auto_increment comment '主键ID'
        primary key,
    interaction_setting_id bigint                              not null comment '互动设置ID',
    class_time_id          bigint                              not null comment '课次ID',
    device_id              bigint                              not null comment '设备ID',
    campus_id              bigint                              not null comment '校区ID',
    class_room_id          bigint                              not null comment '教室ID',
    student_id             varchar(255)                        not null comment '校管家学生ID',
    student_no             varchar(255)                        not null comment '校管家学号',
    student_name           varchar(255)                        not null comment '校管家学生名称',
    integral_number        int                                 null comment '抢红包-抢到积分数量',
    answer_option          varchar(10)                         null comment '答题抢票器-选项',
    ctime                  datetime                            null comment '创建时间',
    creator                varchar(64)                         null comment '创建者',
    mtime                  datetime                            null comment '编辑时间',
    modifer                varchar(64)                         null comment '编辑者',
    migrated_at            timestamp default CURRENT_TIMESTAMP not null comment '迁移时间'
)
    comment '互动结果历史表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

create index idx_campus_id
    on ss_interaction_consequence_history (campus_id);

create index idx_class_room_id
    on ss_interaction_consequence_history (class_room_id);

create index idx_class_time_id
    on ss_interaction_consequence_history (class_time_id);

create index idx_device_id
    on ss_interaction_consequence_history (device_id);

create index idx_interaction_setting_id
    on ss_interaction_consequence_history (interaction_setting_id);

create table ss_interaction_red_packet_detail
(
    id                          bigint auto_increment comment '主键ID'
        primary key,
    interaction_setting_id      bigint      not null comment '互动设置ID',
    class_time_id               bigint      not null comment '课次ID',
    device_id                   bigint      null comment '设备ID',
    campus_id                   bigint      null comment '校区ID',
    class_room_id               bigint      null comment '教室ID',
    integral_number             int         not null comment '红包积分数量',
    red_packet_number           int         null comment '红包数量',
    remainder_integral_number   int         null comment '平分后剩余积分',
    remainder_red_packet_number int         null comment '平分后剩余红包数量',
    ctime                       datetime    null comment '创建时间',
    creator                     varchar(64) null comment '创建者',
    mtime                       datetime    null comment '编辑时间',
    modifer                     varchar(64) null comment '编辑者'
)
    comment '红包拆分明细表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

create index idx_campus_id
    on ss_interaction_red_packet_detail (campus_id);

create index idx_class_room_id
    on ss_interaction_red_packet_detail (class_room_id);

create index idx_class_time_id
    on ss_interaction_red_packet_detail (class_time_id);

create index idx_device_id
    on ss_interaction_red_packet_detail (device_id);

create index idx_interaction_setting_id
    on ss_interaction_red_packet_detail (interaction_setting_id);

create table ss_interaction_setting
(
    id                   bigint auto_increment comment '主键ID'
        primary key,
    class_time_id        bigint      null comment '课次ID',
    send_type            tinyint     not null comment '下发类型: 1-主讲端下发; 2-门店端下发;',
    device_id            int         not null comment '设备ID',
    interaction_type     tinyint     not null comment '互动类型: 0-计时器; 1-答题抢票器; 2-抢红包;',
    timing_time          int         null comment '计时器-计时器时间(单位:分钟)',
    option_number        int         null comment '答题抢票器-选项个数',
    answer_countdown     int         null comment '答题抢票器-答题倒计时(单位:秒)',
    integral_number      int         null comment '抢红包-积分数量',
    red_packet_num       int         null comment '红包个数',
    interaction_duration int         null comment '互动持续时间',
    key_interval_num     int         null comment '互动器按键间隔生效次数',
    ctime                datetime    null comment '创建时间',
    creator              varchar(64) null comment '创建者',
    mtime                datetime    null comment '编辑时间',
    modifer              varchar(64) null comment '编辑者'
)
    comment '互动设置表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_class_time_id
    on ss_interaction_setting (class_time_id);

create index idx_device_id
    on ss_interaction_setting (device_id);

create index idx_interaction_type
    on ss_interaction_setting (interaction_type);

create index idx_send_type
    on ss_interaction_setting (send_type);

create table ss_lecturer
(
    id              bigint auto_increment comment '主键ID'
        primary key,
    xgj_lecturer_id varchar(255)      not null comment '校管家主讲老师ID',
    lecturer_name   varchar(255)      not null comment '主讲老师名称',
    lecturer_state  tinyint default 0 not null comment '主讲老师状态: 0-启用; 1-禁用;',
    ctime           datetime          null comment '创建时间',
    creator         varchar(64)       null comment '创建者',
    mtime           datetime          null comment '编辑时间',
    modifer         varchar(64)       null comment '编辑人'
)
    comment '主讲老师表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_lecturer_state
    on ss_lecturer (lecturer_state);

create index idx_xgj_lecturer_id
    on ss_lecturer (xgj_lecturer_id);

create table ss_operate_event_log
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    event_type    varchar(255) not null comment '操作事件类型',
    operate_id    bigint       null comment '操作用户ID',
    operate_name  varchar(255) null comment '操作用户名称',
    request_param text         not null comment '请求参数',
    result_param  text         not null comment '返回参数',
    ip_address    varchar(255) not null comment 'ip地址',
    ctime         datetime     null comment '创建时间',
    creator       varchar(64)  null comment '创建者',
    mtime         datetime     null comment '编辑时间',
    modifer       varchar(64)  null comment '编辑者'
)
    comment '操作事件记录表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

create table ss_recording
(
    id                                        bigint auto_increment comment '主键ID'
        primary key,
    recording_type                            tinyint           not null comment '录制类型:0-点播课;1-培训会议;',
    device_id                                 bigint            null comment '录制设备ID',
    grade                                     tinyint           null comment '年级(字典类型: ss_level)',
    books_id                                  varchar(255)      null comment '书籍ID',
    books_name                                varchar(255)      null comment '书籍名称',
    lecturer_id                               bigint            null comment '主讲老师ID(ss_lecturer主键ID)',
    lecturer_name                             varchar(255)      null comment '教室名称',
    original_course_start_date                datetime          null comment '原考勤班级上课开始时间',
    original_course_end_date                  datetime          null comment '原考勤班级上课结束时间',
    agora_record_id                           varchar(255)      null comment '声网录制ID',
    room_uuid                                 varchar(255)      null comment '声网房间UUID',
    shelf_status                              tinyint default 1 not null comment '上下架状态:0-未上架;1-已上架',
    recording_status                          tinyint default 0 not null comment '录制状态:0-待录制;1-录制中;2-正常录制完成;3-录制作废（重新录制）;4-视频处理中;',
    storage_type                              tinyint default 0 null comment '资源存储类型:0-OSS;1-VOD',
    vod_video_id                              varchar(255)      null comment '视频点播Vod中videoId',
    recording_resources                       varchar(255)      null comment '录制资源',
    agora_cloud_record_id                     varchar(255)      null comment '声网云端录制id',
    cloud_recording_resources                 varchar(500)      null comment '云端录制资源地址',
    agora_cloud_record_individual_resource_id varchar(500)      null comment '声网单流云录制iD',
    agora_cloud_record_individual_id          varchar(255)      null comment '声网云端录制单流id',
    ctime                                     datetime          null comment '创建时间',
    creator                                   varchar(64)       null comment '创建者',
    mtime                                     datetime          null comment '编辑时间',
    modifer                                   varchar(64)       null comment '编辑人'
)
    comment '双师录课' collate = utf8mb4_general_ci
                       row_format = DYNAMIC;

create index idx_device_id
    on ss_recording (device_id);

create index idx_lecturer_id
    on ss_recording (lecturer_id);

create index idx_recording_status
    on ss_recording (recording_status);

create index idx_recording_type
    on ss_recording (recording_type);

create index idx_shelf_status
    on ss_recording (shelf_status);

create index idx_storage_type
    on ss_recording (storage_type);

create index idx_vod_video_id
    on ss_recording (vod_video_id);

create table ss_screenshot_detail
(
    id              bigint auto_increment comment '主键ID'
        primary key,
    class_time_id   bigint       not null comment '课次ID',
    device_id       bigint       not null comment '教室端设备ID',
    resources_name  varchar(255) null comment '资源名称',
    resources_path  varchar(255) null comment '资源路径',
    ctime           datetime     null comment '创建时间',
    creator         varchar(64)  null comment '创建者',
    mtime           datetime     null comment '编辑时间',
    modifer         varchar(64)  null comment '编辑人',
    screenshot_time datetime     null comment '截图时间'
)
    comment '双师截图明细表' row_format = DYNAMIC;

create table ss_xiaogj_log
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    request_id     varchar(255) not null comment '请求ID',
    request_param  text         not null comment '请求参数',
    response_param text         null comment '返回参数',
    response_code  int          null comment '返回状态码: 200-响应成功; 400-响应失败;',
    event_key      varchar(64)  null comment '事件类型: class_course-双师排课;',
    timestamp      bigint       null comment '请求时间戳',
    ctime          datetime     null comment '创建时间',
    creator        varchar(64)  null comment '创建者',
    mtime          datetime     null comment '编辑时间',
    modifer        varchar(64)  null comment '编辑者'
)
    comment '校管家日志表' collate = utf8mb4_general_ci
                           row_format = DYNAMIC;

create index idx_request_id
    on ss_xiaogj_log (request_id);

create table ss_xiaogj_log_copy1
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    request_id     varchar(255) not null comment '请求ID',
    request_param  text         not null comment '请求参数',
    response_param text         null comment '返回参数',
    response_code  int          null comment '返回状态码: 200-响应成功; 400-响应失败;',
    event_key      varchar(64)  null comment '事件类型: class_course-双师排课;',
    timestamp      bigint       null comment '请求时间戳',
    ctime          datetime     null comment '创建时间',
    creator        varchar(64)  null comment '创建者',
    mtime          datetime     null comment '编辑时间',
    modifer        varchar(64)  null comment '编辑者'
)
    comment '校管家日志表' collate = utf8mb4_general_ci
                           row_format = DYNAMIC;

create index idx_request_id
    on ss_xiaogj_log_copy1 (request_id);

create table sys_dict_data
(
    id          bigint auto_increment comment '主键ID'
        primary key,
    dict_lable  varchar(30)   not null comment '字典标签',
    dict_value  varchar(1000) not null comment '字典健值',
    dict_type   varchar(30)   not null comment '字典类型',
    dict_pvalue varchar(30)   null comment '父级字典健值（对应父级dict_value字段值）',
    dict_sort   int           null comment '字典排序',
    status      int default 0 not null comment '状态: 0-正常；1-停用;',
    remark      varchar(300)  null comment '备注',
    is_del      int default 0 not null comment '是否删除: 0-否；1-是;',
    ctime       datetime      null comment '创建时间',
    creator     varchar(64)   null comment '创建者',
    mtime       datetime      null comment '编辑时间',
    modifer     varchar(64)   null comment '编辑者',
    dtime       datetime      null comment '删除时间'
)
    comment '字典数据表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create table sys_dict_type
(
    id        bigint auto_increment comment '主键ID'
        primary key,
    dict_name varchar(30)   not null comment '字典名称',
    dict_type varchar(30)   not null comment '字典类型',
    status    int default 0 not null comment '状态: 0-正常；1-停用;',
    remark    varchar(300)  null comment '备注',
    is_del    int default 0 not null comment '是否删除: 0-否；1-是;',
    ctime     datetime      null comment '创建时间',
    creator   varchar(64)   null comment '创建者',
    mtime     datetime      null comment '编辑时间',
    modifer   varchar(64)   null comment '编辑者',
    dtime     datetime      null comment '删除时间'
)
    comment '字典类型表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_dict_type
    on sys_dict_type (dict_type);

create table sys_manager
(
    id              bigint auto_increment comment '主键ID'
        primary key,
    uc_manager_id   bigint            not null comment '用户关联uc中的用户ID',
    mobile          varchar(11)       not null comment '管理员手机号，用于登录',
    state           tinyint default 0 not null comment '用户状态：0-启用; 1-禁用;',
    login_ip        varchar(32)       null comment '最后登录ip',
    last_login_time datetime          null comment '最后登录时间',
    ctime           datetime          null comment '创建时间',
    creator         varchar(64)       null comment '创建者',
    mtime           datetime          null comment '编辑时间',
    modifer         varchar(64)       null comment '编辑者',
    constraint uni_mobile
        unique (mobile)
)
    comment '双师系统-管理员表' collate = utf8mb4_general_ci
                                row_format = DYNAMIC;

create index idx_uc_manager_id
    on sys_manager (uc_manager_id);

create table sys_manager_info
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    uc_manager_id bigint            not null comment '用户关联uc中的用户ID',
    real_name     varchar(32)       not null comment '姓名',
    gender        tinyint default 0 not null comment '性别: 0-未知; 1-男; 2-女; ',
    age           int               null comment '年龄',
    email         varchar(32)       null comment '邮箱地址',
    remark        varchar(128)      null comment '备注信息',
    entry_time    date              null comment '入职时间',
    ctime         datetime          null comment '创建时间',
    creator       varchar(64)       null comment '创建者',
    mtime         datetime          null comment '编辑时间',
    modifer       varchar(64)       null comment '编辑者',
    constraint idx_uc_manager_id
        unique (uc_manager_id)
)
    comment '双师系统-管理员扩展信息表' collate = utf8mb4_general_ci
                                        row_format = DYNAMIC;

create table sys_manager_role
(
    id            bigint unsigned auto_increment comment '主键ID'
        primary key,
    uc_manager_id bigint      not null comment '用户关联uc中的用户ID',
    role_id       bigint      not null comment '角色ID',
    ctime         datetime    null comment '创建时间',
    creator       varchar(64) null comment '创建者',
    mtime         datetime    null comment '编辑时间',
    modifer       varchar(64) null comment '编辑者'
)
    comment '双师系统-用户与角色关联表' collate = utf8mb4_general_ci
                                        row_format = DYNAMIC;

create index idx_manager_id
    on sys_manager_role (uc_manager_id);

create index idx_role_id
    on sys_manager_role (role_id);

create table sys_menu
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    title         varchar(255)      null comment '菜单名称',
    label         varchar(255)      null comment '菜单组件',
    parent_id     bigint            not null comment '父菜单ID, 一级菜单为0',
    type          tinyint           not null comment '菜单类型: 1-一级目录; 2-功能模块; ',
    path_name     varchar(255)      null comment '访问路径',
    state         tinyint default 0 not null comment '菜单状态: 0-启用; 1-禁用; ',
    is_cache      tinyint default 0 not null comment '是否缓存: 0-否; 1-是;',
    is_show       tinyint default 0 not null comment '显示状态: 0-展示; 1-隐藏;',
    hide_children tinyint default 0 not null comment '是否展示子菜单: 0-展示; 1-不展示;',
    redirect      varchar(255)      null comment '页面重定向',
    sort          int               not null comment '排序',
    icon          varchar(50)       null comment '目录图标',
    remark        varchar(255)      null comment '备注',
    ctime         datetime          null comment '创建时间',
    creator       varchar(64)       null comment '创建者',
    mtime         datetime          null comment '编辑时间',
    modifer       varchar(64)       null comment '编辑者'
)
    comment '双师系统-菜单表' collate = utf8mb4_general_ci
                              row_format = DYNAMIC;

create table sys_role
(
    id      bigint unsigned auto_increment comment '主键ID'
        primary key,
    name    varchar(32)       not null comment '名称',
    state   tinyint default 0 not null comment '角色状态: 0-启用; 1-禁用; ',
    remark  varchar(255)      null comment '备注',
    ctime   datetime          null comment '创建时间',
    creator varchar(64)       null comment '创建者',
    mtime   datetime          null comment '编辑时间',
    modifer varchar(64)       null comment '编辑者'
)
    comment '双师系统-角色表' collate = utf8mb4_general_ci
                              row_format = DYNAMIC;

create table sys_role_menu
(
    id      bigint unsigned auto_increment comment '主键ID'
        primary key,
    menu_id bigint      not null comment '菜单ID',
    role_id bigint      not null comment '角色ID',
    ctime   datetime    null comment '创建时间',
    creator varchar(64) null comment '创建者',
    mtime   datetime    null comment '编辑时间',
    modifer varchar(64) null comment '编辑者'
)
    comment '双师系统-角色与菜单关联表' collate = utf8mb4_general_ci
                                        row_format = DYNAMIC;

create index idx_menu_id
    on sys_role_menu (menu_id);

create index idx_role_id
    on sys_role_menu (role_id);

create table xgj_campus
(
    cID     varchar(255)                 null,
    cField1 varchar(255) charset utf8mb4 null
)
    collate = utf8mb4_general_ci
    row_format = DYNAMIC;