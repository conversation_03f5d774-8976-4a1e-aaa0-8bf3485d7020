CREATE TABLE live_room_plan (
                                id BIGINT AUTO_INCREMENT COMMENT '主键id' PRIMARY KEY,
                                plan_name VARCHAR(255) NULL COMMENT '计划名称',
                                level INT NULL COMMENT '年级',
                                live_room INT NULL COMMENT '直播间',
                                publish_status TINYINT DEFAULT 0 NULL COMMENT '发布状态:0-未发布;1-已发布',
                                publisher_id BIGINT NULL COMMENT '发布人id',
                                publisher_name VARCHAR(255) NULL COMMENT '发布人姓名',
                                publish_time DATETIME NULL COMMENT '发布时间',
                                close_status TINYINT DEFAULT 0 NULL COMMENT '关闭状态:0-未关闭;1-已关闭',
                                create_by VARCHAR(255) NULL COMMENT '创建人',
                                create_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
                                update_by VARCHAR(255) NULL COMMENT '修改人',
                                update_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '修改时间',
                                del_flag TINYINT UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除:0-否;1-是'
) COMMENT '直播间计划';

CREATE TABLE live_room_plan_draft (
                                      id BIGINT AUTO_INCREMENT COMMENT '主键id' PRIMARY KEY,
                                      live_room_plan_id BIGINT NOT NULL COMMENT '对应主表的直播间计划id',
                                      draft_plan_name VARCHAR(255) NULL COMMENT '草稿计划名称',
                                      draft_level INT NULL COMMENT '草稿年级',
                                      draft_live_room INT NULL COMMENT '草稿直播间',
                                      create_by VARCHAR(255) NULL COMMENT '草稿创建人',
                                      create_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '草稿创建时间',
                                      update_by VARCHAR(255) NULL COMMENT '草稿修改人',
                                      update_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '草稿修改时间',
                                      del_flag TINYINT UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除:0-否;1-是'
) COMMENT '直播间计划影子表';

create table live_room_plan_detail
(
    id BIGINT AUTO_INCREMENT COMMENT '主键id' PRIMARY KEY,
    live_room_plan_id  bigint   null comment '直播间计划id',
    room_uuid    varchar(255)  null comment '声网UUID',
    time_slot_id     bigint   null comment '时段管理id',
    class_detail_type tinyint null comment '排期明细类型:1-单节课;2-常规课;3-精品课',
    class_start_time datetime null comment '上课开始日期（yyyy-MM-dd HH:mm:ss）',
    class_end_time   datetime null comment '上课结束时间（yyyy-MM-dd HH:mm:ss）',
    operator_name VARCHAR(255) NOT NULL COMMENT '操作人',
    operation_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '操作时间',
    create_by VARCHAR(255) NULL COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    update_by VARCHAR(255) NULL COMMENT '修改人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '修改时间',
    del_flag TINYINT UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除:0-否;1-是'
) COMMENT '直播间计划明细';

CREATE TABLE time_slot_management (
                                      id BIGINT AUTO_INCREMENT COMMENT '主键id' PRIMARY KEY,
                                      period_name VARCHAR(50) NOT NULL COMMENT '时段名称',
                                      start_time TIME NOT NULL COMMENT '开始时间 (HH:mm)',
                                      end_time TIME NOT NULL COMMENT '结束时间 (HH:mm)',
                                      operator_name VARCHAR(255) NOT NULL COMMENT '操作人',
                                      operation_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '操作时间',
                                      create_by VARCHAR(255) NULL COMMENT '创建人',
                                      create_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
                                      update_by VARCHAR(255) NULL COMMENT '修改人',
                                      update_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '修改时间',
                                      del_flag TINYINT UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除:0-否;1-是'
) COMMENT '时段管理表';

create table teaching_plan
(
    id BIGINT AUTO_INCREMENT COMMENT '主键id' PRIMARY KEY,
    live_room_plan_id bigint null comment '直播间计划id',
    course_package_id bigint null comment '课程包id',
    lecture_id        int    null comment '主讲id',
    publish_status TINYINT DEFAULT 0 NULL COMMENT '发布状态:0-未发布;1-已发布',
    publisher_id BIGINT NULL COMMENT '发布人id',
    publisher_name VARCHAR(255) NULL COMMENT '发布人姓名',
    publish_time DATETIME NULL COMMENT '发布时间',
    close_status TINYINT DEFAULT 0 NULL COMMENT '关闭状态:0-未关闭;1-已关闭',
    create_by VARCHAR(255) NULL COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    update_by VARCHAR(255) NULL COMMENT '修改人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '修改时间',
    del_flag TINYINT UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除:0-否;1-是'
)
    comment '教学计划';

CREATE TABLE teaching_plan_draft (
                                     id BIGINT AUTO_INCREMENT COMMENT '主键id' PRIMARY KEY,
                                     teaching_plan_id BIGINT NOT NULL COMMENT '对应主表的教学计划id',
                                     draft_live_room_plan_id BIGINT NULL COMMENT '草稿直播间计划id',
                                     draft_course_package_id BIGINT NULL COMMENT '草稿课程包id',
                                     draft_lecture_id INT NULL COMMENT '草稿主讲id',
                                     create_by VARCHAR(255) NULL COMMENT '草稿创建人',
                                     create_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '草稿创建时间',
                                     update_by VARCHAR(255) NULL COMMENT '草稿修改人',
                                     update_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '草稿修改时间',
                                     del_flag TINYINT UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除:0-否;1-是'
) COMMENT '教学计划影子表';


create table teaching_plan_detail
(
    id BIGINT AUTO_INCREMENT COMMENT '主键id' PRIMARY KEY,
    teaching_plan_id  bigint null comment '教学计划id',
    course_package_id bigint null comment '课程包id',
    course_package_detail_id bigint null comment '课程包明细id',
    live_room_plan_id bigint null comment '直播间计划id',
    live_room_plan_detail_id bigint null comment '直播间计划明细id',
    lecture_id        int    null comment '主讲id',
    create_by VARCHAR(255) NULL COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    update_by VARCHAR(255) NULL COMMENT '修改人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '修改时间',
    del_flag TINYINT UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除:0-否;1-是'
)
    comment '教学计划明细表';

create table lecture_management
(
    id BIGINT AUTO_INCREMENT COMMENT '主键id' PRIMARY KEY,
    wxwork_lecture_id bigint            null comment '企微中主讲老师id',
    `lesson_status`  tinyint default 0 null comment '带课状态:0-不带课;1-带课',
    create_by VARCHAR(255) NULL COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    update_by VARCHAR(255) NULL COMMENT '修改人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '修改时间',
    del_flag TINYINT UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除:0-否;1-是'
)
    comment '老师管理';