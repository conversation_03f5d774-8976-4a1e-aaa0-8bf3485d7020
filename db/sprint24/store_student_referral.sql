-- 为store_student表添加转介绍学员ID字段
-- 用于存储意向会员的转介绍学员信息

ALTER TABLE `store_student`
    ADD COLUMN `referral_student_id` bigint NULL DEFAULT NULL COMMENT '转介绍学员ID' AFTER `responsible_person`;

-- 添加索引以提高查询性能
CREATE INDEX `idx_referral_student_id` ON `store_student` (`referral_student_id`);

-- 添加外键约束（可选，确保数据完整性）
-- ALTER TABLE `store_student`
--     ADD CONSTRAINT `fk_store_student_referral`
--     FOREIGN KEY (`referral_student_id`) REFERENCES `store_student` (`user_id`);
