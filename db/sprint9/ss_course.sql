CREATE TABLE `store_course_hours_record`  (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                              `school_id` bigint NOT NULL COMMENT '校区ID',
                                              `store_id` bigint NOT NULL COMMENT '门店ID',
                                              `student_id` bigint NOT NULL COMMENT '学生ID',
                                              `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型: GIFT:赠送,,TRIAL:试听,ENROLL:正式,CONSUME:消耗,REFUND:退回\n',
                                              `count` int NOT NULL COMMENT '总课时',
                                              `batch_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '批次号,主要用于区分赠送与正式课时的关系,年月日+8位随机数组成',
                                              `quantity` int NOT NULL COMMENT '剩余课时',
                                              `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                              `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                              `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除',
                                              `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
                                              `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              INDEX `idx_student_id`(`student_id` ASC) USING BTREE,
                                              INDEX `idx_school_id`(`school_id` ASC) USING BTREE,
                                              INDEX `idx_store_id`(`store_id` ASC) USING BTREE,
                                              INDEX `idx_operation_type`(`operation_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 287 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '课时操作记录表' ROW_FORMAT = Dynamic;

CREATE TABLE `store_school`
(
    `id`            int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `school_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '校区名称',
    `region_id`     tinyint unsigned NOT NULL DEFAULT '0' COMMENT '大区ID',
    `province_code` varchar(255) COLLATE utf8mb4_general_ci                       DEFAULT NULL,
    `city_code`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
    `district_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
    `create_by`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '创建人',
    `update_by`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '更新人',
    `create_time`   datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `del_flag`      tinyint(1) DEFAULT '0' COMMENT '删除标记，0未删除，1已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='校区';


ALTER TABLE `t_employee_campus`
    ADD COLUMN `school_id` int UNSIGNED NULL DEFAULT NULL COMMENT '校区ID' AFTER `app_user_id`,
    ADD COLUMN `role_id` int UNSIGNED NULL DEFAULT NULL COMMENT '角色ID',
    ADD COLUMN `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态 0:正常 1:离职';

ALTER TABLE `t_class`
    ADD COLUMN `school_id` int UNSIGNED NULL DEFAULT NULL COMMENT '校区ID' AFTER `id`;


ALTER TABLE `t_class_student`
    ADD COLUMN `school_id` int UNSIGNED NULL DEFAULT NULL COMMENT '校区ID' AFTER `id`;

ALTER TABLE `t_student`
    ADD COLUMN `school_id` int UNSIGNED NULL DEFAULT NULL COMMENT '校区ID' AFTER `user_id`,
    ADD COLUMN `stage_id` int UNSIGNED NULL DEFAULT NULL COMMENT '阶段ID',
    ADD COLUMN `course_hours_gift` int DEFAULT '0'  COMMENT '赠送课时',
    ADD COLUMN `course_hours_trial` int DEFAULT '0'  COMMENT '试听课时',
    ADD COLUMN `course_hours_formal` int DEFAULT '0'  COMMENT '正式课时',
    ADD COLUMN `init_regular` tinyint UNSIGNED NULL DEFAULT NULL  COMMENT '初始标识 0: 试听 1: 正式';


ALTER TABLE `ss_campus`
    ADD COLUMN `create_time` datetime DEFAULT NULL COMMENT '创建时间',
ADD COLUMN `update_time` datetime DEFAULT NULL COMMENT '更新时间',
ADD COLUMN `del_flag` tinyint DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
ADD COLUMN `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
ADD COLUMN `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
ADD COLUMN `region_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '大区ID',
ADD COLUMN `province_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '省份编码',
ADD COLUMN `city_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市编码',
ADD COLUMN `district_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '区县编码',
ADD COLUMN `school_id` int UNSIGNED NULL DEFAULT NULL COMMENT '校区ID' AFTER `id`,
ADD COLUMN `signing_subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签约主体',
ADD COLUMN `signing_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签约类型',
ADD COLUMN `signing_subject_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签约主体手机号',
ADD COLUMN `service_period_start` date  DEFAULT NULL COMMENT '合作协议运营服务期',
ADD COLUMN `service_period_end` date  DEFAULT NULL COMMENT '合作协议运营服务期';

ALTER TABLE `ss_campus`
    MODIFY COLUMN `campus_type` tinyint UNSIGNED NOT NULL DEFAULT 2 COMMENT '校区类型: 1-主讲端; 2-教室端;' AFTER `campus_state`;

ALTER TABLE `ss_class_room`
    ADD COLUMN `school_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '校区ID',
ADD COLUMN `create_time` datetime DEFAULT NULL COMMENT '创建时间',
ADD COLUMN `update_time` datetime DEFAULT NULL COMMENT '更新时间',
ADD COLUMN `del_flag` tinyint DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
ADD COLUMN `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
ADD COLUMN `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人';

CREATE TABLE store_course_hours_log
(
    id             BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    school_id      int      NOT NULL COMMENT '学校ID',
    store_id       int      NOT NULL COMMENT '门店ID',
    student_id     BIGINT      NOT NULL COMMENT '学生ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型: GIFT,TRIAL,ENROLL,CONSUME,REFUND等',
    related_id     BIGINT                                                        DEFAULT NULL COMMENT '关联ID',
    course_hours   INT         NOT NULL COMMENT '每次操作的课时数量',
    create_time    DATETIME                                                      DEFAULT NULL COMMENT '创建时间',
    update_time    DATETIME                                                      DEFAULT NULL COMMENT '更新时间',
    del_flag       TINYINT                                                       DEFAULT '0' COMMENT '是否删除',
    create_by      VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    update_by      VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    INDEX          idx_student_id (student_id),
    INDEX          idx_school_id (school_id),
    INDEX          idx_store_id (store_id),
    INDEX          idx_operation_type (operation_type)
) COMMENT='课时操作日志表';

CREATE TABLE `store_course_hours_record`  (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                              `school_id` bigint NOT NULL COMMENT '校区ID',
                                              `store_id` bigint NOT NULL COMMENT '门店ID',
                                              `student_id` bigint NOT NULL COMMENT '学生ID',
                                              `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型: GIFT:赠送,,TRIAL:试听,ENROLL:正式,CONSUME:消耗,REFUND:退回\n',
                                              `count` int NOT NULL COMMENT '总课时',
                                              `batch_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '批次号,主要用于区分赠送与正式课时的关系,年月日+8位随机数组成',
                                              `quantity` int NOT NULL COMMENT '剩余课时',
                                              `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                              `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                              `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除',
                                              `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
                                              `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              INDEX `idx_student_id`(`student_id` ASC) USING BTREE,
                                              INDEX `idx_school_id`(`school_id` ASC) USING BTREE,
                                              INDEX `idx_store_id`(`store_id` ASC) USING BTREE,
                                              INDEX `idx_operation_type`(`operation_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 287 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '课时操作记录表' ROW_FORMAT = Dynamic;


ALTER TABLE `ss_course_dev`.`t_class`
    ADD COLUMN `head_teacher_id` bigint(20) NULL COMMENT '班主任id（非校管家）' AFTER `head_master_user_id`;

CREATE TRIGGER before_ss_campus_insert
    BEFORE INSERT ON ss_campus
    FOR EACH ROW
BEGIN
    DECLARE next_id INT;
    IF NEW.campus_no IS NULL OR NEW.campus_no = '' THEN
    -- 插入一条记录到计数器表以获取新的 ID
    INSERT INTO sequence () VALUES ();
    -- 获取插入的 ID
    SET next_id = LAST_INSERT_ID();
    -- 设置 campus_no
    SET NEW.campus_no = CONCAT('NSCH', LPAD(next_id, 4, '0'));
END IF;

IF NEW.xgj_campus_id IS NULL OR NEW.xgj_campus_id = '' THEN
        -- 生成并设置 xgj_campus_id
        SET NEW.xgj_campus_id = CONCAT('NEW-', UPPER(REPLACE(UUID(), '-', '')));END IF;
END

ALTER TABLE `t_class`
    MODIFY COLUMN `company_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL AFTER `update_time`,
    MODIFY COLUMN `shift_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '课程ID' AFTER `company_id`,
    MODIFY COLUMN `max_students_amount` int NULL COMMENT '预招人数,一对一的课程默认为1,0表示不限制' AFTER `pinyin_pre`,
    MODIFY COLUMN `open_date` datetime(3) NULL COMMENT '计划开班日期，格式yyyyMMdd' AFTER `max_students_amount`,
    MODIFY COLUMN `c_order` int NULL AFTER `classroom_id`,
    MODIFY COLUMN `describe` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注' AFTER `c_order`,
    MODIFY COLUMN `classid_dx` int NULL AFTER `max_birth_date`;


CREATE TRIGGER before_store_employee_insert
    BEFORE INSERT ON store_employee
    FOR EACH ROW
BEGIN
    DECLARE next_id INT;
    IF NEW.username IS NULL OR NEW.username = '' THEN
    -- 插入一条记录到计数器表以获取新的 ID
    INSERT INTO sequence () VALUES ();
    -- 获取插入的 ID
    SET next_id = LAST_INSERT_ID();
    -- 设置 username
    SET NEW.username = CONCAT('NE', LPAD(next_id, 4, '0'));
END IF;

IF NEW.xgj_user_id IS NULL OR NEW.xgj_user_id = '' THEN
        -- 生成并设置 xgj_user_id
        SET NEW.xgj_user_id = CONCAT('NEW-', UPPER(REPLACE(UUID(), '-', '')));END IF;
END



ALTER TABLE `store_employee`
MODIFY COLUMN `salt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '盐值' AFTER `password`;

ALTER TABLE `store_employee`
    MODIFY COLUMN `company_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' AFTER `wx_openid`,
    MODIFY COLUMN `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL AFTER `image`;



ALTER TABLE `store_employee`
    MODIFY COLUMN `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '密码' AFTER `username`;

ALTER TABLE `store_employee`
    MODIFY COLUMN `in_date` datetime(3) NULL DEFAULT NULL COMMENT '有效期' AFTER `sex`,
    MODIFY COLUMN `serial` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '序号' AFTER `is_binding_work_wx`;


ALTER TABLE `t_employee_campus`
    MODIFY COLUMN `campus_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' AFTER `employee_user_id`,
    MODIFY COLUMN `company_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' AFTER `position`;


ALTER TABLE `t_class_student`
    ADD COLUMN `change_class` tinyint NOT NULL DEFAULT 0 COMMENT '1转班' AFTER `update_by`;


CREATE TABLE sequence (
                          id INT AUTO_INCREMENT PRIMARY KEY
);

CREATE TABLE sequence_student (
   id INT AUTO_INCREMENT PRIMARY KEY
);



CREATE TRIGGER before_student_insert
    BEFORE INSERT ON t_student
    FOR EACH ROW
BEGIN
    DECLARE next_id INT;
    IF NEW.username IS NULL OR NEW.username = '' THEN
    -- 插入一条记录到计数器表以获取新的 ID
    INSERT INTO sequence_student () VALUES ();
    -- 获取插入的 ID
    SET next_id = LAST_INSERT_ID();
    -- 设置 username
    SET NEW.username = CONCAT('NS', LPAD(next_id, 4, '0'));
END IF;

IF NEW.xgj_user_id IS NULL OR NEW.xgj_user_id = '' THEN
        -- 生成并设置 xgj_user_id
        SET NEW.xgj_user_id = CONCAT('NEW-', UPPER(REPLACE(UUID(), '-', '')));END IF;
END


ALTER TABLE `store_employee`
    MODIFY COLUMN `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '密码' AFTER `username`;

CREATE TRIGGER before_student_insert
    BEFORE INSERT ON t_student
    FOR EACH ROW
BEGIN
    DECLARE next_id INT;
    DECLARE v_store_id INT;

    -- 生成并设置 username
    IF NEW.username IS NULL OR NEW.username = '' THEN
        -- 插入一条记录到计数器表以获取新的 ID
        INSERT INTO sequence_student () VALUES ();
        -- 获取插入的 ID
        SET next_id = LAST_INSERT_ID();
        -- 设置 username
        SET NEW.username = CONCAT('NS', LPAD(next_id, 4, '0'));
END IF;

-- 生成并设置 xgj_user_id
IF NEW.xgj_user_id IS NULL OR NEW.xgj_user_id = '' THEN
        SET NEW.xgj_user_id = CONCAT('NEW-', UPPER(REPLACE(UUID(), '-', '')));
END IF;

    -- 如果 store_id 为空，则根据 campus_id 查询并填充 store_id
    IF NEW.store_id IS NULL THEN
SELECT `id` INTO v_store_id
FROM `ss_campus`
WHERE `xgj_campus_id` COLLATE utf8mb4_0900_ai_ci = NEW.`campus_id` COLLATE utf8mb4_0900_ai_ci;

-- 设置 store_id
SET NEW.store_id = v_store_id;
END IF;
END


CREATE TRIGGER before_student_update
    BEFORE UPDATE ON t_student
    FOR EACH ROW
BEGIN
    DECLARE v_store_id INT;
    DECLARE old_campus_id CHAR(36);

    -- 获取旧的 campus_id
    SET old_campus_id = OLD.campus_id;

    -- 如果 campus_id 发生变化，则根据新的 campus_id 查询并填充 store_id
    IF NEW.campus_id <> old_campus_id THEN
    SELECT `id` INTO v_store_id
    FROM `ss_campus`
    WHERE `xgj_campus_id` COLLATE utf8mb4_0900_ai_ci = NEW.`campus_id` COLLATE utf8mb4_0900_ai_ci;

    -- 设置 store_id
    SET NEW.store_id = v_store_id;
END IF;
END

ALTER TABLE `t_employee_campus`
    MODIFY COLUMN `campus_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '校区' AFTER `employee_user_id`;

ALTER TABLE `t_class_student`
    ADD COLUMN `change_class` tinyint NOT NULL DEFAULT 0 COMMENT '1转班' AFTER `update_by`;


ALTER TABLE `t_employee_campus`
DROP INDEX `idx_user_id`,
ADD UNIQUE INDEX `idx_user_id`(`employee_user_id`, `campus_id`, `school_id`) USING BTREE;

ALTER TABLE store_course_hours_log
    ADD COLUMN `timetable_id` bigint DEFAULT '0' COMMENT '课表ID 非课消默认为0' AFTER `operation_type`;

ALTER TABLE store_course_hours_log
    ADD COLUMN `log_type` INT DEFAULT 0  COMMENT '日志类型,0:新增试听学员 1:学员添加课时 2:学员续费 3:学员部分退费 4:学员全部退费 5:学员课消' AFTER `timetable_id`;


CREATE TRIGGER `before_insert_t_employee_campus`
    BEFORE INSERT ON `t_employee_campus`
    FOR EACH ROW
BEGIN
    DECLARE v_store_id INT;
    DECLARE v_app_user_id BIGINT;

    -- 检查 campus_id 是否设置
    IF NEW.`campus_id` IS NOT NULL AND NEW.`campus_id` <> '' THEN
        -- 检查 campus_id 是否存在
    SELECT `id` INTO v_store_id
    FROM `ss_campus`
    WHERE `xgj_campus_id` COLLATE utf8mb4_0900_ai_ci = NEW.`campus_id` COLLATE utf8mb4_0900_ai_ci;

    IF v_store_id IS NOT NULL THEN
            -- 设置 store_id
            SET NEW.`store_id` = v_store_id;
    ELSE
            -- 如果 campus_id 不存在，设置 store_id 为默认值或不修改
            SET NEW.`store_id` = NULL;
END IF;
END IF;

    -- 检查 employee_user_id 是否设置
    IF NEW.`employee_user_id` IS NOT NULL AND NEW.`employee_user_id` <> '' THEN
        -- 获取 app_user_id 从 store_employee 表
SELECT `user_id` INTO v_app_user_id
FROM `store_employee`
WHERE `xgj_user_id` = NEW.`employee_user_id`;

IF v_app_user_id IS NOT NULL THEN
            -- 设置 app_user_id
            SET NEW.`app_user_id` = v_app_user_id;
ELSE
            -- 如果 employee_user_id 不存在，设置 app_user_id 为默认值或不修改
            SET NEW.`app_user_id` = NULL;
END IF;
END IF;
END;


CREATE TRIGGER `before_update_t_employee_campus`
    BEFORE UPDATE ON `t_employee_campus`
    FOR EACH ROW
BEGIN
    DECLARE v_store_id INT;
    DECLARE v_app_user_id BIGINT;

    -- 检查 campus_id 是否变更
    IF NEW.`campus_id` IS NOT NULL AND NEW.`campus_id` <> '' AND NEW.`campus_id` <> OLD.`campus_id` THEN
        -- 检查 campus_id 是否存在
    SELECT `id` INTO v_store_id
    FROM `ss_campus`
    WHERE `xgj_campus_id` COLLATE utf8mb4_0900_ai_ci = NEW.`campus_id` COLLATE utf8mb4_0900_ai_ci;

    IF v_store_id IS NOT NULL THEN
            -- 设置 store_id
            SET NEW.`store_id` = v_store_id;
    ELSE
            -- 如果 campus_id 不存在，设置 store_id 为默认值或不修改
            SET NEW.`store_id` = NULL;
END IF;
END IF;

    -- 检查 employee_user_id 是否变更
    IF NEW.`employee_user_id` IS NOT NULL AND NEW.`employee_user_id` <> '' AND NEW.`employee_user_id` <> OLD.`employee_user_id` THEN
        -- 获取 app_user_id 从 store_employee 表
SELECT `user_id` INTO v_app_user_id
FROM `store_employee`
WHERE `xgj_user_id` = NEW.`employee_user_id`;

IF v_app_user_id IS NOT NULL THEN
            -- 设置 app_user_id
            SET NEW.`app_user_id` = v_app_user_id;
ELSE
            -- 如果 employee_user_id 不存在，设置 app_user_id 为默认值或不修改
            SET NEW.`app_user_id` = NULL;
END IF;
END IF;
END;




