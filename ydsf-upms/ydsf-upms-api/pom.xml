<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, ydsf All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: ydsf
  ~
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yuedu</groupId>
        <artifactId>ydsf-upms</artifactId>
        <version>5.6.16-SNAPSHOT</version>
    </parent>

    <artifactId>ydsf-upms-api</artifactId>
    <packaging>jar</packaging>

    <description>ydsf 通用用户权限管理系统公共api模块</description>


    <dependencies>
        <!-- 连表查询注解 -->
        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join-annotation</artifactId>
        </dependency>
        <!--core 工具类-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-core</artifactId>
        </dependency>
        <!--mybatis plus extension,包含了mybatis plus core-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <!--feign 工具类-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-feign</artifactId>
        </dependency>
        <!-- excel 导入导出 -->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-excel</artifactId>
        </dependency>
        <!--字段审计注解-->
        <dependency>
            <groupId>org.javers</groupId>
            <artifactId>javers-core</artifactId>
        </dependency>
        <!-- 脱敏工具类-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-sensitive</artifactId>
        </dependency>
    </dependencies>
</project>
