package com.yuedu.ydsf.admin.api.feign;

import com.yuedu.ydsf.admin.api.vo.AreaVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7
 */
@FeignClient(contextId = "remoteAreaService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteAreaService {

    /**
     * 获取所有省市区接口
     *
     * @return R 返回结果对象，包含所有省市区信息列表
     */
    @GetMapping("/sysArea/list")
    @NoToken
    R<List<AreaVO>> getAllArea();

}
