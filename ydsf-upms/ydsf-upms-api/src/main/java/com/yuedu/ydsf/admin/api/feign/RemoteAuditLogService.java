/*
 *
 *      Copyright (c) 2018-2025, ydsf All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: ydsf
 *
 */

package com.yuedu.ydsf.admin.api.feign;

import com.yuedu.ydsf.admin.api.entity.SysAuditLog;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-27
 */
@FeignClient(contextId = "remoteAuditLogService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteAuditLogService {

	/**
	 * 保存日志
	 * @param auditLogList 日志实体 列表
	 * @return succes、false
	 */
	@NoToken
	@PostMapping("/audit")
	R<Boolean> saveLog(@RequestBody List<SysAuditLog> auditLogList);

}
