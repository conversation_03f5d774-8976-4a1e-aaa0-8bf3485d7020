<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, ydsf All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: ydsf
  ~
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yuedu</groupId>
    <artifactId>ydsf</artifactId>
    <version>5.6.16-SNAPSHOT</version>
    <name>${project.artifactId}</name>
    <packaging>pom</packaging>
    <organization>
        <name>pig4cloud</name>
        <url>https://www.pig4cloud.com</url>
    </organization>

    <properties>
        <ydsf-common-bom.version>5.6.16-SNAPSHOT</ydsf-common-bom.version>
        <spring-boot.version>3.3.3</spring-boot.version>
        <spring-cloud.version>2023.0.3</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.1.2</spring-cloud-alibaba.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <spring-boot-admin.version>3.3.3</spring-boot-admin.version>
        <spring.authorization.version>1.3.1</spring.authorization.version>
        <maven.compiler.version>3.11.0</maven.compiler.version>
        <spring.checkstyle.version>0.0.39</spring.checkstyle.version>
        <git.commit.version>4.9.9</git.commit.version>
        <captcha.version>1.3.0</captcha.version>
        <captcha.image.version>2.2.3</captcha.image.version>
        <cas.sdk.version>3.6.4</cas.sdk.version>
        <flowable.version>7.0.0</flowable.version>
        <dingtalk.old.version>2.0.0</dingtalk.old.version>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <knife4j.version>3.0.5</knife4j.version>
        <jasypt.version>3.0.5</jasypt.version>
        <ttl.version>2.12.6</ttl.version>
        <jaxb.version>2.3.5</jaxb.version>
        <jimu.version>1.6.6-beta2</jimu.version>
        <aws.version>1.12.261</aws.version>
        <xxl.job.version>2.4.0</xxl.job.version>
        <docker.registry>registry.cn-hangzhou.aliyuncs.com</docker.registry>
        <docker.host>http://127.0.0.1:2375</docker.host>
        <docker.namespace>pigcloudx</docker.namespace>
        <docker.username>佩小格科技</docker.username>
        <docker.password>qq123456</docker.password>
        <docker.plugin.version>0.33.0</docker.plugin.version>
        <!--  默认忽略docker构建 -->
        <docker.skip>false</docker.skip>
        <log-path>/var/log/</log-path>
        <versions-maven-plugin.version>2.17.1</versions-maven-plugin.version>
        <maven-release-plugin.version>3.1.1</maven-release-plugin.version>
        <lombok-maven-plugin.version>*********</lombok-maven-plugin.version>
        <maven-release-plugin.version>3.1.1</maven-release-plugin.version>
        <opentelemetry.version>2.10.0-alpha</opentelemetry.version>
    </properties>

    <scm>
        <developerConnection>scm:git:http://gitlab.yuedushufang.com/java/ydsf.git</developerConnection>
        <tag>ydsf-*******</tag>
  </scm>

    <dependencies>
        <!--配置文件处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--jasypt配置文件加解密-->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>
        <!--监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--监控客户端-->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin.version}</version>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- JAVA 17 -->
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>${jaxb.version}</version>
        </dependency>
        <!--测试依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--可观测链路 OpenTelemetry 版-->
        <dependency>
            <groupId>io.opentelemetry.instrumentation</groupId>
            <artifactId>opentelemetry-logback-mdc-1.0</artifactId>
            <version>${opentelemetry.version}</version>
        </dependency>
    </dependencies>

    <modules>
        <module>ydsf-register</module>
        <module>ydsf-gateway</module>
        <module>ydsf-auth</module>
        <module>ydsf-upms</module>
        <module>ydsf-common</module>
        <module>ydsf-flow</module>
        <module>ydsf-visual</module>
        <module>ydsf-app-server</module>
        <module>ydsf-service</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!--ydsf 公共版本定义-->
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-bom</artifactId>
                <version>${ydsf-common-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--spring boot 公共版本定义-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--spring cloud 公共版本定义-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--spring cloud alibaba-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--mac dns 解析-->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver-dns-native-macos</artifactId>
                <classifier>osx-aarch_64</classifier>
            </dependency>
            <!--web 模块-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <!--排除tomcat依赖-->
                    <exclusion>
                        <artifactId>spring-boot-starter-tomcat</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <!--spring boot 默认插件-->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!--maven  docker 打包插件 -->
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.plugin.version}</version>
                    <configuration>
                        <dockerHost>${docker.host}</dockerHost>
                        <registry>${docker.registry}</registry>
                        <authConfig>
                            <push>
                                <username>${docker.username}</username>
                                <password>${docker.password}</password>
                            </push>
                        </authConfig>
                        <images>
                            <image>
                                <name>${docker.registry}/${docker.namespace}/${project.name}:${project.version}</name>
                                <build>
                                    <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                                </build>
                            </image>
                        </images>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!--代码格式插件，默认使用spring 规则-->
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
                <version>${spring.checkstyle.version}</version>
            </plugin>
            <!--代码编译指定版本插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.version}</version>
                <configuration>
                    <target>${maven.compiler.target}</target>
                    <source>${maven.compiler.source}</source>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.34</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!--打包关联最新 git commit 信息插件-->
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
                <version>${git.commit.version}</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>validate</phase> <!-- 或者使用 prepare-package -->
                    </execution>
                </executions>
                <configuration>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <!--因为项目定制了jackson的日期时间序列化/反序列化格式，因此这里要进行配置,不然通过management.info.git.mode=full进行完整git信息监控时会存在问题-->
                    <dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
                    <includeOnlyProperties>
                        <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
                        <includeOnlyProperty>^git.commit.(id|message|time).*$</includeOnlyProperty>
                    </includeOnlyProperties>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>${versions-maven-plugin.version}</version>
                <configuration>
                    <ruleSet>
                        <ignoreVersions>
                            <!-- 可以使用 ignoreVersion 配置忽略 SNAPSHOT、alpha、beta 版等 -->
                            <ignoreVersion>
                                <!-- 'exact' (默认) 或 'regex' -->
                                <type>regex</type>
                                <version>(.+-SNAPSHOT|.+-M\d)</version>
                            </ignoreVersion>
                            <ignoreVersion>
                                <type>regex</type>
                                <version>.+-(alpha|beta)</version>
                            </ignoreVersion>
                        </ignoreVersions>
                    </ruleSet>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>${maven-release-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-maven-plugin</artifactId>
                <version>${lombok-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>delombok</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.username />
                <nacos.password />
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>cloud</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.username />
                <nacos.password />
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>prod</profiles.active>
                <nacos.username />
                <nacos.password />
            </properties>
        </profile>
        <profile>
            <id>boot</id>
            <modules>
                <module>ydsf-boot</module>
            </modules>
        </profile>
    </profiles>

    <!--  增加云效nexus仓库 -->
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>https://packages.aliyun.com/66e3a23f5d0a63a08ebdee89/maven/maven-releases</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>https://packages.aliyun.com/66e3a23f5d0a63a08ebdee89/maven/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
