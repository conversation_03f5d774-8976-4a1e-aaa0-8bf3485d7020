<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 pig4cloud Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yuedu</groupId>
        <artifactId>ydsf</artifactId>
        <version>********</version>
    </parent>

    <artifactId>ydsf-boot</artifactId>
    <packaging>jar</packaging>

    <description>ydsf 单体版本启动</description>

    <dependencies>
        <!--必备：认证中心模块-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-auth</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--必备：用户管理模块-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-upms-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--选配：代码生成模块 -->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-codegen</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-app-server-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-daemon-quartz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-flow-engine-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-flow-task-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-mp-platform</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-pay-platform</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-security</artifactId>
        </dependency>
        <!-- 接口文档UI  -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springboot</groupId>
            <artifactId>knife4j-boot-openapi3-ui</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <!--接口文档-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-swagger</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
