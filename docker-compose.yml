# 使用说明 V5.2
# 1. 使用docker-compose  宿主机不需要配置host来发现
# 2. 无需修改源码，根目录  docker-compose up 即可
# 3. 静静等待服务启动

version: '3'
services:
  ydsf-mysql:
    build:
      context: ./db
    environment:
      MYSQL_ROOT_HOST: "%"
      MYSQL_ROOT_PASSWORD: root
    restart: always
    container_name: ydsf-mysql
    image: ydsf-mysql
    command: --lower_case_table_names=1
    networks:
      - spring_cloud_default

  ydsf-redis:
    container_name: ydsf-redis
    image: registry.cn-hangzhou.aliyuncs.com/dockerhub_mirror/redis
    restart: always
    networks:
      - spring_cloud_default

  ydsf-register:
    build:
      context: ./ydsf-register
    restart: always
    container_name: ydsf-register
    image: ydsf-register
    ports:
      - 8848:8848
    networks:
      - spring_cloud_default

  ydsf-gateway:
    build:
      context: ./ydsf-gateway
    restart: always
    container_name: ydsf-gateway
    image: ydsf-gateway
    ports:
      - 9999:9999
    networks:
      - spring_cloud_default

  ydsf-auth:
    build:
      context: ./ydsf-auth
    restart: always
    container_name: ydsf-auth
    image: ydsf-auth
    networks:
      - spring_cloud_default

  ydsf-upms:
    build:
      context: ./ydsf-upms/ydsf-upms-biz
    restart: always
    container_name: ydsf-upms
    image: ydsf-upms
    networks:
      - spring_cloud_default

  ydsf-flow-task:
    build:
      context: ./ydsf-flow/ydsf-flow-task/ydsf-flow-task-biz
    restart: always
    container_name: ydsf-flow-task
    image: ydsf-flow-task
    networks:
      - spring_cloud_default

  ydsf-flow-engine:
    build:
      context: ./ydsf-flow/ydsf-flow-engine/ydsf-flow-engine-biz
    restart: always
    container_name: ydsf-flow-engine
    image: ydsf-flow-engine
    networks:
      - spring_cloud_default

  ydsf-app-server:
    build:
      context: ./ydsf-app-server/ydsf-app-server-biz
    restart: always
    container_name: ydsf-app-server
    image: ydsf-app-server
    networks:
      - spring_cloud_default

  ydsf-monitor:
    build:
      context: ./ydsf-visual/ydsf-monitor
    restart: always
    image: ydsf-monitor
    container_name: ydsf-monitor
    ports:
      - 5001:5001
    networks:
      - spring_cloud_default

  ydsf-daemon-quartz:
    build:
      context: ./ydsf-visual/ydsf-daemon-quartz
    restart: always
    image: ydsf-daemon-quartz
    container_name: ydsf-daemon-quartz
    networks:
      - spring_cloud_default

  ydsf-daemon-elastic-job:
    build:
      context: ./ydsf-visual/ydsf-daemon-elastic-job
    restart: always
    image: ydsf-daemon-elastic-job
    container_name: ydsf-daemon-elastic-job
    networks:
      - spring_cloud_default

  ydsf-codegen:
    build:
      context: ./ydsf-visual/ydsf-codegen
    restart: always
    image: ydsf-codegen
    container_name: ydsf-codegen
    networks:
      - spring_cloud_default

  ydsf-mp-platform:
    build:
      context: ./ydsf-visual/ydsf-mp-platform
    restart: always
    image: ydsf-mp-platform
    container_name: ydsf-mp-platform
    networks:
      - spring_cloud_default

  ydsf-pay-platform:
    build:
      context: ./ydsf-visual/ydsf-pay-platform
    restart: always
    image: ydsf-pay-platform
    container_name: ydsf-pay-platform
    networks:
      - spring_cloud_default

  ydsf-report-platform:
    build:
      context: ./ydsf-visual/ydsf-report-platform
    restart: always
    image: ydsf-report-platform
    container_name: ydsf-report-platform
    ports:
      - 9095:9095
    networks:
      - spring_cloud_default

  ydsf-jimu-platform:
    build:
      context: ./ydsf-visual/ydsf-jimu-platform
    restart: always
    image: ydsf-jimu-platform
    container_name: ydsf-jimu-platform
    ports:
      - 5008:5008
    networks:
      - spring_cloud_default

networks:
  spring_cloud_default:
    name:  spring_cloud_default
    driver: bridge
