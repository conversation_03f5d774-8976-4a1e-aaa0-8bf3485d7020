# 学员信息更新跟踪记录功能说明

## 功能概述

在StudentServiceImpl.java中的`editStudent`方法中增加了业务逻辑，当更新学员信息时，如果本次更新的字段中包含推荐老师(recommend_teacher)和推荐学员(recommend_student)且这两个字段有值，则会自动处理跟踪记录。

## 实现逻辑

### 1. 触发条件
- 更新学员信息时，推荐老师(recommendTeacher)字段有值且大于0
- 或者推荐学员(recommendStudent)字段有值且大于0

### 2. 处理流程

#### 2.1 查询现有跟踪记录
- 查询`store_student_track_record`表，检查该学员是否已存在跟踪记录
- 按ID倒序排列，取最新的一条记录

#### 2.2 不存在记录的处理
如果不存在跟踪记录：
- 创建一条新的跟踪记录
- 设置基本信息：学员ID、门店ID、校区ID
- 设置默认意愿等级为1（A级）
- 设置推荐老师和推荐学员字段为本次更新的值
- 设置沟通记录为"系统自动创建：更新推荐信息"
- 插入到`store_student_track_record`表中

#### 2.3 已存在记录的处理
如果已存在跟踪记录：
- 比较推荐老师字段是否发生变化，如有变化则更新
- 比较推荐学员字段是否发生变化，如有变化则更新
- 在沟通记录中追加更新信息，记录变更详情
- 更新现有记录

## 修改的文件

### 1. Student.java 实体类
**文件路径**: `ydsf-service/store-system-service/store-system-service-api/src/main/java/com/yuedu/store/entity/Student.java`

**修改内容**:
- 添加了`referralStudentId`字段，用于存储转介绍学员ID

```java
/**
 * 转介绍学员ID
 */
@Schema(description = "转介绍学员ID")
private Long referralStudentId;
```

### 2. StudentUpdateDTO.java 数据传输对象
**文件路径**: `ydsf-service/store-system-service/store-system-service-api/src/main/java/com/yuedu/store/dto/StudentUpdateDTO.java`

**修改内容**:
- 添加了`recommendTeacher`字段，用于接收推荐老师信息
- 添加了`recommendStudent`字段，用于接收推荐学员信息

```java
/**
 * 推荐老师
 */
@Schema(description = "推荐老师")
private Integer recommendTeacher;

/**
 * 推荐学员
 */
@Schema(description = "推荐学员")
private Integer recommendStudent;
```

### 3. StudentServiceImpl.java 服务实现类
**文件路径**: `ydsf-service/store-system-service/store-system-service-biz/src/main/java/com/yuedu/store/service/impl/StudentServiceImpl.java`

**修改内容**:

#### 3.1 editStudent方法增强
- 添加了`@Transactional`注解确保事务一致性
- 增加了推荐字段检查逻辑
- 调用跟踪记录处理方法

#### 3.2 新增方法

**handleStudentTrackRecord方法**:
- 处理学员跟踪记录的主要逻辑
- 查询现有记录并决定创建或更新

**createNewTrackRecord方法**:
- 创建新的跟踪记录
- 设置默认值和推荐信息

**updateExistingTrackRecord方法**:
- 更新现有跟踪记录
- 比较字段变化并记录更新信息

## 异常处理

### 1. 数据库操作异常
- 插入跟踪记录失败时抛出BizException
- 更新跟踪记录失败时抛出BizException

### 2. 事务管理
- 使用`@Transactional(rollbackFor = Exception.class)`确保出现异常时回滚
- 保证学员信息更新和跟踪记录操作的原子性

## 日志记录

### 1. 信息日志
- 记录跟踪记录处理开始和完成
- 记录创建和更新操作的详细信息
- 记录无需更新的情况

### 2. 错误日志
- 记录处理失败的详细错误信息
- 包含学员ID和错误堆栈信息

## 测试用例

创建了完整的单元测试类`StudentServiceEditTest.java`，包含以下测试场景：

1. **有推荐字段时创建跟踪记录**
2. **无推荐字段时不创建跟踪记录**
3. **存在跟踪记录时更新记录**

## 使用示例

```java
StudentUpdateDTO updateDTO = new StudentUpdateDTO();
updateDTO.setUserId(1001L);
updateDTO.setName("张三");
updateDTO.setPhone("13800138001");
// 设置推荐老师和推荐学员
updateDTO.setRecommendTeacher(3001);
updateDTO.setRecommendStudent(4001);

// 调用更新方法，会自动处理跟踪记录
studentService.editStudent(updateDTO);
```

## 注意事项

1. **字段类型**: 推荐老师和推荐学员字段类型为Integer，与数据库表结构保持一致
2. **空值处理**: 只有当推荐字段有值且大于0时才会处理跟踪记录
3. **事务安全**: 整个操作在事务中执行，确保数据一致性
4. **向后兼容**: 不影响现有的学员更新功能，只是增加了跟踪记录处理
5. **性能考虑**: 只在需要时才查询和操作跟踪记录表

## 数据库要求

确保`store_student_track_record`表存在且包含以下字段：
- `user_id`: 学员ID
- `store_id`: 门店ID  
- `school_id`: 校区ID
- `willingness_level`: 意愿等级
- `communication_records`: 沟通记录
- `recommend_teacher`: 推荐老师
- `recommend_student`: 推荐学员
