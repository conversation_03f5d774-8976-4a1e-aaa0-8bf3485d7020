# 意向学员信息修改优化方案

## 问题描述

当前代码在更新意向会员信息时存在以下问题：

1. **无条件插入跟踪记录**：无论修改什么字段都会向跟踪记录表插入一条记录
2. **转介绍学员信息无法保存**：Student实体类缺少referralStudentId字段，导致转介绍学员ID无法保存到数据库
3. **获取详情时转介绍信息不准确**：只能从跟踪记录中获取转介绍学员信息，不够准确

## 解决方案

### 1. 数据库结构修改

**文件**: `db/sprint24/store_student_referral.sql`

为store_student表添加referral_student_id字段：

```sql
-- 为store_student表添加转介绍学员ID字段
ALTER TABLE `store_student`
    ADD COLUMN `referral_student_id` bigint NULL DEFAULT NULL COMMENT '转介绍学员ID' AFTER `responsible_person`;

-- 添加索引以提高查询性能
CREATE INDEX `idx_referral_student_id` ON `store_student` (`referral_student_id`);
```

### 2. 实体类修改

**文件**: `ydsf-service/store-system-service/store-system-service-api/src/main/java/com/yuedu/store/entity/Student.java`

在Student实体类中添加referralStudentId字段：

```java
/**
 * 转介绍学员ID
 */
@Schema(description = "转介绍学员ID")
private Long referralStudentId;
```

### 3. 业务逻辑优化

**文件**: `ydsf-service/store-system-service/store-system-service-biz/src/main/java/com/yuedu/store/service/impl/StudentServiceImpl.java`

#### 3.1 updateIntentionStudent方法优化

修改跟踪记录插入逻辑，只有在以下情况下才插入跟踪记录：

1. **负责老师发生变化**
2. **转介绍学员发生变化**  
3. **备注信息发生变化且不为空**

```java
// 判断是否需要插入跟踪记录
boolean shouldInsertTrackRecord = false;
StringBuilder changeDescription = new StringBuilder();

// 检查负责老师是否变化
if (!Objects.equals(existingStudent.getResponsiblePerson(), updateIntentionStudentDTO.getResponsiblePerson())) {
    shouldInsertTrackRecord = true;
    changeDescription.append("负责老师变更；");
}

// 检查转介绍学员是否变化
if (!Objects.equals(existingStudent.getReferralStudentId(), updateIntentionStudentDTO.getReferralStudentId())) {
    shouldInsertTrackRecord = true;
    changeDescription.append("转介绍学员变更；");
}

// 检查备注信息是否变化且不为空
if (!Objects.equals(existingStudent.getDescribe(), updateIntentionStudentDTO.getDescribe()) &&
        StringUtils.isNotBlank(updateIntentionStudentDTO.getDescribe())) {
    shouldInsertTrackRecord = true;
    changeDescription.append("备注信息更新；");
}

// 只有在需要时才插入跟踪记录
if (shouldInsertTrackRecord) {
    // 插入跟踪记录的逻辑
} else {
    log.info("意向会员基本信息修改，无需插入跟踪记录，学员ID：{}", updateIntentionStudentDTO.getUserId());
}
```

#### 3.2 getIntentionStudentDetail方法优化

修改获取转介绍学员信息的逻辑，优先从Student表的referralStudentId字段获取：

```java
// 获取转介绍学员信息（优先从Student表的referralStudentId字段获取）
Long referralStudentId = student.getReferralStudentId();

// 如果Student表中没有转介绍学员信息，则从跟进记录中获取（兼容历史数据）
if (referralStudentId == null) {
    // 从跟踪记录中获取的逻辑（保持向后兼容）
}
```

### 4. 测试用例

**文件**: `ydsf-service/store-system-service/store-system-service-biz/src/test/java/com/yuedu/store/service/StudentServiceIntentionUpdateTest.java`

创建了完整的测试用例，覆盖以下场景：

1. **只修改基本信息**：不应该插入跟踪记录
2. **修改负责老师**：应该插入跟踪记录
3. **修改转介绍学员**：应该插入跟踪记录
4. **修改备注信息**：应该插入跟踪记录
5. **修改备注为空**：不应该插入跟踪记录
6. **同时修改多个字段**：应该只插入一条跟踪记录

## 功能验证

### 验证步骤

1. **执行数据库迁移脚本**
   ```bash
   # 在数据库中执行
   source db/sprint24/store_student_referral.sql
   ```

2. **重启应用服务**
   ```bash
   # 重启store-system-service服务
   ```

3. **功能测试**
   - 修改意向学员的基本信息（姓名、手机号等），验证不会插入跟踪记录
   - 修改负责老师，验证会插入跟踪记录
   - 修改转介绍学员ID，验证能正确保存到数据库并插入跟踪记录
   - 获取意向学员详情，验证能正确显示转介绍学员信息

4. **运行单元测试**
   ```bash
   mvn test -Dtest=StudentServiceIntentionUpdateTest
   ```

### 预期结果

1. ✅ 转介绍学员ID能够正确保存到Student表的referral_student_id字段
2. ✅ 只有在特定条件下才会插入跟踪记录，避免无意义的记录
3. ✅ 获取意向学员详情时能正确展示转介绍学员信息
4. ✅ 保持向后兼容，历史数据仍能正常显示

## 注意事项

1. **数据迁移**：需要先执行数据库迁移脚本添加referral_student_id字段
2. **向后兼容**：保留了从跟踪记录中获取转介绍学员信息的逻辑，确保历史数据正常显示
3. **性能优化**：为referral_student_id字段添加了索引，提高查询性能
4. **日志记录**：增加了详细的日志记录，便于问题排查和监控

## 影响范围

- **数据库表**：store_student表新增referral_student_id字段
- **实体类**：Student类新增referralStudentId属性
- **业务逻辑**：updateIntentionStudent和getIntentionStudentDetail方法
- **API接口**：无变化，保持兼容性
- **前端页面**：无需修改，现有功能正常使用
