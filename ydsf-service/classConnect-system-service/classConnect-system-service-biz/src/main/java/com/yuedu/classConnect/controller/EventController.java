package com.yuedu.classConnect.controller;

import com.yuedu.classConnect.api.dto.EventDTO;
import com.yuedu.classConnect.api.valid.EventValidGroup;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.PcPermission;
import com.yuedu.ydsf.common.security.util.PcContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.classConnect.service.EventService;

import java.time.LocalDateTime;

/**
* 事件控制层
*
* <AUTHOR>
* @date  2025/01/07
*/
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/event")
@Tag(description = "event" , name = "课堂事件" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class EventController {


    private final EventService eventService;


    /**
    * 新增
    * @param eventDTO
    * @return R
    */
    @Inner(value = false)
    @PcPermission
    @PostMapping(value = "/add")
    @Operation(summary = "新增事件" , description = "新增事件" )
    public R add(@Validated(V_A.class) @RequestBody EventDTO eventDTO) {
         eventDTO.setDeviceNo(PcContextHolder.getDeviceNo());
         eventDTO.setDeviceType(PcContextHolder.getClientType());
         eventDTO.setStoreId(PcContextHolder.getStoreId());
         eventDTO.setEventTime(LocalDateTime.now());
         return R.ok(eventService.add(eventDTO));
    }



    /**
     * 内部调用新增事件
     * @param eventDTO
     * @return R
     */
    @Inner
    @PostMapping(value = "/addInner")
    @Operation(summary = "新增事件" , description = "新增事件" )
    public R addInner(@Validated(EventValidGroup.EventValidInnerGroup.class) @RequestBody EventDTO eventDTO) {
        eventDTO.setEventTime(LocalDateTime.now());
        return R.ok(eventService.add(eventDTO));
    }


}
