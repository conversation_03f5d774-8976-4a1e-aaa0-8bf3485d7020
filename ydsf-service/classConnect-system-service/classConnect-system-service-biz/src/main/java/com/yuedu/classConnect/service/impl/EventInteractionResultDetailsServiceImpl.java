package com.yuedu.classConnect.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.classConnect.mapper.EventInteractionResultDetailsMapper;
import com.yuedu.classConnect.service.EventInteractionResultDetailsService;
import com.yuedu.classConnect.api.query.EventInteractionResultDetailsQuery;
import com.yuedu.classConnect.api.dto.EventInteractionResultDetailsDTO;
import com.yuedu.classConnect.api.vo.EventInteractionResultDetailsVO;
import com.yuedu.classConnect.entity.EventInteractionResultDetails;

import java.io.Serializable;
import java.util.Optional;
import java.util.List;


/**
* 服务层
*
* <AUTHOR>
* @date  2025/01/07
*/
@Service
public class EventInteractionResultDetailsServiceImpl extends ServiceImpl<EventInteractionResultDetailsMapper, EventInteractionResultDetails>
    implements EventInteractionResultDetailsService {


    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param eventInteractionResultDetailsQuery
     * @return IPage 分页结果
     */
    @Override
    public IPage<EventInteractionResultDetailsVO> page(Page page, EventInteractionResultDetailsQuery eventInteractionResultDetailsQuery) {
        return page(page, Wrappers.<EventInteractionResultDetails>lambdaQuery())
                .convert(entity -> {
                    EventInteractionResultDetailsVO eventInteractionResultDetailsVO = new EventInteractionResultDetailsVO();
                    BeanUtils.copyProperties(entity, eventInteractionResultDetailsVO);
                    return eventInteractionResultDetailsVO;
                });
    }


    /**
     * 根据ID获得信息
     *
     * @param id id
     * @return EaEventInteractionResultDetailsVO 详细信息
     */
    @Override
    public EventInteractionResultDetailsVO getInfoById(Serializable id) {
        return Optional.of(getById(id))
                .map(entity -> {
                    EventInteractionResultDetailsVO eventInteractionResultDetailsVO = new EventInteractionResultDetailsVO();
                    BeanUtils.copyProperties(entity, eventInteractionResultDetailsVO);
                    return eventInteractionResultDetailsVO;
                })
                .orElseThrow(()-> new CheckedException("查询结果为空"));
    }


    /**
     * 新增
     *
     * @param eventInteractionResultDetailsDTO
     * @return boolean 执行结果
     */
    @Override
    public boolean add(EventInteractionResultDetailsDTO eventInteractionResultDetailsDTO) {
        EventInteractionResultDetails eventInteractionResultDetails = new EventInteractionResultDetails();
        BeanUtils.copyProperties(eventInteractionResultDetailsDTO, eventInteractionResultDetails);
        return save(eventInteractionResultDetails);
    }


    /**
     * 修改
     *
     * @param eventInteractionResultDetailsDTO
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(EventInteractionResultDetailsDTO eventInteractionResultDetailsDTO) {
        EventInteractionResultDetails eventInteractionResultDetails = new EventInteractionResultDetails();
        BeanUtils.copyProperties(eventInteractionResultDetailsDTO, eventInteractionResultDetails);
        return updateById(eventInteractionResultDetails);
    }


    /**
     * 导出excel 表格
     *
     * @param eventInteractionResultDetailsQuery 查询条件
     * @param ids 导出指定ID
     * @return List<EaEventInteractionResultDetailsVO> 结果集合
     */
    @Override
    public List<EventInteractionResultDetailsVO> export(EventInteractionResultDetailsQuery eventInteractionResultDetailsQuery, Long[] ids) {
        return list(Wrappers.<EventInteractionResultDetails>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), EventInteractionResultDetails::getId, ids))
            .stream()
            .map(entity -> {
                EventInteractionResultDetailsVO eventInteractionResultDetailsVO = new EventInteractionResultDetailsVO();
                BeanUtils.copyProperties(entity, eventInteractionResultDetailsVO);
                return eventInteractionResultDetailsVO;
            }).toList();
    }

}
