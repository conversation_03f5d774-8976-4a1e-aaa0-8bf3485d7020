package com.yuedu.classConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 事件类型枚举
 * 
 * <AUTHOR>
 * @date 2025/01/07
 */
@AllArgsConstructor
public enum EventTypeEnum {
    /**
     * 进入直播间
     */
    EVENT_TYPE_0(0, "进入直播间"),
    /**
     * 离开直播间
     */
    EVENT_TYPE_1(1, "离开直播间"),
    /**
     * 发起互动
     */
    EVENT_TYPE_2(2, "发起互动"),
    /**
     * 结束互动
     */
    EVENT_TYPE_3(3, "结束互动"),
    /**
     * 进入播放器
     */
    EVENT_TYPE_4(4, "进入播放器"),
    /**
     * 离开播发器
     */
    EVENT_TYPE_5(5, "离开播发器"),
    /**
     * 播放器翻页
     */
    EVENT_TYPE_6(6, "播放器翻页"),
    /**
     * 学生互动结果
     */
    EVENT_TYPE_7(7, "学生互动结果");

    public final Integer code;

    public final String desc;
}