package com.yuedu.classConnect.api.query;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;


/**
* 
*
* <AUTHOR>
* @date  2025/01/07
*/
@Data
@Schema(description = "查询对象")
public class EventQuery {

    /**
     * 事件ID
     */
    @Schema(description = "事件ID")
    private Long id;

    /**
     * 是否删除:0-正常;1-删除
     */
    @Schema(description = "是否删除:0-正常;1-删除 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 事件类型:0-进入直播间;1-离开直播间;2-发起互动;3-结束互动;4-进入播放器;5-离开播发器;6-播放器翻页;7-学生互动结果
     */
    @Schema(description = "事件类型:0-进入直播间;1-离开直播间;2-发起互动;3-结束互动;4-进入播放器;5-离开播发器;6-播放器翻页;7-学生互动结果 字典类型：event_type" ,type = "event_type")
    private Integer eventType;

    /**
     * 事件时间
     */
    @Schema(description = "事件时间")
    private LocalDateTime eventTime;

    /**
     * 设备类型:1-直播端;2-教师端;3-讲师端
     */
    @Schema(description = "设备类型:1-直播端;2-教师端;3-讲师端 字典类型：device_type" ,type = "device_type")
    private Integer deviceType;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    private String deviceNo;

    /**
     * 课程编号
     */
    @Schema(description = "课程编号")
    private Long lessonNo;

}
