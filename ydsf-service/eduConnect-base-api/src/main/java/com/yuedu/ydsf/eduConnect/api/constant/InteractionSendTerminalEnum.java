package com.yuedu.ydsf.eduConnect.api.constant;

/**
 * 互动发送端类型
 *
 * @date 2024/4/15 10:22
 * @project @Title: InteractionSendTerminalEnum.java
 */
public enum InteractionSendTerminalEnum {
  HIGH_TWO_BITS(0xC0, "两端都显示"), // 1100 0000
  MAIN_SPEAKER(0x80, "主讲端显示"), // 1000 0000
  CLASSROOM(0x40, "教室端显示"); // 0100 0000

  private final int code;

  private final String name;

  InteractionSendTerminalEnum(int code, String name) {
    this.code = code;
    this.name = name;
  }

  public int getCode() {
    return code;
  }

  public String getName() {
    return name;
  }
}
