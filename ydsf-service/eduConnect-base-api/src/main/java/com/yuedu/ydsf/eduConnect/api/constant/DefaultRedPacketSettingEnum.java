package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 门店默认红包规则设置枚举
 * <AUTHOR>
 * @date 2024/11/4 9:40
 */
@AllArgsConstructor
public enum DefaultRedPacketSettingEnum {
    /**
     * 红包个数
     */
    RED_PACKET_COUNT(30, "红包个数"),
    /**
     * 红包总分数
     */
    RED_PACKET_NUMBER(1000, "红包总分数"),
    /**
     * 红包分数上限
     */
    PACKET_NUMBER_UPPER_LIMIT(200, "红包分数上限"),
    /**
     * 红包分数下限
     */
    PACKET_NUMBER_LOWER_LIMIT(100, "红包分数下限");

    public final Integer code;

    public final String desc;
}
