package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 教学计划明细状态枚举类
 * <AUTHOR>
 * @date 2024/11/29 16:16
 */
@AllArgsConstructor
public enum TeachingPlanDetailPubStateEnum {

    /**
     * 未开始(距离开课30分钟之前)
     */
    TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_0(0,"未开始(距离开课30分钟之前)"),
    /**
     * 即将开始(距离开课前30分钟内)
     */
    TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_1(1,"即将开始(距离开课前30分钟内)"),
    /**
     * 进行中
     */
    TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_2(2,"进行中"),
    /**
     * 已结束
     */
    TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_3(3,"已结束");

    public final Integer CODE;

    public final String MSG;

}
