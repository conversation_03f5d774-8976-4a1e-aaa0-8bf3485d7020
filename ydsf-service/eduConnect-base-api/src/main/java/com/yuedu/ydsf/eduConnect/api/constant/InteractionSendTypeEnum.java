package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.Getter;

/**
 * 互动互动类型枚举
 *
 * @date 2024/4/15 10:27
 * @project @Title: InteractionSendTypeEnum.java
 */
public enum InteractionSendTypeEnum {
  GRAB_RED_PACKET(0x01, 2, InteractionSendTerminalEnum.HIGH_TWO_BITS), // 0000 0001
  ANSWER_QUESTION(0x02, 1, InteractionSendTerminalEnum.HIGH_TWO_BITS), // 0000 0010
  COUNTDOWN(0x04, 0, InteractionSendTerminalEnum.HIGH_TWO_BITS), // 0000 0100
  BIND(0x06, 3, InteractionSendTerminalEnum.CLASSROOM),
  UNBIND(0x08, 4, InteractionSendTerminalEnum.CLASSROOM),
  STOP_INTERACTION(0x10, 5, InteractionSendTerminalEnum.HIGH_TWO_BITS),
  VOTE(0x20, 6, InteractionSendTerminalEnum.HIGH_TWO_BITS),
  SHOW_RESULT(0x40, 7, InteractionSendTerminalEnum.HIGH_TWO_BITS),
  ROLL_CALL(0x03, 8, InteractionSendTerminalEnum.CLASSROOM),
  MEDAL(0x05, 9, InteractionSendTerminalEnum.CLASSROOM),
  CLICKER_ENABLE(0x07, 10, InteractionSendTerminalEnum.CLASSROOM),
  CLICKER_DISABLE(0x09, 11, InteractionSendTerminalEnum.CLASSROOM),
  DEVICE_COMMAND(0x0B, 12, InteractionSendTerminalEnum.CLASSROOM);

  @Getter private final int code;

  @Getter private final int bussinessType;

  @Getter private final InteractionSendTerminalEnum sendTerminalEnum;

  InteractionSendTypeEnum(
      int code, int bussinessType, InteractionSendTerminalEnum sendTerminalEnum) {
    this.code = code;
    this.bussinessType = bussinessType;
    this.sendTerminalEnum = sendTerminalEnum;
  }

  /**
   * 根据业务类型获取所对应的枚举类
   *
   * @param bussinessType
   * @return com.yuedushufang.ss.api.enums.InteractionSendTypeEnum
   * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
   * @date 2024/4/15 11:05
   */
  public static InteractionSendTypeEnum getByBussinessType(int bussinessType) {
    for (InteractionSendTypeEnum enumValue : InteractionSendTypeEnum.values()) {
      if (enumValue.getBussinessType() == bussinessType) {
        return enumValue;
      }
    }
    return null; // 如果没有找到对应的枚举类型，则返回 null 或抛出异常，根据需要进行处理
  }
}
