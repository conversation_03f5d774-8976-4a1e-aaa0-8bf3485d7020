package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 互动来源枚举
 *
 * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
 * @date 2024/11/27 17:50
 * @project @Title: InteractionSourceTypeEnum.java
 */
@AllArgsConstructor
public enum InteractionSourceTypeEnum {

  /** 直播课主讲发互动 */
  INTERACTION_SOURCE_1(1, "直播课主讲发互动"),

  /** 主讲录课发互动 */
  INTERACTION_SOURCE_2(2, "主讲录课发互动"),

  /** 教室点播发互动 */
  INTERACTION_SOURCE_3(3, "教室点播发互动"),

  /** 直播课教室发互动 */
  INTERACTION_SOURCE_4(4, "直播课教室发互动");

  public final Integer code;

  public final String desc;
}
