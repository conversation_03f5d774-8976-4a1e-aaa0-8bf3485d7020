package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * @author: zhangchuan<PERSON>
 * @date: 2024/10/08
 **/
@AllArgsConstructor
public enum ClassTypeEnum {
    /**
     * 直播课
     */
    ATTENDCLASSTYPE_0(0,"直播课"),
    /**
     * 点播课
     */
    ATTENDCLASSTYPE_1(1,"点播课");

    public final Integer CODE;

    public final String MSG;

    /**
     * 根据name获取code
     */
    public static Integer getCodeByName(String name) {
        for (IsSyncXiaogjEnum adTypeEnum : IsSyncXiaogjEnum.values()) {
            if (name.equals(adTypeEnum.MSG)) {
                return adTypeEnum.CODE;
            }
        }
        return null;
    }

}
