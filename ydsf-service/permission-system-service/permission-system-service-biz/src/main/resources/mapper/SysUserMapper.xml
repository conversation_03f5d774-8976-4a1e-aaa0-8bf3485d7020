<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.permission.mapper.SysUserMapper">

    <resultMap id="sysUserMap" type="com.yuedu.permission.api.entity.SysUser">
        <id property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="salt" column="salt"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="lockFlag" column="lock_flag"/>
        <result property="passwordExpireFlag" column="password_expire_flag"/>
        <result property="passwordModifyTime" column="password_modify_time"/>
        <result property="phone" column="phone"/>
        <result property="avatar" column="avatar"/>
        <result property="deptId" column="dept_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="wxOpenid" column="wx_openid"/>
        <result property="wxCpUserid" column="wx_cp_userid"/>
        <result property="wxDingUserid" column="wx_ding_userid"/>
        <result property="miniOpenid" column="mini_openid"/>
        <result property="qqOpenid" column="qq_openid"/>
        <result property="giteeLogin" column="gitee_login"/>
        <result property="oscId" column="osc_id"/>
        <result property="nickname" column="nickname"/>
        <result property="name" column="name"/>
        <result property="email" column="email"/>
    </resultMap>

    <!-- 查询所有用户 -->
    <select id="selectUserList" resultMap="sysUserMap">
        select *
        from sys_user
        where user_id != 1
          and lock_flag != 9
    </select>

    <!-- 动态更新用户 -->
    <update id="updateUser">
        update sys_user
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="salt != null">salt = #{salt},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="lockFlag != null">lock_flag = #{lockFlag},</if>
            <if test="passwordExpireFlag != null">password_expire_flag = #{passwordExpireFlag},</if>
            <if test="passwordModifyTime != null">password_modify_time = #{passwordModifyTime},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="wxOpenid != null">wx_openid = #{wxOpenid},</if>
            <if test="wxDingUserid != null">wx_ding_userid = #{wxDingUserid},</if>
            <if test="miniOpenid != null">mini_openid = #{miniOpenid},</if>
            <if test="qqOpenid != null">qq_openid = #{qqOpenid},</if>
            <if test="giteeLogin != null">gitee_login = #{giteeLogin},</if>
            <if test="oscId != null">osc_id = #{oscId},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="name != null">name = #{name},</if>
            <if test="email != null">email = #{email}</if>
        </set>
        where wx_cp_userid = #{wxCpUserid}
    </update>

</mapper>
