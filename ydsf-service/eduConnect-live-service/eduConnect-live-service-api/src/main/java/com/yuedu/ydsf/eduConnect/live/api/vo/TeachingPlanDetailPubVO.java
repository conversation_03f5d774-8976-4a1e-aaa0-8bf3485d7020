package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 已发布的教学计划明细表 视图类
 *
 * <AUTHOR>
 * @date 2025-01-08 09:24:31
 */
@Data
@Schema(description = "已发布的教学计划明细表视图类")
public class TeachingPlanDetailPubVO {


	/**
	* 主键id
	*/
    @Schema(description="主键id")
    private Long id;

	/**
	* 教学计划id
	*/
    @Schema(description="教学计划id")
    private Long planId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 课程id
	*/
    @Schema(description="课程id")
    private Long courseId;

	/**
	* 课程名字
	*/
    @Schema(description="课程名字")
    private String courseName;

	/**
	* 课节id
	*/
    @Schema(description="课节id")
    private Long lessonId;

	/**
	* 课节名字
	*/
    @Schema(description="课节名字")
    private String lessonName;

	/**
	* 主讲id
	*/
    @Schema(description="主讲id")
    private Long lectureId;

	/**
	* 主讲名字
	*/
    @Schema(description="主讲名字")
    private String lectureName;

	/**
	* 书籍ID
	*/
    @Schema(description="书籍ID")
    private Long bookId;

	/**
	* 书籍名称
	*/
    @Schema(description="书籍名称")
    private String bookName;

	/**
	* 上课开始日期
	*/
    @Schema(description="上课开始日期")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    private Object classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    private Object classEndTime;

	/**
	* 上课开始日期时间
	*/
    @Schema(description="上课开始日期时间")
    private LocalDateTime classStartDateTime;

	/**
	* 上课结束日期时间
	*/
    @Schema(description="上课结束日期时间")
    private LocalDateTime classEndDateTime;

	/**
	* 直播间ID
	*/
    @Schema(description="直播间ID")
    private Long liveRoomId;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 课程版本(直播端进入课程时存储)
	*/
    @Schema(description="课程版本(直播端进入课程时存储)")
    private Integer courseVersion;

	/**
	* 课件版本(直播端进入课程时存储)
	*/
    @Schema(description="课件版本(直播端进入课程时存储)")
    private Integer coursewareVersion;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;

    /**
     * 阶段名称
     */
    @Schema(description="阶段名称")
    private String stageName;

    /**
     * 封面(全路径)
     */
    @Schema(description = "封面")
    private String imgUrl;

    /**
     * 课件ID
     */
    @Schema(description = "课件ID")
    private Integer coursewareId;

    /**
     * 课件名称
     */
    @Schema(description = "课节名称")
    private String coursewareName;

    /**
     * 频道ID
     */
    @Schema(description="频道ID")
    private String channelId;

    /**
     * 教学计划状态: 0-未开始(距离开课30分钟之前); 1-即将开始(距离开课前30分钟内); 2-进行中; 3-已结束;
     */
    @Schema(description="教学计划状态: 0-未开始(距离开课30分钟之前); 1-即将开始(距离开课前30分钟内); 2-进行中; 3-已结束;")
    private Integer teachingPlanDetailPubState;

    /**
     * 上课时段类型:1-上午;2-下午;3-晚上
     */
    @Schema(description="时段类型:1-上午;2-下午;3-晚上")
    private Integer timeType;

    /**
     * 主讲老师昵称
     */
    @Schema(description = "主讲老师昵称")
    private String lectureNickName;

}

