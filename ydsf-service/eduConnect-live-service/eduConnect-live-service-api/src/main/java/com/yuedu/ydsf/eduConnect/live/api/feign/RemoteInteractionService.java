package com.yuedu.ydsf.eduConnect.live.api.feign;

import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsCourseStepDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 远程互动服务
 *
 * @date 2025/1/15 11:41
 * @project @Title: RemoteInteractionService.java
 */
@FeignClient(
    contextId = "remoteInteractionService",
    value = ServiceNameConstants.EDU_CONNECT_LIVE_SERVICE)
public interface RemoteInteractionService {

  /**
   * 随机点名
   *
   * @param courseStepDTO
   * @return
   */
  @NoToken
  @PostMapping("/v2/ssInteractionSetting/mini/rollCall")
  R rollCall(@RequestBody SsCourseStepDTO courseStepDTO);

  /**
   * 激励勋章
   *
   * @param courseStepDTO
   * @return
   */
  @NoToken
  @PostMapping("/v2/ssInteractionSetting/mini/medal")
  R medal(@RequestBody SsCourseStepDTO courseStepDTO);
}
