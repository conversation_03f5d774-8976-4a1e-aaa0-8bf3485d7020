package com.yuedu.ydsf.eduConnect.live.api.query;

import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;


/**
 * 获取rtm token 查询类
 * <AUTHOR>
 * @date 2025/1/7 11:23
 */
@Data
@Schema(description = "获取rtm token 查询类")
public class RtmTokenQuery implements Serializable {

    @Serial private static final long serialVersionUID = 1L;
    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    private Long userId;

    /**
     * 业务ID: 直播端: 传已发布的教学计划明细表ID; 教室端: 传课表ID;
     */
    @Schema(description = "直播端: 传已发布的教学计划明细表ID; 教室端: 传课表ID;")
    private Long businessId;

    /**
     * 课程类型: 1-直播课; 2-点播课; 3-补课;
     */
    @Schema(description = "课程类型: 1-直播课; 2-点播课; 3-补课;")
    private Integer courseType;

    /**
     * 上课结束日期时间
     */
    @Schema(description="上课结束日期时间")
    private LocalDateTime classEndDateTime;

    /**
     * 设备号
     */
    @Schema(description = "设备号")
    private String deviceNo;

    /**
     * 设备类型: 1-直播端; 2-教室端; 3-讲师端;
     */
    @Schema(description = "设备类型: 1-直播端; 2-教室端; 3-讲师端;")
    private Integer deviceType;

    /**
     * 是否强制进入直播间0否1是
     */
    @Schema(description = "是否强制进入直播间")
    private String forceInLive = YesNoEnum.NO.getCode();

    /**
     * 直播间房间号
     */
    @Schema(description = "直播间房间号")
    private String roomUuid;

}
