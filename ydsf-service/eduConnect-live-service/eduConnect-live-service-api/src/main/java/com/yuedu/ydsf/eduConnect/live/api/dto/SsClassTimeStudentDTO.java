package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.eduConnect.live.api.valid.SsClassTimeStudentValidGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

/**
* 校区上课学生表
*
* <AUTHOR>
* @date  2024/11/04
*/
@Data
@Schema(description = "校区上课学生表传输对象")
public class SsClassTimeStudentDTO implements Serializable {


    /**
     * 主键ID
     */
    @NotNull(groups = {V_E.class}, message = "主键ID不能为空")
    private Long id;



    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    @NotNull(groups = {V_A_E.class }, message = "班级ID不能为空")
    private Long classId;

    /**
     * 课次ID
     */
    @Schema(description = "课次ID")
    @NotNull(groups = {V_A_E.class }, message = "课次ID不能为空")
    private Long classTimeId;

    /**
     * 课次授权教室表ID
     */
    @Schema(description = "课次授权教室表ID")
    @NotNull(groups = {V_A_E.class }, message = "课次授权教室表ID不能为空")
    private Long classTimeAuthRoomId;

    /**
     * 校区ID
     */
    @Schema(description = "校区ID")
    @NotNull(groups = {V_A_E.class }, message = "校区ID不能为空")
    private Long campusId;

    /**
     * 教室ID
     */
    @Schema(description = "教室ID")
    @NotNull(groups = {V_A_E.class }, message = "教室ID不能为空")
    private Long classRoomId;

    /**
     * 教室设备ID
     */
    @Schema(description = "教室设备ID")
    @NotNull(groups = {V_A_E.class }, message = "教室设备ID不能为空")
    private Long deviceId;

    /**
     * 校管家学生ID
     */
    @Schema(description = "校管家学生ID")
    @NotBlank(groups = {V_A_E.class }, message = "校管家学生ID不能为空")
    @Length(groups = {V_A_E.class }, max =255 ,message = "校管家学生ID长度不能大于255")
    private String studentId;

    /**
     * 校管家学生手机号
     */
    @Schema(description = "校管家学生手机号")
    @NotBlank(groups = {V_A_E.class }, message = "校管家学生手机号不能为空")
    @Length(groups = {V_A_E.class }, max =255 ,message = "校管家学生手机号长度不能大于255")
    private String studentMobile;

    /**
     * 校管家学生名称
     */
    @Schema(description = "校管家学生名称")
    @NotBlank(groups = {V_A_E.class }, message = "校管家学生名称不能为空")
    @Length(groups = {V_A_E.class }, max =255 ,message = "校管家学生名称长度不能大于255")
    private String studentName;

    /**
     * 互动题绑定状态: 0-未绑定; 1-已绑定;(废弃)
     */
    @Schema(description = "互动题绑定状态: 0-未绑定; 1-已绑定;(废弃) 字典类型：interactor_bind_status" ,type = "interactor_bind_status", defaultValue = "0")
    @NotNull(groups = {V_A_E.class }, message = "互动题绑定状态不能为空")
    private Integer interactorBindStatus;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Length(groups = {V_A_E.class }, max =64 ,message = "创建者长度不能大于64")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @Schema(description = "编辑者")
    @Length(groups = {V_A_E.class }, max =64 ,message = "编辑者长度不能大于64")
    private String modifer;

    /**
     * 删除标识
     */
    @Schema(description = "删除标识", defaultValue = "'0'")
    @NotNull(groups = {V_A_E.class }, message = "删除标识不能为空")
    private Integer delFlag;

    /**
     * 设备号
     */
    @Schema(description = "设备号")
    @NotBlank(groups = {SsClassTimeStudentValidGroup.BindAndUnbindGroup.class}, message = "设备号不能为空")
    private String deviceNo;

    /**
     * 房间UUID
     */
    @Schema(description = "房间UUID")
//    @NotBlank(groups = {SsClassTimeStudentValidGroup.BindAndUnbindGroup.class}, message = "房间UUID不能为空")
    private String roomUuid;

    /**
     * 上课类型: 0-直播课; 1-点播课;
     */
    @Schema(description = "上课类型")
    @NotNull(groups = {SsClassTimeStudentValidGroup.BindAndUnbindGroup.class}, message = "上课类型不能为空")
    private Integer attendClassType;

    /**
     * 答题器sn码
     */
    @Schema(description = "答题器sn码")
    private String sn;

    /**
     * 解绑or绑定
     */
    private Integer operType;


}
