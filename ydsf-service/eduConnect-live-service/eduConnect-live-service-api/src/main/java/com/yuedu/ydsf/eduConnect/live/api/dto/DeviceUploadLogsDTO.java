package com.yuedu.ydsf.eduConnect.live.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 设备上传日志命令DTO
 *
 * @date 2024/12/19
 */
@Data
@Schema(description = "设备上传日志命令DTO")
public class DeviceUploadLogsDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    @Schema(description = "设备编号", required = true)
    private String deviceNo;

    /**
     * 获取日志的天数
     */
    @Min(value = 1, message = "日志天数不能小于1天")
    @Schema(description = "获取日志的天数，默认为1天", example = "1")
    private Integer logDays = 1;

    /**
     * 日志类型
     */
    @Schema(description = "日志类型：all-全部日志，error-错误日志，info-信息日志，debug-调试日志", example = "all")
    private String logType = "all";

    /**
     * 上传地址
     */
    @Schema(description = "日志上传地址，如果不指定则使用默认地址")
    private String uploadUrl;

    /**
     * 命令描述
     */
    @Schema(description = "命令描述")
    private String commandDesc;

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒），默认300秒（5分钟）")
    private Integer timeout = 300;

    /**
     * 是否需要响应确认
     */
    @Schema(description = "是否需要响应确认，默认false")
    private Boolean needAck = false;
}
