package com.yuedu.ydsf.eduConnect.live.api.query;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;


/**
* 班级信息表
*
* <AUTHOR>
* @date  2024/11/01
*/
@Data
@Schema(description = "班级信息表查询对象")
public class SsClassQuery {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 班级名称
     */
    @Schema(description = "班级名称")
    private String className;

    /**
     * 年级(字典类型: grade)
     */
    @Schema(description = "年级(字典类型: grade) 字典类型：grade" ,type = "grade")
    private Integer grade;

    /**
     * 班级状态(字典类型: class_state)
     */
    @Schema(description = "班级状态(字典类型: class_state) 字典类型：class_state" ,type = "class_state")
    private Integer classState;

    /**
     * 是否同步校管家(字典类型: is_sync_xiaogj)
     */
    @Schema(description = "是否同步校管家(字典类型: is_sync_xiaogj) 字典类型：is_sync_xiaogj" ,type = "is_sync_xiaogj")
    private Integer isSyncXiaogj;

    /**
     * 班级类型(字典类型: class_type)
     */
    @Schema(description = "班级类型(字典类型: class_type) 字典类型：class_type" ,type = "class_type")
    private Integer classType;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @Schema(description = "编辑者")
    private String modifer;

    /**
     * 是否删除: 0-未删除;1-已删除
     */
    @Schema(description = "是否删除: 0-未删除;1-已删除 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;

}
