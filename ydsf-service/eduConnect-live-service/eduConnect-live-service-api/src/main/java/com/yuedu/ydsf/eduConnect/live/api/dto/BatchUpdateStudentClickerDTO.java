package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.eduConnect.live.api.valid.BReceiverValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 批量更新学生答题器绑定状态请求DTO
 *
 * <AUTHOR>
 * @date 2025/07/10
 */
@Data
@Schema(description = "批量更新学生答题器绑定状态请求DTO")
public class BatchUpdateStudentClickerDTO implements Serializable {

    /**
     * 学生答题器更新操作列表
     */
    @Schema(description = "学生答题器更新操作列表")
    @NotEmpty(
        groups = {BReceiverValidGroup.BatchUpdateStudentClicker.class},
        message = "学生答题器更新操作列表不能为空")
    @Valid
    private List<BInteractionReceiverClickerDTO> studentClickerUpdates;
}
