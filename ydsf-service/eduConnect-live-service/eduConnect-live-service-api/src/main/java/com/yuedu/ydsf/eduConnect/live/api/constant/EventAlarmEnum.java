package com.yuedu.ydsf.eduConnect.live.api.constant;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/03/10
 **/
@AllArgsConstructor
public enum EventAlarmEnum {

    /**
     * 摄像头未开启
     */
    EVENT_ALARM_STATUS_0(0, "摄像头未开启"),

    /**
     *  图片上传完毕
     *
     */
    EVENT_ALARM_STATUS_1(1, "图片上传完毕"),

    /**
     * 拍照失败
     */
    EVENT_ALARM_STATUS_2(2, "拍照失败");


    public final Integer code;

    public final String desc;
}
