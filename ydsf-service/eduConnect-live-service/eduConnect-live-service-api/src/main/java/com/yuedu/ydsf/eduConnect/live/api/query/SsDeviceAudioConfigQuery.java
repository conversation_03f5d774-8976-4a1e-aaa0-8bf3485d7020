package com.yuedu.ydsf.eduConnect.live.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 设备音频相关配置
 *
 * <AUTHOR>
 * @date 2024/09/28
 */
@Data
@Schema(description = "设备音频相关配置查询对象")
public class SsDeviceAudioConfigQuery {

  /** 主键ID */
  @Schema(description = "主键ID")
  private Long id;

  /**
   * 参数配置类: che.audio.start_debug_recording-本地生成音频文件 che.audio.codec.name-打开OPUS和百家云相似,关闭OPUS是声网独特音质
   * che.audio.mute.input.channel-忽略右声道杂音 che.audio.current.recording.boostMode-关闭系统音量调整
   * che.audio.enable.agc-关闭增益调整 che.audio.input.volume-输入音量
   */
  @Schema(description = "参数配置类")
  private String parameters;

  /** 加入频道之后的参数 */
  @Schema(description = "加入频道之后的参数")
  private String inClassParameters;

  /** 音量增益 */
  @Schema(description = "音量增益")
  private Integer adjustRecordingSignalVolume;

  /** 创建时间 */
  @Schema(description = "创建时间")
  private LocalDateTime ctime;

  /** 创建者 */
  @Schema(description = "创建者")
  private String creator;

  /** 编辑时间 */
  @Schema(description = "编辑时间")
  private LocalDateTime mtime;

  /** 编辑者 */
  @Schema(description = "编辑者")
  private String modifer;

  /** 备注 */
  @Schema(description = "备注")
  private String remark;

  /** 是否删除:0-未删除;1-已删除 */
  @Schema(description = "是否删除:0-未删除;1-已删除")
  private Integer delFlag;
}
