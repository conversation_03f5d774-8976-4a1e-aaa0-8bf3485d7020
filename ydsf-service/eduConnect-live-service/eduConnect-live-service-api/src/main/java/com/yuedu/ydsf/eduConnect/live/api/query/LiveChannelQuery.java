package com.yuedu.ydsf.eduConnect.live.api.query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 直播频道 查询类
 *
 * <AUTHOR>
 * @date 2025-01-07 20:29:36
 */
@Data
@Schema(description = "直播频道查询类")
public class LiveChannelQuery {


	/**
	* 主键
	*/
    @Schema(description="主键")
    private Long id;

	/**
	* 直播间排期计划ID
	*/
    @Schema(description="直播间排期计划ID")
    private Long liveRoomPlanDetailId;

	/**
	* 频道ID
	*/
    @Schema(description="频道ID")
    private String channelId;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-未删除;1-已删除
	*/
    @Schema(description="是否删除: 0-未删除;1-已删除")
    private Integer delFlag;
}
