package com.yuedu.ydsf.eduConnect.live.api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备命令类型
 *
 * @date 2025/6/6 8:55
 * @project @Title: DeviceCommandTypeEnum.java
 */
@AllArgsConstructor
@Getter
public enum DeviceCommandTypeEnum {

  /** 日志上传 */
  LOG_UPLOAD(1, "日志上传", "logUpload"),

  /** 重启设备 */
  RESTART_DEVICE(1, "重启设备", "restart"),

  /** 关闭设备 */
  SHUTDOWN_DEVICE(2, "关闭设备", "shutdown"),

  /** 更新配置 */
  UPDATE_CONFIG(3, "更新配置", "updateConfig"),

  /** 获取状态 */
  GET_STATUS(4, "获取状态", "getStatus"),

  /** 自定义命令 */
  CUSTOM_COMMAND(5, "自定义命令", "custom"),

  /** 音量控制 */
  VOLUME_CONTROL(6, "音量控制", "volumeControl"),

  /** 屏幕控制 */
  SCREEN_CONTROL(7, "屏幕控制", "screenControl"),

  /** 网络诊断 */
  NETWORK_DIAGNOSIS(8, "网络诊断", "networkDiagnosis"),

  /** 上传日志 */
  UPLOAD_LOGS(9, "上传日志", "uploadLogs");

  /** 命令类型代码 */
  private final Integer code;

  /** 命令类型描述 */
  private final String desc;

  /** 命令标识 */
  private final String command;

  /**
   * 根据代码获取枚举
   *
   * @param code 代码
   * @return 枚举
   */
  public static DeviceCommandTypeEnum getByCode(Integer code) {
    if (code == null) {
      return null;
    }
    for (DeviceCommandTypeEnum typeEnum : DeviceCommandTypeEnum.values()) {
      if (typeEnum.getCode().equals(code)) {
        return typeEnum;
      }
    }
    return null;
  }

  /**
   * 根据命令标识获取枚举
   *
   * @param command 命令标识
   * @return 枚举
   */
  public static DeviceCommandTypeEnum getByCommand(String command) {
    if (command == null || command.trim().isEmpty()) {
      return null;
    }
    for (DeviceCommandTypeEnum typeEnum : DeviceCommandTypeEnum.values()) {
      if (typeEnum.getCommand().equals(command)) {
        return typeEnum;
      }
    }
    return null;
  }
}
