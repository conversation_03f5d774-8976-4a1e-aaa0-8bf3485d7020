package com.yuedu.ydsf.eduConnect.live.api.query;

import com.yuedu.ydsf.eduConnect.live.api.valid.SsInteractionSettingValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取互动二维码
 * <AUTHOR>
 * @date 2024/11/6 9:22
 */
@Data
@Schema(description = "获取互动二维码")
public class SsInteractionQRCodeQuery implements Serializable {


	/**
	 * 设备编号
	 */
	@Schema(description="设备编号")
	@NotBlank(groups = {SsInteractionSettingValidGroup.GetInteractionQRCodeGroup.class}, message = "设备编号不能为空")
	private String deviceNo;

	/**
	 * 房间roomUuid
	 */
	@Schema(description="房间roomUuid")
//	@NotBlank(groups = {SsInteractionSettingValidGroup.GetInteractionQRCodeGroup.class}, message = "房间roomUuid不能为空")
	private String roomUuid;

	/**
	 * 校区设备类型: 1-主讲端; 2-教室端;
	 */
	@Schema(description="校区设备类型")
	@NotNull(groups = {SsInteractionSettingValidGroup.GetInteractionQRCodeGroup.class}, message = "校区设备类型不能为空")
	private Integer deviceType;

	/**
	 * 上课类型: 0-直播课; 1-点播课;
	 */
	@Schema(description="上课类型: 0-直播课; 1-点播课;")
	@NotNull(groups = {SsInteractionSettingValidGroup.GetInteractionQRCodeGroup.class}, message = "上课类型不能为空")
	private Integer attendClassType;

	/**
	 * 课次ID
	 */
	@Schema(description = "课次ID")
	private Long classTimeId;


}
