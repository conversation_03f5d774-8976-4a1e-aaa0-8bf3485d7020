package com.yuedu.ydsf.eduConnect.live.api.query;

import com.yuedu.ydsf.eduConnect.live.api.valid.SsInteractionRedPacketSettingValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 门店红包规则设置表 查询类
 *
 * <AUTHOR>
 * @date 2024-11-02 11:22:20
 */
@Data
@Schema(description = "门店红包规则设置表查询类")
public class SsInteractionRedPacketSettingQuery {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	* 飞天账号ID/校区ID
	*/
    @Schema(description="飞天账号ID/校区ID")
    @NotNull(groups = {SsInteractionRedPacketSettingValidGroup.GetRedPacketSettingByStoreIdGroup.class}, message = "飞天账号ID/校区ID不能为空")
    private Long source;

	/**
	* 校管家校区ID
	*/
    @Schema(description="校管家校区ID")
	@NotBlank(groups = {SsInteractionRedPacketSettingValidGroup.GetRedPacketSettingByXgjCampusIdGroup.class}, message = "校管家校区ID不能为空")
    private String xgjCampusId;

	/**
	* 红包总分数
	*/
    @Schema(description="红包总分数")
    private Integer redPacketNumber;

	/**
	* 红包分数上限
	*/
    @Schema(description="红包分数上限")
    private Integer redPacketUpperLimit;

	/**
	* 红包分数下限
	*/
    @Schema(description="红包分数下限")
    private Integer redPacketLowerLimit;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-未删除;1-已删除
	*/
    @Schema(description="是否删除: 0-未删除;1-已删除")
    private Integer delFlag;


}
