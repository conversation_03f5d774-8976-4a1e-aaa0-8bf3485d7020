package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 上课时间段下的教学计划明细 视图类
 *
 * <AUTHOR>
 * @date 2024-11-29 15:29:56
 */
@Data
@Schema(description = "已发布的教学计划明细表视图类")
public class TimeTypeTeachingPlanDetailPubVO {

    /**
     * 上课时段类型:1-上午;2-下午;3-晚上
     */
    @Schema(description="时段类型:1-上午;2-下午;3-晚上")
    private Integer timeType;

    /**
     * 上课时间段下的教学计划明细
     */
    @Schema(description="上课时间段下的教学计划明细")
    private List<TeachingPlanDetailPubVO> teachingPlanDetailPubVOList;

}

