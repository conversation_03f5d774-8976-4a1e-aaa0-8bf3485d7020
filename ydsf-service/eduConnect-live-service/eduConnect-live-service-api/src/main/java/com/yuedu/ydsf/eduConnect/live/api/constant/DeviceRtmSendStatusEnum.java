package com.yuedu.ydsf.eduConnect.live.api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备RTM命令发送状态枚举
 *
 */
@AllArgsConstructor
@Getter
public enum DeviceRtmSendStatusEnum {

    /**
     * 发送成功
     */
    SUCCESS(0, "发送成功"),

    /**
     * 发送失败
     */
    FAILED(1, "发送失败");

    /**
     * 状态代码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 枚举值
     */
    public static DeviceRtmSendStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeviceRtmSendStatusEnum statusEnum : DeviceRtmSendStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否为成功状态
     *
     * @param code 状态代码
     * @return 是否成功
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.getCode().equals(code);
    }

    /**
     * 判断是否为失败状态
     *
     * @param code 状态代码
     * @return 是否失败
     */
    public static boolean isFailed(Integer code) {
        return FAILED.getCode().equals(code);
    }
}
