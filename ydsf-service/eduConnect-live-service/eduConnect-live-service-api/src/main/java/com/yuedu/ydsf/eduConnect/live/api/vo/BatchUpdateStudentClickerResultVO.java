package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 批量更新学生答题器绑定状态结果VO
 *
 * <AUTHOR>
 * @date 2025/07/10
 */
@Data
@Schema(description = "批量更新学生答题器绑定状态结果VO")
public class BatchUpdateStudentClickerResultVO implements Serializable {

    /**
     * 是否全部成功
     */
    @Schema(description = "是否全部成功")
    private Boolean allSuccess;

    /**
     * 成功处理的数量
     */
    @Schema(description = "成功处理的数量")
    private Integer successCount;

    /**
     * 失败处理的数量
     */
    @Schema(description = "失败处理的数量")
    private Integer failureCount;

    /**
     * 总处理数量
     */
    @Schema(description = "总处理数量")
    private Integer totalCount;

    /**
     * 成功结果列表
     */
    @Schema(description = "成功结果列表")
    private List<StudentClickerUpdateResultVO> successResults;

    /**
     * 失败结果列表
     */
    @Schema(description = "失败结果列表")
    private List<StudentClickerUpdateResultVO> failureResults;

    /**
     * 操作摘要信息
     */
    @Schema(description = "操作摘要信息")
    private String summary;
}
