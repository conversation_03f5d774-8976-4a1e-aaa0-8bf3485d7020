package com.yuedu.ydsf.eduConnect.live.api.query;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 互动设置表
 *
 * <AUTHOR>
 * @date 2024/11/04
 */
@Data
@Schema(description = "互动设置表查询对象")
public class SsInteractionSettingQuery {

  /** 主键ID */
  @Schema(description = "主键ID")
  private Long id;

  /** 课次ID */
  @Schema(description = "课次ID")
  private Long classTimeId;

  /** 点播库id */
  @Schema(description = "点播库id")
  private String recordingId;

  /** 下发类型: 1-主讲端下发; 2-门店端下发; */
  @Schema(description = "下发类型: 1-主讲端下发; 2-门店端下发;")
  private Byte sendType;

  /** 设备ID */
  @Schema(description = "设备ID")
  private Integer deviceId;

  /** 互动类型: 0-计时器; 1-答题抢票器; 2-抢红包; */
  @Schema(description = "互动类型: 0-计时器; 1-答题抢票器; 2-抢红包;")
  private Byte interactionType;

  /** 计时器-计时器时间(单位:分钟) */
  @Schema(description = "计时器-计时器时间(单位:分钟)")
  private Integer timingTime;

  /** 答题抢票器-选项个数 */
  @Schema(description = "答题抢票器-选项个数")
  private Integer optionNumber;

  /** 答题抢票器-答题倒计时(单位:秒) */
  @Schema(description = "答题抢票器-答题倒计时(单位:秒)")
  private Integer answerCountdown;

  /** 抢红包-积分数量 */
  @Schema(description = "抢红包-积分数量")
  private Integer integralNumber;

  /** 红包个数 */
  @Schema(description = "红包个数")
  private Integer redPacketNum;

  /** 互动持续时间 */
  @Schema(description = "互动持续时间")
  private Integer interactionDuration;

  /** 互动器按键间隔生效次数 */
  @Schema(description = "互动器按键间隔生效次数")
  private Integer keyIntervalNum;

  /** 创建时间 */
  @Schema(description = "创建时间")
  private LocalDateTime ctime;

  /** 创建者 */
  @Schema(description = "创建者")
  private String creator;

  /** 编辑时间 */
  @Schema(description = "编辑时间")
  private LocalDateTime mtime;

  /** 编辑者 */
  @Schema(description = "编辑者")
  private String modifer;

  /** 是否删除:0-未删除;1-已删除 */
  @Schema(description = "是否删除:0-未删除;1-已删除")
  private Integer delFlag;

  /** 互动页ID */
  @Schema(description = "互动页ID")
  private Long pageId;

  /** 题目标题 */
  @Schema(description = "题目标题")
  private String questionTitle;

  /** 题目内容(JSON格式,包含选项等详细信息) */
  @Schema(description = "题目内容(JSON格式,包含选项等详细信息)")
  private String questionContent;
}
