package com.yuedu.ydsf.eduConnect.live.api.query;

import com.yuedu.ydsf.common.core.util.V_A_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 设备表
 *
 * <AUTHOR>
 * @date 2024/09/26
 */
@Data
public class SsDeviceQuery implements Serializable {
  /** 主键ID */
  @Schema(description = "主键ID")
  private Long id;

  /** 校区ID */
  @Schema(description = "校区ID")
  private Long campusId;

  /** 教室ID */
  @Schema(description = "教室ID")
  private Long classRoomId;

  /** 设备名称 */
  @Size(
      groups = {V_A_E.class},
      max = 255,
      message = "设备名称字符长度不能大于255")
  @Schema(description = "设备名称")
  private String deviceName;

  /** 设备号 */
  @NotBlank(
      groups = {V_A_E.class},
      message = "设备号不能为空")
  @Size(
      groups = {V_A_E.class},
      max = 255,
      message = "设备号字符长度不能大于255")
  @Schema(description = "设备号")
  private String deviceNo;

  /**
   * 设备类型:1-主讲端;2-教室端
   *
   * @type ss_device_device_type
   */
  @Schema(description = "设备类型:1-主讲端;2-教室端")
  private Byte deviceType;

  /**
   * 设备状态:0-启用;1-禁用
   *
   * @type ss_device_device_state
   */
  @Schema(description = "设备状态:0-启用;1-禁用")
  private Byte deviceState;

  /**
   * 设备是否激活:0-未激活;1-已激活
   *
   * @type ss_device_device_active
   */
  @Schema(description = "设备是否激活:0-未激活;1-已激活")
  private Integer deviceActive;

  /**
   * 设备欠费状态:0-正常;1-其他状态为欠费
   *
   * @type ss_device_device_arrears
   */
  @Schema(description = "设备欠费状态:0-正常;1-其他状态为欠费")
  private Integer deviceArrears;

  /**
   * 是否在线:0-否;1-是
   *
   * @type ss_device_is_on_line
   */
  @Schema(description = "是否在线:0-否;1-是")
  private Byte isOnLine;

  /**
   * 设备是否永久:0-否;1-是
   *
   * @type ss_device_indate_forever
   */
  @Schema(description = "设备是否永久:0-否;1-是")
  private Integer indateForever;

  /** 有效期开始时间 */
  @Schema(description = "有效期开始时间")
  private LocalDateTime indateStart;

  /** 有效期结束时间 */
  @Schema(description = "有效期结束时间")
  private LocalDateTime indateEnd;

  /** 设备配置表ID */
  @Schema(description = "设备配置表ID")
  private Long configId;

  /** 音频配置ID */
  @Schema(description = "音频配置ID")
  private Long audioConfigId;

  /**
   * 主讲端录课方式:0-页面录制;1-云端录制
   *
   * @type ss_device_agora_recording_type
   */
  @Schema(description = "主讲端录课方式:0-页面录制;1-云端录制")
  private Byte agoraRecordingType;

  /** 直播背景图路径 */
  @Size(
      groups = {V_A_E.class},
      max = 500,
      message = "直播背景图路径字符长度不能大于500")
  @Schema(description = "直播背景图路径")
  private String liveBackground;

  /**
   * 终端SDK版本:0-webSDK;1-Electron
   *
   * @type ss_device_sdk_type
   */
  @Schema(description = "终端SDK版本:0-webSDK;1-Electron")
  private Byte sdkType;

  /** 创建时间 */
  @Schema(description = "创建时间")
  private LocalDateTime ctime;

  /** 创建者 */
  @Size(
      groups = {V_A_E.class},
      max = 64,
      message = "创建者字符长度不能大于64")
  @Schema(description = "创建者")
  private String creator;

  /** 编辑时间 */
  @Schema(description = "编辑时间")
  private LocalDateTime mtime;

  /** 编辑者 */
  @Size(
      groups = {V_A_E.class},
      max = 64,
      message = "编辑者字符长度不能大于64")
  @Schema(description = "编辑者")
  private String modifer;

  /** 设备是否是由UUID生成不为空代表注册设备码是由uuid生成 */
  @Size(
      groups = {V_A_E.class},
      max = 255,
      message = "设备是否是由UUID生成不为空代表注册设备码是由uuid生成字符长度不能大于255")
  @Schema(description = "设备是否是由UUID生成不为空代表注册设备码是由uuid生成")
  private String deviceUuid;

  /**
   * 是否删除:0-未删除;1-已删除
   *
   * @type ss_device_del_flag
   */
  @Schema(description = "是否删除:0-未删除;1-已删除")
  private Integer delFlag;

  /** 主讲端直播间ID */
  @Schema(description = "主讲端直播间ID")
  private Long liveRoomId;

  /** 多条件搜索 */
  @Schema(description = "多条件搜索")
  private String searchContent;
}
