package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.eduConnect.api.constant.ReleaseEnum;
import com.yuedu.ydsf.eduConnect.live.api.valid.SsInteractionConsequenceValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 互动结果表
 *
 * <AUTHOR>
 * @date 2024/11/05
 */
@Data
@Schema(description = "互动结果表传输对象")
public class SsInteractionConsequenceDTO implements Serializable {

  /** 主键ID */
  @NotNull(
      groups = {V_E.class},
      message = "主键ID不能为空")
  private Long id;

  /** 互动设置ID */
  @Schema(description = "互动设置ID")
  @NotNull(
      groups = {
        V_A_E.class,
        SsInteractionConsequenceValidGroup.SaveInteractionConsequenceGroup.class,
        SsInteractionConsequenceValidGroup.GetInteractionConsequenceGroup.class
      },
      message = "互动设置ID不能为空")
  private Long interactionSettingId;

  /** 课次ID */
  @Schema(description = "课次ID")
  @NotNull(
      groups = {
        V_A_E.class,
        SsInteractionConsequenceValidGroup.SaveInteractionConsequenceGroup.class
      },
      message = "课次ID不能为空")
  private Long classTimeId;

  /** 设备ID */
  @Schema(description = "设备ID")
  @NotNull(
      groups = {
        V_A_E.class,
        SsInteractionConsequenceValidGroup.SaveInteractionConsequenceGroup.class
      },
      message = "设备ID不能为空")
  private Long deviceId;

  /** 校区ID */
  @Schema(description = "校区ID")
  @NotNull(
      groups = {
        V_A_E.class,
        SsInteractionConsequenceValidGroup.SaveInteractionConsequenceGroup.class
      },
      message = "校区ID不能为空")
  private Long campusId;

  /** 教室ID */
  @Schema(description = "教室ID")
  @NotNull(
      groups = {
        V_A_E.class,
        SsInteractionConsequenceValidGroup.SaveInteractionConsequenceGroup.class
      },
      message = "教室ID不能为空")
  private Long classRoomId;

  /** 校管家学生ID */
  @Schema(description = "校管家学生ID")
  @NotBlank(
      groups = {V_A_E.class},
      message = "校管家学生ID不能为空")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "校管家学生ID长度不能大于255")
  private String studentId;

  /** 校管家学号 */
  @Schema(description = "校管家学号")
  @NotBlank(
      groups = {V_A_E.class},
      message = "校管家学号不能为空")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "校管家学号长度不能大于255")
  private String studentNo;

  /** 校管家学生名称 */
  @Schema(description = "校管家学生名称")
  @NotBlank(
      groups = {V_A_E.class},
      message = "校管家学生名称不能为空")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "校管家学生名称长度不能大于255")
  private String studentName;

  /** 抢红包-抢到积分数量 */
  @Schema(description = "抢红包-抢到积分数量")
  private Integer integralNumber;

  /** 答题抢票器-选项 */
  @Schema(description = "答题抢票器-选项")
  @Length(
      groups = {V_A_E.class},
      max = 10,
      message = "答题抢票器-选项长度不能大于10")
  private String answerOption;

  /** 创建时间 */
  @Schema(description = "创建时间")
  private LocalDateTime ctime;

  /** 创建者 */
  @Schema(description = "创建者")
  @Length(
      groups = {V_A_E.class},
      max = 64,
      message = "创建者长度不能大于64")
  private String creator;

  /** 编辑时间 */
  @Schema(description = "编辑时间")
  private LocalDateTime mtime;

  /** 编辑者 */
  @Schema(description = "编辑者")
  @Length(
      groups = {V_A_E.class},
      max = 64,
      message = "编辑者长度不能大于64")
  private String modifer;

  /** 删除标记 */
  @Schema(description = "删除标记")
  @NotNull(
      groups = {V_A_E.class},
      message = "删除标记不能为空")
  private Byte delFlag;

  /** 互动类型 */
  @NotNull(
      groups = {SsInteractionConsequenceValidGroup.GetInteractionConsequenceGroup.class},
      message = "互动类型不能为空")
  private Byte interactionType;

  /** 答题器选项Map传输参数 */
  private Map<String, Integer> answerOptionMap;

  /** 获取结果的终端类型 */
  @NotNull(
      groups = {SsInteractionConsequenceValidGroup.GetInteractionConsequenceGroup.class},
      message = "终端类型不能为空")
  private Integer terminalResult;

  /** roomUUID */
  @NotNull(
      groups = {SsInteractionConsequenceValidGroup.GetInteractionConsequenceGroup.class},
      message = "roomUUID不能为空")
  private String roomUUID;

  /** 版本 */
  private Integer release;
}
