package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.eduConnect.live.api.valid.RecordingValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 主讲录课表 传输类
 *
 * <AUTHOR>
 * @date 2024-12-03 08:43:22
 */
@Data
@Schema(description = "主讲录课表传输类")
public class RecordingDTO implements Serializable {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	* 录课设备ID
	*/
    @Schema(description="录课设备ID")
    private Long deviceId;

	/**
	* 录课任务ID
	*/
    @Schema(description="录课任务ID")
	@NotNull(groups = {RecordingValidGroup.CreateRecordingGroup.class},message = "录课任务ID不能为空")
    private Long recordVideoTaskId;

	/**
	* 声网录制ID
	*/
    @Schema(description="声网录制ID")
    private String agoraRecordId;

	/**
	* 声网房间UUID
	*/
    @Schema(description="声网房间UUID")
    private String roomUuid;

	/**
	* 录制状态: 0-待录制; 1-录制中; 2-正常录制完成; 3-录制作废(重新录制); 4-视频处理中; 5-视频处理失败; 6-停止录制; 7-转码中; 8-转码失败;
	*/
    @Schema(description="录制状态: 0-待录制; 1-录制中; 2-正常录制完成; 3-录制作废(重新录制); 4-视频处理中; 5-视频处理失败; 6-停止录制; 7-转码中; 8-转码失败;")
    private Integer recordingStatus;

	/**
	* 录制资源
	*/
    @Schema(description="录制资源")
    private String recordingResources;

	/**
	* 声网云端录制ID
	*/
    @Schema(description="声网云端录制ID")
    private String agoraCloudRecordId;

	/**
	* 云端录制资源地址
	*/
    @Schema(description="云端录制资源地址")
    private String cloudRecordingResources;

    /**
     * 视频点播Vod中videoId
     */
    @Schema(description="视频点播Vod中videoId")
    private String vodVideoId;

	/**
	* 声网单流云录制ID
	*/
    @Schema(description="声网单流云录制ID")
    private String agoraCloudRecordIndividualResourceId;

	/**
	* 声网云端录制单流ID
	*/
    @Schema(description="声网云端录制单流ID")
    private String agoraCloudRecordIndividualId;

	/**
	* 下载地址
	*/
    @Schema(description="下载地址")
    private String downloadUrl;

    /**
     * 下载Vod的Id
     */
    @Schema(description="下载Vod的Id")
    private String downloadVodId;

	/**
	* 提交状态: 0-未提交; 1-待审核; 2-已通过; 3-未通过;
	*/
    @Schema(description="提交状态: 0-未提交; 1-待审核; 2-已通过; 3-未通过;")
    private Integer auditStatus;

	/**
	* 录制时间(yyyy-MM-dd HH:mm:ss)
	*/
    @Schema(description="录制时间(yyyy-MM-dd HH:mm:ss)")
    private LocalDateTime recordingTime;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

	/**
	 * 课节名称
	 */
	@Schema(description="课节名称")
	@NotBlank(groups = {RecordingValidGroup.CreateRecordingGroup.class},message = "课节名称不能为空")
	private String lessonName;

	/**
	 * 主讲老师ID
	 */
	@Schema(description = "主讲老师ID")
	@NotNull(groups = {RecordingValidGroup.CreateRecordingGroup.class},message = "主讲老师ID不能为空")
	private Long lecturerId;

}
