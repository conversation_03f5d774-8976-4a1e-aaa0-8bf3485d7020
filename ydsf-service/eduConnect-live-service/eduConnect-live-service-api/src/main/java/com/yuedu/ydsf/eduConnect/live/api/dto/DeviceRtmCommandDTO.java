package com.yuedu.ydsf.eduConnect.live.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 设备RTM命令请求DTO
 *
 * @date 2024/12/19
 */
@Data
@Schema(description = "设备RTM命令请求DTO")
public class DeviceRtmCommandDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    @Schema(description = "设备编号", required = true)
    private String deviceNo;

    /**
     * 命令类型
     */
    @NotNull(message = "命令类型不能为空")
    @Schema(description = "命令类型：0-上传日志，1-重启设备，2-关闭设备，3-更新配置，4-获取状态，5-自定义命令", required = true)
    private Integer commandType;

    /**
     * 命令类型
     */
    @NotNull(message = "获取日志天数")
    @Schema(description = "获取日志天数")
    private Integer logDay;

    /**
     * 命令参数
     */
    @Schema(description = "命令参数，JSON格式字符串")
    private String commandParams;

    /**
     * 命令描述
     */
    @Schema(description = "命令描述")
    private String commandDesc;

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒），默认30秒")
    private Integer timeout = 30;

    /**
     * 是否需要响应确认
     */
    @Schema(description = "是否需要响应确认，默认true")
    private Boolean needAck = false;
}
