package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.eduConnect.live.api.valid.BReceiverValidGroup;
import com.yuedu.ydsf.eduConnect.live.api.valid.BReceiverValidGroup.UpdateStudentClicker;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 互动答题器
 *
 * <AUTHOR>
 * @date 2025/04/08
 */
@Data
@Schema(description = "互动答题器传输对象")
public class BInteractionReceiverClickerDTO implements Serializable {

  /** 主键ID */
  @NotNull(
      groups = {V_E.class},
      message = "主键ID不能为空")
  private Long id;

  /** 接收器sn码 */
  @Schema(description = "接收器sn码")
  @NotNull(
      groups = {V_A_E.class, BReceiverValidGroup.UpdateStudentClicker.class},
      message = "接收器sn码不能为空")
  private String receiverSnNumber;

  /** 自增序号 */
  @Schema(description = "自增序号")
  @NotNull(
      groups = {V_A_E.class},
      message = "自增序号不能为空")
  private Integer serialNumber;

  /** 答题器SN码 */
  @Schema(description = "答题器SN码")
  @NotBlank(
      groups = {
        V_A_E.class,
        BReceiverValidGroup.Reg.class,
        BReceiverValidGroup.UpdateStudentClicker.class
      },
      message = "答题器SN码不能为空")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "答题器SN码长度不能大于255")
  private String snNumber;

  /** 答题器状态: 0-启用; 1-禁用; */
  @Schema(description = "答题器状态: 0-启用; 1-禁用;")
  @NotNull(
      groups = {V_A_E.class},
      message = "答题器状态不能为空")
  private Byte clickerState;

  /** 创建人 */
  @Schema(description = "创建人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "创建人长度不能大于255")
  private String createBy;

  /** 创建时间 */
  @Schema(description = "创建时间")
  @NotNull(
      groups = {V_A_E.class},
      message = "创建时间不能为空")
  private LocalDateTime createTime;

  /** 修改人 */
  @Schema(description = "修改人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "修改人长度不能大于255")
  private String updateBy;

  /** 修改时间 */
  @Schema(description = "修改时间")
  @NotNull(
      groups = {V_A_E.class},
      message = "修改时间不能为空")
  private LocalDateTime updateTime;

  /** 是否删除: 0-否; 1-是; */
  @Schema(description = "是否删除: 0-否; 1-是;")
  @NotNull(
      groups = {V_A_E.class},
      message = "是否删除不能为空")
  private Byte delFlag;

  @NotNull(
      groups = {BReceiverValidGroup.UpdateStudentClicker.class},
      message = "classStudentId不能为空")
  private Long classStudentId;
}
