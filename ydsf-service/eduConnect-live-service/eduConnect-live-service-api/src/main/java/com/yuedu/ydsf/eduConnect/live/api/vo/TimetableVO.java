package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;

/**
 * @author: 张浩宇
 * @date: 2024/12/11
 **/
@Data
public class TimetableVO {

    /**
     * 主键ID
     */
    @Schema(description="主键ID")
    private Long id;

    /**
     * 门店ID
     */
    @Schema(description="门店ID")
    private Long storeId;

    /**
     * 这节课的编号，课程类型（1位）+ 第几节课（3位）+ 主表的ID
     */
    @Schema(description="这节课的编号，课程类型（1位）+ 第几节课（3位）+ 主表的ID")
    private Long lessonNo;

    /**
     * 门店已约直播课ID、门店已约点播课排期表ID、门店补课表ID
     */
    @Schema(description="门店已约直播课ID、门店已约点播课排期表ID、门店补课表ID")
    private Long coursePlanId;

    /**
     * 课程类型: 1-直播课; 2-点播课; 3-补课;
     */
    @Schema(description="课程类型: 1-直播课; 2-点播课; 3-补课;")
    private Integer courseType;

    /**
     * 教室ID
     */
    @Schema(description="教室ID")
    private Long classroomId;

    /**
     * 班级ID
     */
    @Schema(description="班级ID")
    private Long classId;

    /**
     * 主讲老师ID
     */
    @Schema(description="主讲老师ID")
    private Long lectureId;

    /**
     * 指导老师ID
     */
    @Schema(description="指导老师ID")
    private Long teacherId;

    /**
     * 课程ID
     */
    @Schema(description="课程ID")
    private Long courseId;

    /**
     * 第几节课
     */
    @Schema(description="第几节课")
    private Integer lessonOrder;

    /**
     * 上课时段ID
     */
    @Schema(description="上课时段ID")
    private Long timeSlotId;

    /**
     * 时段类型: 1-上午; 2-下午; 3-晚上;
     */
    @Schema(description="时段类型: 1-上午; 2-下午; 3-晚上;")
    private Integer timeSlotType;

    /**
     * 上课日期
     */
    @Schema(description="上课日期")
    private LocalDate classDate;

    /**
     * 上课开始时间
     */
    @Schema(description="上课开始时间")
    private LocalTime classStartTime;

    /**
     * 上课结束时间
     */
    @Schema(description="上课结束时间")
    private LocalTime classEndTime;

    /**
     * 上课开始日期时间
     */
    @Schema(description="上课开始日期时间")
    private LocalDateTime classStartDateTime;

    /**
     * 上课结束日期时间
     */
    @Schema(description="上课结束日期时间")
    private LocalDateTime classEndDateTime;

    /**
     * 创建人
     */
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description="修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 主讲老师名字
     */
    @Schema(description = "主讲老师名字")
    private String lectureName;

    /**
     * 主讲老师名字
     */
    @Schema(description = "主讲老师名字")
    private String lectureNickName;

    /**
     * 教室名称
     */
    @Schema(description = "教室名称")
    private String classRoomName;

    /**
     * 指导老师名称
     */
    @Schema(description = "指导老师名称")
    private String teacherName;

    /**
     * 课程名称
     */
    @Schema(description = "课程名称")
    private String courseName;

    /**
     * 课节名称
     */
    @Schema(description = "课节名称")
    private String lessonName;

    /**
     * 课程图片
     */
    @Schema(description = "课程图片")
    private String imgUrl;

    /**
     * 阶段名称
     */
    @Schema(description="阶段名称")
    private String stageName;

    /**
     * 上课时段名称
     */
    @Schema(description="上课时段名称")
    private String timeSlotName;

    /**
     * 班级名称
     */
    @Schema(description="班级名称")
    private String className;

    /**
     * 频道ID
     */
    @Schema(description="频道ID")
    private String channelId;

    /**
     * 声网云端录制ID
     */
    @Schema(description="声网云端录制ID")
    private String agoraCloudRecordId;

    /**
     * 阿里云视频播放地址
     */
    @Schema(description="阿里云视频播放地址")
    private String aliyunPlayUrl;

    /**
     * 上课状态: 0-未开始(距离开课30分钟之前); 1-即将开始(距离开课前30分钟内); 2-进行中; 3-已结束;
     */
    @Schema(description="上课状态: 0-未开始(距离开课30分钟之前); 1-即将开始(距离开课前30分钟内); 2-进行中; 3-已结束;")
    private Integer attendClassState;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    private Integer coursewareId;

    /**
     * 学生人数
     */
    @Schema(description = "学生人数")
    private Integer studentCount = 0;

    /**
     * 课程版本号
     */
    @Schema(description="课程版本号")
    private Integer courseVersion;

    /**
     * 课件版本
     */
    @Schema(description="课件版本")
    private Integer coursewareVersion;

    /**
     * 教学计划ID
     */
    @Schema(description="教学计划ID")
    private Long teachingPlanId;


    /**
     * 课程类型id
     */
    @Schema(description = "课程类型id")
    private Long courseTypeId;

}
