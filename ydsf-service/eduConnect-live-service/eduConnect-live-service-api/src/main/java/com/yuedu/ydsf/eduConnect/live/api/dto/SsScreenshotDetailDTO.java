package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

/**
* 双师截图明细表
*
* <AUTHOR>
* @date  2025/02/18
*/
@Data
@Schema(description = "双师截图明细表传输对象")
public class SsScreenshotDetailDTO implements Serializable {


    /**
     * 主键ID
     */
    @NotNull(groups = {V_E.class}, message = "主键ID不能为空")
    private Long id;



    /**
     * 课次ID
     */
    @Schema(description = "课次ID")
    @NotNull(groups = {V_A_E.class }, message = "课次ID不能为空")
    private Long classTimeId;

    /**
     * 教室端设备ID
     */
    @Schema(description = "教室端设备ID")
    @NotNull(groups = {V_A_E.class }, message = "教室端设备ID不能为空")
    private Long deviceId;

    /**
     * 资源名称
     */
    @Schema(description = "资源名称")
    @Length(groups = {V_A_E.class }, max =255 ,message = "资源名称长度不能大于255")
    private String resourcesName;

    /**
     * 资源路径
     */
    @Schema(description = "资源路径")
    @Length(groups = {V_A_E.class }, max =255 ,message = "资源路径长度不能大于255")
    private String resourcesPath;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Length(groups = {V_A_E.class }, max =64 ,message = "创建者长度不能大于64")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑人
     */
    @Schema(description = "编辑人")
    @Length(groups = {V_A_E.class }, max =64 ,message = "编辑人长度不能大于64")
    private String modifer;

    /**
     * 是否删除: 0-未删除;1-已删除
     */
    @Schema(description = "是否删除: 0-未删除;1-已删除 字典类型：del_flag" ,type = "del_flag", defaultValue = "0")
    private Integer delFlag;

    /**
     * 截图时间
     */
    @Schema(description = "截图时间")
    private LocalDateTime screenshotTime;

    /**
     * 识别总数量
     */
    @Schema(description = "识别总数量", defaultValue = "0")
    private Integer recognitionTotalNum;

    /**
     * 识别幼儿数量
     */
    @Schema(description = "识别幼儿数量", defaultValue = "0")
    private Integer recognitionChildrenNum;

    /**
     * 识别青少年数量
     */
    @Schema(description = "识别青少年数量", defaultValue = "0")
    private Integer recognitionTeenagersNum;

    /**
     * 识别青年数量
     */
    @Schema(description = "识别青年数量", defaultValue = "0")
    private Integer recognitionYouthNum;

    /**
     * 识别中年数量
     */
    @Schema(description = "识别中年数量", defaultValue = "0")
    private Integer recognitionMiddleNum;

    /**
     * 识别老年数量
     */
    @Schema(description = "识别老年数量", defaultValue = "0")
    private Integer recognitionElderlyNum;

    /**
     * 识别状态: 0-未处理; 1-处理成功; 2-处理失败
     */
    @Schema(description = "识别状态: 0-未处理; 1-处理成功; 2-处理失败 字典类型：recognition_status" ,type = "recognition_status", defaultValue = "0")
    private Integer recognitionStatus;


}
