package com.yuedu.ydsf.eduConnect.live.api.query;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;


/**
* 互动接收器
*
* <AUTHOR>
* @date  2025/04/08
*/
@Data
@Schema(description = "互动接收器查询对象")
public class BInteractionReceiverQuery {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 教室ID
     */
    @Schema(description = "教室ID")
    private Long classroomId;

    /**
     * 接收器sn码
     */
    @Schema(description = "接收器sn码")
    private String snNumber;

    /**
     * 接收器型号
     */
    @Schema(description = "接收器型号")
    private String modelNo;

    /**
     * 接收器通道号
     */
    @Schema(description = "接收器通道号")
    private Long aisle;

    /**
     * 接收器状态: 0-启用; 1-禁用;
     */
    @Schema(description = "接收器状态: 0-启用; 1-禁用;")
    private Byte receiverState;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @Schema(description = "是否删除: 0-否; 1-是;")
    private Byte delFlag;

}
