package com.yuedu.ydsf.eduConnect.live.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * @ClassName CourseVodDTO
 * @Description 点播课程DTO
 * <AUTHOR>
 * @Date 2024/12/9 10:52
 * @Version v0.0.1
 */

@Data
public class CourseVodDTO {
    /**
     * 点播课程id
     */
    @NotNull(message = "点播课程id不能为空")
    private Long id;

    /**
     * 第几节课
     */
    @Schema(description="第几节课")
    private Integer lessonOrder;

    /**
     * 主讲id集合
     */
    @Schema(description = "主讲id集合")
    private List<Long> lectureIdList;

    /**
     * 课程ID
     */
    @Schema(description="课程ID")
    private Long courseId;

}
