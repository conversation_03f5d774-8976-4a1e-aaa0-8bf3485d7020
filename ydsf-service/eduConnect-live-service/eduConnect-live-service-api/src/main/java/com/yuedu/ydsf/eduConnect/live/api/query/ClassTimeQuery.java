package com.yuedu.ydsf.eduConnect.live.api.query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 上课时段表 查询类
 *
 * <AUTHOR>
 * @date 2025-01-08 09:27:04
 */
@Data
@Schema(description = "上课时段表查询类")
public class ClassTimeQuery {


	/**
	* 主键id
	*/
    @Schema(description="主键id")
    private Long id;

	/**
	* 时段名称
	*/
    @Schema(description="时段名称")
    private String name;

	/**
	* 时段类型:1-上午;2-下午;3-晚上
	*/
    @Schema(description="时段类型:1-上午;2-下午;3-晚上")
    private Integer type;

	/**
	* 开始时间 (HH:mm:ss)
	*/
    @Schema(description="开始时间 (HH:mm:ss)")
    private LocalTime startTime;

	/**
	* 结束时间 (HH:mm:ss)
	*/
    @Schema(description="结束时间 (HH:mm:ss)")
    private LocalTime endTime;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
