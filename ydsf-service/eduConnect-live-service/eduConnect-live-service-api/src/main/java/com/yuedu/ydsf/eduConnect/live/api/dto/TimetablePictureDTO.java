package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.eduConnect.live.api.valid.TimetableValidGroup.EventAlarmGroup;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

/**
* 上课拍照记录
*
* <AUTHOR>
* @date  2025/02/13
*/
@Data
@Schema(description = "上课拍照记录传输对象")
public class TimetablePictureDTO implements Serializable {


    /**
     * 主键ID
     */
    @NotNull(groups = {V_E.class}, message = "主键ID不能为空")
    private Long id;


    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;



    /**
     * 上课课次ID
     */
    @Schema(description = "上课课次ID")
    @NotNull(groups = {V_A.class, EventAlarmGroup.class}, message = "课次ID不能为空")
    private Long timeableId;

    /**
     * 拍照设备ID
     */
    @Schema(description = "拍照设备ID")
    private Long deviceId;

    /**
     * 拍照照片url
     */
    @Schema(description = "拍照照片url")
    @Length(groups = {V_A_E.class }, max =255 ,message = "拍照照片url长度不能大于255")
    private String photoUrl;

    /**
     * 识别总数量
     */
    @Schema(description = "识别总数量", defaultValue = "0")
    private Integer recognitionTotalNum;

    /**
     * 识别幼儿数量
     */
    @Schema(description = "识别幼儿数量")
    private Integer recognitionChildrenNum;

    /**
     * 识别青少年数量
     */
    @Schema(description = "识别青少年数量")
    private Integer recognitionTeenagersNum;

    /**
     * 识别青年数量
     */
    @Schema(description = "识别青年数量")
    private Integer recognitionYouthNum;

    /**
     * 识别中年数量
     */
    @Schema(description = "识别中年数量")
    private Integer recognitionMiddleNum;

    /**
     * 识别老年数量
     */
    @Schema(description = "识别老年数量")
    private Integer recognitionElderlyNum;

    /**
     * 识别状态: 0-未处理; 1-处理成功; 2-处理失败
     */
    @Schema(description = "识别状态: 0-未处理; 1-处理成功; 2-处理失败 字典类型：recognition_status" ,type = "recognition_status", defaultValue = "0")
    private Integer recognitionStatus;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "创建人长度不能大于255")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", defaultValue = "CURRENT_TIMESTAMP")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "修改人长度不能大于255")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间", defaultValue = "CURRENT_TIMESTAMP")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @Schema(description = "是否删除: 0-否; 1-是; 字典类型：del_flag" ,type = "del_flag", defaultValue = "'0'")
    private Integer delFlag;

    /**
     * 课中事件: 0-未检测到摄像头; 2-图片上传完毕; 2-拍照失败;
     */
    @Schema(description = "课中事件:" ,type = "event_alarm_type", defaultValue = "'0'")
    @NotNull(groups = { V_A.class}, message = "课中事件不能为空")
    private Integer event;


    /**
     * 计数第几张
     */
    @Schema(description = "计数第几张")
    private Integer sort;

    /**
     * 距离上课时长,单位：秒
     */
    @Schema(description = "距离上课时长,单位：秒")
    private Integer duration;


    /**
     * 上课课号
     */
    @Schema(description = "上课课号")
    private Long lessionNo;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
