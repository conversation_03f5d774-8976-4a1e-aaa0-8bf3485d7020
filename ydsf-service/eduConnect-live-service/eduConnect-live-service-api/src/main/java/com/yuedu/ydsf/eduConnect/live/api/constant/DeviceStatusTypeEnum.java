package com.yuedu.ydsf.eduConnect.live.api.constant;

import com.yuedu.ydsf.common.core.exception.ErrorCode;
import lombok.AllArgsConstructor;

/**
 * 设备状态异常枚举类
 *
 * @date 2024/11/14 8:44
 * @project @Title: DeviceStatusTypeEnum.java
 */
public enum DeviceStatusTypeEnum implements ErrorCode {

  /** 错误状态 */
  DEVICE_ERROR_STATUS("50020", "设备错误状态"),

  /**
   * 设备重复注册
   */
  DEVICE_REG_REPEAT("50021", "设备重复注册");

  public final String code;

  public final String desc;

  DeviceStatusTypeEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  @Override
  public String getCode() {
    return code;
  }

  @Override
  public String getDescription() {
    return desc;
  }
}
