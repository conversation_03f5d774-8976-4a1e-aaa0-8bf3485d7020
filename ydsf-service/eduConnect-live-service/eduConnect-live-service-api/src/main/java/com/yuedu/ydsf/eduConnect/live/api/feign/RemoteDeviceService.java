package com.yuedu.ydsf.eduConnect.live.api.feign;

import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsDeviceVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(contextId = "remoteDeviceService", value = ServiceNameConstants.EDU_CONNECT_LIVE_SERVICE)
public interface RemoteDeviceService {

    @NoToken
    @GetMapping("/ssDevice/deviceInfo/{deviceNo}")
    R<SsDeviceVO> getDeviceByDeviceNo(@PathVariable String deviceNo);

}
