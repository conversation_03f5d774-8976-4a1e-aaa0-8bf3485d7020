package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.eduConnect.live.api.valid.SsInteractionSettingValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 更新互动明细
 *
 * <AUTHOR>
 * @date 2024/11/04
 */
@Data
@Schema(description = "更新互动明细传输对象")
public class SsInteractionSettingDetailUpdateDTO implements Serializable {

  @NotNull(
      groups = {SsInteractionSettingValidGroup.UpdateSettingDetail.class},
      message = "互动设置不能为空")
  @Valid
  private List<SsInteractionSettingDetailDTO> detailList;
}
