package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.eduConnect.api.constant.ReleaseEnum;
import com.yuedu.ydsf.eduConnect.live.api.valid.SsInteractionSettingValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * 更新课件环节DTO
 *
 * @date 2025/1/6 10:33
 * @project @Title: SsUpdateStepDTO.java
 */
@Data
@Schema(description = "更新课件环节")
public class SsCourseStepDTO implements Serializable {

  /** 设备编号 */
  @NotBlank(
      groups = {SsInteractionSettingValidGroup.StopInteraction.class},
      message = "设备编号不能为空")
  private String deviceNo;

  /** 课件页id */
  @NotNull(
      groups = {SsInteractionSettingValidGroup.UpdateRecordStep.class},
      message = "课件页id不能为空")
  @Schema(description = "课件页id")
  private Long pageId;

  /** 终端类型 */
  @NotNull(
      groups = {SsInteractionSettingValidGroup.UpdateRecordStep.class},
      message = "终端类型不能为空")
  @Schema(description = "终端类型")
  private Integer deviceType;

  /** 课件ID */
  @NotNull(
      groups = {
        SsInteractionSettingValidGroup.UpdateRecordStep.class,
        SsInteractionSettingValidGroup.CourseAdd.class
      },
      message = "课件ID不能为空")
  @Schema(description = "课件ID")
  private Long coursewareId;

  /** 教学计划ID */
  @Schema(description = "教学计划ID")
  private Long teachingPlanDetailId;

  /** 直播间id */
  @Schema(description = "直播间id")
  private String roomUUID;

  /** 课表ID */
  @Schema(description = "课表ID")
  private Long timeTableId;

  /** 上课类型: 0-直播课; 1-点播课; */
  @NotNull(
      groups = {SsInteractionSettingValidGroup.CourseAdd.class},
      message = "上课类型不能为空")
  @Schema(description = "上课类型: 0-直播课; 1-点播课录课;")
  private Integer attendClassType;

  /** 互动内容设置 */
  @NotNull(
      groups = {SsInteractionSettingValidGroup.CourseAdd.class},
      message = "互动设置不能为空")
  @Valid
  private SsInteractionSettingDTO properties;

  /** 版本 */
  private Integer release = ReleaseEnum.V_2.code;

  /** 发送端类型 */
  private Integer sendTerminal;

  /** 获取结果的终端类型 */
  private Integer terminalResult;

  /** 录课任务ID */
  private Long recordTaskId;
}
