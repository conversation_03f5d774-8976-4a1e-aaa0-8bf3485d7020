package com.yuedu.ydsf.eduConnect.live.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 操作记录表 传输类
 *
 * <AUTHOR>
 * @date 2024-11-04 10:00:15
 */
@Data
@Schema(description = "操作记录表传输类")
public class SsOperateLogDTO implements Serializable {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	 * 操作记录名称
	 */
	@Schema(description="操作记录名称")
	private String operateTitle;

	/**
	 * 业务ID
	 */
	@Schema(description="业务ID")
	private Long objectId;

	/**
	* 类别: 1-红包;
	*/
    @Schema(description="类别: 1-红包;")
    private Integer category;

	/**
	* 类型: 1-新增; 2-修改; 3-删除;
	*/
    @Schema(description="类型: 1-新增; 2-修改; 3-删除;")
    private Integer type;

	/**
	* 日志详情
	*/
    @Schema(description="日志详情")
    private String detail;

	/**
	* 操作人ID
	*/
    @Schema(description="操作人ID")
    private Long operateId;

	/**
	* 操作人姓名
	*/
    @Schema(description="操作人姓名")
    private String operateName;

	/**
	* 操作人用户名
	*/
    @Schema(description="操作人用户名")
    private String createBy;

	/**
	* 操作时间
	*/
    @Schema(description="操作时间")
    private LocalDateTime createTime;

	/**
	* 是否删除: 0-未删除;1-已删除
	*/
    @Schema(description="是否删除: 0-未删除;1-已删除")
    private Integer delFlag;
}
