package com.yuedu.ydsf.eduConnect.live.api.query;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 互动明细表
 *
 * <AUTHOR>
 * @date 2024/11/04
 */
@Data
@Schema(description = "互动明细表查询对象")
public class SsInteractionSettingDetailQuery {

  /** 主键id */
  @Schema(description = "主键id")
  private Long id;

  /** 互动设置id */
  @Schema(description = "互动设置id")
  private Long interactionSettingId;

  /** 触发时间点(视频播放到几秒触发) */
  @Schema(description = "触发时间点(视频播放到几秒触发)")
  private Long actionTime;

  /** 互动持续时间(单位秒) */
  @Schema(description = "互动持续时间(单位秒)")
  private Integer interactionDuration;

  /** 创建人 */
  @Schema(description = "创建人")
  private String createBy;

  /** 创建时间 */
  @Schema(description = "创建时间")
  private LocalDateTime createTime;

  /** 修改人 */
  @Schema(description = "修改人")
  private String updateBy;

  /** 修改时间 */
  @Schema(description = "修改时间")
  private LocalDateTime updateTime;

  /** 是否删除:0-否;1-是 */
  @Schema(description = "是否删除:0-否;1-是")
  private Integer delFlag;

  /**
   * 触发互动时参数
   */
  @Schema(description = "触发互动时参数")
  private String interactionArgs;
}
