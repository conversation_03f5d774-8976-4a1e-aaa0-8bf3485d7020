package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.eduConnect.live.api.valid.SsInteractionSettingValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建互动Dto
 *
 * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
 * @date 2024/4/11 10:27
 * @project @Title: SsInteractionSettingAddDto.java
 */
@Data
public class SsInteractionSettingAddDTO {

  /** 设备编号 */
  @NotBlank(
      groups = {
        SsInteractionSettingValidGroup.Add.class,
        SsInteractionSettingValidGroup.StopInteraction.class
      },
      message = "设备编号不能为空")
  private String deviceNo;

  /** 房间UUID */
  private String roomUUID;

  /** 上课类型: 0-直播课; 1-点播课; */
  @NotNull(
      groups = {SsInteractionSettingValidGroup.Add.class},
      message = "上课类型不能为空")
  @Schema(description = "上课类型: 0-直播课; 1-点播课录课;")
  private Integer attendClassType;

  /** 课次id */
  @Schema(description = "课次id")
  private Long classTimeId;

  /** 二维码类型 */
  private Integer qrcodeType;

  /** 互动设置 */
  @NotNull(
      groups = {SsInteractionSettingValidGroup.Add.class},
      message = "互动设置不能为空")
  @Valid
  private SsInteractionSettingDTO properties;

  /** 声网更新房间属性entity */
  private AgoraUpdateRoomPropertiesDTO agoraProperties;

  /** 版本 */
  private Integer release;
}
