package com.yuedu.ydsf.eduConnect.live.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;

/**
 * 已发布的教学计划明细表 传输类
 *
 * <AUTHOR>
 * @date 2025-01-08 09:24:31
 */
@Data
@Schema(description = "已发布的教学计划明细表传输类")
public class TeachingPlanDetailPubDTO implements Serializable {


	/**
	* 主键id
	*/
    @Schema(description="主键id")
    private Long id;

	/**
	* 教学计划id
	*/
    @Schema(description="教学计划id")
    private Long planId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 课程id
	*/
    @Schema(description="课程id")
    private Long courseId;

	/**
	* 课程名字
	*/
    @Schema(description="课程名字")
    private String courseName;

	/**
	* 课节id
	*/
    @Schema(description="课节id")
    private Long lessonId;

	/**
	* 课节名字
	*/
    @Schema(description="课节名字")
    private String lessonName;

	/**
	* 主讲id
	*/
    @Schema(description="主讲id")
    private Long lectureId;

	/**
	* 主讲名字
	*/
    @Schema(description="主讲名字")
    private String lectureName;

	/**
	* 书籍ID
	*/
    @Schema(description="书籍ID")
    private Long bookId;

	/**
	* 书籍名称
	*/
    @Schema(description="书籍名称")
    private String bookName;

	/**
	* 上课开始日期
	*/
    @Schema(description="上课开始日期")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    private LocalTime classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    private LocalTime classEndTime;

	/**
	* 上课开始日期时间
	*/
    @Schema(description="上课开始日期时间")
    private LocalDateTime classStartDateTime;

	/**
	* 上课结束日期时间
	*/
    @Schema(description="上课结束日期时间")
    private LocalDateTime classEndDateTime;

	/**
	* 直播间ID
	*/
    @Schema(description="直播间ID")
    private Long liveRoomId;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 课程版本(直播端进入课程时存储)
	*/
    @Schema(description="课程版本(直播端进入课程时存储)")
    private Integer courseVersion;

	/**
	* 课件版本(直播端进入课程时存储)
	*/
    @Schema(description="课件版本(直播端进入课程时存储)")
    private Integer coursewareVersion;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
