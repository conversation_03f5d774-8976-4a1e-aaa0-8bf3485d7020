package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.eduConnect.live.api.valid.SsInteractionConsequenceValidGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 批量保存互动结果
 *
 * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
 * @date 2024/4/17 11:32
 * @project @Title: SsInteractionConsequenceSaveDto.java
 */
@Data
public class SsInteractionConsequenceSaveDTO {

  /** 互动结果批量 */
//  @Valid
//  @NotEmpty(
//      groups = SsInteractionConsequenceValidGroup.SaveInteractionConsequenceGroup.class,
//      message = "互动结果不能为空！")
  private List<SsInteractionConsequenceDTO> interactionConsequenceDtos;

  /** 互动类型: 0-计时器; 1-答题抢票器; 2-抢红包; */
  @NotNull(
      groups = {SsInteractionConsequenceValidGroup.SaveInteractionConsequenceGroup.class},
      message = "互动类型不能为空")
  private Byte interactionType;
}
