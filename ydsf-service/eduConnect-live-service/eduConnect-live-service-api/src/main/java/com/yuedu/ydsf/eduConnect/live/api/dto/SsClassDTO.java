package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

/**
* 班级信息表
*
* <AUTHOR>
* @date  2024/11/01
*/
@Data
@Schema(description = "班级信息表传输对象")
public class SsClassDTO implements Serializable {


    /**
     * 主键ID
     */
    @NotNull(groups = {V_E.class}, message = "主键ID不能为空")
    private Long id;



    /**
     * 班级名称
     */
    @Schema(description = "班级名称")
    @NotBlank(groups = {V_A_E.class }, message = "班级名称不能为空")
    @Length(groups = {V_A_E.class }, max =255 ,message = "班级名称长度不能大于255")
    private String className;

    /**
     * 年级(字典类型: grade)
     */
    @Schema(description = "年级(字典类型: grade) 字典类型：grade" ,type = "grade")
    @NotNull(groups = {V_A_E.class }, message = "年级(字典类型不能为空")
    private Integer grade;

    /**
     * 班级状态(字典类型: class_state)
     */
    @Schema(description = "班级状态(字典类型: class_state) 字典类型：class_state" ,type = "class_state", defaultValue = "0")
    @NotNull(groups = {V_A_E.class }, message = "班级状态(字典类型不能为空")
    private Integer classState;

    /**
     * 是否同步校管家(字典类型: is_sync_xiaogj)
     */
    @Schema(description = "是否同步校管家(字典类型: is_sync_xiaogj) 字典类型：is_sync_xiaogj" ,type = "is_sync_xiaogj", defaultValue = "1")
    @NotNull(groups = {V_A_E.class }, message = "是否同步校管家(字典类型不能为空")
    private Integer isSyncXiaogj;

    /**
     * 班级类型(字典类型: class_type)
     */
    @Schema(description = "班级类型(字典类型: class_type) 字典类型：class_type" ,type = "class_type")
    @NotNull(groups = {V_A_E.class }, message = "班级类型(字典类型不能为空")
    private Integer classType;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Length(groups = {V_A_E.class }, max =64 ,message = "创建者长度不能大于64")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @Schema(description = "编辑者")
    @Length(groups = {V_A_E.class }, max =64 ,message = "编辑者长度不能大于64")
    private String modifer;

    /**
     * 是否删除: 0-未删除;1-已删除
     */
    @Schema(description = "是否删除: 0-未删除;1-已删除 字典类型：del_flag" ,type = "del_flag", defaultValue = "0")
    private Integer delFlag;


}
