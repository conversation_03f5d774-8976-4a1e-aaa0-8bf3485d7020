package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 声网更新房间属性Dto
 *
 * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
 * @date 2024/4/15 10:43
 * @project @Title: AgoraUpdateRoomPropertiesDto.java
 */
@Data
public class AgoraUpdateRoomPropertiesDTO {

  /** 互动类型 */
  private Integer k;

  /** 红包明细 */
  private v v;

  /** 消息时间戳 */
  private Long t = System.currentTimeMillis();

  @Data
  public static class v {

    /** 本次互动ID */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long i;

    /** 每个班的红包数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer n;

    /** 每个班的红包积分总数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer c;

    /** 选项个数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer x;

    /** 倒计时时间 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer t;

    /** 互动持续时间 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer s;

    /** 互动器按键间隔生效次数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer u;

    /** 学员 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String m;

    /** 答题器sn */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String h;

    /** 互动结果页面是否关闭 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer p;

    /** 互动结果页面持续时间 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer r;

    /** 互动开始前的预备时间 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer d;

    /** 互动结果是否全国展示标识位置 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer q;

    /** 发送端类型 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer a;

    /** 当前互动所属课次id */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer y;

    /** 答题正确答案 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String b;

    /** 学生姓名 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String l;

    /** 学生id */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String e;

    /** 激励勋章类型 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String f;

    /** 课次学生id */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long w;
  }
}
