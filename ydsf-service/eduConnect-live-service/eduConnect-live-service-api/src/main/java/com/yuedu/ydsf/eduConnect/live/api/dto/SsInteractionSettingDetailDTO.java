package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.eduConnect.live.api.valid.SsInteractionSettingValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 互动明细表
 *
 * <AUTHOR>
 * @date 2024/11/04
 */
@Data
@Schema(description = "互动明细表传输对象")
public class SsInteractionSettingDetailDTO implements Serializable {

  /** 主键id */
  @NotNull(
      groups = {V_E.class, SsInteractionSettingValidGroup.UpdateSettingDetail.class},
      message = "主键id不能为空")
  private Long id;

  /** 互动设置id */
  @Schema(description = "互动设置id")
  @NotNull(
      groups = {V_A_E.class,SsInteractionSettingValidGroup.UpdateSettingDetail.class},
      message = "互动设置id不能为空")
  private Long interactionSettingId;

  /** 触发时间点(视频播放到几秒触发) */
  @Schema(description = "触发时间点(视频播放到几秒触发)")
  @NotNull(
      groups = {V_A_E.class,SsInteractionSettingValidGroup.UpdateSettingDetail.class},
      message = "触发时间点(视频播放到几秒触发)不能为空")
  private Long actionTime;

  /** 互动持续时间(单位秒) */
  @Schema(description = "互动持续时间(单位秒)")
  @NotNull(
      groups = {V_A_E.class,SsInteractionSettingValidGroup.UpdateSettingDetail.class},
      message = "互动持续时间不能为空")
  private Integer interactionDuration;

  /** 创建人 */
  @Schema(description = "创建人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "创建人长度不能大于255")
  private String createBy;

  /** 创建时间 */
  @Schema(description = "创建时间")
  @NotNull(
      groups = {V_A_E.class},
      message = "创建时间不能为空")
  private LocalDateTime createTime;

  /** 修改人 */
  @Schema(description = "修改人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "修改人长度不能大于255")
  private String updateBy;

  /** 修改时间 */
  @Schema(description = "修改时间")
  @NotNull(
      groups = {V_A_E.class},
      message = "修改时间不能为空")
  private LocalDateTime updateTime;

  /** 是否删除:0-否;1-是 */
  @Schema(description = "是否删除:0-否;1-是")
  @NotNull(
      groups = {V_A_E.class},
      message = "是否删除不能为空")
  private Integer delFlag;

  /**
   * 触发互动时参数
   */
  @Schema(description = "触发互动时参数")
  private String interactionArgs;
}
