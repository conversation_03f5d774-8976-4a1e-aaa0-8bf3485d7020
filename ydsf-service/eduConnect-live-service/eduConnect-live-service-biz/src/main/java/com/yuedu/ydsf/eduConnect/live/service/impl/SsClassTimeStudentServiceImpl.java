package com.yuedu.ydsf.eduConnect.live.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.api.feign.RemoteLecturerService;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.InteractionSendTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.InteractionTypeEnum;
import com.yuedu.ydsf.eduConnect.live.api.dto.AgoraUpdateRoomPropertiesDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsClassTimeStudentDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingAddDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingDTO;
import com.yuedu.ydsf.eduConnect.live.api.query.SsClassTimeStudentQuery;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsClassTimeStudentVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.live.entity.SsClass;
import com.yuedu.ydsf.eduConnect.live.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.live.entity.SsClassTimeStudent;
import com.yuedu.ydsf.eduConnect.live.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.live.manager.SsInteractionSettingManager;
import com.yuedu.ydsf.eduConnect.live.mapper.SsClassMapper;
import com.yuedu.ydsf.eduConnect.live.mapper.SsClassTimeMapper;
import com.yuedu.ydsf.eduConnect.live.mapper.SsClassTimeStudentMapper;
import com.yuedu.ydsf.eduConnect.live.mapper.SsDeviceMapper;
import com.yuedu.ydsf.eduConnect.live.service.SsClassTimeStudentService;
import com.yuedu.ydsf.eduConnect.live.service.SsInteractionSettingService;
import com.yuedu.ydsf.eduConnect.live.util.DeviceContextHolder;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.PublishPeerDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 校区上课学生表服务层
 *
 * <AUTHOR>
 * @date 2024/11/05
 */
@Service
@AllArgsConstructor
@Slf4j
public class SsClassTimeStudentServiceImpl extends ServiceImpl<SsClassTimeStudentMapper, SsClassTimeStudent> implements SsClassTimeStudentService {

    private final SsClassTimeStudentMapper ssClassTimeStudentMapper;

    private final AgoraService agoraService;

    private final SsInteractionSettingManager smsInteractionSettingManager;

    private final SsClassTimeMapper ssClassTimeMapper;

    private final SsDeviceMapper ssDeviceMapper;

    private final SsClassMapper ssClassMapper;

    private final RemoteLecturerService remoteLecturerService;

    private final SsInteractionSettingManager ssInteractionSettingManager;


    /**
   * 根据房间id获取学生数量
   *
   * <AUTHOR>
   * @date 2024/11/6 11:12
   * @param roomUUID
   * @return int
   */
  @Override
  public int getCountByRoomUUID(String roomUUID,Long deviceId) {
    return ssClassTimeStudentMapper.getCountByRoomUUID(roomUUID,deviceId);
  }

    /**
     * 查询绑定答题器学生列表
     * @param ssClassTimeStudentQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.live.api.vo.SsClassTimeStudentVO>
     * <AUTHOR>
     * @date 2024/11/8 9:24
     */
    @Override
    public List<SsClassTimeStudentVO> getStudentByRoomUuidAndDeviceNo(SsClassTimeStudentQuery ssClassTimeStudentQuery) {

        // 查询课次信息
        SsClassTime ssClassTime = getClassTimeInfo(ssClassTimeStudentQuery);

        // 查询校区设备
        SsDevice ssDevice = ssDeviceMapper.selectOne(Wrappers.lambdaQuery(SsDevice.class)
                .eq(SsDevice::getDeviceNo, ssClassTimeStudentQuery.getDeviceNo())
        );

        if (Objects.isNull(ssDevice)) {
            throw new BizException("未查询到教室设备信息!");
        }

        // 查询学生信息
        List<SsClassTimeStudent> ssClassTimeStudentList = ssClassTimeStudentMapper.selectList(Wrappers.lambdaQuery(SsClassTimeStudent.class)
                .eq(SsClassTimeStudent::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                .eq(SsClassTimeStudent::getClassTimeId, ssClassTime.getId())
                .eq(SsClassTimeStudent::getDeviceId, ssDevice.getId())
                .and(StringUtils.isNotBlank(ssClassTimeStudentQuery.getSearchContent()), wrapper -> wrapper.like(SsClassTimeStudent::getStudentMobile, ssClassTimeStudentQuery.getSearchContent())
                        .or()
                        .like(SsClassTimeStudent::getStudentName, ssClassTimeStudentQuery.getSearchContent())
                )
                .orderByAsc(SsClassTimeStudent::getCtime)
        );

        return ssClassTimeStudentList.stream().map(entity -> {
            SsClassTimeStudentVO ssClassTimeStudentVO = new SsClassTimeStudentVO();
            BeanUtils.copyProperties(entity, ssClassTimeStudentVO);
            return ssClassTimeStudentVO;
        }).toList();

    }

    /**
     * 查询课次信息
     * @param ssClassTimeStudentQuery
     * @return com.yuedu.ydsf.eduConnect.live.api.vo.SsClassTimeVO
     * <AUTHOR>
     * @date 2024/11/8 13:50
     */
    @Override
    public SsClassTimeVO getClassTimeByRoomUuid(SsClassTimeStudentQuery ssClassTimeStudentQuery) {

        // 查询课次信息
        SsClassTime ssClassTime = getClassTimeInfo(ssClassTimeStudentQuery);

        SsClassTimeVO ssClassTimeVO = new SsClassTimeVO();
        BeanUtils.copyProperties(ssClassTime, ssClassTimeVO);

        // 查询班级信息
        SsClass ssClass = ssClassMapper.selectById(ssClassTime.getClassId());

        // 查询设备信息
        SsDevice ssDevice = ssDeviceMapper.selectById(ssClassTime.getDeviceId());

        // 查询讲师信息
        R<LecturerVO> lecture = remoteLecturerService.getLecture(ssClassTime.getLecturerId());
        if (lecture.isOk() && Objects.nonNull(lecture.getData())) {
            ssClassTimeVO.setLecturerName(lecture.getData().getLecturerName());
        }

        ssClassTimeVO.setGrade(Objects.nonNull(ssClass) ? ssClass.getGrade() : null);
        ssClassTimeVO.setClassName(Objects.nonNull(ssClass) ? ssClass.getClassName() : null);
        ssClassTimeVO.setDeviceNo(Objects.nonNull(ssDevice) ? ssDevice.getDeviceNo() : "");

        return ssClassTimeVO;
    }

    /**
     * 查询课次信息
     * @param ssClassTimeStudentQuery
     * @return com.yuedu.ydsf.eduConnect.live.entity.SsClassTime
     * <AUTHOR>
     * @date 2024/11/27 14:09
     */
    private SsClassTime getClassTimeInfo(SsClassTimeStudentQuery ssClassTimeStudentQuery) {

        if(StringUtils.isBlank(ssClassTimeStudentQuery.getRoomUuid())
                && Objects.isNull(ssClassTimeStudentQuery.getClassTimeId())){
            throw new BizException("声网uuid或课次id不能为空!");
        }

        // 查询课次
        SsClassTime ssClassTime = ssClassTimeMapper.selectOne(Wrappers.lambdaQuery(SsClassTime.class)
                .eq(StringUtils.isNotBlank(ssClassTimeStudentQuery.getRoomUuid()), SsClassTime::getRoomUuid, ssClassTimeStudentQuery.getRoomUuid())
                .eq(Objects.nonNull(ssClassTimeStudentQuery.getClassTimeId()), SsClassTime::getId, ssClassTimeStudentQuery.getClassTimeId())
        );

        if (Objects.isNull(ssClassTime)) {
            throw new BizException("未查询到课次信息!");
        }

        return ssClassTime;
    }

    /**
     * 学生绑定/解绑答题器
     * @param ssClassTimeStudentDTO
     * @return void
     * <AUTHOR>
     * @date 2024/11/6 16:58
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void studentBindAndUnbind(SsClassTimeStudentDTO ssClassTimeStudentDTO) {

        log.info("学生绑定/解绑答题器请求参数: {}", ssClassTimeStudentDTO);

        // 校验直播中是否存在互动情况
        SsInteractionSettingAddDTO ssInteractionSettingAddDto = new SsInteractionSettingAddDTO();
        ssInteractionSettingAddDto.setAttendClassType(ssClassTimeStudentDTO.getAttendClassType());
        ssInteractionSettingAddDto.setRoomUUID(ssClassTimeStudentDTO.getRoomUuid());
        ssInteractionSettingAddDto.setDeviceNo(ssClassTimeStudentDTO.getDeviceNo());
        ssInteractionSettingAddDto.setClassTimeId(ssClassTimeStudentDTO.getClassTimeId());

        // 标识互动来源
        ssInteractionSettingAddDto.setProperties(new SsInteractionSettingDTO());
        ssInteractionSettingManager.getInteractionSourceAndKey(ssInteractionSettingAddDto);

        // 校验直播中是否存在互动情况
        smsInteractionSettingManager.checkInteractionIng(ssInteractionSettingAddDto);

        // 解绑or绑定
        ssClassTimeStudentDTO.setOperType(StringUtils.isNotBlank(ssClassTimeStudentDTO.getSn()) ?
                InteractionTypeEnum.INTERACTION_TYPE_3.code :
                InteractionTypeEnum.INTERACTION_TYPE_4.code);

        // RTM 发送点对点消息
        syncAgoraPeerMessage(ssClassTimeStudentDTO);

    }

    /**
     * RTM 发送点对点消息
     * @param ssClassTimeStudentDTO
     * @return void
     * <AUTHOR>
     * @date 2024/11/7 10:10
     */
    private void syncAgoraPeerMessage(SsClassTimeStudentDTO ssClassTimeStudentDTO) {

        // 根据互动类型转换成压缩所需要的
        InteractionSendTypeEnum typeEnum = InteractionSendTypeEnum.getByBussinessType(ssClassTimeStudentDTO.getOperType());

        PublishPeerDTO publishPeerDTO = new PublishPeerDTO();

        AgoraUpdateRoomPropertiesDTO roomPropertiesDto = new AgoraUpdateRoomPropertiesDTO();
        AgoraUpdateRoomPropertiesDTO.v v = new AgoraUpdateRoomPropertiesDTO.v();
        v.setM(String.format("%s&%s&%s",
                ssClassTimeStudentDTO.getStudentId(),
                ssClassTimeStudentDTO.getStudentMobile(),
                ssClassTimeStudentDTO.getStudentName())
        );
        v.setH(ssClassTimeStudentDTO.getSn());
        v.setY(Objects.nonNull(ssClassTimeStudentDTO.getClassTimeId()) ? ssClassTimeStudentDTO.getClassTimeId().intValue() : null);
        roomPropertiesDto.setV(v);
        roomPropertiesDto.setK((typeEnum.getSendTerminalEnum().getCode()) | typeEnum.getCode());

        publishPeerDTO.setDeviceId(DeviceContextHolder.getDeviceId().toString());
        publishPeerDTO.setMessage(JSON.toJSONString(roomPropertiesDto));

        log.info("学生绑定/解绑答题器发送点对点消息请求参数: {}", publishPeerDTO);

        agoraService.publishPeerMessage(publishPeerDTO);

    }


}
