package com.yuedu.ydsf.eduConnect.live.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.eduConnect.api.constant.AuditStatusEnum;
import com.yuedu.ydsf.eduConnect.live.aop.TerminalStatusCheck;
import com.yuedu.ydsf.eduConnect.live.api.dto.RecordingDTO;
import com.yuedu.ydsf.eduConnect.live.api.valid.RecordingValidGroup;
import com.yuedu.ydsf.eduConnect.live.api.vo.RecordingVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.RecordingVerificationVO;
import com.yuedu.ydsf.eduConnect.live.entity.Recording;
import com.yuedu.ydsf.eduConnect.live.service.RecordingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 主讲录课表 控制类
 *
 * <AUTHOR>
 * @date 2024-12-03 08:43:22
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/pcRecording" )
@Tag(description = "ea_recording" , name = "主讲录课表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@TerminalStatusCheck
public class PcRecordingController {

    private final  RecordingService recordingService;

    /**
     * 创建录制
     * @param recordingDTO
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.live.api.vo.RecordingVO>
     * <AUTHOR>
     * @date 2024/12/3 8:57
     */
    @SysLog("创建录制")
    @PostMapping("/createRecording")
    @Operation(summary = "创建录制", description = "创建录制")
    @Idempotent(expireTime = 5)
    public R<RecordingVO> createRecording(@Validated(RecordingValidGroup.CreateRecordingGroup.class) @RequestBody RecordingDTO recordingDTO){
        return R.ok(recordingService.createRecording(recordingDTO));
    }

    /**
     * 开始录制
     * @param roomUuid
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/12/3 8:55
     */
    @SysLog("开始录制")
    @PutMapping("/startRecording/{roomUuid}")
    @Operation(summary = "开始录制", description = "开始录制")
    @Idempotent(expireTime = 5)
    public R startRecording(@PathVariable("roomUuid")
                            @NotBlank(message = "roomUuid不能为空")
                            @Parameter(description = "房间唯一编号") String roomUuid) {
        return R.ok(recordingService.startRecording(roomUuid));
    }

    /**
     * 停止录制
     * @param roomUuid
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/12/3 8:56
     */
    @SysLog("停止录制")
    @PutMapping("/stopRecording/{roomUuid}")
    @Operation(summary = "停止录制", description = "停止录制")
    @Idempotent(expireTime = 5)
    public R stopRecording(@PathVariable("roomUuid")
                           @NotBlank(message = "roomUuid不能为空")
                           @Parameter(description = "房间唯一编号") String roomUuid) {
        return R.ok(recordingService.stopRecording(roomUuid));
    }

    /**
     * 重新录制
     * @param roomUuid
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/12/3 8:56
     */
    @PutMapping("/resetRecording/{roomUuid}")
    @Operation(summary = "重新录制", description = "重新录制")
    @Idempotent(expireTime = 5)
    public R resetRecording(@PathVariable("roomUuid")
                            @NotBlank(message = "roomUuid不能为空")
                            @Parameter(description = "房间唯一编号") String roomUuid) {
        return R.ok(recordingService.resetRecording(roomUuid));
    }

    /**
     * 录课视频分页列表
     *
     * @param page 分页对象
     * @return R<Page<RecordingVerificationVO>>
     */
    @Operation(summary = "录课视频分页列表" , description = "录课视频分页列表")
    @GetMapping("/page")
    public R<Page<RecordingVerificationVO>> getRecordingPage(@ParameterObject Page<Recording> page){
        return R.ok(recordingService.getRecordingPage(page));
    }

    /**
     * 回收站视频分页列表
     *
     * @param page 分页对象
     * @return R<Page<CourseVodVO>>
     */
    @Operation(summary = "回收站视频分页列表" , description = "回收站视频分页列表")
    @GetMapping("/recycle/page")
    public R<Page<RecordingVerificationVO>> getRecycleRecordingPage(@ParameterObject Page<Recording> page){
        return R.ok(recordingService.getRecycleRecordingPage(page));
    }

    /**
     * 删除视频
     *
     * @param id 主讲录课id
     * @return R<Boolean>
     */
    @Operation(summary = "删除视频" , description = "删除视频")
    @DeleteMapping("/delete/{id}")
    public R<Boolean> delete(@PathVariable("id")
                             @NotNull(message = "主讲录课id不能为空")
                             @Parameter(description = "主讲录课id")Long id){

        return R.ok(recordingService.deleteRecording(id));
    }

    /**
     * 恢复视频
     *
     * @param id 主讲录课id
     * @return R<Boolean>
     */
    @Operation(summary = "恢复视频" , description = "恢复视频")
    @PutMapping("/restore/{id}")
    public R<Boolean> restore(@PathVariable("id")
                              @NotNull(message = "主讲录课id不能为空")
                              @Parameter(description = "主讲录课id")Long id){

        return R.ok(recordingService.restoreRecording(id));
    }

    /**
     * 提交视频
     * @param id
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/12/11 17:38
     */
    @SysLog("提交视频")
    @PutMapping("/audit/{id}")
    @Operation(summary = "提交视频", description = "提交视频")
    public R auditStatus(@PathVariable("id")  @Parameter(description = "主讲录课id") @NotNull(message = "主讲录课id不能为空") Long id){

        Recording recording = new Recording();
        recording.setId(id);
        recording.setAuditStatus(AuditStatusEnum.AUDIT_STATUS_2.code);
        recordingService.submitVideo(recording);
        return R.ok();
    }

    /**
     * 获取录课详情
     *
     * @param id 主讲录课id
     * @return R<Recording>
     */
    @Operation(summary = "获取录课详情" , description = "获取录课详情")
    @GetMapping("/info/{id}")
    public R<Recording> getRecordingInfo(@PathVariable("id")
                                         @NotNull(message = "主讲录课id不能为空")
                                         @Parameter(description = "主讲录课id")Long id){
        return R.ok(recordingService.getById(id));
    }
}
