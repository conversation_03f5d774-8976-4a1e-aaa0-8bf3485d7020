package com.yuedu.ydsf.eduConnect.live.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 互动题目信息工具类 用于解析和处理互动题目相关信息
 *
 * @date 2025/6/5 15:18
 * @project @Title: InteractionQuestionUtil.java
 */
@Slf4j
public class InteractionQuestionUtil {

  /**
   * 安全地从课件详情中提取题目标题
   *
   * @param details 课件详情JSON对象
   * @return 题目标题，如果提取失败返回默认值
   */
  public static String safeExtractQuestionTitle(JSONObject details) {
    if (details == null) {
      return "题目信息缺失";
    }

    try {
      // 尝试从question对象中获取题目内容
      JSONObject questionObj = details.getJSONObject("question");
      if (questionObj != null) {
        String content = questionObj.getString("content");
        if (StringUtils.isNotBlank(content)) {
          return content;
        }
      }

      // 尝试从title字段获取
      String title = details.getString("title");
      if (StringUtils.isNotBlank(title)) {
        return title;
      }

      // 尝试从question字段获取（兼容旧格式）
      String question = details.getString("question");
      if (StringUtils.isNotBlank(question)) {
        return question;
      }

      return "题目标题未设置";

    } catch (Exception e) {
      log.warn("提取题目标题失败: {}", e.getMessage());
      return "题目标题提取失败";
    }
  }

  /**
   * 安全地构建题目内容JSON
   *
   * @param details 课件详情JSON对象
   * @param questionType 题目类型
   * @param options 选项数组
   * @param correctAnswers 正确答案
   * @return 题目内容JSON字符串
   */
  public static String safeBuildQuestionContent(
      JSONObject details, String questionType, JSONArray options, String correctAnswers) {
    JSONObject questionContentJson = new JSONObject();

    try {
      // 基本信息
      questionContentJson.put("questionType", questionType);
      questionContentJson.put("options", options);
      questionContentJson.put("correctAnswers", correctAnswers);

      // 安全地添加其他字段
      safeAddField(questionContentJson, details, "question");
      safeAddField(questionContentJson, details, "notes");
      safeAddField(questionContentJson, details, "background");

      // 添加题目标题
      String title = safeExtractQuestionTitle(details);
      questionContentJson.put("title", title);

      return questionContentJson.toJSONString();

    } catch (Exception e) {
      log.warn("构建题目内容JSON失败: {}", e.getMessage());
      // 返回最基本的信息
      JSONObject basicJson = new JSONObject();
      basicJson.put("questionType", questionType);
      basicJson.put("correctAnswers", correctAnswers);
      return basicJson.toJSONString();
    }
  }

  /** 安全地添加字段到JSON对象 */
  private static void safeAddField(JSONObject target, JSONObject source, String fieldName) {
    try {
      if (source.containsKey(fieldName)) {
        target.put(fieldName, source.get(fieldName));
      }
    } catch (Exception e) {
      log.debug("添加字段{}失败: {}", fieldName, e.getMessage());
    }
  }

  /**
   * 解析题目内容JSON，获取格式化的题目信息
   *
   * @param questionContent 题目内容JSON字符串
   * @return 格式化的题目信息
   */
  public static String parseQuestionInfo(String questionContent) {
    if (StringUtils.isBlank(questionContent)) {
      return "题目信息为空";
    }

    try {
      JSONObject questionJson = JSON.parseObject(questionContent);
      StringBuilder info = new StringBuilder();

      // 题目类型
      String questionType = questionJson.getString("questionType");
      info.append("题目类型: ").append(getQuestionTypeDesc(questionType)).append("\n");

      // 题目标题
      String title = questionJson.getString("title");
      if (StringUtils.isNotBlank(title)) {
        info.append("题目标题: ").append(title).append("\n");
      }

      // 题目描述
      String description = questionJson.getString("description");
      if (StringUtils.isNotBlank(description)) {
        info.append("题目描述: ").append(description).append("\n");
      }

      // 选项信息
      JSONArray options = questionJson.getJSONArray("options");
      if (options != null && !options.isEmpty()) {
        info.append("选项信息:\n");
        for (int i = 0; i < options.size(); i++) {
          JSONObject option = options.getJSONObject(i);
          String optionText = option.getString("option");
          String optionContent = option.getString("content");
          boolean isCorrect = option.getBooleanValue("correct");

          info.append("  ").append(optionText).append(": ");
          if (StringUtils.isNotBlank(optionContent)) {
            info.append(optionContent);
          }
          if (isCorrect) {
            info.append(" [正确答案]");
          }
          info.append("\n");
        }
      }

      // 正确答案
      String correctAnswers = questionJson.getString("correctAnswers");
      if (StringUtils.isNotBlank(correctAnswers)) {
        info.append("正确答案: ").append(correctAnswers).append("\n");
      }

      // 解释说明
      String explanation = questionJson.getString("explanation");
      if (StringUtils.isNotBlank(explanation)) {
        info.append("解释说明: ").append(explanation).append("\n");
      }

      return info.toString();

    } catch (Exception e) {
      log.error("解析题目内容JSON失败: {}", e.getMessage(), e);
      return "题目信息解析失败: " + e.getMessage();
    }
  }

  /**
   * 获取题目类型描述
   *
   * @param questionType 题目类型
   * @return 题目类型描述
   */
  private static String getQuestionTypeDesc(String questionType) {
    if (StringUtils.isBlank(questionType)) {
      return "未知";
    }

    switch (questionType) {
      case "choiceTopic":
        return "选择题";
      case "vote":
        return "投票";
      default:
        return questionType;
    }
  }

  /**
   * 从题目内容JSON中提取选项列表
   *
   * @param questionContent 题目内容JSON字符串
   * @return 选项列表
   */
  public static List<String> extractOptions(String questionContent) {
    List<String> optionList = new ArrayList<>();

    if (StringUtils.isBlank(questionContent)) {
      return optionList;
    }

    try {
      JSONObject questionJson = JSON.parseObject(questionContent);
      JSONArray options = questionJson.getJSONArray("options");

      if (options != null) {
        for (int i = 0; i < options.size(); i++) {
          JSONObject option = options.getJSONObject(i);
          String optionText = option.getString("option");
          String optionContent = option.getString("content");

          if (StringUtils.isNotBlank(optionContent)) {
            optionList.add(optionText + ": " + optionContent);
          } else {
            optionList.add(optionText);
          }
        }
      }
    } catch (Exception e) {
      log.error("提取选项列表失败: {}", e.getMessage(), e);
    }

    return optionList;
  }

  /**
   * 从题目内容JSON中提取正确答案
   *
   * @param questionContent 题目内容JSON字符串
   * @return 正确答案
   */
  public static String extractCorrectAnswers(String questionContent) {
    if (StringUtils.isBlank(questionContent)) {
      return "";
    }

    try {
      JSONObject questionJson = JSON.parseObject(questionContent);
      return questionJson.getString("correctAnswers");
    } catch (Exception e) {
      log.error("提取正确答案失败: {}", e.getMessage(), e);
      return "";
    }
  }

  /**
   * 验证题目内容JSON格式是否正确
   *
   * @param questionContent 题目内容JSON字符串
   * @return 是否格式正确
   */
  public static boolean isValidQuestionContent(String questionContent) {
    if (StringUtils.isBlank(questionContent)) {
      return false;
    }

    try {
      JSONObject questionJson = JSON.parseObject(questionContent);
      // 基本字段验证
      return questionJson.containsKey("questionType") && questionJson.containsKey("options");
    } catch (Exception e) {
      log.error("验证题目内容JSON格式失败: {}", e.getMessage(), e);
      return false;
    }
  }
}
