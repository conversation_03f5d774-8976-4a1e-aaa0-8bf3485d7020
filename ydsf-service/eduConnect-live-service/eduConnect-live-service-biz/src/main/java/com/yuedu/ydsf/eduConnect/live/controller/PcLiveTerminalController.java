package com.yuedu.ydsf.eduConnect.live.controller;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.live.aop.TerminalStatusCheck;
import com.yuedu.ydsf.eduConnect.live.api.query.TeachingPlanDetailPubQuery;
import com.yuedu.ydsf.eduConnect.live.api.valid.TeachingPlanDetailPubValidGroup;
import com.yuedu.ydsf.eduConnect.live.api.vo.TimeTypeTeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.live.service.TeachingPlanDetailPubService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 直播端接口 控制类
 * <AUTHOR>
 * @date 2024/11/30 9:00
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/pcLiveTerminal")
@Tag(description = "pcLiveTerminal" , name = "直播端接口管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@TerminalStatusCheck
public class PcLiveTerminalController {

    private final TeachingPlanDetailPubService teachingPlanDetailPubService;


    /**
     * 查询直播端-读书会列表
     * @param
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.ydsf.eduConnect.api.vo.TimeTypeTeachingPlanDetailPubVO>>
     * <AUTHOR>
     * @date 2024/11/30 9:03
     */
    @Operation(summary = "查询直播端-读书会列表" , description = "查询直播端-读书会列表" )
    @GetMapping("/getReadingPartyList" )
    public R<List<TimeTypeTeachingPlanDetailPubVO>> getReadingPartyList(@Validated(TeachingPlanDetailPubValidGroup.GetPcTeachingPlanDetailPubListGroup.class) @ParameterObject TeachingPlanDetailPubQuery teachingPlanDetailPubQuery) {

        List<TimeTypeTeachingPlanDetailPubVO> timeTypeTeachingPlanDetailPubVOList = teachingPlanDetailPubService.getPcTeachingPlanDetailPubList(teachingPlanDetailPubQuery);

        return R.ok(timeTypeTeachingPlanDetailPubVOList);
    }

}
