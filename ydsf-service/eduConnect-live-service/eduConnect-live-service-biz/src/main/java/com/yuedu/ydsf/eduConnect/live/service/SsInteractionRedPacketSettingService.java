package com.yuedu.ydsf.eduConnect.live.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.live.api.query.SsInteractionRedPacketSettingQuery;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionRedPacketSettingVO;
import com.yuedu.ydsf.eduConnect.live.entity.SsInteractionRedPacketSetting;

/**
 * 门店红包规则设置表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-02 11:22:20
 */
public interface SsInteractionRedPacketSettingService extends IService<SsInteractionRedPacketSetting> {

    /**
     * 查询校区自定义红包设置
     * @param ssInteractionRedPacketSettingQuery
     * @return com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionRedPacketSettingVO
     * <AUTHOR>
     * @date 2024/11/5 11:24
     */
    SsInteractionRedPacketSettingVO getRedPacketSettingByXgjCampusId(SsInteractionRedPacketSettingQuery ssInteractionRedPacketSettingQuery);

    /**
     * v1-查询校区自定义红包设置
     * @param ssInteractionRedPacketSettingQuery
     * @return com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionRedPacketSettingVO
     * <AUTHOR>
     * @date 2024/11/5 11:24
     */
    SsInteractionRedPacketSettingVO getRedPacketSettingByStoreId(SsInteractionRedPacketSettingQuery ssInteractionRedPacketSettingQuery);


}
