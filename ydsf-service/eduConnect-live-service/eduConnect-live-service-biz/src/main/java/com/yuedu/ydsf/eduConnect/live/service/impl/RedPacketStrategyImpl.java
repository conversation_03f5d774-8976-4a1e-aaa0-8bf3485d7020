package com.yuedu.ydsf.eduConnect.live.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.constant.DefaultRedPacketSettingEnum;
import com.yuedu.ydsf.eduConnect.api.constant.EduLiveConstant;
import com.yuedu.ydsf.eduConnect.api.constant.InteractionSourceTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.TerminalEnum;
import com.yuedu.ydsf.eduConnect.live.api.dto.*;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionConsequenceResultVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionConsequenceVO;
import com.yuedu.ydsf.eduConnect.live.entity.SsInteractionConsequence;
import com.yuedu.ydsf.eduConnect.live.entity.SsInteractionSetting;
import com.yuedu.ydsf.eduConnect.live.mapper.SsInteractionSettingMapper;
import com.yuedu.ydsf.eduConnect.live.service.InteractionStrategyService;
import com.yuedu.ydsf.eduConnect.live.service.SsClassTimeStudentService;
import com.yuedu.ydsf.eduConnect.live.service.SsInteractionConsequenceService;
import com.yuedu.ydsf.eduConnect.live.util.DeviceContextHolder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 红包互动策略
 *
 * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
 * @date 2024/11/5 11:12
 * @project @Title: RedPacketStrategyImpl.java
 */
@Service
@AllArgsConstructor
@Slf4j
public class RedPacketStrategyImpl implements InteractionStrategyService {

  private final Integer DEFAULT_DURATION = 10;

  private final SsInteractionConsequenceService consequenceService;

  private final SsClassTimeStudentService classTimeStudentService;

  private final RedisTemplate<String, Object> redisTemplate;

  private final SsInteractionSettingMapper ssInteractionSettingMapper;

  @Value("${interaction.redpacket.duration:7}")
  private int resultDuration;

  @Override
  public void checkRule(SsInteractionSettingAddDTO settingAddDto) {
  }

  @Override
  public void execute(SsInteractionSettingAddDTO settingAddDto) {
    log.info("开始执行红包数量统计, roomUUID: {}", settingAddDto.getRoomUUID());

    try {
      // 构建Redis键值
      String redisKey = buildClassRoomStudentRedisKey(settingAddDto.getProperties().getInteractionCacheKey());
      int redPackNum = 0;
      if (DeviceContextHolder.isLecturer()
          && Boolean.FALSE.equals(redisTemplate.hasKey(redisKey))) {
        // 获取直播课学生数量作为红包数量
        redPackNum =
            classTimeStudentService.getCountByRoomUUID(
                settingAddDto.getRoomUUID(),
                DeviceContextHolder.isClassroom() ? DeviceContextHolder.getDeviceId() : null);
        log.info("直播间发红包获取到学生端学生数量: {}, roomUUID: {}", redPackNum, settingAddDto.getRoomUUID());
      } else if (DeviceContextHolder.isClassroom()) {

        redPackNum = settingAddDto.getProperties().getRedPacketNum();


      }
      // 存储到Redis，设置过期时间
      redisTemplate.opsForValue().set(redisKey, redPackNum, 24, TimeUnit.HOURS);
      log.info("学生端红包数量已存储到Redis, key: {}, value: {}", redisKey, redPackNum);


    } catch (Exception e) {
      log.error("学生端存储红包数量失败, roomUUID: {}, error: ", settingAddDto.getRoomUUID(), e);
      throw new BizException("学生端红包数量统计失败");
    }
  }

  @Override
  public void handleAgoraUpdateProperties(SsInteractionSettingAddDTO settingAddDto) {
    AgoraUpdateRoomPropertiesDTO.v v = settingAddDto.getAgoraProperties().getV();
    v.setT(DEFAULT_DURATION);
    v.setQ(settingAddDto.getProperties().getIsGetNationwideResult());
    v.setD(3);
    // 红包结果页面持续3秒
    v.setR(resultDuration);
    // 若是教室发红包则取教室端自己设置的红包数量以及红包积分数
    if (DeviceContextHolder.isClassroom()) {
      v.setN(settingAddDto.getProperties().getRedPacketNum());
      v.setC(settingAddDto.getProperties().getIntegralNumber());
    }
    // 若是主讲发红包为了兼容老版本则需要走默认的规则
    if (DeviceContextHolder.isLecturer()) {
      v.setN(DefaultRedPacketSettingEnum.RED_PACKET_COUNT.code);
      v.setC(DefaultRedPacketSettingEnum.RED_PACKET_NUMBER.code);
    }
  }

  /**
   * 获取红包的互动结果
   *
   * @param interactionConsequenceDto
   * @return com.yuedushufang.ss.api.domain.vo.SsInteractionConsequenceVo
   * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
   * @date 2024/4/16 15:03
   */
  @Override
  public SsInteractionConsequenceResultVO getInteractionResult(
      SsInteractionConsequenceDTO interactionConsequenceDto) {
    log.info("开始获取红包互动结果, 参数: {}", JSON.toJSONString(interactionConsequenceDto));

    // 参数校验
    if (interactionConsequenceDto == null
        || interactionConsequenceDto.getInteractionSettingId() == null) {
      log.error("获取红包互动结果参数异常: {}", JSON.toJSONString(interactionConsequenceDto));
      throw new BizException("参数异常");
    }

    SsInteractionConsequenceResultVO consequenceVo = new SsInteractionConsequenceResultVO();
    try {
      // 获取终端类型
      Integer terminalResult = interactionConsequenceDto.getTerminalResult();

      List<SsInteractionConsequenceVO> consequences =
          consequenceService
              .list(
                  Wrappers.lambdaQuery(SsInteractionConsequence.class)
                      .eq(
                          SsInteractionConsequence::getInteractionSettingId,
                          interactionConsequenceDto.getInteractionSettingId())
                      .orderByDesc(SsInteractionConsequence::getIntegralNumber) // 按积分
                      .orderByAsc(SsInteractionConsequence::getCtime) // 同积分按时间升序
                  )
              .parallelStream()
              .map(this::convertToVO)
              .toList();
      // PC端处理逻辑：按照红包金额大小和时间顺序排序
      if (Objects.nonNull(terminalResult) && Objects.equals(terminalResult, TerminalEnum.PC.code)) {
        log.info("PC端获取红包结果, settingId: {}", interactionConsequenceDto.getInteractionSettingId());

        if (CollectionUtils.isEmpty(consequences)) {
          log.info(
              "未查询到红包领取记录, settingId: {}", interactionConsequenceDto.getInteractionSettingId());
          return consequenceVo;
        }

        consequenceVo.setNationwideResult(consequences);
        consequenceVo.setAlreadyIntegralReceived(null);

      }
      // H5端处理逻辑
      else {
        log.info("H5端获取红包结果, settingId: {}", interactionConsequenceDto.getInteractionSettingId());
        SsInteractionSetting interactionSetting = ssInteractionSettingMapper.selectById(interactionConsequenceDto.getInteractionSettingId());
        String redisKey =
            DeviceContextHolder.isLecturer()
                ? buildRedisKey(interactionConsequenceDto.getRoomUUID())
                : buildClassRoomStudentRedisKey(
                    Objects.equals(
                            interactionSetting.getInteractionSource(),
                            InteractionSourceTypeEnum.INTERACTION_SOURCE_3.code)
                        ? String.valueOf(interactionConsequenceDto.getClassTimeId())
                        : interactionConsequenceDto.getRoomUUID());
        try {
          // 从Redis获取红包总数
          Object redisValue = redisTemplate.opsForValue().get(redisKey);
          if (redisValue == null) {
            log.warn("Redis中未找到红包数量信息, key: {}", redisKey);
            // 可以选择重新计算总数，或返回默认值
            consequenceVo.setIntegralTotal(0);
          } else {
            // 安全地进行类型转换
            Integer totalCount = null;
            if (redisValue instanceof Integer) {
              totalCount = (Integer) redisValue;
            } else if (redisValue instanceof String) {
              totalCount = Integer.parseInt((String) redisValue);
            }
            if (totalCount == null) {
              log.error("Redis数据类型转换失败, value: {}, type: {}", redisValue, redisValue.getClass());
              throw new BizException("数据格式异常");
            }
            consequenceVo.setIntegralTotal(totalCount);
            log.info(
                "获取到红包总数: {}, roomUUID: {}", totalCount, interactionConsequenceDto.getRoomUUID());
          }
          consequenceVo.setAlreadyIntegralReceived(CollectionUtils.size(consequences));
        } catch (Exception e) {

          log.error(
              "H5端查询红包结果异常, settingId: {}, error: ",
              interactionConsequenceDto.getInteractionSettingId(),
              e);
          throw new BizException("查询红包结果失败");
        }
      }

      log.info(
          "获取红包互动结果成功, settingId: {}, result: {}",
          interactionConsequenceDto.getInteractionSettingId(),
          JSON.toJSONString(consequenceVo));

      return consequenceVo;

    } catch (Exception e) {
      log.error(
          "获取红包互动结果异常, settingId: {}, error: ",
          interactionConsequenceDto.getInteractionSettingId(),
          e);
      throw new BizException("获取红包结果失败");
    }
  }

  /**
   * 红包实体转换成 VO对象
   *
   * <AUTHOR>
   * @date 2024/11/5 14:57
   * @param entity
   * @return com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionConsequenceVO
   */
  private SsInteractionConsequenceVO convertToVO(SsInteractionConsequence entity) {
    if (entity == null) {
      return null;
    }
    try {
      SsInteractionConsequenceVO vo = new SsInteractionConsequenceVO();
      BeanUtils.copyProperties(entity, vo);
      return vo;
    } catch (Exception e) {
      log.error("对象转换异常, entity: {}, error: ", entity, e);
      throw new BizException("数据转换失败");
    }
  }

  /**
   * 存储红包互动结果到数据库
   *
   * @param consequenceSaveDto
   * @return void
   * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
   * @date 2024/4/24 9:23
   */
  @Override
  public void saveInteractionResult(SsInteractionConsequenceSaveDTO consequenceSaveDto) {
    log.info("开始保存红包互动结果, 参数: {}", JSON.toJSONString(consequenceSaveDto));

    if (consequenceSaveDto == null
        || CollectionUtils.isEmpty(consequenceSaveDto.getInteractionConsequenceDtos())) {
      log.warn("保存红包互动结果参数为空");
      return;
    }

    try {
      int successCount = 0;
      for (SsInteractionConsequenceDTO consequenceDto :
          consequenceSaveDto.getInteractionConsequenceDtos()) {
        try {
          log.debug("开始保存单条红包互动结果, 数据: {}", JSON.toJSONString(consequenceDto));

          SsInteractionConsequence ssInteractionConsequence = new SsInteractionConsequence();
          BeanUtils.copyProperties(consequenceDto, ssInteractionConsequence);

          boolean saved = consequenceService.save(ssInteractionConsequence);
          if (saved) {
            successCount++;
            log.debug("保存单条红包互动结果成功, id: {}", ssInteractionConsequence.getId());
          } else {
            log.error("保存单条红包互动结果失败, 数据: {}", JSON.toJSONString(consequenceDto));
          }
        } catch (Exception e) {
          log.error("保存单条红包互动结果异常, 数据: {}, 错误: ", JSON.toJSONString(consequenceDto), e);
        }
      }

      log.info(
          "保存红包互动结果完成, 总数: {}, 成功数: {}",
          consequenceSaveDto.getInteractionConsequenceDtos().size(),
          successCount);

    } catch (Exception e) {
      log.error("保存红包互动结果发生异常, 参数: {}, 错误: ", JSON.toJSONString(consequenceSaveDto), e);
      throw new BizException("保存红包互动结果失败");
    }
  }

  private String buildRedisKey(String roomUUID) {
    return EduLiveConstant.SS_CLASS_TIME_STUDENT + roomUUID;
  }

  private String buildClassRoomStudentRedisKey(String roomUUID) {
    return String.format(
        EduLiveConstant.SS_CLASS_TIME_CLASS_ROOM_STUDENT,
        roomUUID,
        DeviceContextHolder.getDeviceId());
  }
}
