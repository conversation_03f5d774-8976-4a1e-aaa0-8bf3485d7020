package com.yuedu.ydsf.eduConnect.live.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.JSONUtils;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceStatusTypeEnum;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsDeviceDTO;
import com.yuedu.ydsf.eduConnect.live.api.query.SsDeviceQuery;
import com.yuedu.ydsf.eduConnect.live.api.vo.*;
import com.yuedu.ydsf.eduConnect.live.api.dto.DeviceRtmCommandDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.DeviceUploadLogsDTO;
import com.yuedu.ydsf.eduConnect.live.api.vo.DeviceRtmCommandResultVO;
import com.yuedu.ydsf.eduConnect.live.api.constant.DeviceCommandTypeEnum;
import com.yuedu.ydsf.eduConnect.live.api.constant.DeviceRtmSendStatusEnum;
import com.yuedu.ydsf.eduConnect.live.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.live.entity.SsDeviceAudioConfig;
import com.yuedu.ydsf.eduConnect.live.entity.SsDeviceConfig;
import com.yuedu.ydsf.eduConnect.live.manager.SsDeviceManager;
import com.yuedu.ydsf.eduConnect.live.mapper.SsDeviceMapper;
import com.yuedu.ydsf.eduConnect.live.service.SsDeviceAudioConfigService;
import com.yuedu.ydsf.eduConnect.live.service.SsDeviceConfigService;
import com.yuedu.ydsf.eduConnect.live.service.SsDeviceService;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AgoraTokenGenerator;
import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.AgoraProperties;
import com.yuedu.ydsf.eduConnect.api.constant.InteractionSendTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.InteractionTypeEnum;
import com.yuedu.ydsf.eduConnect.live.api.dto.AgoraUpdateRoomPropertiesDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.PublishPeerDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 设备表服务层
 *
 * <AUTHOR>
 * @date 2024/09/26
 */
@Service
@AllArgsConstructor
@Slf4j
public class SsDeviceServiceImpl extends ServiceImpl<SsDeviceMapper, SsDevice>
    implements SsDeviceService {

  private final SsDeviceManager deviceManager;

  private final AgoraProperties agoraProperties;

  private final AgoraTokenGenerator agoraTokenGenerator;

  private final SsDeviceConfigService deviceConfigService;

  private final SsDeviceAudioConfigService audioConfigService;

  private final AgoraService agoraService;

  /**
   * 设备码注册
   *
   * @param deviceDto
   * @return com.yuedu.ydsf.eduConncet.vo.SsDeviceVO
   * <AUTHOR>
   * @date 2024/9/26 13:52
   */
  @Override
  public SsDeviceVO registerDevice(SsDeviceDTO deviceDto) {
    if (this.exists(
        Wrappers.lambdaQuery(SsDevice.class)
            .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code)
            .eq(SsDevice::getDeviceNo, deviceDto.getDeviceNo()))) {
      throw new BizException(DeviceStatusTypeEnum.DEVICE_REG_REPEAT, "注册失败，设备码重复！");
    }
    SsDevice ssDevice = new SsDevice();
    BeanUtils.copyProperties(deviceDto, ssDevice);
    save(ssDevice);
    SsDeviceVO deviceVo = new SsDeviceVO();
    BeanUtils.copyProperties(ssDevice, deviceVo);
    return deviceVo;
  }

  /**
   * 校验设备状态
   *
   * @param ssDeviceQuery
   * @return com.yuedu.ydsf.eduConncet.api.vo.SsDeviceStatusVo
   * <AUTHOR>
   * @date 2024/9/28 14:12
   */
  @Override
  public SsDeviceStatusVO getDeviceStatus(SsDeviceQuery ssDeviceQuery) {
    SsDeviceStatusVO statusVo = new SsDeviceStatusVO();

    SsDevice device =
        getOne(
            Wrappers.lambdaQuery(SsDevice.class)
                .eq(SsDevice::getDeviceNo, ssDeviceQuery.getDeviceNo())
                .last("LIMIT 1"));

    if (device == null) {
      return new SsDeviceStatusVO();
    }

    SsDeviceVO deviceVO = new SsDeviceVO();
    BeanUtils.copyProperties(device, deviceVO);
    // 添加空值检查，使用Optional或三元运算符处理可能为null的字段
    deviceVO.setDeviceActive(
        Objects.nonNull(device.getDeviceActive())
            ? String.valueOf(device.getDeviceActive())
            : null);
    deviceVO.setDeviceArrears(
        Objects.nonNull(device.getDeviceArrears())
            ? String.valueOf(device.getDeviceArrears())
            : null);
    deviceVO.setDeviceState(
        Objects.nonNull(device.getDeviceState()) ? String.valueOf(device.getDeviceState()) : null);
    deviceVO.setDeviceType(
        Objects.nonNull(device.getDeviceType()) ? String.valueOf(device.getDeviceType()) : null);
    deviceVO.setIsOnLine(
        Objects.nonNull(device.getIsOnLine()) ? String.valueOf(device.getIsOnLine()) : null);
    deviceVO.setIndateForever(
        Objects.nonNull(device.getIndateForever())
            ? String.valueOf(device.getIndateForever())
            : null);
    deviceVO.setSdkType(
        Objects.nonNull(device.getSdkType()) ? String.valueOf(device.getSdkType()) : null);

    // 获取远程数据(校区和教室信息)
    if (device.getCampusId() != null || device.getClassRoomId() != null) {
      try {
        Map<String, Map<Long, String>> remoteData =
            deviceManager.fetchRemoteData(Collections.singletonList(device));

        // 设置校区名称与校管家校区ID
        if (device.getCampusId() != null && remoteData.containsKey("campus")) {
          deviceVO.setCampusName(remoteData.get("campus").get(device.getCampusId()));
          CampusVO campusVO = deviceManager.fetchRemoteDataCampus(device.getCampusId());
          if (Objects.nonNull(campusVO)) {
            deviceVO.setXgjCampusId(campusVO.getXgjCampusId());
          }
        }

        // 设置教室名称
        if (device.getClassRoomId() != null && remoteData.containsKey("classRoom")) {
          deviceVO.setClassRoomName(remoteData.get("classRoom").get(device.getClassRoomId()));
        }
      } catch (Exception e) {
        log.error(
            "获取远程数据失败, deviceNo={}, campusId={}, classRoomId={}",
            device.getDeviceNo(),
            device.getCampusId(),
            device.getClassRoomId(),
            e);
      }
    }
    // 获取相关的参数配置
    SsDeviceConfigVO configVO = getDeviceConfig(device);
    deviceVO.setSsConfigVo(configVO);

    statusVo.setDeviceVo(deviceVO);
    statusVo.setSsConfigVo(configVO);

    deviceManager.cacheDeviceInfo(deviceVO);

    return statusVo;
  }

  /**
   * 获取设备以及音频配置信息
   *
   * @param device
   * @return com.yuedu.ydsf.eduConncet.api.vo.SsDeviceConfigVO
   * <AUTHOR>
   * @date 2024/9/28 15:36
   */
  private SsDeviceConfigVO getDeviceConfig(SsDevice device) {
    SsDeviceConfigVO configVO = new SsDeviceConfigVO();
    configVO.setAppId(agoraProperties.getAgoraAppId());

    if (device.getConfigId() != null) {
      SsDeviceConfig deviceConfig = getDeviceConfigById(device.getConfigId());
      if (deviceConfig != null) {
        configVO.setDeviceConfig(fromSsDeviceConfig(device.getDeviceType(), deviceConfig));
      }
    }

    if (device.getAudioConfigId() != null) {
      SsDeviceAudioConfig audioConfig = getAudioConfigById(device.getAudioConfigId());
      if (audioConfig != null) {
        SsDeviceAudioSettingVO audioConfigVO = new SsDeviceAudioSettingVO();
        String parameters = audioConfig.getParameters();
        List<String> jsonObjects = JSONUtils.splitJsonObjects(parameters);
        audioConfigVO.setParametersArr(jsonObjects);
        audioConfigVO.setAdjustRecordingSignalVolume(audioConfig.getAdjustRecordingSignalVolume());
        configVO.setAudioConfig(audioConfigVO);
      }
    }
    return configVO;
  }

  /**
   * 获取音频配置
   *
   * <AUTHOR>
   * @date 2024/11/4 9:15
   * @param audioConfigId
   * @return com.yuedu.ydsf.eduConnect.live.entity.SsDeviceAudioConfig
   */
  private SsDeviceAudioConfig getAudioConfigById(Long audioConfigId) {
    return audioConfigService.getById(audioConfigId);
  }

  /**
   * 获取设备配置
   *
   * <AUTHOR>
   * @date 2024/11/4 9:14
   * @param configId
   * @return com.yuedu.ydsf.eduConnect.live.entity.SsDeviceConfig
   */
  private SsDeviceConfig getDeviceConfigById(Long configId) {
    return deviceConfigService.getById(configId);
  }

  /**
   * 从SsDeviceConfig转换到设备需要的
   *
   * @param deviceType
   * @param deviceConfig
   * @return com.yuedushufang.ss.api.domain.vo.SsDeviceSettingVo
   * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
   * @date 2024/5/24 15:48
   */
  public static SsDeviceSettingVO fromSsDeviceConfig(Byte deviceType, SsDeviceConfig deviceConfig) {
    SsDeviceSettingVO deviceSettingVo = new SsDeviceSettingVO();
    if (Objects.nonNull(deviceType)) {
      if (Objects.equals(deviceType.intValue(), DeviceTypeEnum.DEVICETYPE_1.code)) {
        deviceSettingVo.setBw(deviceConfig.getTBw());
        deviceSettingVo.setBh(deviceConfig.getTBh());
        deviceSettingVo.setBb(deviceConfig.getTBb());
        deviceSettingVo.setBf(deviceConfig.getTBf());
        deviceSettingVo.setSw(deviceConfig.getTSw());
        deviceSettingVo.setSh(deviceConfig.getTSh());
        deviceSettingVo.setSb(deviceConfig.getTSb());
        deviceSettingVo.setSf(deviceConfig.getTSf());
        deviceSettingVo.setHd(deviceConfig.getTHd());
      } else if (Objects.equals(deviceType.intValue(), DeviceTypeEnum.DEVICETYPE_2.code)) {
        deviceSettingVo.setBw(deviceConfig.getSBw());
        deviceSettingVo.setBh(deviceConfig.getSBh());
        deviceSettingVo.setBb(deviceConfig.getSBb());
        deviceSettingVo.setBf(deviceConfig.getSBf());
        deviceSettingVo.setSw(deviceConfig.getSSw());
        deviceSettingVo.setSh(deviceConfig.getSSh());
        deviceSettingVo.setSb(deviceConfig.getSSb());
        deviceSettingVo.setSf(deviceConfig.getSSf());
        deviceSettingVo.setHd(deviceConfig.getSHd());
      }
    }
    deviceSettingVo.setLogEnable(deviceConfig.getLogEnable());
    deviceSettingVo.setSShowNumber(deviceConfig.getSShowNumber());
    return deviceSettingVo;
  }

  /**
   * 获取RTM Token
   *
   * <AUTHOR>
   * @date 2024/11/8 14:49
   * @param userId
   * @return com.yuedu.ydsf.eduConnect.live.api.vo.SsRTMResultVO
   */
  @Override
  public SsRTMResultVO getRtmToken(Long userId) {
    log.info("开始获取RTM Token, userId={}", userId);
    SsRTMResultVO rtmResultVO = new SsRTMResultVO();
    rtmResultVO.setRtmToken(agoraTokenGenerator.getAgoraRtmToken(String.valueOf(userId)));
    rtmResultVO.setRtmAppId(agoraProperties.getAgoraRtmAppId());
    return rtmResultVO;
  }

  /**
   * 发送RTM命令到设备
   *
   * @param commandDTO 命令参数
   * @return 发送结果
   * @date 2024/12/19
   */
  @Override
  public DeviceRtmCommandResultVO sendRtmCommand(DeviceRtmCommandDTO commandDTO) {
    log.info("开始发送RTM命令到设备 - 设备编号：{}，命令类型：{}", commandDTO.getDeviceNo(), commandDTO.getCommandType());

    DeviceRtmCommandResultVO resultVO = new DeviceRtmCommandResultVO();
    String commandId = UUID.randomUUID().toString();
    resultVO.setCommandId(commandId);
    resultVO.setDeviceNo(commandDTO.getDeviceNo());
    resultVO.setCommandType(commandDTO.getCommandType());
    resultVO.setCommandDesc(commandDTO.getCommandDesc());
    resultVO.setSendTime(LocalDateTime.now());

    try {
      // 1. 验证设备是否存在
      SsDevice device = getOne(
          Wrappers.lambdaQuery(SsDevice.class)
              .eq(SsDevice::getDeviceNo, commandDTO.getDeviceNo())
              .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code)
              .last("LIMIT 1"));

      if (Objects.isNull(device)) {
        resultVO.setSendStatus(DeviceRtmSendStatusEnum.FAILED.getCode());
        resultVO.setSendMessage("设备不存在");
        log.warn("设备不存在 - 设备编号：{}", commandDTO.getDeviceNo());
        return resultVO;
      }

      // 2. 验证命令类型
      DeviceCommandTypeEnum commandTypeEnum = DeviceCommandTypeEnum.getByCode(commandDTO.getCommandType());
      if (commandTypeEnum == null) {
        resultVO.setSendStatus(DeviceRtmSendStatusEnum.FAILED.getCode());
        resultVO.setSendMessage("不支持的命令类型");
        log.warn("不支持的命令类型 - 命令类型：{}", commandDTO.getCommandType());
        return resultVO;
      }

      // 3. 构建RTM消息
      AgoraUpdateRoomPropertiesDTO roomPropertiesDto = buildDeviceCommandMessage(commandDTO, commandId, commandTypeEnum);

      // 4. 获取设备ID（使用设备编号作为设备ID）
      String deviceId = String.valueOf(device.getId());
      resultVO.setDeviceId(String.valueOf(deviceId));

      // 5. 发送RTM点对点消息
      sendPeerMessage(deviceId, roomPropertiesDto);

      resultVO.setSendStatus(DeviceRtmSendStatusEnum.SUCCESS.getCode());
      resultVO.setSendMessage(DeviceRtmSendStatusEnum.SUCCESS.getDesc());
      log.info("RTM命令发送成功 - 设备编号：{}，命令ID：{}", commandDTO.getDeviceNo(), commandId);

    } catch (Exception e) {
      resultVO.setSendStatus(DeviceRtmSendStatusEnum.FAILED.getCode());
      resultVO.setSendMessage(DeviceRtmSendStatusEnum.FAILED.getDesc() + "：" + e.getMessage());
      log.error("RTM命令发送失败 - 设备编号：{}，错误信息：{}", commandDTO.getDeviceNo(), e.getMessage(), e);
    }

    return resultVO;
  }

  /**
   * 构建设备命令RTM消息
   *
   * @param commandDTO 命令参数
   * @param commandId 命令ID
   * @param commandTypeEnum 命令类型枚举
   * @return RTM消息
   */
  private AgoraUpdateRoomPropertiesDTO buildDeviceCommandMessage(
      DeviceRtmCommandDTO commandDTO, String commandId, DeviceCommandTypeEnum commandTypeEnum) {

    log.info("构建设备命令RTM消息 - 设备编号：{}，命令类型：{}，命令ID：{}",
        commandDTO.getDeviceNo(), commandTypeEnum.getDesc(), commandId);

    AgoraUpdateRoomPropertiesDTO roomPropertiesDto = new AgoraUpdateRoomPropertiesDTO();
    AgoraUpdateRoomPropertiesDTO.v v = new AgoraUpdateRoomPropertiesDTO.v();

    // 设置设备命令相关字段
    v.setH(commandDTO.getDeviceNo()); // 设备编号
    v.setM(commandId); // 命令ID
    v.setL(commandTypeEnum.getCommand()); // 命令标识
    v.setE(commandDTO.getCommandParams()); // 命令参数
    v.setT(commandDTO.getTimeout()); // 超时时间

    roomPropertiesDto.setV(v);

    // 设置消息类型代码
    InteractionSendTypeEnum typeEnum = InteractionSendTypeEnum.getByBussinessType(
        InteractionTypeEnum.INTERACTION_TYPE_12.code);
    int messageCode = (typeEnum.getSendTerminalEnum().getCode()) | typeEnum.getCode();
    roomPropertiesDto.setK(messageCode);

    log.info("设备命令RTM消息构建完成 - 最终消息代码：{}，命令ID：{}", messageCode, commandId);
    return roomPropertiesDto;
  }

  /**
   * 发送上传日志命令到设备
   *
   * @param uploadLogsDTO 上传日志命令参数
   * @return 发送结果
   * @date 2024/12/19
   */
  @Override
  public DeviceRtmCommandResultVO sendUploadLogsCommand(DeviceUploadLogsDTO uploadLogsDTO) {
    log.info("开始发送上传日志命令到设备 - 设备编号：{}，日志天数：{}",
        uploadLogsDTO.getDeviceNo(), uploadLogsDTO.getLogDays());

    DeviceRtmCommandResultVO resultVO = new DeviceRtmCommandResultVO();
    String commandId = UUID.randomUUID().toString();
    resultVO.setCommandId(commandId);
    resultVO.setDeviceNo(uploadLogsDTO.getDeviceNo());
    resultVO.setCommandType(DeviceCommandTypeEnum.UPLOAD_LOGS.getCode());
    resultVO.setCommandDesc(uploadLogsDTO.getCommandDesc() != null ?
        uploadLogsDTO.getCommandDesc() : "上传日志");
    resultVO.setSendTime(LocalDateTime.now());

    try {
      // 1. 验证设备是否存在
      SsDevice device = getOne(
          Wrappers.lambdaQuery(SsDevice.class)
              .eq(SsDevice::getDeviceNo, uploadLogsDTO.getDeviceNo())
              .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code)
              .last("LIMIT 1"));

      if (Objects.isNull(device)) {
        resultVO.setSendStatus(DeviceRtmSendStatusEnum.FAILED.getCode());
        resultVO.setSendMessage("设备不存在");
        log.warn("设备不存在 - 设备编号：{}", uploadLogsDTO.getDeviceNo());
        return resultVO;
      }

      // 2. 构建上传日志命令参数
      String commandParams = buildUploadLogsParams(uploadLogsDTO);

      // 3. 构建RTM消息
      AgoraUpdateRoomPropertiesDTO roomPropertiesDto = buildUploadLogsMessage(
          uploadLogsDTO, commandId, commandParams);

      // 4. 获取设备ID
      String deviceId = String.valueOf(device.getId());
      resultVO.setDeviceId(deviceId);

      // 5. 发送RTM点对点消息
      sendPeerMessage(deviceId, roomPropertiesDto);

      resultVO.setSendStatus(DeviceRtmSendStatusEnum.SUCCESS.getCode());
      resultVO.setSendMessage(DeviceRtmSendStatusEnum.SUCCESS.getDesc());
      log.info("上传日志命令发送成功 - 设备编号：{}，命令ID：{}，日志天数：{}",
          uploadLogsDTO.getDeviceNo(), commandId, uploadLogsDTO.getLogDays());

    } catch (Exception e) {
      resultVO.setSendStatus(DeviceRtmSendStatusEnum.FAILED.getCode());
      resultVO.setSendMessage(DeviceRtmSendStatusEnum.FAILED.getDesc() + "：" + e.getMessage());
      log.error("上传日志命令发送失败 - 设备编号：{}，错误信息：{}",
          uploadLogsDTO.getDeviceNo(), e.getMessage(), e);
    }

    return resultVO;
  }

  /**
   * 构建上传日志命令参数
   *
   * @param uploadLogsDTO 上传日志DTO
   * @return JSON格式的命令参数
   */
  private String buildUploadLogsParams(DeviceUploadLogsDTO uploadLogsDTO) {
    StringBuilder params = new StringBuilder();
    params.append("{");
    params.append("\"logDays\":").append(uploadLogsDTO.getLogDays());
    params.append(",\"logType\":\"").append(uploadLogsDTO.getLogType()).append("\"");

    if (uploadLogsDTO.getUploadUrl() != null && !uploadLogsDTO.getUploadUrl().trim().isEmpty()) {
      params.append(",\"uploadUrl\":\"").append(uploadLogsDTO.getUploadUrl()).append("\"");
    }

    params.append(",\"timeout\":").append(uploadLogsDTO.getTimeout());
    params.append(",\"needAck\":").append(uploadLogsDTO.getNeedAck());
    params.append("}");

    return params.toString();
  }

  /**
   * 构建上传日志RTM消息
   *
   * @param uploadLogsDTO 上传日志DTO
   * @param commandId 命令ID
   * @param commandParams 命令参数
   * @return RTM消息
   */
  private AgoraUpdateRoomPropertiesDTO buildUploadLogsMessage(
      DeviceUploadLogsDTO uploadLogsDTO, String commandId, String commandParams) {

    log.info("构建上传日志RTM消息 - 设备编号：{}，命令ID：{}，日志天数：{}",
        uploadLogsDTO.getDeviceNo(), commandId, uploadLogsDTO.getLogDays());

    AgoraUpdateRoomPropertiesDTO roomPropertiesDto = new AgoraUpdateRoomPropertiesDTO();
    AgoraUpdateRoomPropertiesDTO.v v = new AgoraUpdateRoomPropertiesDTO.v();

    // 设置上传日志命令相关字段
    v.setH(uploadLogsDTO.getDeviceNo()); // 设备编号
    v.setM(commandId); // 命令ID
    v.setL(DeviceCommandTypeEnum.UPLOAD_LOGS.getCommand()); // 命令标识：uploadLogs
    v.setE(commandParams); // 命令参数（包含日志天数等信息）
    v.setT(uploadLogsDTO.getTimeout()); // 超时时间

    roomPropertiesDto.setV(v);

    // 设置消息类型代码
    InteractionSendTypeEnum typeEnum = InteractionSendTypeEnum.getByBussinessType(
        InteractionTypeEnum.INTERACTION_TYPE_12.code);
    int messageCode = (typeEnum.getSendTerminalEnum().getCode()) | typeEnum.getCode();
    roomPropertiesDto.setK(messageCode);

    log.info("上传日志RTM消息构建完成 - 最终消息代码：{}，命令ID：{}，日志天数：{}",
        messageCode, commandId, uploadLogsDTO.getLogDays());
    return roomPropertiesDto;
  }

  /**
   * 发送RTM点对点消息
   *
   * @param deviceId 设备ID
   * @param roomPropertiesDto RTM消息内容
   */
  private void sendPeerMessage(String deviceId, AgoraUpdateRoomPropertiesDTO roomPropertiesDto) {
    log.info("准备发送RTM点对点消息 - 设备ID：{}", deviceId);

    PublishPeerDTO publishPeerDTO = new PublishPeerDTO();
    publishPeerDTO.setDeviceId(deviceId);
    publishPeerDTO.setMessage(JSON.toJSONString(roomPropertiesDto));

    log.info("RTM点对点消息请求参数 - 设备ID：{}，消息内容：{}", deviceId, JSON.toJSONString(publishPeerDTO));

    try {
      agoraService.publishPeerMessage(publishPeerDTO);
      log.info("RTM点对点消息发送成功 - 设备ID：{}", deviceId);
    } catch (Exception e) {
      log.error("RTM点对点消息发送失败 - 设备ID：{}，错误信息：{}", deviceId, e.getMessage(), e);
      throw e;
    }
  }
}
