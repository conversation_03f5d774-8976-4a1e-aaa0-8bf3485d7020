package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
* 调课记录表
*
* <AUTHOR>
* @date  2025/02/12
*/
@Data
@Schema(description = "调课记录表展示对象")
public class TimetableChangeVO
    {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 学员ID
     */
    @Schema(description = "学员ID")
    private Long studentId;

    /**
     * 原课程编号
     */
    @Schema(description = "原课程编号")
    private Long sourceLessonNo;

    /**
     * 原课程ID
     */
    @Schema(description = "原课程ID")
    private Long sourceCourseId;

    /**
     * 原课程第几节课
     */
    @Schema(description = "原课程第几节课")
    private Integer sourceLessonOrder;

    /**
     * 原门店ID
     */
    @Schema(description = "原门店ID")
    private Long sourceStoreId;

    /**
     * 目标课程编号
     */
    @Schema(description = "目标课程编号")
    private Long targetLessonNo;

    /**
     * 目标课程ID
     */
    @Schema(description = "目标课程ID")
    private Long targetCourseId;

    /**
     * 目标课程第几节课
     */
    @Schema(description = "目标课程第几节课")
    private Integer targetLessonOrder;

    /**
     * 目标门店ID
     */
    @Schema(description = "目标门店ID")
    private Long targetStoreId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是
     */
    @Schema(description = "是否删除: 0-否; 1-是")
    private Byte delFlag;

}
