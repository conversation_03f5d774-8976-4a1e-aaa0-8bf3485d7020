package com.yuedu.ydsf.eduConnect.jw.api.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 门店已约直播课 视图类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:20:15
 */
@Data
@Schema(description = "门店已约直播课视图类")
public class CourseLiveVO {

	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    private Long courseId;

    /**
     * 课程名称
     */
    @Schema(description = "课程名称")
    private String courseName;

    /**
     * 上课时段ID
     */
    @Schema(description = "上课时段ID")
    private Long timeSlotId;

	/**
	* 教学计划ID
	*/
    @Schema(description="教学计划ID")
    private Long teachingPlanId;

    /**
     * 教学计划名称
     */
    @Schema(description = "教学计划名称")
    private String teachingPlanName;


    /**
     * 上课时间
     */
    @Schema(description = "上课时间")
    private String classTime;


    /**
     * 上课周期
     */
    @Schema(description = "上课周期")
    private String liveRoomPlanDuration;

    /**
     * 直播间计划id
     */
    @Schema(description = "直播间计划id")
    private Long liveRoomPlanId;

    /**
     * 直播间计划名称
     */
    @Schema(description = "直播间计划名称")
    private String liveRoomPlanName;


	/**
	* 阶段
	*/
    @Schema(description="阶段")
    private Integer stage;

    /**
     * 阶段
     */
    @Schema(description = "阶段名称")
    private String stageName;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

    /**
     * 班级ID
     */
    @Schema(description = "班级名称")
    private String className;

	/**
	* 教室ID
	*/
    @Schema(description="教室ID")
    private Long classroomId;

    /**
     * 教室ID
     */
    @Schema(description = "教室名称")
    private String classroomName;

	/**
	* 指导老师ID
	*/
    @Schema(description="指导老师ID")
    private Long teacherId;

    /**
     * 指导老师名称
     */
    @Schema(description = "指导老师名称")
    private String teacherName;


    /**
     * 主讲老师ID
     */
    @Schema(description = "指导老师ID")
    private Long lectureId;


    /**
     * 主讲老师
     */
    @Schema(description = "主讲老师")
    private String lectureName;


    /**
     * 门店名称
     */
    @Schema(description="门店名称")
    private String storeName;

    /**
     * 是否结束
     */
    @Schema(description="是否结束: 0-未结束; 1-已结束")
    private Integer close;


    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;


}

