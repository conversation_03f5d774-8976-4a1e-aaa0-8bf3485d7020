package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 互动答题器
 *
 * <AUTHOR>
 * @date 2025/04/09
 */
@Data
@Schema(description = "互动答题器展示对象")
public class BInteractionReceiverClickerVO {

  /** 主键ID */
  @Schema(description = "主键ID")
  private Long id;

  /** 接收器sn码 */
  @Schema(description = "接收器sn码")
  private String receiverSnNumber;

  /** 自增序号 */
  @Schema(description = "自增序号")
  private Integer serialNumber;

  /** 答题器SN码 */
  @Schema(description = "答题器SN码")
  private String snNumber;

  /** 答题器状态: 0-启用; 1-禁用; */
  @Schema(description = "答题器状态: 0-启用; 1-禁用;")
  private Integer clickerState;

  /** 创建人 */
  @Schema(description = "创建人")
  private String createBy;

  /** 创建时间 */
  @Schema(description = "创建时间")
  private LocalDateTime createTime;

  /** 修改人 */
  @Schema(description = "修改人")
  private String updateBy;

  /** 修改时间 */
  @Schema(description = "修改时间")
  private LocalDateTime updateTime;

  /** 是否删除: 0-否; 1-是; */
  @Schema(description = "是否删除: 0-否; 1-是;")
  private Integer delFlag;
}
