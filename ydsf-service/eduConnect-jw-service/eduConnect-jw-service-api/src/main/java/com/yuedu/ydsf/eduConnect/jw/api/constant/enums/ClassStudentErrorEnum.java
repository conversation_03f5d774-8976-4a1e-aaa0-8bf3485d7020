package com.yuedu.ydsf.eduConnect.jw.api.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考勤相关错误类
 *
 * @date 2025/2/27 17:03
 * @project @Title: ClassStudentErrorEnum.java
 */
@Getter
@AllArgsConstructor
public enum ClassStudentErrorEnum {

    /**
     * 剩余课时为0
     */
    REMAINING_LESSONS_ERROR(20000, "剩余课时为0"),

    /**
     * 没有答题器
     */
    CLICKER_NO_ERROR(20005, "没有答题器"),
    /**
     * 答题器数量不足
     */
    CLICKER_NOT_ENOUGH_ERROR(20010, "答题器数量不足"),

    /**
     * 绑定答题器内部错误
     */
    DISTRIBUTE_CLICKER_ERROR(20015, "绑定答题器内部错误"),
    /**
     * 过了可取消考勤的时间
     */
    CANCEL_CHECK_IN_ERROR(20020, "过了可取消考勤的时间"),
    /**
     * 退费学员不允许取消考勤
     */
    REFUND_STUDENT_CANCEL_CHECK_IN_ERROR(20021, "学员已退费，无法取消考勤"),
    /**
     * 未签到，无需取消考勤
     */
    NO_CHECK_IN_CANCEL_CHECK_IN_ERROR(20022, "未签到，无需取消考勤"),

    /**
     * 退费学员不允许补签考勤
     */
    REFUND_STUDENT_CHECKIN_REISSUE_ERROR(20023, "学员已退费，无法补签"),

    /**
     * 考勤结算周期已锁定，不允许补签考勤和取消考勤
     */
    CHECKIN_SETTLEMENT_CYCLE_LOCKED_ERROR(20024, "考勤周期已锁定，无法操作"),

    /**
     * 调课学生不支持补签考勤
     */
    TRANSFERRED_STUDENT_REISSUE_ERROR(20025, "学生已调课，不支持在原课程补签考勤"),

    /**
     * 学生未报名当前课程类型，需要选择其他课程类型扣减
     */
    UNENROLLED_COURSE_SELECT_OTHER_ERROR(20030, "学员[%s %s]未报名%s课程。是否从其他课程类型扣减？"),

    /**
     * 学生未报名当前课程类型且无其他可用课程类型
     */
    UNENROLLED_COURSE_NO_OTHER_ERROR(20031, "学员[%s %s]未报名%s课程且无其他可用课程类型，无法签到"),

    /**
     * 学生当前课程类型剩余课次为0，需要选择其他课程类型扣减
     */
    ZERO_SESSIONS_SELECT_OTHER_ERROR(20032, "学员[%s %s]%s课程剩余课次为0。是否从其他课程类型扣减？"),

    /**
     * 学生剩余课次为0，需要确认强制签到
     */
    ZERO_SESSIONS_FORCE_CHECKIN_ERROR(20033, "学员[%s %s]剩余课次为0，是否继续签到？"),

    /**
     * 学生未报名任何课程类型
     */
    NO_COURSE_ENROLLMENT_ERROR(20034, "学员[%s %s]未报名任何课程类型，无法签到"),

    /**
     * 学生存在引流课，需要选择扣减课程类型
     */
    TRIAL_COURSE_SELECT_TYPE_ERROR(20035, "学员[%s %s]存在引流课，请选择扣减课程类型"),

    /**
     * 试听学员试听课时为0无法进行考勤
     */
    TRIAL_STUDENT_ZERO_HOURS_ERROR(20036, "该学员试听课时为0无法进行考勤");

    /**
     * 类型
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;
}
