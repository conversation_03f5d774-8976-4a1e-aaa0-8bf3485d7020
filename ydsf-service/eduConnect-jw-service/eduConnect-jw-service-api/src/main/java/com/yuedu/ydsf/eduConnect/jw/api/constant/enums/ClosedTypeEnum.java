package com.yuedu.ydsf.eduConnect.jw.api.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/02/25
 **/
@Getter
@AllArgsConstructor
public enum ClosedTypeEnum {

    CLOSED_TYPE_1(1, "已结束"),
    CLOSED_TYPE_0(0, "未结束");

    /**
     * 类型
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;
}
