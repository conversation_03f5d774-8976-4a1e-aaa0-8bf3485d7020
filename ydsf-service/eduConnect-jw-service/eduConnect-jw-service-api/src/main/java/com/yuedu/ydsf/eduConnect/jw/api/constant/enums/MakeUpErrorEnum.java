package com.yuedu.ydsf.eduConnect.jw.api.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 补课相关枚举类
 *
 * @date 2025/4/28 14:54
 * @project @Title: MakeUpErrorEnum.java
 */
@Getter
@AllArgsConstructor
public enum MakeUpErrorEnum {

  /** 没有补课视频 */
  RESOURCES_NOT_HAS_ERROR(50552, "该课节暂无补课视频，无法进行补课操作&请联系总部教务老师进行处理");


  /** 类型 */
  private final Integer code;

  /** 描述 */
  private final String desc;
}
