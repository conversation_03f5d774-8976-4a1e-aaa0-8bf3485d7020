package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 门店运营数据-出勤数据 视图类
 * <AUTHOR>
 * @date 2025/3/10 14:10
 */
@Data
public class StoreAttendanceDataStatisticsVO {

    /**
     * 应出勤学员
     */
    @Schema(description="应出勤学员")
    private Integer incomingStudentCount = 0;

    /**
     * 出勤学员
     */
    @Schema(description="出勤学员")
    private Integer actualStudentCount = 0;

    /**
     * 学员出勤率
     */
    @Schema(description="学员出勤率")
    private Double attendanceRate = 0.0;


}
