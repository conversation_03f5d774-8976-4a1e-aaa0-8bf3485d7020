package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
* 调课记录表
*
* <AUTHOR>
* @date  2025/02/12
*/
@Data
@Schema(description = "调课记录表传输对象")
public class TimetableChangeDTO implements Serializable {

    /**
     * 主键ID
     */
    @NotNull(groups = {V_E.class}, message = "主键ID不能为空")
    private Long id;



    /**
     * 学员ID
     */
    @Schema(description = "学员ID")
    @NotNull(groups = {V_A_E.class }, message = "学员ID不能为空")
    private Long studentId;

    /**
     * 原课程编号
     */
    @Schema(description = "原课程编号")
    @NotNull(groups = {V_A_E.class }, message = "原课程编号不能为空")
    private Long sourceLessonNo;

    /**
     * 原课程ID
     */
    @Schema(description = "原课程ID")
    private Long sourceCourseId;

    /**
     * 原课程第几节课
     */
    @Schema(description = "原课程第几节课")
    private Integer sourceLessonOrder;

    /**
     * 原门店ID
     */
    @Schema(description = "原门店ID")
    @NotNull(groups = {V_A_E.class }, message = "原门店ID不能为空")
    private Long sourceStoreId;

    /**
     * 目标课程编号
     */
    @Schema(description = "目标课程编号")
    @NotNull(groups = {V_A_E.class }, message = "目标课程编号不能为空")
    private Long targetLessonNo;

    /**
     * 目标课程ID
     */
    @Schema(description = "目标课程ID")
    private Long targetCourseId;

    /**
     * 目标课程第几节课
     */
    @Schema(description = "目标课程第几节课")
    private Integer targetLessonOrder;

    /**
     * 目标门店ID
     */
    @Schema(description = "目标门店ID")
    @NotNull(groups = {V_A_E.class }, message = "目标门店ID不能为空")
    private Long targetStoreId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "创建人长度不能大于255")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(groups = {V_A_E.class }, message = "创建时间不能为空")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "修改人长度不能大于255")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @NotNull(groups = {V_A_E.class }, message = "修改时间不能为空")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是
     */
    @Schema(description = "是否删除: 0-否; 1-是")
    @NotNull(groups = {V_A_E.class }, message = "是否删除不能为空")
    private Byte delFlag;


}
