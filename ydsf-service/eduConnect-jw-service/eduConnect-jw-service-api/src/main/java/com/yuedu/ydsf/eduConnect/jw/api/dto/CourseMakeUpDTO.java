package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.eduConnect.jw.api.valid.CourseMakeUpValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;

/**
 * 门店补课表 传输类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:23:17
 */
@Data
@Schema(description = "门店补课表传输类")
public class CourseMakeUpDTO implements Serializable {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    @NotNull(groups = {CourseMakeUpValidGroup.UpdateCourseMakeUpGroup.class}, message = "补课ID不能为空")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    @NotNull(groups = {CourseMakeUpValidGroup.AddCourseMakeUpGroup.class}, message = "门店ID不能为空")
    private Long storeId;

	/**
	* 已约直播课/点播课课表ID
	*/
    @Schema(description="已约直播课/点播课课表ID")
    @NotNull(groups = {CourseMakeUpValidGroup.AddCourseMakeUpGroup.class}, message = "课表ID不能为空")
    private Long timetableId;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    private Long courseId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    @NotNull(groups = {CourseMakeUpValidGroup.AddCourseMakeUpGroup.class,
        CourseMakeUpValidGroup.UpdateCourseMakeUpGroup.class}, message = "上课时段ID不能为空")
    private Long timeSlotId;

    /**
     * 时段类型: 1-上午; 2-下午; 3-晚上;
     */
    @Schema(description="时段类型: 1-上午; 2-下午; 3-晚上;")
    @NotNull(groups = {CourseMakeUpValidGroup.AddCourseMakeUpGroup.class,
        CourseMakeUpValidGroup.UpdateCourseMakeUpGroup.class}, message = "上课时段类型不能为空")
    private Integer timeSlotType;

	/**
	* 主讲老师ID
	*/
    @Schema(description="主讲老师ID")
    private Long lectureId;

	/**
	* 上课教室ID
	*/
    @Schema(description="上课教室ID")
    @NotNull(groups = {CourseMakeUpValidGroup.AddCourseMakeUpGroup.class,
        CourseMakeUpValidGroup.UpdateCourseMakeUpGroup.class}, message = "上课教室ID不能为空")
    private Long classRoomId;

    /**
     * 班级ID
     */
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 上课日期
	*/
    @Schema(description="上课日期")
    @NotNull(groups = {CourseMakeUpValidGroup.AddCourseMakeUpGroup.class,
        CourseMakeUpValidGroup.UpdateCourseMakeUpGroup.class}, message = "上课日期不能为空")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    @NotNull(groups = {CourseMakeUpValidGroup.AddCourseMakeUpGroup.class,
        CourseMakeUpValidGroup.UpdateCourseMakeUpGroup.class}, message = "上课开始时间不能为空")
    private LocalTime classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    @NotNull(groups = {CourseMakeUpValidGroup.AddCourseMakeUpGroup.class,
        CourseMakeUpValidGroup.UpdateCourseMakeUpGroup.class}, message = "上课结束时间不能为空")
    private LocalTime classEndTime;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;
}
