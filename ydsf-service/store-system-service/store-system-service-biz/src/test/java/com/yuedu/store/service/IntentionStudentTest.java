package com.yuedu.store.service;

import com.yuedu.store.dto.IntentionStudentDTO;
import com.yuedu.ydsf.common.core.exception.BizException;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 意向会员功能测试类
 *
 * <AUTHOR>
 * @date 2025/06/25
 */
@SpringBootTest
@ActiveProfiles("test")
public class IntentionStudentTest {

    /**
     * 测试IntentionStudentDTO的基本验证
     */
    @Test
    public void testIntentionStudentDTOValidation() {
        // 创建一个有效的意向会员DTO
        IntentionStudentDTO dto = new IntentionStudentDTO();
        dto.setName("张三");
        dto.setSex(1); // 男
        dto.setGrade(3); // 三年级
        dto.setPhone("13800138000");
        dto.setStageId(1);
        dto.setFulltimeSchool("北京小学");
        dto.setResponsiblePerson(1001L);
        dto.setReferralStudentId(2001L);
        dto.setDescribe("这是一个测试意向会员");

        // 验证基本字段设置
        assertEquals("张三", dto.getName());
        assertEquals(Integer.valueOf(1), dto.getSex());
        assertEquals(Integer.valueOf(3), dto.getGrade());
        assertEquals("13800138000", dto.getPhone());
        assertEquals(Integer.valueOf(1), dto.getStageId());
        assertEquals("北京小学", dto.getFulltimeSchool());
        assertEquals(Long.valueOf(1001L), dto.getResponsiblePerson());
        assertEquals(Long.valueOf(2001L), dto.getReferralStudentId());
        assertEquals("这是一个测试意向会员", dto.getDescribe());
    }

    /**
     * 测试必填字段验证
     */
    @Test
    public void testRequiredFieldsValidation() {
        IntentionStudentDTO dto = new IntentionStudentDTO();
        
        // 测试姓名为空的情况
        dto.setName("");
        dto.setSex(1);
        dto.setGrade(3);
        dto.setPhone("13800138000");
        
        // 在实际的验证框架中，这里应该抛出验证异常
        // 这里只是演示测试结构
        assertTrue(dto.getName().isEmpty());
        
        // 测试手机号为空的情况
        dto.setName("张三");
        dto.setPhone("");
        assertTrue(dto.getPhone().isEmpty());
    }

    /**
     * 测试学员重复性校验逻辑
     */
    @Test
    public void testStudentDuplicationCheck() {
        // 这里可以模拟重复学员的情况
        // 在实际测试中，需要mock数据库查询结果
        
        IntentionStudentDTO dto1 = new IntentionStudentDTO();
        dto1.setName("李四");
        dto1.setSex(0); // 女
        dto1.setGrade(2);
        dto1.setPhone("13900139000");
        
        IntentionStudentDTO dto2 = new IntentionStudentDTO();
        dto2.setName("李四");
        dto2.setSex(0);
        dto2.setGrade(3); // 不同年级
        dto2.setPhone("13900139000"); // 相同手机号
        
        // 验证姓名和手机号的组合
        assertEquals(dto1.getName(), dto2.getName());
        assertEquals(dto1.getPhone(), dto2.getPhone());
        
        // 在实际的服务测试中，这里应该抛出BizException
        // assertThrows(BizException.class, () -> studentService.saveIntentionStudent(dto2));
    }

    /**
     * 测试非必填字段的处理
     */
    @Test
    public void testOptionalFields() {
        IntentionStudentDTO dto = new IntentionStudentDTO();
        dto.setName("王五");
        dto.setSex(1);
        dto.setGrade(4);
        dto.setPhone("13700137000");
        
        // 非必填字段可以为空
        assertNull(dto.getStageId());
        assertNull(dto.getFulltimeSchool());
        assertNull(dto.getResponsiblePerson());
        assertNull(dto.getReferralStudentId());
        assertNull(dto.getDescribe());
        
        // 设置非必填字段
        dto.setStageId(2);
        dto.setFulltimeSchool("上海小学");
        dto.setResponsiblePerson(1002L);
        dto.setReferralStudentId(2002L);
        dto.setDescribe("转介绍学员");
        
        assertNotNull(dto.getStageId());
        assertNotNull(dto.getFulltimeSchool());
        assertNotNull(dto.getResponsiblePerson());
        assertNotNull(dto.getReferralStudentId());
        assertNotNull(dto.getDescribe());
    }
}
