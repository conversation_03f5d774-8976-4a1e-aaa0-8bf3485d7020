package com.yuedu.store.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.yuedu.store.api.feign.RemoteLecturerService;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.common.core.util.R;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.cloud.openfeign.EnableFeignClients;


@EnableFeignClients("com.yuedu.store.api.feign")
class RemoteLecturerServiceTest {

    @Mock
    private RemoteLecturerService remoteLecturerService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    void getLecture() {
        //组装对象
        LecturerVO lecturerVO = new LecturerVO();
        lecturerVO.setId(2L);
        lecturerVO.setLecturerState(0);
        lecturerVO.setLecturerName("李金鑫【丸子老师】");
        lecturerVO.setXgjLecturerId("7603E121-D673-4C88-92C9-41A0A4C3B3E0");


        Mockito.when(remoteLecturerService.getLecture(2L)).thenReturn(R.ok(lecturerVO));


        //是否为同一个实例
        Assertions.assertInstanceOf(R.ok(lecturerVO).getClass(), remoteLecturerService.getLecture(2L));
    }

    @Test
    void getPageList() {
        LecturerDTO lecturerDTO = new LecturerDTO();
        lecturerDTO.setPage(new PageDTO<>(1, 5));
        lecturerDTO.setLecturerName("老师");
        remoteLecturerService.getPageList(lecturerDTO);
    }

    @Test
    void listLecturersByName() {
        String lecturerName = "";
        remoteLecturerService.listLecturersByName(lecturerName);
    }
}