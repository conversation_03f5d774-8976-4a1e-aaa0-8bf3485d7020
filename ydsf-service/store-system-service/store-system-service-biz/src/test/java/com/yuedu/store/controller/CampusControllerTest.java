package com.yuedu.store.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.http.param.MediaType;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.service.CampusService;
import com.yuedu.store.vo.CampusVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @ClassName CampusControllerTest
 * @Description 测试校区接口
 * <AUTHOR>
 * @Date 2024/10/9 16:05
 * @Version v0.0.1
 */

@SpringBootTest
public class CampusControllerTest {

    private MockMvc mockMvc;

    @Mock
    private CampusService campusService;

    @InjectMocks
    private CampusController campusController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        this.mockMvc = MockMvcBuilders.standaloneSetup(campusController).build();
    }

    @Test
    public void list_ShouldReturnListOfSchools() throws Exception {
        List<CampusVO> selectVOList = new ArrayList<>();
        CampusVO selectVo = new CampusVO();
        String name = "Shan Dong";
        selectVo.setId(1L);
        selectVo.setXgjCampusId("CAMPUS001");
        selectVo.setCampusName("School Name");
        selectVo.setCampusNo("SCHOOL123");
        selectVo.setCampusType(2);
        selectVo.setCampusState(0);
        selectVOList.add(selectVo);

        when(campusService.listCampus(name)).thenReturn(selectVOList);

        mockMvc.perform(get("/campus/list")
                        .param("query", name)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"data\":[{\"id\":1,\"xgjCampusId\":\"CAMPUS001\",\"regionName\":\"Shan Dong\",\"campusName\":\"School Name\",\"campusNo\":\"SCHOOL123\",\"campusState\":0,\"campusType\":2}],\"msg\":null}"));
    }

    @Test
    public void campusList_ShouldReturnListOfSchools() throws Exception {
        List<CampusVO> selectVOList = new ArrayList<>();
        List<Long> schoolIdList = new ArrayList<>();
        schoolIdList.add(1L);
        CampusVO selectVo = new CampusVO();
        selectVo.setId(1L);
        selectVo.setXgjCampusId("CAMPUS001");
        selectVo.setCampusName("School Name");
        selectVo.setCampusNo("SCHOOL123");
        selectVo.setCampusType(2);
        selectVo.setCampusState(0);
        selectVOList.add(selectVo);

        CampusDTO campusDTO = new CampusDTO();
        campusDTO.setSchoolIdList(schoolIdList);
        campusDTO.setCampusName("School Name");

        String content = JSONUtil.toJsonStr(campusDTO);

        when(campusService.selectBySchoolId(campusDTO.getSchoolIdList(), campusDTO.getCampusName())).thenReturn(selectVOList);

        mockMvc.perform(post("/campus/campusList")
                        .content(content)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().json("{\"code\":0,\"data\":[{\"id\":1,\"xgjCampusId\":\"CAMPUS001\",\"regionName\":\"Shan Dong\",\"campusName\":\"School Name\",\"campusNo\":\"SCHOOL123\",\"campusState\":0,\"campusType\":2}],\"msg\":null}"));
    }

    @Test
    public void campusList_ShouldReturnEmptyList() throws Exception {
        List<CampusVO> selectVOList = new ArrayList<>();
        List<Long> schoolIdList = new ArrayList<>();
        String campusName = "";

        when(campusService.selectBySchoolId(schoolIdList, campusName)).thenReturn(selectVOList);

        String requestBody = "{\"schoolIdList\":[1]}";

        mockMvc.perform(post("/campus/campusList", schoolIdList)
                        .content(requestBody)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"data\":[],\"msg\":null}"));
    }

    @Test
    public void info_ShouldReturnSchoolInfo() throws Exception {
        Long schoolId = 1L;
        CampusVO selectVo = new CampusVO();
        selectVo.setId(1L);
        selectVo.setXgjCampusId("CAMPUS001");
        selectVo.setCampusName("School Name");
        selectVo.setCampusNo("SCHOOL123");
        selectVo.setCampusType(2);
        selectVo.setCampusState(0);

        when(campusService.selectOneBySchoolId(schoolId)).thenReturn(selectVo);

        mockMvc.perform(get("/campus/info/{schoolId}", schoolId))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"data\":{\"id\":1,\"xgjCampusId\":\"CAMPUS001\",\"regionName\":\"Shan Dong\",\"campusName\":\"School Name\",\"campusNo\":\"SCHOOL123\",\"campusState\":0,\"campusType\":2},\"msg\":null}"));
    }

    @Test
    public void info_ShouldReturnEmptyList() throws Exception {
        Long schoolId = 0L;
        CampusVO selectVo = new CampusVO();

        when(campusService.selectOneBySchoolId(schoolId)).thenReturn(selectVo);

        mockMvc.perform(get("/campus/info/{schoolId}", schoolId))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"data\":{},\"msg\":null}"));
    }
}