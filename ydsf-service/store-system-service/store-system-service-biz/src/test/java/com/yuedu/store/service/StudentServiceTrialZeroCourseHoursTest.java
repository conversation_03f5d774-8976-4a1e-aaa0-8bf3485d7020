package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.entity.Student;
import com.yuedu.store.query.StudentQuery;
import com.yuedu.store.vo.StudentVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * 试听且剩余课次为0的学员查询测试
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@SpringBootTest
@ActiveProfiles("test")
public class StudentServiceTrialZeroCourseHoursTest {

    @Resource
    private StudentService studentService;

    @Test
    public void testGetTrialStudentsWithZeroCourseHours() {
        // 准备测试数据
        Page<Student> page = new Page<>(1, 10);
        StudentQuery studentQuery = new StudentQuery();
        studentQuery.setStoreId(1L); // 设置测试门店ID

        // 调用新接口
        Page<StudentVO> result = studentService.getTrialStudentsWithZeroCourseHours(page, studentQuery);

        // 验证结果
        System.out.println("查询到的试听且剩余课次为0的学员数量: " + result.getTotal());

        // 验证每个学员都是试听状态且剩余课次为0
        result.getRecords().forEach(student -> {
            System.out.println("学员ID: " + student.getUserId() +
                             ", 姓名: " + student.getName() +
                             ", 状态: " + student.getStatus() +
                             ", 是否正式学员: " + student.getIsRegularStudents() +
                             ", 剩余课次: " + student.getCourseHours());

            // 断言验证：确保都是试听学员且剩余课次为0
            assert student.getIsRegularStudents() == 0 : "学员应该是试听学员";
            assert student.getCourseHours() == 0 : "学员剩余课次应该为0";
        });
    }

    @Test
    public void testGetTrialStudentsWithZeroCourseHoursWithConditions() {
        // 测试带查询条件的情况
        Page<Student> page = new Page<>(1, 10);
        StudentQuery studentQuery = new StudentQuery();
        studentQuery.setStoreId(1L);
        studentQuery.setQueryCondition("张"); // 测试姓名模糊查询
        studentQuery.setStageId(1L); // 测试阶段过滤

        Page<StudentVO> result = studentService.getTrialStudentsWithZeroCourseHours(page, studentQuery);

        System.out.println("带条件查询结果数量: " + result.getTotal());
        result.getRecords().forEach(student -> {
            System.out.println("学员: " + student.getName() + ", 阶段ID: " + student.getStageId());
            // 验证查询条件
            assert student.getIsRegularStudents() == 0 : "应该是试听学员";
            assert student.getCourseHours() == 0 : "剩余课次应该为0";
        });
    }

    @Test
    public void testQueryLogicConsistency() {
        // 测试新方法与原方法的查询逻辑一致性
        Page<Student> page1 = new Page<>(1, 100);
        Page<Student> page2 = new Page<>(1, 100);

        StudentQuery originalQuery = new StudentQuery();
        originalQuery.setStoreId(1L);
        originalQuery.setIsArrears(0); // 测试欠费状态

        StudentQuery newQuery = new StudentQuery();
        newQuery.setStoreId(1L);
        newQuery.setIsArrears(0); // 相同的欠费状态

        // 调用原方法和新方法
        Page<StudentVO> originalResult = studentService.getListByStoreId(page1, originalQuery);
        Page<StudentVO> newResult = studentService.getTrialStudentsWithZeroCourseHours(page2, newQuery);

        System.out.println("原方法查询结果数量: " + originalResult.getTotal());
        System.out.println("新方法查询结果数量: " + newResult.getTotal());

        // 新方法的结果应该是原方法结果的子集（只包含试听且剩余课次为0的学员）
        System.out.println("新方法查询的学员都应该是试听且剩余课次为0:");
        newResult.getRecords().forEach(student -> {
            System.out.println("学员: " + student.getName() +
                             ", 试听状态: " + student.getIsRegularStudents() +
                             ", 剩余课次: " + student.getCourseHours());
        });
    }
}
