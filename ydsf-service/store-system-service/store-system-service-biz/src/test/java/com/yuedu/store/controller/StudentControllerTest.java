package com.yuedu.store.controller;


import com.yuedu.store.service.CourseHoursRecordService;
import com.yuedu.store.service.StudentService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;

/**
 * @ClassName CampusControllerTest
 * @Description 测试校区接口
 * <AUTHOR>
 * @Date 2024/10/9 16:05
 * @Version v0.0.1
 */

@SpringBootTest
public class StudentControllerTest {

    private MockMvc mockMvc;

    @Mock
    private StudentService studentService;

    @Mock
    private CourseHoursRecordService courseHoursCancel;

    @InjectMocks
    private StudentController studentController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        this.mockMvc = MockMvcBuilders.standaloneSetup(studentController).build();
    }

}