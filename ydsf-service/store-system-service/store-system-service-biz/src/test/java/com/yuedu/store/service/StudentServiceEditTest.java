package com.yuedu.store.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuedu.store.dto.StudentUpdateDTO;
import com.yuedu.store.entity.Student;
import com.yuedu.store.entity.StoreStudentTrackRecord;
import com.yuedu.store.mapper.StudentMapper;
import com.yuedu.store.mapper.StoreStudentTrackRecordMapper;
import com.yuedu.store.mq.producer.StudentProducer;
import com.yuedu.store.service.impl.StudentServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 学员服务编辑功能测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025/07/29
 */
@ExtendWith(MockitoExtension.class)
class StudentServiceEditTest {

    @Mock
    private StudentMapper studentMapper;

    @Mock
    private StoreStudentTrackRecordMapper storeStudentTrackRecordMapper;

    @Mock
    private StudentProducer studentProducer;

    @InjectMocks
    private StudentServiceImpl studentService;

    private Student existingStudent;
    private StudentUpdateDTO updateDTO;

    @BeforeEach
    void setUp() {
        // 设置现有学员信息
        existingStudent = new Student();
        existingStudent.setUserId(1001L);
        existingStudent.setName("张三");
        existingStudent.setPhone("13800138001");
        existingStudent.setStoreId(100L);
        existingStudent.setSchoolId(200L);
        existingStudent.setSex(1);
        existingStudent.setGrade(5);
        existingStudent.setStageId(1);
        existingStudent.setFulltimeSchool("测试小学");
        existingStudent.setResponsiblePerson(2001L);
        existingStudent.setDescribe("原始备注");

        // 设置更新DTO
        updateDTO = new StudentUpdateDTO();
        updateDTO.setUserId(1001L);
        updateDTO.setName("张三");
        updateDTO.setPhone("13800138001");
        updateDTO.setGrade(5);
        updateDTO.setSex(1);
        updateDTO.setStageId(1);
        updateDTO.setFulltimeSchool("测试小学");
        updateDTO.setResponsiblePerson(2001L);
        updateDTO.setDescribe("原始备注");
    }

    @Test
    void testEditStudent_WithRecommendFields_ShouldCreateTrackRecord() {
        // 设置推荐老师和推荐学员
        updateDTO.setRecommendTeacher(3001);
        updateDTO.setRecommendStudent(4001);

        try (MockedStatic<StoreContextHolder> mockedStatic = mockStatic(StoreContextHolder.class)) {
            mockedStatic.when(StoreContextHolder::getStoreId).thenReturn(100L);
            mockedStatic.when(StoreContextHolder::getSchoolId).thenReturn(200L);

            // Mock数据库操作
            when(studentMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(existingStudent);
            when(studentMapper.exists(any(LambdaQueryWrapper.class))).thenReturn(false);
            when(storeStudentTrackRecordMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
            when(storeStudentTrackRecordMapper.insert(any(StoreStudentTrackRecord.class))).thenReturn(1);
            when(studentMapper.update(any(Student.class), any())).thenReturn(1);

            // 执行测试
            studentService.editStudent(updateDTO);

            // 验证跟踪记录被创建
            verify(storeStudentTrackRecordMapper, times(1)).insert(any(StoreStudentTrackRecord.class));
        }
    }

    @Test
    void testEditStudent_WithoutRecommendFields_ShouldNotCreateTrackRecord() {
        // 不设置推荐老师和推荐学员
        updateDTO.setRecommendTeacher(null);
        updateDTO.setRecommendStudent(null);

        try (MockedStatic<StoreContextHolder> mockedStatic = mockStatic(StoreContextHolder.class)) {
            mockedStatic.when(StoreContextHolder::getStoreId).thenReturn(100L);
            mockedStatic.when(StoreContextHolder::getSchoolId).thenReturn(200L);

            // Mock数据库操作
            when(studentMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(existingStudent);
            when(studentMapper.exists(any(LambdaQueryWrapper.class))).thenReturn(false);
            when(studentMapper.update(any(Student.class), any())).thenReturn(1);

            // 执行测试
            studentService.editStudent(updateDTO);

            // 验证跟踪记录没有被创建
            verify(storeStudentTrackRecordMapper, never()).insert(any(StoreStudentTrackRecord.class));
            verify(storeStudentTrackRecordMapper, never()).updateById(any(StoreStudentTrackRecord.class));
        }
    }

    @Test
    void testEditStudent_WithExistingTrackRecord_ShouldUpdateRecord() {
        // 设置推荐老师和推荐学员
        updateDTO.setRecommendTeacher(3002);
        updateDTO.setRecommendStudent(4002);

        // 创建现有跟踪记录
        StoreStudentTrackRecord existingRecord = new StoreStudentTrackRecord();
        existingRecord.setId(5001L);
        existingRecord.setUserId(1001);
        existingRecord.setStoreId(100);
        existingRecord.setSchoolId(200);
        existingRecord.setRecommendTeacher(3001);
        existingRecord.setRecommendStudent(4001);
        existingRecord.setCommunicationRecords("原始记录");

        try (MockedStatic<StoreContextHolder> mockedStatic = mockStatic(StoreContextHolder.class)) {
            mockedStatic.when(StoreContextHolder::getStoreId).thenReturn(100L);
            mockedStatic.when(StoreContextHolder::getSchoolId).thenReturn(200L);

            // Mock数据库操作
            when(studentMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(existingStudent);
            when(studentMapper.exists(any(LambdaQueryWrapper.class))).thenReturn(false);
            when(storeStudentTrackRecordMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(existingRecord);
            when(storeStudentTrackRecordMapper.updateById(any(StoreStudentTrackRecord.class))).thenReturn(1);
            when(studentMapper.update(any(Student.class), any())).thenReturn(1);

            // 执行测试
            studentService.editStudent(updateDTO);

            // 验证跟踪记录被更新
            verify(storeStudentTrackRecordMapper, times(1)).updateById(any(StoreStudentTrackRecord.class));
            verify(storeStudentTrackRecordMapper, never()).insert(any(StoreStudentTrackRecord.class));
        }
    }
}
