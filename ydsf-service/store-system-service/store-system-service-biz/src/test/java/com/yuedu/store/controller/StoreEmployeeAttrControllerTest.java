package com.yuedu.store.controller;

import com.yuedu.store.dto.StoreEmployeeDTO;
import com.yuedu.store.service.EmployeeAttrService;
import com.yuedu.store.vo.StoreEmployeeVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Slf4j
class StoreEmployeeAttrControllerTest {

    private MockMvc mockMvc;
    @Mock
    private EmployeeAttrService storeEmployeeAttrService;

    @InjectMocks
    private EmployeeController storeEmployeeAttrController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(storeEmployeeAttrController).build();
    }

    @Test
    void testUpdateById() {
        StoreEmployeeDTO storeEmployeeDTO = new StoreEmployeeDTO();
        storeEmployeeDTO.setUserId(1L);
        when(storeEmployeeAttrService.updateInfo(any(StoreEmployeeDTO.class))).thenReturn(true);

        log.info("testUpdateById:{}", storeEmployeeAttrService.updateInfo(storeEmployeeDTO));

    }

    @Test
    void testUpdateAvatar() {
        StoreEmployeeDTO storeEmployeeDTO = new StoreEmployeeDTO();
        storeEmployeeDTO.setAvatar("avatar");
        when(storeEmployeeAttrService.updateAvatar(any(StoreEmployeeDTO.class))).thenReturn(true);

        log.info("testUpdateAvatar:{}", storeEmployeeAttrService.updateAvatar(storeEmployeeDTO));
    }

    @Test
    void testGetInfoById() {
        StoreEmployeeVO storeEmployeeVO = new StoreEmployeeVO();
        storeEmployeeVO.setUserId(1L);
        storeEmployeeVO.setUserName("userName");
        storeEmployeeVO.setPhone("phone");
        storeEmployeeVO.setPhoto("photo");
        storeEmployeeVO.setIntroduce("introduce");
        storeEmployeeVO.setAvatar("avatar");
        when(storeEmployeeAttrService.getInfoById(anyLong())).thenReturn(storeEmployeeVO);

        log.info("testGetInfoById:{}", storeEmployeeAttrService.getInfoById(1L));
    }
}
