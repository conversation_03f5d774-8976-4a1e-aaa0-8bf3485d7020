package com.yuedu.store.service;

import com.yuedu.store.dto.UpdateIntentionStudentDTO;
import com.yuedu.store.entity.Student;
import com.yuedu.store.entity.StoreStudentTrackRecord;
import com.yuedu.store.mapper.StudentMapper;
import com.yuedu.store.mapper.StoreStudentTrackRecordMapper;
import com.yuedu.store.service.impl.StudentServiceImpl;
import com.yuedu.ydsf.common.core.util.R;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 意向学员信息更新功能测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-29
 */
@ExtendWith(MockitoExtension.class)
public class StudentServiceIntentionUpdateTest {

    @Mock
    private StudentMapper studentMapper;

    @Mock
    private StoreStudentTrackRecordMapper storeStudentTrackRecordMapper;

    @Mock
    private StoreStudentTrackRecordService storeStudentTrackRecordService;

    @InjectMocks
    private StudentServiceImpl studentService;

    private Student existingStudent;
    private UpdateIntentionStudentDTO updateDTO;

    @BeforeEach
    void setUp() {
        // 设置现有学员信息
        existingStudent = new Student();
        existingStudent.setUserId(1001L);
        existingStudent.setName("张三");
        existingStudent.setPhone("13800138001");
        existingStudent.setGrade(5);
        existingStudent.setSex(1);
        existingStudent.setFulltimeSchool("测试小学");
        existingStudent.setResponsiblePerson(2001L);
        existingStudent.setReferralStudentId(3001L);
        existingStudent.setDescribe("原始备注");
        existingStudent.setIsRegularStudents(2); // 意向会员

        // 设置更新DTO
        updateDTO = new UpdateIntentionStudentDTO();
        updateDTO.setUserId(1001L);
        updateDTO.setName("张三");
        updateDTO.setPhone("13800138001");
        updateDTO.setGrade(5);
        updateDTO.setSex(1);
        updateDTO.setFulltimeSchool("测试小学");
        updateDTO.setResponsiblePerson(2001L);
        updateDTO.setReferralStudentId(3001L);
        updateDTO.setDescribe("原始备注");
    }

    @Test
    void testUpdateIntentionStudent_OnlyBasicInfoChanged_NoTrackRecord() {
        // 只修改基本信息（姓名），不应该插入跟踪记录
        updateDTO.setName("李四");
        
        when(studentMapper.selectOne(any())).thenReturn(existingStudent);
        when(studentMapper.updateById(any())).thenReturn(1);
        
        R result = studentService.updateIntentionStudent(updateDTO);
        
        assertTrue(result.getCode() == 0);
        verify(storeStudentTrackRecordService, never()).add(any());
    }

    @Test
    void testUpdateIntentionStudent_ResponsiblePersonChanged_ShouldInsertTrackRecord() {
        // 修改负责老师，应该插入跟踪记录
        updateDTO.setResponsiblePerson(2002L);
        
        when(studentMapper.selectOne(any())).thenReturn(existingStudent);
        when(studentMapper.updateById(any())).thenReturn(1);
        when(storeStudentTrackRecordService.add(any())).thenReturn(true);
        
        R result = studentService.updateIntentionStudent(updateDTO);
        
        assertTrue(result.getCode() == 0);
        verify(storeStudentTrackRecordService, times(1)).add(any());
    }

    @Test
    void testUpdateIntentionStudent_ReferralStudentChanged_ShouldInsertTrackRecord() {
        // 修改转介绍学员，应该插入跟踪记录
        updateDTO.setReferralStudentId(3002L);
        
        when(studentMapper.selectOne(any())).thenReturn(existingStudent);
        when(studentMapper.updateById(any())).thenReturn(1);
        when(storeStudentTrackRecordService.add(any())).thenReturn(true);
        
        R result = studentService.updateIntentionStudent(updateDTO);
        
        assertTrue(result.getCode() == 0);
        verify(storeStudentTrackRecordService, times(1)).add(any());
    }

    @Test
    void testUpdateIntentionStudent_DescribeChanged_ShouldInsertTrackRecord() {
        // 修改备注信息，应该插入跟踪记录
        updateDTO.setDescribe("新的备注信息");
        
        when(studentMapper.selectOne(any())).thenReturn(existingStudent);
        when(studentMapper.updateById(any())).thenReturn(1);
        when(storeStudentTrackRecordService.add(any())).thenReturn(true);
        
        R result = studentService.updateIntentionStudent(updateDTO);
        
        assertTrue(result.getCode() == 0);
        verify(storeStudentTrackRecordService, times(1)).add(any());
    }

    @Test
    void testUpdateIntentionStudent_DescribeChangedToEmpty_NoTrackRecord() {
        // 修改备注信息为空，不应该插入跟踪记录
        updateDTO.setDescribe("");
        
        when(studentMapper.selectOne(any())).thenReturn(existingStudent);
        when(studentMapper.updateById(any())).thenReturn(1);
        
        R result = studentService.updateIntentionStudent(updateDTO);
        
        assertTrue(result.getCode() == 0);
        verify(storeStudentTrackRecordService, never()).add(any());
    }

    @Test
    void testUpdateIntentionStudent_MultipleFieldsChanged_ShouldInsertOneTrackRecord() {
        // 同时修改多个需要跟踪的字段，应该只插入一条跟踪记录
        updateDTO.setResponsiblePerson(2002L);
        updateDTO.setReferralStudentId(3002L);
        updateDTO.setDescribe("新的备注信息");
        
        when(studentMapper.selectOne(any())).thenReturn(existingStudent);
        when(studentMapper.updateById(any())).thenReturn(1);
        when(storeStudentTrackRecordService.add(any())).thenReturn(true);
        
        R result = studentService.updateIntentionStudent(updateDTO);
        
        assertTrue(result.getCode() == 0);
        verify(storeStudentTrackRecordService, times(1)).add(any());
    }
}
