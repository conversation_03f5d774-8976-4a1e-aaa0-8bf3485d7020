package com.yuedu.store.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.yuedu.store.api.feign.*;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.dto.ClassRoomDTO;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.entity.Employee;
import com.yuedu.store.entity.LecturerEntity;
import com.yuedu.store.query.AppUserQuery;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.vo.*;
import com.yuedu.ydsf.common.core.util.R;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName RemoteControllerTest
 * @Description fegin接口测试
 * <AUTHOR>
 * @Date 2024/10/16 16:12
 * @Version v0.0.1
 */

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@EnableFeignClients("com.yuedu.store.api.feign")
public class RemoteControllerTest {

    @Resource
    private RemoteClassRoomService remoteClassRoomService;


    @Resource
    private RemoteCampusService remoteCampusService;


    @Resource
    private RemoteLecturerService remoteLecturerService;

    @Resource
    private RemoteEmployeeService remoteEmployeeService;

    @Resource
    private RemoteClassService remoteClassService;

    @Resource
    private RemoteStudentService remoteStudentService;

    @Test
    public void getClassRoomById() {
        R<ClassRoomVO> classRoomVOR = remoteClassRoomService.getDetailById(2L);
        log.info("教师信息:{}", JSON.toJSONString(classRoomVOR));
    }


    @Test
    public void getClassRoomListByIds() {
        ClassRoomDTO classRoomDTO = new ClassRoomDTO();
        List<Long> idList = new ArrayList<>();
        idList.add(2L);
        idList.add(3L);
        classRoomDTO.setClassRoomIdList(idList);
        R<List<ClassRoomVO>> classRoomVOR = remoteClassRoomService.getList(classRoomDTO);
        log.info("教室列表信息:{}", JSON.toJSONString(classRoomVOR));
    }


    @Test
    public void getCampusById() {
        R<CampusVO> classRoomVOR = remoteCampusService.getLecturerById(2L);
        log.info("校区信息:{}", JSON.toJSONString(classRoomVOR));
    }


    @Test
    public void getCampusListByIds() {
        CampusDTO campusDTO = new CampusDTO();
        List<Long> idList = new ArrayList<>();
        idList.add(2L);
        idList.add(3L);
        campusDTO.setSchoolIdList(idList);
        R<List<CampusVO>> classRoomVOR = remoteCampusService.getCampusList(campusDTO);
        log.info("校区列表信息:{}", JSON.toJSONString(classRoomVOR));
    }


    @Test
    public void getLecturerById() {
        R<LecturerVO> lecturerVOR = remoteLecturerService.getLecture(2L);
        log.info("讲师信息:{}", JSON.toJSONString(lecturerVOR));
    }


    @Test
    public void getPageList() {
        LecturerDTO lecturerDTO = new LecturerDTO();
        lecturerDTO.setLecturerName("老师");
        lecturerDTO.setId(1L);
        lecturerDTO.setPage(new PageDTO<>(1, 15));
        R<Page<LecturerEntity>> lecturerVOR = remoteLecturerService.getPageList(lecturerDTO);
        log.info("讲师分页列表信息:{}", JSON.toJSONString(lecturerVOR));
    }


    @Test
    public void getLecturerListByIds() {
        LecturerDTO lecturerDTO = new LecturerDTO();
        List<Long> idList = new ArrayList<>();
        idList.add(2L);
        idList.add(3L);
        lecturerDTO.setIds(idList);
        R<List<LecturerVO>> lecturerList = remoteLecturerService.getLecturerList(lecturerDTO);
        log.info("讲师列表信息:{}", JSON.toJSONString(lecturerList));
    }

    @Test
    public void getEmployee() {
        R<List<EmployeeVO>> employeeById = remoteEmployeeService.getEmployeeByCampusId(CampusQuery.builder().campusId(54L).build());
        log.info("通过id查询{}", employeeById);

        R<List<EmployeeVO>> employeeByCampusId = remoteEmployeeService.getEmployeeByCampusId(CampusQuery.builder()
                .xgjCampusId("CB83B05B-FA8D-4425-9AEC-10706A3C16C0").build());
        log.info("通过校区id查询{}", employeeByCampusId);
    }

    @Test
    public void getClassInfo() {
        R<List<ClassVO>> classById = remoteClassService.getClassByCampusId(CampusQuery.builder().campusId(173L).build());
        log.info("通过id查询{}", classById);

        R<List<ClassVO>> classByCampusId = remoteClassService.getClassByCampusId(CampusQuery.builder()
                .xgjCampusId("02063152-8618-4C74-8C1B-53C9B63CE2DD").build());
        log.info("通过校区id查询{}", classByCampusId);
    }

    @Test
    public void getClassRoom() {
        R<List<ClassRoomVO>> classRoomById = remoteClassRoomService.getClassRoomByCampusId(CampusQuery.builder().campusId(54L).build());
        log.info("通过id查询{}", classRoomById);

        R<List<ClassRoomVO>> classRoomByCampusId = remoteClassRoomService.getClassRoomByCampusId(CampusQuery.builder()
                .xgjCampusId("CB83B05B-FA8D-4425-9AEC-10706A3C16C0").build());
        log.info("通过校区id查询{}", classRoomByCampusId);

    }

    @Test
    public void getByPhone() {
        R<Employee> byPhone = remoteEmployeeService.getByPhone(AppUserQuery.builder()
                .userId(3876L).phone("13148535268").build());
        log.info("通过id查询{}", byPhone);
    }

    @Test
    public void getClassRoomMapByIdList() {
        List<Long> longs = new ArrayList<>();
        longs.add(10L);
        longs.add(11L);
        R<Map<Long, ClassRoomVO>> classRoomMapByIdList = remoteClassRoomService.getClassRoomMapByIdList(longs);
        log.info("通过id查询{}", classRoomMapByIdList);
    }

    @Test
    public void getEmployeeMapByIdList() {
        List<Long> longs = new ArrayList<>();
        longs.add(10L);
        longs.add(11L);
        R<List<EmployeeVO>> employeeMapByIdList = remoteEmployeeService.getEmployeeMapByIdList(longs);
        log.info("通过id查询{}", employeeMapByIdList);
    }

    @Test
    public void getClassByIdList() {
        List<Long> longs = new ArrayList<>();
        longs.add(219494L);
        longs.add(219495L);
        R<List<ClassVO>> classByIdList = remoteClassService.getClassByIdList(longs);
        log.info("通过id查询{}", classByIdList);
    }

    @Test
    public void getStudent() {
        ArrayList<Integer> objects = new ArrayList<>();
        objects.add(874);
        objects.add(2987);
//        objects.add(80604);
        R<List<StudentVO>> student = remoteStudentService.getStudentByClassId(objects, "");
        log.info("通过id查询{}", student);
    }

    @Test
    public void getStudentList() {
        R<List<StudentVO>> student = remoteStudentService.getListByClassId(874);
        log.info("通过id查询{}", student);
    }

}