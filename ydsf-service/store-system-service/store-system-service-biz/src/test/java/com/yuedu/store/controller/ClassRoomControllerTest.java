package com.yuedu.store.controller;

import com.alibaba.nacos.common.http.param.MediaType;
import com.yuedu.store.api.feign.RemoteClassRoomService;
import com.yuedu.store.dto.ClassRoomDTO;
import com.yuedu.store.service.ClassRoomService;
import com.yuedu.store.vo.ClassRoomVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @author: 张浩宇
 * @date: 2024/10/09
 **/
@SpringBootTest
public class ClassRoomControllerTest {

    @Mock
    RemoteClassRoomService remoteClassRoomService;
    private MockMvc mockMvc;
    @Mock
    private ClassRoomService classRoomService;
    @InjectMocks
    private ClassRoomController classRoomController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        this.mockMvc = MockMvcBuilders.standaloneSetup(classRoomController).build();
    }

    @Test
    public void list_ShouldReturnListOfClassRoomVO() throws Exception {
        List<ClassRoomVO> classRoomVOList = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        ids.add(2L);
        ids.add(3L);
        ClassRoomVO classRoomVO1 = new ClassRoomVO();
        classRoomVO1.setId(2L);
        classRoomVO1.setCampusId(1L);
        classRoomVO1.setClassRoomName("classRoom2");
        classRoomVO1.setClassRoomType(1);

        ClassRoomVO classRoomVO2 = new ClassRoomVO();
        classRoomVO2.setId(3L);
        classRoomVO2.setCampusId(1L);
        classRoomVO2.setClassRoomName("classRoom3");
        classRoomVO2.setClassRoomType(1);

        classRoomVOList.add(classRoomVO1);
        classRoomVOList.add(classRoomVO2);

        String content = "{\"classRoomIdList\":[2,3]}";

        ClassRoomDTO classRoomDTO = new ClassRoomDTO();
        classRoomDTO.setClassRoomIdList(ids);
        when(classRoomService.queryList(classRoomDTO)).thenReturn(classRoomVOList);

        mockMvc.perform(post("/classRoom/classRoomList")
                        .content(content)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":[{\"id\":2,\"campusId\":1,\"classRoomName\":\"classRoom2\",\"classRoomType\":1},{\"id\":3,\"campusId\":1,\"classRoomName\":\"classRoom3\",\"classRoomType\":1}]}"));
    }

    @Test
    public void Entity_ShouldReturnEntityOfClassRoomVO() throws Exception {
        Long id = 2L;
        ClassRoomVO classRoomVO1 = new ClassRoomVO();
        classRoomVO1.setId(2L);
        classRoomVO1.setCampusId(1L);
        classRoomVO1.setClassRoomName("classRoom2");
        classRoomVO1.setClassRoomType(1);


        when(classRoomService.queryByClassRoomId(id)).thenReturn(classRoomVO1);

        mockMvc.perform(get("/classRoom/info/2"))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":{\"id\":2,\"campusId\":1,\"classRoomName\":\"classRoom2\",\"classRoomType\":1}}"));
    }

    @Test
    public void testEdit() throws Exception {
        ClassRoomDTO classRoomDTO = new ClassRoomDTO();
        classRoomDTO.setId(1012L);
        classRoomDTO.setClassRoomName("开发测试22");

        String content = "{\"id\":1012,\"classRoomName\":\"开发测试22\"}";

        when(classRoomService.edit(classRoomDTO)).thenReturn(true);

        mockMvc.perform(put("/classRoom/edit")
                        .content(content)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void testAdd() throws Exception {
        ClassRoomDTO classRoomDTO = new ClassRoomDTO();

        classRoomDTO.setClassRoomName("开发测试22");

        String content = "{\"classRoomName\":\"开发测试22\"}";

        when(classRoomService.addClassRoom(classRoomDTO)).thenReturn(true);

        mockMvc.perform(post("/classRoom/add")
                        .content(content)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void testDelete() throws Exception {
        ClassRoomDTO classRoomDTO = new ClassRoomDTO();

        classRoomDTO.setId(1012L);

        String content = "{\"id\":1012}";

        when(classRoomService.addClassRoom(classRoomDTO)).thenReturn(true);

        mockMvc.perform(delete("/classRoom/del")
                        .content(content)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
