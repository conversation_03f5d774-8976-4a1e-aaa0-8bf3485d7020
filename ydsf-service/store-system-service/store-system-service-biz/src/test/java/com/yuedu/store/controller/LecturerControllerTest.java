package com.yuedu.store.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.http.param.MediaType;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.service.LecturerService;
import com.yuedu.store.vo.LecturerVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @ClassName LecturerControllerTest
 * @Description 主讲老师测试类
 * <AUTHOR>
 * @Date 2024/10/14 12:12
 * @Version v0.0.1
 */

@SpringBootTest
public class LecturerControllerTest {
    private MockMvc mockMvc;

    @Mock
    private LecturerService lecturerService;

    @InjectMocks
    private LecturerController lecturerController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        this.mockMvc = MockMvcBuilders.standaloneSetup(lecturerController).build();
    }

    @Test
    public void list_ShouldReturnListOfSchools() throws Exception {
        List<LecturerVO> lecturerVOList = new ArrayList<>();
        String name = "Lecturer Name";
        LecturerVO lecturerVO = new LecturerVO();
        lecturerVO.setId(1L);
        lecturerVO.setLecturerName("Lecturer Name");
        lecturerVO.setXgjLecturerId("LECTURER123");
        lecturerVOList.add(lecturerVO);

        when(lecturerService.listLecturer(name)).thenReturn(lecturerVOList);

        mockMvc.perform(get("/lecturer/list")
                        .param("query", name)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"data\":[{\"id\":1,\"lecturerName\":\"Lecturer Name\",\"xgjLecturerId\":\"LECTURER123\"}],\"msg\":null}"));
    }


    @Test
    public void campusList_ShouldReturnListOfSchools() throws Exception {
        List<LecturerVO> lecturerVOList = new ArrayList<>();
        LecturerVO lecturerVO = new LecturerVO();
        List<Long> teacherIdList = new ArrayList<>();
        teacherIdList.add(1L);
        lecturerVO.setId(1L);
        lecturerVO.setLecturerName("Lecturer Name");
        lecturerVO.setXgjLecturerId("LECTURER123");
        lecturerVOList.add(lecturerVO);
        LecturerDTO lecturerDTO = new LecturerDTO();
        lecturerDTO.setIds(teacherIdList);
        lecturerDTO.setLecturerName("Lecturer Name");

        String content = JSONUtil.toJsonStr(lecturerDTO);

        when(lecturerService.selectListByLecturerId(teacherIdList, lecturerDTO.getLecturerName())).thenReturn(lecturerVOList);

        mockMvc.perform(post("/lecturer/lecturerList")
                        .content(content)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"data\":[{\"id\":1,\"lecturerName\":\"Lecturer Name\",\"xgjLecturerId\":\"LECTURER123\"}],\"msg\":null}"));
    }

    @Test
    public void campusList_ShouldReturnEmptyList() throws Exception {
        List<LecturerVO> lecturerVOList = new ArrayList<>();
        List<Long> teacherIdList = new ArrayList<>();
        String lecturerName = "";

        when(lecturerService.selectListByLecturerId(teacherIdList, lecturerName)).thenReturn(lecturerVOList);

        String requestBody = "{\"schoolIdList\":[1]}";

        mockMvc.perform(post("/lecturer/lecturerList")
                        .content(requestBody)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"data\":[],\"msg\":null}"));
    }

    @Test
    public void info_ShouldReturnSchoolInfo() throws Exception {
        Long teacherId = 1L;
        LecturerVO lecturerVO = new LecturerVO();
        lecturerVO.setId(1L);
        lecturerVO.setLecturerName("Lecturer Name");
        lecturerVO.setXgjLecturerId("LECTURER123");
        lecturerVO.setLecturerState(0);
        when(lecturerService.selectByLecturerId(teacherId)).thenReturn(lecturerVO);

        mockMvc.perform(get("/lecturer/info/{teacherId}", teacherId))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"data\":{\"id\":1,\"lecturerName\":\"Lecturer Name\",\"xgjLecturerId\":\"LECTURER123\",\"lecturerState\":0},\"msg\":null}"));
    }

    @Test
    public void info_ShouldReturnEmptyList() throws Exception {
        Long teacherId = 1L;
        LecturerVO lecturerVO = new LecturerVO();

        when(lecturerService.selectByLecturerId(teacherId)).thenReturn(lecturerVO);

        mockMvc.perform(get("/lecturer/info/{teacherId}", teacherId))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"data\":{},\"msg\":null}"));
    }

    @Test
    public void listByLecturerName_ShouldReturnListOfSchools() throws Exception {
        String lecturerName = "Lecturer Name";
        List<LecturerVO> lecturerVOList = new ArrayList<>();
        LecturerVO lecturerVO = new LecturerVO();
        lecturerVO.setId(1L);
        lecturerVO.setLecturerName("Lecturer Name");
        lecturerVO.setXgjLecturerId("LECTURER123");
        lecturerVOList.add(lecturerVO);

        when(lecturerService.selectByLecturer(lecturerName)).thenReturn(lecturerVOList);

        mockMvc.perform(get("/lecturer/listByLecturerName")
                        .param("lecturerName", lecturerName))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"data\":[{\"id\":1,\"lecturerName\":\"Lecturer Name\",\"xgjLecturerId\":\"LECTURER123\"}],\"msg\":null}"));
    }
}