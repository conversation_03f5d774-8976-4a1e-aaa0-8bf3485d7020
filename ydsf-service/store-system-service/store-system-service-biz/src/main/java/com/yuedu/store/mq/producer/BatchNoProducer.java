package com.yuedu.store.mq.producer;

import com.yuedu.store.mq.dto.BatchNoDTO;
import com.yuedu.teaching.constant.MqConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 学生消费者
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class BatchNoProducer {

    private final RocketMQClientTemplate rocketMQClientTemplate;

    @Value("${rocketmq.topics.brush_student_batch_no_topic}")
    private String batchSyncTopic;


    @Async
    public void sendMessage(List<Long> batchIds) {
        if (batchIds.isEmpty()){
            return;
        }
        for (Long batchId : batchIds) {
            BatchNoDTO batchNoDto = new BatchNoDTO();
            batchNoDto.setBatchNo(batchId);
            log.info("批次收费单, batchNoDto:{}",batchNoDto);

            Message<BatchNoDTO> message = MessageBuilder.withPayload(batchNoDto).build();

            try {
                rocketMQClientTemplate.convertAndSend(batchSyncTopic + MqConstant.COLON_TAG, message);
            } catch (Exception e) {
                log.error("批次收费单，batchNoDto:{}，发送消息失败, error", batchId, e);
            }
        }



    }
}
