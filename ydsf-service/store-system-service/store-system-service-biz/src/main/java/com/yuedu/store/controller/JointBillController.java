package com.yuedu.store.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.mq.dto.BatchNoDTO;
import com.yuedu.store.query.JointBillQuery;
import com.yuedu.store.service.CourseHoursPayService;
import com.yuedu.store.service.JointBillService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("jointBill")
@Tag(description = "jointBill", name = "联营收款单")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class JointBillController {

    private final CourseHoursPayService courseHoursPayService;
    private final JointBillService jointBillService;

    /**
     * 分页查询
     *
     * @param page   分页对象
     * @param jointBillQuery 查询条件
     * @return R
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("joint_bill_view")
    public R getJointBillPage(@ParameterObject Page page, @ParameterObject JointBillQuery jointBillQuery) {
        return R.ok(jointBillService.pageQuery(page, jointBillQuery));
    }


    /**
     * 推送收费单
     *
     * @return R
     */
    @Operation(summary = "推送收费单到财务", description = "推送收费单到财务")
    @PostMapping("/push")
    @HasPermission("joint_bill_push")
    public R pushJointBill(@RequestBody BatchNoDTO params) {
        courseHoursPayService.createJointBill(params.getBatchNo());
        return R.ok();
    }
}