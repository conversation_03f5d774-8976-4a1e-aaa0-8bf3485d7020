package com.yuedu.store.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import com.yuedu.store.constant.StudentSqlConstants;
import com.yuedu.store.constant.cst.StoreConstant;
import com.yuedu.store.constant.enums.*;
import com.yuedu.store.dto.*;
import com.yuedu.store.entity.*;
import com.yuedu.store.entity.ft.Member;
import com.yuedu.store.mapper.*;
import com.yuedu.store.mapper.ft.MemberMapper;
import com.yuedu.store.mq.producer.StudentProducer;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.query.IntentionStudentQuery;
import com.yuedu.store.query.StudentQuery;
import com.yuedu.store.query.StudentQueryDTO;
import com.yuedu.store.service.*;
import com.yuedu.store.util.StudentQueryUtil;
import com.yuedu.store.utils.StudentUtils;
import com.yuedu.store.vo.*;
import com.yuedu.teaching.api.feign.RemoteCourseTypeService;
import com.yuedu.teaching.api.feign.RemoteStageService;
import com.yuedu.teaching.dto.CourseTypeDTO;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.admin.api.entity.SysDictItem;
import com.yuedu.ydsf.admin.api.feign.RemoteDictService;
import com.yuedu.ydsf.admin.api.feign.RemoteMessageService;
import com.yuedu.ydsf.common.core.constant.CacheConstants;
import com.yuedu.ydsf.common.core.constant.SecurityConstants;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.exception.ValidateCodeException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.vo.ErrorMessage;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.api.constant.StageProductEnum;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteTimetableService;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableClassTimeVO;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.BindingResult;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.nimbusds.jose.JOSEObjectType.JWT;

/**
 * @ClassName StudentServiceImpl
 * @Description 学员服务类
 * <AUTHOR>
 * @Date 2025/02/10 09:43
 * @Version v0.0.1
 */
@Slf4j
@Service
@RefreshScope
@AllArgsConstructor
public class StudentServiceImpl extends ServiceImpl<StudentMapper, Student> implements StudentService {

    private final ClassStudentMapper classStudentMapper;

    private final CourseHoursLogMapper courseHoursLogMapper;

    private final CourseHoursStudentMapper courseHoursStudentMapper;

    private final CampusService campusService;

    private final StudentProducer studentProducer;

    private final CourseHoursRecordService courseHoursRecordService;

    private final RemoteDictService remoteDictService;

    private final RemoteMessageService remoteMessageService;

    private final RemoteCourseTypeService remoteCourseTypeService;

    private final RedisTemplate redisTemplate;
    private final EmployeeService employeeService;

    private final StudentConversionService studentConversionService;

    private final StoreStudentTrackRecordService storeStudentTrackRecordService;

    @Resource
    private RemoteTimetableService remoteTimetableService;

    @Resource
    private RemoteStageService stageService;

    @Resource
    private StudentMapper studentMapper;

    @Resource
    private PaperUserMapper paperUserMapper;

    @Resource
    private StoreStudentTrackRecordMapper storeStudentTrackRecordMapper;

    @Value("${test.code.auth:true}")
    private Boolean testCodeAuth;


    @Value("${intention.invite.link:https://test-store.yuedushufang.com/IntentStudentForm}")
    private String intentionInviteLink;


    @Value("${jwt.secret:c1va5h7cSfs2THnnKKcD760quf9uDT9l}")
    private String secret;
    @Autowired
    private PaperChildrenAnswerMapper paperChildrenAnswerMapper;
    @Autowired
    private PaperParentAnswerMapper paperParentAnswerMapper;
    @Autowired
    private CourseHoursPayMapper courseHoursPayMapper;
    @Autowired
    private CourseHoursRecordMapper courseHoursRecordMapper;
    @Resource
    private MemberMapper memberMapper;


    @NotNull
    private static StudentVO getStudentVO(Student student, List<Long> storeIdList) {
        StudentVO studentVO = new StudentVO();

        BeanUtil.copyProperties(student, studentVO);

        //门店编号列表
        storeIdList.add(student.getStoreId());
        //常规课次+赠送课次+试听课次
        studentVO.setGrade(StudentUtils.grade(student.getGrade(), student.getGradeBaseDate()));

        return studentVO;
    }

    /**
     * 统一设置学员错误信息
     *
     * @param code    错误码
     * @param student 学员信息
     * @return 结果
     */
    private static SaveStudentErrorVO getStudentError(Integer code, Student student) {
        SaveStudentErrorVO errorVO = new SaveStudentErrorVO();
        errorVO.setCode(code);
        errorVO.setMsg(String.format(ErrorCodeByStudentEnum.getDescByCode(code), student.getName(), student.getPhone()));
        errorVO.setUserId(student.getUserId());
        return errorVO;
    }


    /**
     * 校验课次、金额是否为有效的非负整数
     *
     * @param errorMessageList 错误信息列表
     */
    private static void validateCourseHours(BatchImportStudentVO studentVO, List<ErrorMessage> errorMessageList) {
        if (studentVO.getCourseHoursFormal() == null || studentVO.getCourseHoursFormal() < 0) {
            setErrorMessage("正式课时未填写&小于零&表格字段名称错误", studentVO.getLineNum(), errorMessageList);
            return;
        }

        if (studentVO.getCourseHoursTrial() == null || studentVO.getCourseHoursTrial() < 0) {
            setErrorMessage("试听课时未填写&小于零&表格字段名称错误", studentVO.getLineNum(), errorMessageList);
            return;
        }

        if (studentVO.getCourseHoursGift() == null || studentVO.getCourseHoursGift() < 0) {
            setErrorMessage("赠送课时未填写&小于零&表格字段名称错误", studentVO.getLineNum(), errorMessageList);
            return;
        }

        if (studentVO.getCourseHoursFormalAmount().compareTo(BigDecimal.ZERO) < 0) {
            setErrorMessage("正式课时未填写&小于零&表格字段名称错误", studentVO.getLineNum(), errorMessageList);
            return;
        }

        if (studentVO.getCourseHoursTrialAmount().compareTo(BigDecimal.ZERO) < 0) {
            setErrorMessage("试听课时未填写&小于零&表格字段名称错误", studentVO.getLineNum(), errorMessageList);
            return;
        }

        if (studentVO.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            setErrorMessage("剩余总金额未填写&小于零&表格字段名称错误", studentVO.getLineNum(), errorMessageList);
            return;
        }

        // 计算常规课和试听课金额之和
        BigDecimal expectedTotal = studentVO.getCourseHoursFormalAmount().add(studentVO.getCourseHoursTrialAmount());

        // 比较实际总金额与预期总金额是否相等（使用BigDecimal的compareTo方法确保精度安全）
        if (studentVO.getAmount().compareTo(expectedTotal) != 0) {
            setErrorMessage("剩余总金额不等于常规课剩余费用+试听课剩余费用", studentVO.getLineNum(), errorMessageList);
        }
    }

    /**
     * 课次数判断是否为数值型
     *
     * @param str 参数
     * @return 结果 数值型为true   不是数值型为false
     */
    public static boolean isInteger(String str) {
        String regex = "^-?\\d+$";
        return str.matches(regex);
    }


    /**
     * 学员性别校验
     *
     * @param studentVO        学员信息
     * @param errorMessageList 错误列表
     */
    private static void validateSex(BatchImportStudentVO studentVO, Map<String, Integer> genderMap, List<ErrorMessage> errorMessageList) {
        // 性别处理
        String gender = studentVO.getGender();
        if (CharSequenceUtil.isEmpty(gender)) {
            setErrorMessage("学员性别未填写或表格字段名称错误", studentVO.getLineNum(), errorMessageList);
        } else {
            //判断 性别是否存在genderMap的key中
            if (!genderMap.containsKey(gender)) {
                setErrorMessage("学员性别填写不正确", studentVO.getLineNum(), errorMessageList);
            }
            studentVO.setSex(genderMap.get(gender));
        }
    }

    /**
     * 学员姓名校验
     *
     * @param studentVO        学员信息
     * @param errorMessageList 错误列表
     */
    private static void validateName(BatchImportStudentVO studentVO, List<ErrorMessage> errorMessageList) {
        String name = studentVO.getName();
        if (CharSequenceUtil.isEmpty(name)) {
            setErrorMessage("学员姓名未填写或表格字段名称错误", studentVO.getLineNum(), errorMessageList);
            return;
        }

        // 定义合法字符的正则表达式
        String regex = "^[\\u4e00-\\u9fa5a-zA-Z·\\-]+$";
        if (!name.matches(regex)) {
            setErrorMessage("学员姓名包含非法字符", studentVO.getLineNum(), errorMessageList);
        }

        // 长度处理
        if (name.length() < 2 || name.length() > 20) {
            setErrorMessage("姓名格式错误，请重新填写", studentVO.getLineNum(), errorMessageList);
        }
    }

    /**
     * 错误信息处理
     *
     * @param msg              错误信息
     * @param lineNum          行号
     * @param errorMessageList 错误信息列表
     */

    private static void setErrorMessage(String msg, Long lineNum, List<ErrorMessage> errorMessageList) {
        ErrorMessage errorMessage = new ErrorMessage();
        Set<String> errors = new HashSet<>();
        errors.add(msg);
        errorMessage.setLineNum(lineNum);
        errorMessage.setErrors(errors);
        errorMessageList.add(errorMessage);
    }

    /**
     * 学员手机号校验
     *
     * @param studentVO        学员信息
     * @param errorMessageList 错误列表
     */
    private static void validatePhone(BatchImportStudentVO studentVO, List<ErrorMessage> errorMessageList) {
        // 手机号处理
        String phone = studentVO.getPhone();
        if (CharSequenceUtil.isEmpty(phone)) {
            setErrorMessage("学员手机号未填写或表格字段名称错误", studentVO.getLineNum(), errorMessageList);
        } else {
            String regex = "^1[3-9]\\d{9}$";
            if (!phone.matches(regex)) {
                // 姓名为空时，默认为姓名
                if (CharSequenceUtil.isEmpty(studentVO.getName())) {
                    studentVO.setName("姓名");
                }
                setErrorMessage("学员" + studentVO.getName() + "[" + studentVO.getPhone() + "]手机号格式错误", studentVO.getLineNum(), errorMessageList);
            }
        }
    }


    /**
     * 学员年级校验
     *
     * @param studentVO        学员信息
     * @param errorMessageList 错误列表
     */
    private static void validateGrade(BatchImportStudentVO studentVO, Map<String, Integer> studentGradeTypeMap, List<ErrorMessage> errorMessageList) {
        // 年级
        String grade = studentVO.getGradeStr();
        if (CharSequenceUtil.isEmpty(grade)) {
            setErrorMessage("学员年级未填写或表格年级名称错误", studentVO.getLineNum(), errorMessageList);
        } else {
            if (!studentGradeTypeMap.containsKey(grade)) {
                setErrorMessage("学员年级填写不正确", studentVO.getLineNum(), errorMessageList);
            }
            studentVO.setGrade(studentGradeTypeMap.get(grade));
        }
    }

    /**
     * 学员状态校验
     *
     * @param studentVO        学员信息
     * @param errorMessageList 错误列表
     */
    private static void validateStatus(BatchImportStudentVO studentVO, Map<String, Integer> studentStatusMap, List<ErrorMessage> errorMessageList) {
        // 状态
        String status = studentVO.getStatusStr();
        if (CharSequenceUtil.isEmpty(status)) {
            setErrorMessage("学员状态未填写或表格学生状态名称错误", studentVO.getLineNum(), errorMessageList);
        } else {
            if (!studentStatusMap.containsKey(status)) {
                setErrorMessage("学员状态填写不正确", studentVO.getLineNum(), errorMessageList);
            }
            studentVO.setStatus(studentStatusMap.get(status));
        }
    }


    /**
     * 学员类型校验
     *
     * @param studentVO        学员信息
     * @param errorMessageList 错误列表
     */
    private static void validateIsRegular(BatchImportStudentVO studentVO, Map<String, Integer> isRegularMap, List<ErrorMessage> errorMessageList) {
        // 类型
        String type = studentVO.getType();
        if (CharSequenceUtil.isEmpty(type)) {
            setErrorMessage("学员类型未填写或表格学员类型名称错误", studentVO.getLineNum(), errorMessageList);
        } else {
            if (!isRegularMap.containsKey(type)) {
                setErrorMessage("学员类型填写不正确", studentVO.getLineNum(), errorMessageList);
            }
            studentVO.setIsRegularStudents(isRegularMap.get(type));
        }
    }


    /**
     * 多条件分页查询学员信息
     *
     * @param page         分页信息
     * @param studentQuery 学员信息
     * @return 结果
     */
    @Override
    public Page<StudentVO> allStudentPage(Page<Student> page, StudentQuery studentQuery) {

        Wrapper<Student> wrapper = Wrappers.lambdaQuery(Student.class).eq(studentQuery.getStoreId() != null, Student::getStoreId,
                studentQuery.getStoreId()).like(!StrUtil.isEmpty(studentQuery.getName()), Student::getName, studentQuery.getName()).eq(!StrUtil.isEmpty(studentQuery.getPhone()), Student::getPhone, studentQuery.getPhone()).orderByDesc(Student::getUserId);

        //开始分页查询
        Page<Student> pageList = baseMapper.selectPage(page, wrapper);

        return getStudentPage(pageList);
    }

    /**
     * 获取学员分页信息
     *
     * @param pageList 分页信息
     * @return 结果
     */
    @NotNull
    private Page<StudentVO> getStudentPage(Page<Student> pageList) {
        Page<StudentVO> pageVO = new Page<>();
        //门店Id列表
        List<Long> storeIdList = new ArrayList<>();
        //组装学员列表信息
        if (pageList.getSize() > StoreConstant.CODE) {
            //设置总条数
            pageVO.setTotal(pageList.getTotal());
            //设置当前页
            pageVO.setCurrent(pageList.getCurrent());
            //设置每页条数
            pageVO.setSize(pageList.getSize());
            //设置总页数
            pageVO.setPages(pageList.getPages());
            //设置学员信息
            pageVO.setRecords(pageList.getRecords().stream().map(student -> getStudentVO(student, storeIdList)).toList());
        }

        //门店编号存在时批量获取门店信息
        if (!CollectionUtils.isEmpty(storeIdList)) {
            //获取门店信息
            obtainCampusRegion(storeIdList, pageVO);
        }

        return pageVO;
    }

    /**
     * 获取门店信息
     *
     * @param storeIdList 门店Id列表
     * @param pageVO      学员分页信息
     */
    private void obtainCampusRegion(List<Long> storeIdList, Page<StudentVO> pageVO) {
        //通过门店列表查询门店所属大区等信息
        List<CampusVO> campusVOList = campusService.getCampusList(storeIdList);
        if (!CollectionUtils.isEmpty(campusVOList)) {
            for (StudentVO studentVO : pageVO.getRecords()) {
                campusVOList.stream().filter(campusVO -> studentVO.getStoreId() != null && studentVO.getStoreId().equals(campusVO.getId())).forEach(campusVO -> {
                    studentVO.setStoreName(campusVO.getCampusName());
                    studentVO.setRegionId(campusVO.getRegionId());
                    studentVO.setSchoolName(campusVO.getSchoolName());
                    studentVO.setRegionName(campusVO.getRegionName());
                });
            }
        }
    }

    /**
     * 阶段信息校验
     *
     * @param studentVO        学员信息
     * @param errorMessageList 错误信息列表
     */
    private void validateStage(BatchImportStudentVO studentVO, Map<String, Integer> stageListMap, List<ErrorMessage> errorMessageList) {
        // 阶段处理
        String stageName = studentVO.getStageName();
        if (CharSequenceUtil.isEmpty(stageName)) {
            setErrorMessage("学员阶段未填写或表格字段名称错误", studentVO.getLineNum(), errorMessageList);
        } else {
            //判断 性别是否存在genderMap的key中
            if (!stageListMap.containsKey(stageName)) {
                setErrorMessage("学员阶段填写错误", studentVO.getLineNum(), errorMessageList);
            }
            studentVO.setStageId(stageListMap.get(stageName));
        }
    }

    /**
     * 接受的数据进行重复性校验
     *
     * @param studentList      全部学员列表
     * @param errorMessageList 错误信息列表
     */
    private void validateDuplicate(List<BatchImportStudentVO> studentList, List<ErrorMessage> errorMessageList) {
        Map<String, Long> namePhoneMap = new HashMap<>();
        for (BatchImportStudentVO student : studentList) {
            if (!StrUtil.isEmpty(student.getName()) && !StrUtil.isEmpty(student.getPhone())) {
                String key = student.getName() + "_" + student.getPhone();
                if (namePhoneMap.containsKey(key)) {
                    String msg = "学员" + student.getName() + "[" + student.getPhone() + "]与第" + student.getLineNum() + "行重复";
                    setErrorMessage(msg, namePhoneMap.get(key), errorMessageList);
                } else {
                    namePhoneMap.put(key, student.getLineNum());
                }
            }
        }
    }


    private void validateFormat(List<BatchImportStudentVO> studentList) {
        for (BatchImportStudentVO student : studentList) {
            boolean isFormatError;

            // 公共字段校验
            isFormatError = CharSequenceUtil.isEmpty(student.getName()) && CharSequenceUtil.isEmpty(student.getPhone());

            // 如果格式错误，设置错误信息
            if (isFormatError) {
                throw new BizException("文档模板格式错误!");
            }
        }
    }


    private Map<String, Integer> getDictByType(String type) {
        R<List<SysDictItem>> dictResponse = remoteDictService.getDictsByType(type);

        if (dictResponse != null
                && dictResponse.isOk()
                && CollectionUtils.isNotEmpty(dictResponse.getData())) {

            return dictResponse.getData().stream()
                    .collect(Collectors.toMap(
                            SysDictItem::getLabel,
                            item -> Integer.parseInt(item.getItemValue()),
                            (existing, replacement) -> existing
                    ));
        }
        return Collections.emptyMap();
    }

    /**
     * 导入学员列表
     *
     * @param studentVOList 学员列表
     * @param bindingResult 绑定结果
     */
    @Override
    public R<ImportStudentVO> importStudent(List<BatchImportStudentVO> studentVOList, BindingResult bindingResult) {
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();
        if (CollUtil.isEmpty(studentVOList)) {
            throw new BizException("导入学员时,学员信息不能为空!");
        }

        if (studentVOList.size() >= 500) {
            throw new BizException("导入学员数量不能超过500条!");
        }

        // 初始化导入结果对象
        ImportStudentVO importStudentVO = new ImportStudentVO();
        importStudentVO.setImportTime(LocalDateTime.now());
        importStudentVO.setImportNum(studentVOList.size());

        List<ErrorMessage> errorMessageList = (List<ErrorMessage>) bindingResult.getTarget();
        assert errorMessageList != null;

        Map<String, Integer> genderMap = getDictByType("gender");
        Map<String, Integer> stageListMap = getDictByType("stage_list");
        Map<String, Integer> studentGradeTypeMap = getDictByType("student_grade_type");
        Map<String, Integer> studentStatusMap = getDictByType("student_status");
        Map<String, Integer> isRegularMap = Map.of(
                StudentRegularEnum.TRIAL.getDesc(), StudentRegularEnum.TRIAL.getCode(),
                StudentRegularEnum.FORMAL.getDesc(), StudentRegularEnum.FORMAL.getCode()
        );
        // 表格内模板格式校验
        validateFormat(studentVOList);

        // 表格内重复性校验
        validateDuplicate(studentVOList, errorMessageList);

        // 并行处理数据校验
        studentVOList.parallelStream().forEach(studentVO -> {
            // 校验学员姓名
            validateName(studentVO, errorMessageList);
            // 校验学员性别
            validateSex(studentVO, genderMap, errorMessageList);
            // 校验学员阶段
            validateStage(studentVO, stageListMap, errorMessageList);
            // 校验学员手机号
            validatePhone(studentVO, errorMessageList);
            // 校验学员年级
            validateGrade(studentVO, studentGradeTypeMap, errorMessageList);
            //验证学员类型
            validateIsRegular(studentVO, isRegularMap, errorMessageList);
            //验证学员状态
            validateStatus(studentVO, studentStatusMap, errorMessageList);
            // 校验学员类型
            validateCourseHours(studentVO, errorMessageList);
        });

        // 数据库重复性校验
        validateDuplicateDataBase(studentVOList, storeId, errorMessageList);

        // 错误信息整合
        if (CollUtil.isNotEmpty(errorMessageList)) {
            Map<Long, ErrorMessage> errorMap = new HashMap<>();
            for (ErrorMessage errorMessage : errorMessageList) {
                errorMap.merge(errorMessage.getLineNum(), errorMessage, (existing, replacement) -> {
                    existing.getErrors().addAll(replacement.getErrors());
                    return existing;
                });
            }
            importStudentVO.setImportStatus(0);
            importStudentVO.setErrorMessageList(new ArrayList<>(errorMap.values()));
            return R.ok(importStudentVO);
        } else {
            studentVOList.forEach(studentVO -> {
                Student student = createStudent(studentVO, storeId, schoolId);
                operateCourseHours(student, studentVO);
            });

            importStudentVO.setImportStatus(1);

            return R.ok(importStudentVO);
        }
    }


    /**
     * 操作学员课次信息
     */
    private void operateCourseHours(Student student, BatchImportStudentVO studentVO) {

        List<StoreCourseHoursLog> courseHoursLogList = new ArrayList<>();

        if (studentVO.getCourseHoursFormal() > 0 || studentVO.getCourseHoursGift() > 0) {
            CourseHoursPay courseHoursPay = new CourseHoursPay();
            BeanUtil.copyProperties(student, courseHoursPay);
            courseHoursPay.setId(IdUtil.getSnowflakeNextId());
            courseHoursPay.setStudentId(student.getUserId());
            courseHoursPay.setFormal(studentVO.getCourseHoursFormal());
            courseHoursPay.setGift(studentVO.getCourseHoursGift());
            courseHoursPay.setCourseType(1);
            courseHoursPay.setTotalAmount(studentVO.getCourseHoursFormalAmount());
            courseHoursPayMapper.insert(courseHoursPay);

            CourseHoursStudent courseHoursStudent = new CourseHoursStudent();
            BeanUtil.copyProperties(courseHoursPay, courseHoursStudent, "id");
            courseHoursStudent.setCourseHours(courseHoursPay.getGift() + courseHoursPay.getFormal());
            courseHoursStudentMapper.insert(courseHoursStudent);

            if (studentVO.getCourseHoursFormal() > 0) {
                StoreCourseHoursRecord courseHoursRecord = new StoreCourseHoursRecord();
                BeanUtil.copyProperties(courseHoursStudent, courseHoursRecord, "id");
                courseHoursRecord.setBatchNo(courseHoursPay.getId());
                courseHoursRecord.setCount(courseHoursPay.getFormal());
                courseHoursRecord.setQuantity(courseHoursPay.getFormal());
                courseHoursRecord.setOperationType(CourseHoursOperationEnum.ENROLL.getDesc());
                courseHoursRecord.setTotalAmount(courseHoursPay.getTotalAmount());
                courseHoursRecord.setResidueAmount(courseHoursPay.getTotalAmount());
                courseHoursRecord.setUnitPrice(courseHoursPay.getTotalAmount().divide(BigDecimal.valueOf(courseHoursPay.getFormal()), 4, RoundingMode.DOWN));
                courseHoursRecordMapper.insert(courseHoursRecord);
                createCourseHoursLog(courseHoursRecord, courseHoursLogList);
            }

            if (studentVO.getCourseHoursGift() > 0) {
                StoreCourseHoursRecord courseHoursRecord = new StoreCourseHoursRecord();
                BeanUtil.copyProperties(courseHoursStudent, courseHoursRecord, "id");
                courseHoursRecord.setBatchNo(courseHoursPay.getId());
                courseHoursRecord.setCount(courseHoursPay.getGift());
                courseHoursRecord.setQuantity(courseHoursPay.getGift());
                courseHoursRecord.setOperationType(CourseHoursOperationEnum.GIFT.getDesc());
                courseHoursRecord.setTotalAmount(BigDecimal.ZERO);
                courseHoursRecord.setResidueAmount(BigDecimal.ZERO);
                courseHoursRecord.setUnitPrice(BigDecimal.ZERO);
                courseHoursRecordMapper.insert(courseHoursRecord);
                createCourseHoursLog(courseHoursRecord, courseHoursLogList);
            }
        }

        if (studentVO.getCourseHoursTrial() > 0) {
            CourseHoursPay courseHoursPay = new CourseHoursPay();
            BeanUtil.copyProperties(student, courseHoursPay);
            courseHoursPay.setId(IdUtil.getSnowflakeNextId());
            courseHoursPay.setStudentId(student.getUserId());
            courseHoursPay.setFormal(studentVO.getCourseHoursTrial());
            courseHoursPay.setGift(0);
            courseHoursPay.setCourseType(2);
            courseHoursPay.setTotalAmount(studentVO.getCourseHoursTrialAmount());
            courseHoursPayMapper.insert(courseHoursPay);

            CourseHoursStudent courseHoursStudent = new CourseHoursStudent();
            BeanUtil.copyProperties(courseHoursPay, courseHoursStudent, "id");
            courseHoursStudent.setCourseHours(courseHoursPay.getFormal());
            courseHoursStudentMapper.insert(courseHoursStudent);

            StoreCourseHoursRecord courseHoursRecord = new StoreCourseHoursRecord();
            BeanUtil.copyProperties(courseHoursStudent, courseHoursRecord, "id");
            courseHoursRecord.setBatchNo(courseHoursPay.getId());
            courseHoursRecord.setCount(courseHoursPay.getFormal());
            courseHoursRecord.setQuantity(courseHoursPay.getFormal());
            courseHoursRecord.setOperationType(CourseHoursOperationEnum.ENROLL.getDesc());
            courseHoursRecord.setTotalAmount(courseHoursPay.getTotalAmount());
            courseHoursRecord.setResidueAmount(courseHoursPay.getTotalAmount());
            courseHoursRecord.setUnitPrice(courseHoursPay.getTotalAmount().divide(BigDecimal.valueOf(courseHoursPay.getFormal()), 4, RoundingMode.DOWN));
            courseHoursRecordMapper.insert(courseHoursRecord);

            createCourseHoursLog(courseHoursRecord, courseHoursLogList);
        }

        if (!courseHoursLogList.isEmpty()) {
            courseHoursLogMapper.insert(courseHoursLogList);
        }
    }

    private void createCourseHoursLog(StoreCourseHoursRecord courseHoursRecord, List<StoreCourseHoursLog> courseHoursLogList) {
        StoreCourseHoursLog courseHoursLog = new StoreCourseHoursLog();
        BeanUtil.copyProperties(courseHoursRecord, courseHoursLog, "id");
        courseHoursLog.setRelatedId(courseHoursRecord.getId());
        courseHoursLog.setCourseHours(courseHoursRecord.getCount());
        courseHoursLogList.add(courseHoursLog);
    }

    /**
     * 创建学员
     *
     * @param stu      学院信息
     * @param storeId  门店Id
     * @param schoolId 校区Id
     * @return 结果
     */
    private Student createStudent(BatchImportStudentVO stu, Long storeId, Long schoolId) {
        Student student = new Student();
        BeanUtil.copyProperties(stu, student);
        student.setTransformTime(LocalDateTime.now());
        student.setInitRegular(stu.getIsRegularStudents());
        student.setGradeBaseDate(LocalDateTime.now());
        student.setCourseHours(stu.getCourseHoursFormal() + stu.getCourseHoursTrial() + stu.getCourseHoursGift());
        student.setPinyinPre(PinyinUtil.getFirstLetter(stu.getName(), ""));
        student.setStoreId(storeId);
        student.setSchoolId(schoolId);
        student.setOrigin(2);
        studentMapper.insert(student);
        return student;
    }


    /**
     * 数据库中去重并记录失败数量
     *
     * @param studentVOList 学员列表
     * @param storeId       门店Id
     */
    private void validateDuplicateDataBase(List<BatchImportStudentVO> studentVOList, Long storeId, List<ErrorMessage> errorMessageList) {
        // 如果学员列表为空，直接返回
        if (CollUtil.isEmpty(studentVOList)) {
            return;
        }

        // 提取学员的手机号和姓名，作为唯一键，忽略 phone 为空的学员
        Set<String> uniqueKeys = studentVOList.stream()
                // 过滤掉 phone 为空  name为空的学员
                .filter(studentVO -> StringUtils.isNotBlank(studentVO.getPhone())).filter(studentVO -> StringUtils.isNotBlank(studentVO.getName())).map(studentVO -> studentVO.getPhone() + "_" + studentVO.getName()).collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(uniqueKeys)) {
            // 批量查询已存在的学员，忽略 phone name为空的学员
            List<Student> existingStudents =
                    baseMapper.selectList(Wrappers.lambdaQuery(Student.class).select(Student::getPhone, Student::getName).eq(Student::getStoreId,
                            storeId).in(Student::getPhone, uniqueKeys.stream().map(key -> key.split("_")[0]).collect(Collectors.toSet())).in(Student::getName, uniqueKeys.stream().map(key -> key.split("_")[1]).collect(Collectors.toSet())));
            // 将已存在的学员信息存到 Map 中，方便快速查找
            Map<String, Student> existingKeys =
                    existingStudents.stream().collect(Collectors.toMap(student -> student.getPhone() + "_" + student.getName(), student -> student));
            // 检查每个学员是否已存在，忽略 phone 为空的学员
            for (BatchImportStudentVO studentVO : studentVOList) {
                // 只处理 phone name 不为空的学员
                if (StringUtils.isNotBlank(studentVO.getPhone()) && StringUtils.isNotBlank(studentVO.getName())) {
                    String key = studentVO.getPhone() + "_" + studentVO.getName();
                    if (existingKeys.containsKey(key)) {
                        setErrorMessage("学员" + studentVO.getName() + "[" + studentVO.getPhone() + "]已存在", studentVO.getLineNum(), errorMessageList);
                    }
                }
            }
        }
    }

    /**
     * 获取学员信息
     *
     * @param storeId   门店ID
     * @param studentId 学员ID
     * @return Student
     */
    private Student getStudentInfo(Long storeId, Long studentId) {

        Student oldStudent = baseMapper.selectOne(Wrappers.lambdaQuery(Student.class).eq(Student::getUserId, studentId)
                .eq(storeId != null, Student::getStoreId, storeId));

        if (oldStudent == null) {
            throw new BizException("该学员不存在,操作失败!");
        }
        return oldStudent;
    }

    /**
     * 查询学员详情
     *
     * @param studentQuery 学员查询类
     * @return 结果
     */
    @Override
    public StudentVO detail(StudentQuery studentQuery) {

        Student student = getStudentInfo(studentQuery.getStoreId(), studentQuery.getUserId());

        StudentVO studentVO = new StudentVO();

        BeanUtil.copyProperties(student, studentVO);

        studentVO.setGrade(StudentUtils.grade(student.getGrade(), student.getGradeBaseDate()));

        studentVO.setCourseHoursList(courseHoursStudentMapper.selectList(Wrappers.lambdaQuery(CourseHoursStudent.class)
                .eq(CourseHoursStudent::getStudentId, studentQuery.getUserId())
                .eq(CourseHoursStudent::getStudentId, studentQuery.getUserId())
        ));

        StoreCourseHoursLog courseHoursLog =
                courseHoursLogMapper.selectOne(Wrappers.lambdaQuery(StoreCourseHoursLog.class).select(StoreCourseHoursLog::getTimetableId).eq(StoreCourseHoursLog::getStudentId, student.getUserId()).eq(StoreCourseHoursLog::getStoreId, student.getStoreId()).eq(StoreCourseHoursLog::getLogType, CourseHoursLogTypeEnum.CONSUME.getCode()).isNotNull(StoreCourseHoursLog::getTimetableId).orderByDesc(StoreCourseHoursLog::getId).last("limit 1"));

        if (courseHoursLog != null) {
            Long timetableId = courseHoursLog.getTimetableId();

            R<List<TimetableClassTimeVO>> timetableClassTimeVOList = remoteTimetableService.getListByIds(Collections.singletonList(timetableId));

            if (timetableClassTimeVOList.isOk() && CollectionUtils.isNotEmpty(timetableClassTimeVOList.getData())) {
                TimetableClassTimeVO timetableClassTimeVO = timetableClassTimeVOList.getData().get(0);
                studentVO.setFullClassTimeStr(timetableClassTimeVO.getFullClassTimeStr());
            }
        }
        return studentVO;
    }

    /**
     * 更新学员信息
     *
     * @param params 学员信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editStudent(StudentUpdateDTO params) {
        //门店Id
        Long storeId = StoreContextHolder.getStoreId();
        //校区Id
        Long schoolId = StoreContextHolder.getSchoolId();

        Student student = getStudentInfo(storeId, params.getUserId());

        boolean exists = baseMapper.exists(Wrappers.lambdaQuery(Student.class).eq(Student::getName, params.getName()).eq(Student::getPhone,
                params.getPhone()).eq(Student::getStoreId, storeId).ne(Student::getUserId, student.getUserId()));
        if (exists) {
            throw new BizException(String.format(ErrorCodeByStudentEnum.STUDENT_EXIST.getDesc(), params.getName(), params.getPhone()));
        }

        String pinyinPre = PinyinUtil.getFirstLetter(params.getName(), "");

        // 检查是否需要处理跟踪记录（推荐老师和推荐学员字段有值）
        boolean needTrackRecord = (params.getRecommendTeacher() != null && params.getRecommendTeacher() > 0) ||
                (params.getRecommendStudent() != null && params.getRecommendStudent() > 0);

        if (needTrackRecord) {
            handleStudentTrackRecord(params, storeId, schoolId);
        }

        // 更新校区员工信息
        LambdaUpdateWrapper<Student> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(Student::getUserId, student.getUserId())
                .eq(Student::getStoreId, storeId)
                .set(Student::getSex, params.getSex())
                .set(Student::getName, params.getName())
                .set(Student::getPhone, params.getPhone())
                .set(Student::getFulltimeSchool, params.getFulltimeSchool())
                .set(Student::getResponsiblePerson, params.getResponsiblePerson())
                .set(Student::getDescribe, params.getDescribe())
                .set(Student::getPinyinPre, pinyinPre)
                .set(Student::getStageId, params.getStageId());

        if (params.getGrade() != null) {
            updateWrapper.set(Student::getGrade, params.getGrade());
            updateWrapper.set(Student::getGradeBaseDate, LocalDateTime.now());
        }

        baseMapper.update(student, updateWrapper);

        studentProducer.sendMessage(params.getUserId());

    }

    /**
     * 处理学员跟踪记录
     *
     * @param params   学员更新参数
     * @param storeId  门店ID
     * @param schoolId 校区ID
     */
    private void handleStudentTrackRecord(StudentUpdateDTO params, Long storeId, Long schoolId) {
        try {
            log.info("开始处理学员跟踪记录，学员ID：{}, 推荐老师：{}, 推荐学员：{}",
                    params.getUserId(), params.getRecommendTeacher(), params.getRecommendStudent());

            // 查询该意向会员是否已存在跟踪记录
            StoreStudentTrackRecord existingRecord = storeStudentTrackRecordMapper.selectOne(
                    Wrappers.lambdaQuery(StoreStudentTrackRecord.class)
                            .eq(StoreStudentTrackRecord::getUserId, params.getUserId().intValue())
                            .eq(StoreStudentTrackRecord::getStoreId, storeId.intValue())
                            .orderByDesc(StoreStudentTrackRecord::getId)
                            .last("LIMIT 1")
            );

            if (existingRecord == null) {
                // 不存在记录，创建新的跟踪记录
                createNewTrackRecord(params, storeId, schoolId);
            } else {
                // 已存在记录，更新现有记录
                updateExistingTrackRecord(existingRecord, params);
            }

            log.info("学员跟踪记录处理完成，学员ID：{}", params.getUserId());
        } catch (Exception e) {
            log.error("处理学员跟踪记录失败，学员ID：{}, 错误信息：{}", params.getUserId(), e.getMessage(), e);
            throw new BizException("处理学员跟踪记录失败：" + e.getMessage());
        }
    }

    /**
     * 创建新的跟踪记录
     *
     * @param params   学员更新参数
     * @param storeId  门店ID
     * @param schoolId 校区ID
     */
    private void createNewTrackRecord(StudentUpdateDTO params, Long storeId, Long schoolId) {
        StoreStudentTrackRecord newRecord = new StoreStudentTrackRecord();
        newRecord.setUserId(params.getUserId().intValue());
        newRecord.setStoreId(storeId.intValue());
        newRecord.setSchoolId(schoolId.intValue());
        newRecord.setWillingnessLevel(1); // 默认意愿等级为1（A级）
        newRecord.setCommunicationRecords("系统自动创建：更新推荐信息");

        // 设置推荐老师和推荐学员
        if (params.getRecommendTeacher() != null && params.getRecommendTeacher() > 0) {
            newRecord.setRecommendTeacher(params.getRecommendTeacher());
        }
        if (params.getRecommendStudent() != null && params.getRecommendStudent() > 0) {
            newRecord.setRecommendStudent(params.getRecommendStudent());
        }

        int insertResult = storeStudentTrackRecordMapper.insert(newRecord);
        if (insertResult <= 0) {
            throw new BizException("创建跟踪记录失败");
        }

        log.info("创建新跟踪记录成功，学员ID：{}, 记录ID：{}", params.getUserId(), newRecord.getId());
    }

    /**
     * 更新现有跟踪记录
     *
     * @param existingRecord 现有记录
     * @param params         学员更新参数
     */
    private void updateExistingTrackRecord(StoreStudentTrackRecord existingRecord, StudentUpdateDTO params) {
        boolean needUpdate = false;
        StringBuilder updateInfo = new StringBuilder("更新推荐信息：");

        // 检查推荐老师是否需要更新
        if (params.getRecommendTeacher() != null && params.getRecommendTeacher() > 0) {
            if (!Objects.equals(existingRecord.getRecommendTeacher(), params.getRecommendTeacher())) {
                existingRecord.setRecommendTeacher(params.getRecommendTeacher());
                updateInfo.append("推荐老师从").append(existingRecord.getRecommendTeacher())
                        .append("更新为").append(params.getRecommendTeacher()).append("; ");
                needUpdate = true;
            }
        }

        // 检查推荐学员是否需要更新
        if (params.getRecommendStudent() != null && params.getRecommendStudent() > 0) {
            if (!Objects.equals(existingRecord.getRecommendStudent(), params.getRecommendStudent())) {
                existingRecord.setRecommendStudent(params.getRecommendStudent());
                updateInfo.append("推荐学员从").append(existingRecord.getRecommendStudent())
                        .append("更新为").append(params.getRecommendStudent()).append("; ");
                needUpdate = true;
            }
        }

        if (needUpdate) {
            // 更新沟通记录
            String currentRecords = existingRecord.getCommunicationRecords();
            String newRecords = (currentRecords != null ? currentRecords + "\n" : "") + updateInfo.toString();
            existingRecord.setCommunicationRecords(newRecords);

            int updateResult = storeStudentTrackRecordMapper.updateById(existingRecord);
            if (updateResult <= 0) {
                throw new BizException("更新跟踪记录失败");
            }

            log.info("更新跟踪记录成功，学员ID：{}, 记录ID：{}, 更新内容：{}",
                    params.getUserId(), existingRecord.getId(), updateInfo.toString());
        } else {
            log.info("跟踪记录无需更新，学员ID：{}, 记录ID：{}", params.getUserId(), existingRecord.getId());
        }
    }


    /**
     * 更新学员信息阶段
     *
     * @param params 学员信息
     */
    @Override
    public void editStudentStageId(StudentUpdateDTO params) {

        if (params.getStageId() == null || params.getStageId() > 7) {
            return;
        }
        Student student = getStudentInfo(null, params.getUserId());

        if (student.getStageId() == null || !student.getStageId().equals(params.getStageId())) {
            // 更新校区员工信息
            LambdaUpdateWrapper<Student> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(Student::getUserId, params.getUserId())
                    .set(Student::getStageId, params.getStageId());

            baseMapper.update(student, updateWrapper);

            studentProducer.sendMessage(params.getUserId());
        }
    }

    /**
     * 根据班级id列表获取学员列表
     *
     * @param classId 班级id列表
     * @return 学员列表
     */
    @Override
    public List<StudentVO> getStudentByClassId(List<Integer> classId, String condition) {
        // 拿出所有符合条件的学员
        List<Integer> status = new ArrayList<>();
        status.add(StudentStatusEnum.FORMAL.getCode());
        status.add(StudentStatusEnum.TRIAL.getCode());
        status.add(StudentStatusEnum.SUSPENSION.getCode());
        List<ClassStudent> classStudents = classStudentMapper.selectList(Wrappers.lambdaQuery(ClassStudent.class).in(ClassStudent::getClassId,
                classId).in(ClassStudent::getStatus, StudentStatusEnum.FORMAL.getCode()));
        if (classStudents.isEmpty()) {
            return List.of();
        }

        // 去重 studentUserId，保留所有班级ID
        Map<Integer, List<ClassStudent>> uniqueClassStudents = classStudents.stream().collect(Collectors.groupingBy(ClassStudent::getStudentId));

        // 取出classStudents里的studentId作key，转成map，保留所有班级ID
        Map<Integer, List<Integer>> studentMap = uniqueClassStudents.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                entry -> entry.getValue().stream().map(ClassStudent::getClassId).distinct().toList()));

        // 找到符合条件的总学员
        List<Integer> studentIds = uniqueClassStudents.keySet().stream().toList();
        log.info("符合条件学员:{}", studentIds);
        List<StudentVO> students =
                baseMapper.selectList(Wrappers.lambdaQuery(Student.class).in(Student::getUserId, studentIds).eq(Student::getStatus, status)
                        .and(StringUtils.isNotBlank(condition),
                                wrapper -> wrapper.like(Student::getName, condition)
                                        .or().like(Student::getPinyinPre, condition)
                                        .or().like(Student::getPhone, condition))).stream().map(student -> {
                    StudentVO studentVO = new StudentVO();
                    BeanUtil.copyProperties(student, studentVO);
                    return studentVO;
                }).toList();

        // 创建一个新的结果列表
        List<StudentVO> result = new ArrayList<>();

        // 遍历学员设置班级id，创建每个班级的单独记录
        students.forEach(student -> {
            List<Integer> classIds = studentMap.get(student.getUserId().intValue());
            if (classIds != null) {
                for (Integer id : classIds) {
                    StudentVO newStudentVO = new StudentVO();
                    BeanUtil.copyProperties(student, newStudentVO);
                    newStudentVO.setClassId(id);
                    result.add(newStudentVO);
                }
            }
        });

        return result;
    }

    /**
     * 根据班级id列表获取学员列表
     *
     * @param classId 班级id列表
     * @return 学员列表
     */
    @Override
    public List<StudentVO> getList(Integer classId) {

        List<ClassStudent> classStudents = classStudentMapper.getLatestClassStudents(classId);
        if (classStudents.isEmpty()) {
            return List.of();
        }
        // 构建学生ID与班级学生关系的映射
        Map<Integer, ClassStudent> studentClassMap = classStudents.stream().collect(Collectors.toMap(ClassStudent::getStudentId,
                classStudent -> classStudent, (existing, replacement) -> existing));
        // 找出学员id
        List<Integer> studentIds = classStudents.stream().map(ClassStudent::getStudentId).toList();
        // 转成VO返回
        return baseMapper.selectList(Wrappers.lambdaQuery(Student.class).in(Student::getUserId, studentIds)).stream().map(student -> {
            StudentVO studentVO = new StudentVO();
            BeanUtil.copyProperties(student, studentVO);
            // 获取对应的班级学生关系
            ClassStudent classStudent = studentClassMap.get(student.getUserId().intValue());
            if (classStudent != null) {
                // 设置班级学生状态
                studentVO.setClassStudentStatus(classStudent.getStatus());
                // 设置转班状态
                studentVO.setChangeClass(classStudent.getChangeClass());
                // 设置转班进入时间
                studentVO.setInClassTime(classStudent.getCreateTime());
            }
            return studentVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取学员列表
     *
     * @param page            分页参数
     * @param studentQueryDTO 班级id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    @Override
    public Page<StudentVO> getStudentList(Page page, StudentQueryDTO studentQueryDTO) {
        //先查门店下所有的，有教室就拿出教室里的，再根据stage，queryCondition查询
        List<ClassStudent> classStudentList = classStudentMapper.selectList(Wrappers.lambdaQuery(ClassStudent.class).eq(ClassStudent::getStoreId,
                studentQueryDTO.getStoreId()).eq(ObjectUtil.isNotNull(studentQueryDTO.getClassId()), ClassStudent::getClassId,
                studentQueryDTO.getClassId()).eq(ClassStudent::getStatus, StudentStatusEnum.FORMAL.getCode()));
        //取出id
        List<Integer> studentIds = classStudentList.stream().map(ClassStudent::getStudentId).toList();
        //拿出studentId和实体做map
        Map<Integer, ClassStudent> studentMap = classStudentList.stream().collect(Collectors.toMap(ClassStudent::getStudentId,
                classStudent -> classStudent, (existing, replacement) -> existing));
        if (studentIds.isEmpty()) {
            return new Page<>();
        }
        ArrayList<Integer> status = new ArrayList<>();
        status.add(StudentStatusEnum.FORMAL.getCode());
        status.add(StudentStatusEnum.TRIAL.getCode());
        status.add(StudentStatusEnum.SUSPENSION.getCode());

        //查出符合搜索条件的学员列表，使用工具方法构建查询条件
        Page<Student> resultPage = this.page(page,
                StudentQueryUtil.addDefaultStudentOrder(
                        StudentQueryUtil.addStudentSearchCondition(
                                Wrappers.<Student>query()
                                        //有班级，返回所有
                                        .in(ObjectUtil.isEmpty(studentQueryDTO.getClassId()), StudentSqlConstants.FIELD_STATUS, status)
                                        .in(StudentSqlConstants.FIELD_USER_ID, studentIds)
                                        .eq(ObjectUtil.isNotEmpty(studentQueryDTO.getStageId()), StudentSqlConstants.FIELD_STAGE_ID, studentQueryDTO.getStageId()),
                                studentQueryDTO.getQueryCondition()
                        )
                ));

        List<StudentVO> studentVos = resultPage.getRecords().stream().map(student -> {
            StudentVO studentVO = new StudentVO();
            BeanUtil.copyProperties(student, studentVO);
            studentVO.setChangeClass(studentMap.get(student.getUserId().intValue()).getChangeClass());
            studentVO.setClassId(studentQueryDTO.getClassId() != null ? studentQueryDTO.getClassId().intValue() : null);
            return studentVO;
        }).toList();

        return new Page<StudentVO>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal()).setRecords(studentVos);
    }

    /**
     * 根据门店id获取学员列表
     *
     * @param page         分页参数
     * @param studentQuery 班级id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    @Override
    public Page<StudentVO> getListByStoreId(Page<Student> page, StudentQuery studentQuery) {
        ArrayList<Integer> status = new ArrayList<>();

        if (ObjectUtil.isNotNull(studentQuery.getStatus()) && studentQuery.getStatus() == StudentStatusEnum.FORMAL.getCode()) {
            status.add(StudentStatusEnum.FORMAL.getCode());
            status.add(StudentStatusEnum.TRIAL.getCode());
            status.add(StudentStatusEnum.SUSPENSION.getCode());
        }

        if (ObjectUtil.isNotNull(studentQuery.getStatus()) && studentQuery.getStatus() == StudentStatusEnum.LEAVE.getCode()) {
            status.add(StudentStatusEnum.LEAVE.getCode());
        }

        Page<Student> studentPage = this.page(page,
                Wrappers.<Student>query()
                        .eq(StudentSqlConstants.FIELD_STORE_ID, studentQuery.getStoreId())
                        .eq(ObjectUtil.isNotEmpty(studentQuery.getIsRegularStudents()), StudentSqlConstants.FIELD_IS_REGULAR_STUDENTS, studentQuery.getIsRegularStudents())
                        .ne(StudentSqlConstants.FIELD_IS_REGULAR_STUDENTS, StudentRegularEnum.INTENTION.getCode()) // 排除意向会员
                        .in(ObjectUtil.isNotEmpty(status), StudentSqlConstants.FIELD_STATUS, status)
                        .ge(ObjectUtil.isNotEmpty(studentQuery.getIsArrears()) && studentQuery.getIsArrears() == StoreConstant.NOT_ARREARS, StudentSqlConstants.FIELD_COURSE_HOURS, StoreConstant.CODE)
                        .lt(ObjectUtil.isNotEmpty(studentQuery.getIsArrears()) && studentQuery.getIsArrears() == StoreConstant.ARREARS, StudentSqlConstants.FIELD_COURSE_HOURS, StoreConstant.CODE)
                        .eq(ObjectUtil.isNotEmpty(studentQuery.getStageId()), StudentSqlConstants.FIELD_STAGE_ID, studentQuery.getStageId())
                        .and(StringUtils.isNotBlank(studentQuery.getQueryCondition()),
                                wrapper -> wrapper.like(StudentSqlConstants.FIELD_NAME, studentQuery.getQueryCondition())
                                        .or().like(StudentSqlConstants.FIELD_PHONE, studentQuery.getQueryCondition())
                                        .or().like(StudentSqlConstants.FIELD_PINYIN_PRE, studentQuery.getQueryCondition())
                        )
                        .orderByAsc(StudentSqlConstants.ORDER_BY_PINYIN_SPECIAL_CHAR_LAST)
                        .orderByAsc(StudentSqlConstants.FIELD_PINYIN_PRE)
                        .orderByAsc(StudentSqlConstants.FIELD_USER_ID));

        //获取所有学生ID
        List<Long> studentIds = studentPage.getRecords().stream().map(Student::getUserId).toList();

        // 如果 studentIds 为空，直接返回空的 Page<StudentVO>
        if (studentIds.isEmpty()) {
            return new Page<StudentVO>(studentPage.getCurrent(), studentPage.getSize(), studentPage.getTotal()).setRecords(Collections.emptyList());
        }

        //查询每一个学生最后一次上课时间
        Map<Long, Long> studentTimetableMap =
                courseHoursLogMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursLog.class).select(StoreCourseHoursLog::getStudentId,
                        StoreCourseHoursLog::getTimetableId).in(StoreCourseHoursLog::getStudentId, studentIds).eq(StoreCourseHoursLog::getStoreId,
                        studentQuery.getStoreId()).eq(StoreCourseHoursLog::getLogType, CourseHoursLogTypeEnum.CONSUME.getCode()).isNotNull(StoreCourseHoursLog::getTimetableId).orderByDesc(StoreCourseHoursLog::getId).groupBy(StoreCourseHoursLog::getStudentId)).stream().collect(Collectors.toMap(StoreCourseHoursLog::getStudentId, StoreCourseHoursLog::getTimetableId));

        // 获取唯一的 timetableId 列表
        List<Long> uniqueTimetableIds = studentTimetableMap.values().stream().distinct().toList();

        Map<Long, String> timetableClassTimeMap = new HashMap<>();
        if (!uniqueTimetableIds.isEmpty()) {
            R<List<TimetableClassTimeVO>> timetableClassTimeVOList = remoteTimetableService.getListByIds(uniqueTimetableIds);
            if (timetableClassTimeVOList.isOk() && CollectionUtils.isNotEmpty(timetableClassTimeVOList.getData())) {
                timetableClassTimeMap = timetableClassTimeVOList.getData().stream().collect(Collectors.toMap(TimetableClassTimeVO::getId,
                        TimetableClassTimeVO::getFullClassTimeStr));
            }
        }

        Map<Long, String> finalTimetableClassTimeMap = timetableClassTimeMap;
        List<StudentVO> studentVOList = studentPage.getRecords().stream().map(student -> {
            StudentVO studentVO = new StudentVO();
            BeanUtil.copyProperties(student, studentVO);
            Long timetableId = studentTimetableMap.get(student.getUserId());
            studentVO.setFullClassTimeStr(Optional.ofNullable(timetableId).map(finalTimetableClassTimeMap::get).orElse(null));
            return studentVO;
        }).toList();

        return new Page<StudentVO>(studentPage.getCurrent(), studentPage.getSize(), studentPage.getTotal()).setRecords(studentVOList);
    }

    /**
     * 根据门店id获取试听学员和正式学员列表（不限制课时数）
     *
     * @param page         分页参数
     * @param studentQuery 班级id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    @Override
    public Page<StudentVO> getTrialStudentsWithZeroCourseHours(Page<Student> page, StudentQuery studentQuery) {
        // 保持与getListByStoreId相同的状态处理逻辑，但确保试听学员被包含
        ArrayList<Integer> status = new ArrayList<>();

        if (ObjectUtil.isNotNull(studentQuery.getStatus()) && studentQuery.getStatus() == StudentStatusEnum.FORMAL.getCode()) {
            status.add(StudentStatusEnum.FORMAL.getCode());
            status.add(StudentStatusEnum.TRIAL.getCode());
            status.add(StudentStatusEnum.SUSPENSION.getCode());
        }

        if (ObjectUtil.isNotNull(studentQuery.getStatus()) && studentQuery.getStatus() == StudentStatusEnum.LEAVE.getCode()) {
            status.add(StudentStatusEnum.LEAVE.getCode());
        }

        // 如果没有指定状态查询条件，默认包含试听状态的学员
        if (ObjectUtil.isNull(studentQuery.getStatus()) || status.isEmpty()) {
            status.add(StudentStatusEnum.TRIAL.getCode());
        }

        Page<Student> studentPage = this.page(page,
                Wrappers.<Student>query()
                        .eq(StudentSqlConstants.FIELD_STORE_ID, studentQuery.getStoreId())
                        // 学员类型筛选：包含试听学员和正式学员，都不限制课时
                        .and(wrapper -> {
                            if (ObjectUtil.isNotEmpty(studentQuery.getIsRegularStudents())) {
                                // 如果指定了学员类型，按指定类型查询（不限制课时）
                                wrapper.eq(StudentSqlConstants.FIELD_IS_REGULAR_STUDENTS, studentQuery.getIsRegularStudents());
                            } else {
                                // 默认查询：试听学员和正式学员（都不限制课时）
                                wrapper.in(StudentSqlConstants.FIELD_IS_REGULAR_STUDENTS,
                                    StudentRegularEnum.TRIAL.getCode(), StudentRegularEnum.FORMAL.getCode());
                            }
                        })
                        .ne(StudentSqlConstants.FIELD_IS_REGULAR_STUDENTS, StudentRegularEnum.INTENTION.getCode()) // 排除意向会员
                        .in(ObjectUtil.isNotEmpty(status), StudentSqlConstants.FIELD_STATUS, status)
                        // 保持原有的欠费状态判断逻辑
                        .ge(ObjectUtil.isNotEmpty(studentQuery.getIsArrears()) && studentQuery.getIsArrears() == StoreConstant.NOT_ARREARS, StudentSqlConstants.FIELD_COURSE_HOURS, StoreConstant.CODE)
                        .lt(ObjectUtil.isNotEmpty(studentQuery.getIsArrears()) && studentQuery.getIsArrears() == StoreConstant.ARREARS, StudentSqlConstants.FIELD_COURSE_HOURS, StoreConstant.CODE)
                        .eq(ObjectUtil.isNotEmpty(studentQuery.getStageId()), StudentSqlConstants.FIELD_STAGE_ID, studentQuery.getStageId())
                        .and(StringUtils.isNotBlank(studentQuery.getQueryCondition()),
                                wrapper -> wrapper.like(StudentSqlConstants.FIELD_NAME, studentQuery.getQueryCondition())
                                        .or().like(StudentSqlConstants.FIELD_PHONE, studentQuery.getQueryCondition())
                                        .or().like(StudentSqlConstants.FIELD_PINYIN_PRE, studentQuery.getQueryCondition())
                        )
                        .orderByAsc(StudentSqlConstants.ORDER_BY_PINYIN_SPECIAL_CHAR_LAST)
                        .orderByAsc(StudentSqlConstants.FIELD_PINYIN_PRE)
                        .orderByAsc(StudentSqlConstants.FIELD_USER_ID));

        //获取所有学生ID
        List<Long> studentIds = studentPage.getRecords().stream().map(Student::getUserId).toList();

        // 如果 studentIds 为空，直接返回空的 Page<StudentVO>
        if (studentIds.isEmpty()) {
            return new Page<StudentVO>(studentPage.getCurrent(), studentPage.getSize(), studentPage.getTotal()).setRecords(Collections.emptyList());
        }

        //查询每一个学生最后一次上课时间
        Map<Long, Long> studentTimetableMap =
                courseHoursLogMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursLog.class).select(StoreCourseHoursLog::getStudentId,
                        StoreCourseHoursLog::getTimetableId).in(StoreCourseHoursLog::getStudentId, studentIds).eq(StoreCourseHoursLog::getStoreId,
                        studentQuery.getStoreId()).eq(StoreCourseHoursLog::getLogType, CourseHoursLogTypeEnum.CONSUME.getCode()).isNotNull(StoreCourseHoursLog::getTimetableId).orderByDesc(StoreCourseHoursLog::getId).groupBy(StoreCourseHoursLog::getStudentId)).stream().collect(Collectors.toMap(StoreCourseHoursLog::getStudentId, StoreCourseHoursLog::getTimetableId));

        // 获取唯一的 timetableId 列表
        List<Long> uniqueTimetableIds = studentTimetableMap.values().stream().distinct().toList();

        Map<Long, String> timetableClassTimeMap = new HashMap<>();
        if (!uniqueTimetableIds.isEmpty()) {
            R<List<TimetableClassTimeVO>> timetableClassTimeVOList = remoteTimetableService.getListByIds(uniqueTimetableIds);
            if (timetableClassTimeVOList.isOk() && CollectionUtils.isNotEmpty(timetableClassTimeVOList.getData())) {
                timetableClassTimeMap = timetableClassTimeVOList.getData().stream().collect(Collectors.toMap(TimetableClassTimeVO::getId,
                        TimetableClassTimeVO::getFullClassTimeStr));
            }
        }

        Map<Long, String> finalTimetableClassTimeMap = timetableClassTimeMap;
        List<StudentVO> studentVOList = studentPage.getRecords().stream().map(student -> {
            StudentVO studentVO = new StudentVO();
            BeanUtil.copyProperties(student, studentVO);
            Long timetableId = studentTimetableMap.get(student.getUserId());
            studentVO.setFullClassTimeStr(Optional.ofNullable(timetableId).map(finalTimetableClassTimeMap::get).orElse(null));
            return studentVO;
        }).toList();

        return new Page<StudentVO>(studentPage.getCurrent(), studentPage.getSize(), studentPage.getTotal()).setRecords(studentVOList);
    }

    @Override
    public Page<StudentVO> getListBySchoolId(Page<Student> page, StudentQuery studentQuery) {
        ArrayList<Integer> status = new ArrayList<>();

        if (ObjectUtil.isNotNull(studentQuery.getStatus()) && studentQuery.getStatus() == StudentStatusEnum.FORMAL.getCode()) {
            status.add(StudentStatusEnum.FORMAL.getCode());
            status.add(StudentStatusEnum.TRIAL.getCode());
            status.add(StudentStatusEnum.SUSPENSION.getCode());
        }

        if (ObjectUtil.isNotNull(studentQuery.getStatus()) && studentQuery.getStatus() == StudentStatusEnum.LEAVE.getCode()) {
            status.add(StudentStatusEnum.LEAVE.getCode());
        }

        Page<Student> studentPage = this.page(page,
                Wrappers.<Student>query()
                        .eq(StudentSqlConstants.FIELD_SCHOOL_ID, studentQuery.getSchoolId())
                        .eq(ObjectUtil.isNotEmpty(studentQuery.getIsRegularStudents()), StudentSqlConstants.FIELD_IS_REGULAR_STUDENTS, studentQuery.getIsRegularStudents())
                        .ne(StudentSqlConstants.FIELD_IS_REGULAR_STUDENTS, StudentRegularEnum.INTENTION.getCode()) // 排除意向会员
                        .in(ObjectUtil.isNotEmpty(status), StudentSqlConstants.FIELD_STATUS, status)
                        .ge(ObjectUtil.isNotEmpty(studentQuery.getIsArrears()) && studentQuery.getIsArrears() == StoreConstant.NOT_ARREARS, StudentSqlConstants.FIELD_COURSE_HOURS, StoreConstant.CODE)
                        .lt(ObjectUtil.isNotEmpty(studentQuery.getIsArrears()) && studentQuery.getIsArrears() == StoreConstant.ARREARS, StudentSqlConstants.FIELD_COURSE_HOURS, StoreConstant.CODE)
                        .eq(ObjectUtil.isNotEmpty(studentQuery.getStageId()), StudentSqlConstants.FIELD_STAGE_ID, studentQuery.getStageId())
                        .and(StringUtils.isNotBlank(studentQuery.getQueryCondition()),
                                wrapper -> wrapper.like(StudentSqlConstants.FIELD_NAME, studentQuery.getQueryCondition())
                                        .or().like(StudentSqlConstants.FIELD_PHONE, studentQuery.getQueryCondition())
                                        .or().like(StudentSqlConstants.FIELD_PINYIN_PRE, studentQuery.getQueryCondition())
                        )
                        .orderByAsc(StudentSqlConstants.ORDER_BY_PINYIN_SPECIAL_CHAR_LAST)
                        .orderByAsc(StudentSqlConstants.FIELD_PINYIN_PRE)
                        .orderByAsc(StudentSqlConstants.FIELD_USER_ID));

        //获取所有学生ID
        List<Long> studentIds = studentPage.getRecords().stream().map(Student::getUserId).toList();

        // 如果 studentIds 为空，直接返回空的 Page<StudentVO>
        if (studentIds.isEmpty()) {
            return new Page<StudentVO>(studentPage.getCurrent(), studentPage.getSize(), studentPage.getTotal()).setRecords(Collections.emptyList());
        }

        //查询每一个学生最后一次上课时间
        // 获取学生所属的门店ID集合
        Set<Long> storeIds = studentPage.getRecords().stream().map(Student::getStoreId).collect(Collectors.toSet());

        Map<Long, Long> studentTimetableMap =
                courseHoursLogMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursLog.class).select(StoreCourseHoursLog::getStudentId,
                        StoreCourseHoursLog::getTimetableId).in(StoreCourseHoursLog::getStudentId, studentIds).in(StoreCourseHoursLog::getStoreId, storeIds).eq(StoreCourseHoursLog::getLogType, CourseHoursLogTypeEnum.CONSUME.getCode()).isNotNull(StoreCourseHoursLog::getTimetableId).orderByDesc(StoreCourseHoursLog::getId).groupBy(StoreCourseHoursLog::getStudentId)).stream().collect(Collectors.toMap(StoreCourseHoursLog::getStudentId, StoreCourseHoursLog::getTimetableId));

        // 获取唯一的 timetableId 列表
        List<Long> uniqueTimetableIds = studentTimetableMap.values().stream().distinct().toList();

        Map<Long, String> timetableClassTimeMap = new HashMap<>();
        if (!uniqueTimetableIds.isEmpty()) {
            R<List<TimetableClassTimeVO>> timetableClassTimeVOList = remoteTimetableService.getListByIds(uniqueTimetableIds);
            if (timetableClassTimeVOList.isOk() && CollectionUtils.isNotEmpty(timetableClassTimeVOList.getData())) {
                timetableClassTimeMap = timetableClassTimeVOList.getData().stream().collect(Collectors.toMap(TimetableClassTimeVO::getId,
                        TimetableClassTimeVO::getFullClassTimeStr));
            }
        }

        Map<Long, String> finalTimetableClassTimeMap = timetableClassTimeMap;
        List<StudentVO> studentVOList = studentPage.getRecords().stream().map(student -> {
            StudentVO studentVO = new StudentVO();
            BeanUtil.copyProperties(student, studentVO);
            Long timetableId = studentTimetableMap.get(student.getUserId());
            studentVO.setFullClassTimeStr(Optional.ofNullable(timetableId).map(finalTimetableClassTimeMap::get).orElse(null));
            return studentVO;
        }).toList();

        return new Page<StudentVO>(studentPage.getCurrent(), studentPage.getSize(), studentPage.getTotal()).setRecords(studentVOList);
    }

    @Override
    public List<StudentVO> getStudentListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        // 一次性查询所有学员课时记录并按学生ID分组
        Map<Long, List<CourseHoursStudent>> hoursMap = courseHoursStudentMapper.selectList(Wrappers.<CourseHoursStudent>lambdaQuery()
                        .in(CourseHoursStudent::getStudentId, ids))
                .stream()
                .collect(Collectors.groupingBy(CourseHoursStudent::getStudentId));

        // 转换并关联课时数据
        return listByIds(ids).stream()
                .map(student -> {
                    StudentVO vo = new StudentVO();
                    BeanUtil.copyProperties(student, vo);
                    vo.setCourseHoursList(hoursMap.getOrDefault(student.getUserId(), Collections.emptyList()));
                    return vo;
                }).toList();
    }


    /**
     * 补课获取学生列表
     *
     * @param ids
     * @return java.util.List<com.yuedu.store.vo.StudentVO>
     * <AUTHOR>
     * @date 2025/4/25 14:37
     */
    @Override
    public List<StudentVO> getStudentListByMakeup(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<StudentVO> studentVOList;
        studentVOList = listByIds(ids).stream().map(student -> {
            StudentVO studentVO = new StudentVO();
            BeanUtil.copyProperties(student, studentVO);
            // storeid查询门店相关信息
            CampusVO campusVO = campusService.details(student.getStoreId());
            if (Objects.nonNull(campusVO)) {
                studentVO.setSchCode(campusVO.getCampusNo());
                studentVO.setSchIdCode(campusVO.getXgjCampusId());
                studentVO.setSchName(campusVO.getCampusName());
            }
            if (Objects.nonNull(studentVO.getStageId())) {
                R<List<StageVO>> stageList = stageService.getStageList(studentVO.getStageId());
                if (stageList.isOk() && CollectionUtils.isNotEmpty(stageList.getData())) {
                    StageVO stageVO = stageList.getData()
                            .get(0);
                    studentVO.setStageName(stageVO.getStageName());
                }
            }
            return studentVO;
        }).toList();

        return studentVOList;
    }

    /**
     * 新增学员
     *
     * @param params 学员信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaveStudentErrorVO saveStudent(StudentUpdateDTO params) {
        log.info("开始新增学员，学员信息：name={}, phone={}, isRegularStudents={}, grade={}",
                params.getName(), params.getPhone(), params.getIsRegularStudents(), params.getGrade());

        // 门店Id
        Long storeId = StoreContextHolder.getStoreId();
        // 学校Id
        Long schoolId = StoreContextHolder.getSchoolId();


        //判断门店是否已存在
        Student student = baseMapper.selectOne(Wrappers.lambdaQuery(Student.class).eq(Student::getName, params.getName()).eq(Student::getPhone,
                params.getPhone()).eq(Student::getStoreId, storeId));

        if (ObjectUtil.isNull(student)) {
            Student createStudent = new Student();
            BeanUtil.copyProperties(params, createStudent);
            createStudent.setStoreId(storeId);
            createStudent.setSchoolId(schoolId);
            createStudent.setInitRegular(params.getIsRegularStudents());
            createStudent.setTransformTime(LocalDateTime.now());
            createStudent.setStatus(params.getIsRegularStudents());
            createStudent.setOrigin(StudentOriginEnum.MANUAL_INPUT.getCode());
            createStudent.setPinyinPre(PinyinUtil.getFirstLetter(params.getName(), ""));
            if (params.getGrade() != null) {
                createStudent.setGradeBaseDate(LocalDateTime.now());
            }
            baseMapper.insert(createStudent);

            // 调用转换统计服务，插入转换数据到store_student_conversion表
            try {
                StudentRegularEnum studentRegularEnum = StudentRegularEnum.getByCode(params.getIsRegularStudents());
                if (studentRegularEnum != null) {
                    switch (studentRegularEnum) {
                        case INTENTION:
                            studentConversionService.addIntentionMember(createStudent);
                            log.info("新增意向学员转换统计数据插入成功，学员ID：{}", createStudent.getUserId());
                            break;
                        case TRIAL:
                            studentConversionService.addTrialMember(createStudent);
                            log.info("新增试听学员转换统计数据插入成功，学员ID：{}", createStudent.getUserId());
                            break;
                        case FORMAL:
                            studentConversionService.addFormalMember(createStudent);
                            log.info("新增正式学员转换统计数据插入成功，学员ID：{}", createStudent.getUserId());
                            break;
                        default:
                            log.warn("未知的学员类型，跳过转换统计. 学员ID: {}, 类型代码: {}",
                                    createStudent.getUserId(), params.getIsRegularStudents());
                    }
                } else {
                    log.warn("无法找到对应的学员类型枚举. 学员ID: {}, 类型代码: {}",
                            createStudent.getUserId(), params.getIsRegularStudents());
                }
            } catch (Exception e) {
                log.error("插入学员转换统计数据失败，但不影响主流程. 学员ID: {}",
                        createStudent.getUserId(), e);
            }

            studentProducer.sendMessage(createStudent.getUserId());

            return getStudentError(StoreConstant.CODE, createStudent);
        }
        //意向会员
        if (student.getIsRegularStudents() == StudentRegularEnum.INTENTION.getCode()) {
            // 更新校区员工信息
            LambdaUpdateWrapper<Student> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(Student::getUserId, student.getUserId())
                    .eq(Student::getStoreId, storeId)
                    .set(Student::getSex, params.getSex())
                    .set(Student::getName, params.getName())
                    .set(Student::getPhone, params.getPhone())
                    .set(Student::getFulltimeSchool, params.getFulltimeSchool())
                    .set(Student::getResponsiblePerson, params.getResponsiblePerson())
                    .set(Student::getDescribe, params.getDescribe())
                    .set(Student::getIsRegularStudents, params.getIsRegularStudents())
                    .set(Student::getStatus, params.getIsRegularStudents())
                    .set(Student::getInitRegular, params.getIsRegularStudents())
                    .set(Student::getTransformTime, LocalDateTime.now())
                    .set(Student::getOrigin, StudentOriginEnum.MANUAL_INPUT.getCode())
                    .set(Student::getPinyinPre, PinyinUtil.getFirstLetter(params.getName(), ""))
                    .set(Student::getStageId, params.getStageId());

            if (params.getGrade() != null) {
                updateWrapper.set(Student::getGrade, params.getGrade());
                updateWrapper.set(Student::getGradeBaseDate, LocalDateTime.now());
            }

            baseMapper.update(student, updateWrapper);
            log.info("意向会员转换成功，学员ID：{}, 姓名：{}, 新学员类型：{}",
                    student.getUserId(), student.getName(), params.getIsRegularStudents());

            // 调用转换统计服务，插入转换数据到store_student_conversion表
            // 需要更新student对象以反映数据库中的最新状态，供转换服务使用
            student.setIsRegularStudents(params.getIsRegularStudents());
            student.setStatus(params.getIsRegularStudents());

            try {
                StudentRegularEnum targetRegularEnum = StudentRegularEnum.getByCode(params.getIsRegularStudents());
                if (targetRegularEnum != null) {
                    studentConversionService.convertMember(student, targetRegularEnum);
                    log.info("意向会员转换统计数据插入成功，学员ID：{}, 目标类型：{}",
                            student.getUserId(), targetRegularEnum.getDesc());
                } else {
                    log.warn("无法找到对应的学员类型枚举. 学员ID: {}, 类型代码: {}",
                            student.getUserId(), params.getIsRegularStudents());
                }
            } catch (Exception e) {
                log.error("插入意向会员转换统计数据失败，但不影响主流程. 学员ID: {}",
                        student.getUserId(), e);
                // 不抛出异常，确保转换统计失败不影响主业务流程
            }

            return getStudentError(StoreConstant.CODE, student);
        }
        return verifyStudent(student, params.getIsRegularStudents());
    }

    /**
     * 验证学员提示信息
     *
     * @param student           学员信息
     * @param isRegularStudents 是否为正式学员
     * @return SaveStudentErrorVO
     */
    private SaveStudentErrorVO verifyStudent(Student student, Integer isRegularStudents) {

        int code = ErrorCodeByStudentEnum.STUDENT_EXIST.getCode();

        if (student.getStatus().equals(StudentStatusEnum.DELETED.getCode())) {
            code = ErrorCodeByStudentEnum.STUDENT_UNKNOWN.getCode();
        }

        if (student.getIsRegularStudents().equals(StudentRegularEnum.TRIAL.getCode()) && isRegularStudents == StudentRegularEnum.FORMAL.getCode()) {
            code = ErrorCodeByStudentEnum.TRIAL_TRANSFORM_FORMAL.getCode();
        }

        //试听
        if (student.getIsRegularStudents() == StudentRegularEnum.TRIAL.getCode() && student.getStatus().equals(StudentStatusEnum.LEAVE.getCode())) {
            code = ErrorCodeByStudentEnum.TRIAL_HOURS.getCode();
        }

        //正式
        if (student.getIsRegularStudents() == StudentRegularEnum.FORMAL.getCode() && student.getStatus().equals(StudentStatusEnum.LEAVE.getCode())) {
            code = ErrorCodeByStudentEnum.FORMAL_EXIST.getCode();
        }
        return getStudentError(code, student);
    }

    /**
     * 查询门店运营数据-学员数据
     *
     * @param studentDTO
     * @return com.yuedu.store.vo.StoreStudentDataStatisticsVO
     * <AUTHOR>
     * @date 2025/3/10 17:24
     */
    @Override
    public StoreStudentDataStatisticsVO getStudentCountByStoreId(StudentDTO studentDTO) {

        // =============统计门店试听学员总数(校管家同步学员不统计)==========
        Long auditionStudentCount =
                baseMapper.selectCount(Wrappers.lambdaQuery(Student.class).eq(Student::getStoreId, studentDTO.getStoreId()).ne(Student::getOrigin,
                        StudentOriginEnum.XGJ_IMPORT.getCode()).eq(Student::getInitRegular, StudentStatusEnum.TRIAL.getCode()).between(Student::getCreateTime, studentDTO.getSelectDateStart(), studentDTO.getSelectDateEnd()));

        // =============统计门店试听转正式学员总数(校管家同步学员不统计)==========
        Long transformOfficialStudentCount = baseMapper.selectCount(Wrappers.lambdaQuery(Student.class).eq(Student::getStoreId,
                studentDTO.getStoreId()).ne(Student::getOrigin, StudentOriginEnum.XGJ_IMPORT.getCode()).eq(Student::getInitRegular,
                StudentStatusEnum.TRIAL.getCode()).eq(Student::getIsRegularStudents, StudentRegularEnum.FORMAL.getCode()).between(Student::getTransformTime, studentDTO.getSelectDateStart(), studentDTO.getSelectDateEnd()));

        // =============统计录入正式学员总数(校管家同步学员不统计)==========
        Long officialStudentCount =
                baseMapper.selectCount(Wrappers.lambdaQuery(Student.class).eq(Student::getStoreId, studentDTO.getStoreId()).ne(Student::getOrigin,
                        StudentOriginEnum.XGJ_IMPORT.getCode()).eq(Student::getInitRegular, StudentStatusEnum.FORMAL.getCode()).between(Student::getCreateTime, studentDTO.getSelectDateStart(), studentDTO.getSelectDateEnd()));

        StoreStudentDataStatisticsVO storeStudentDataStatisticsVO = new StoreStudentDataStatisticsVO();
        storeStudentDataStatisticsVO.setAuditionStudentCount(auditionStudentCount.intValue());
        storeStudentDataStatisticsVO.setOfficialStudentCount(transformOfficialStudentCount.intValue() + officialStudentCount.intValue());

        // =============统计试听转换率==========
        if (auditionStudentCount != StoreConstant.CODE) {
            storeStudentDataStatisticsVO.setConversionRate(Double.parseDouble(String.format("%.2f",
                    (double) transformOfficialStudentCount / auditionStudentCount * 100)));
        }

        return storeStudentDataStatisticsVO;
    }


    /**
     * 查询门店运营数据-课消数据
     *
     * @param studentDTO
     * @return com.yuedu.store.vo.StoreCourseHoursDataStatisticsVO
     * <AUTHOR>
     * @date 2025/3/11 10:49
     */
    @Override
    public StoreCourseHoursDataStatisticsVO getStudentCourseHoursByStoreId(StudentDTO studentDTO) {

        // =============统计消耗课次(指定时间段)==========
        List<StoreCourseHoursLog> storeCourseHoursLogList =
                courseHoursLogMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                        .eq(StoreCourseHoursLog::getStoreId, studentDTO.getStoreId())
                        .in(StoreCourseHoursLog::getLogType, Arrays.asList(CourseHoursLogTypeEnum.CONSUME.getCode(), CourseHoursLogTypeEnum.CANCEL.getCode()))
                        .between(StoreCourseHoursLog::getCreateTime, studentDTO.getSelectDateStart(), studentDTO.getSelectDateEnd()));

        int totalConsumeCourseHours = storeCourseHoursLogList.stream().mapToInt(StoreCourseHoursLog::getCourseHours).sum();

        // =============统计剩余课次(实时统计, 截止当前时间)==========
        List<Student> students =
                studentMapper.selectList(Wrappers.lambdaQuery(Student.class).eq(Student::getStoreId, studentDTO.getStoreId()));

        int totalCourseHours =
                students.stream().filter(e -> e.getCourseHours() > StoreConstant.CODE).mapToInt(Student::getCourseHours).sum();

        // =============统计欠费课次(指定时间段)==========
        int totalAverageCourseHoursCount =
                storeCourseHoursLogList.stream().filter(e -> Objects.isNull(e.getRelatedId()) && !Objects.equals(e.getNullify(), Integer.valueOf(YesNoEnum.YES.getCode()))).mapToInt(StoreCourseHoursLog::getCourseHours).sum();

        // 数据组装
        StoreCourseHoursDataStatisticsVO storeCourseHoursDataStatisticsVO = new StoreCourseHoursDataStatisticsVO();
        storeCourseHoursDataStatisticsVO.setConsumeCourseHoursCount(Math.abs(totalConsumeCourseHours));
        storeCourseHoursDataStatisticsVO.setResidueCourseHoursCount(totalCourseHours);
        storeCourseHoursDataStatisticsVO.setAverageCourseHoursCount(Math.abs(totalAverageCourseHoursCount));

        return storeCourseHoursDataStatisticsVO;
    }

    /**
     * 查询门店运营数据-续费数据
     *
     * @param studentDTO
     * @return com.yuedu.store.vo.StoreRenewDataStatisticsVO
     * <AUTHOR>
     * @date 2025/3/24 16:41
     */
    @Override
    public StoreRenewDataStatisticsVO getStoreRenewDataStatistics(StudentDTO studentDTO) {

        StoreRenewDataStatisticsVO storeRenewDataStatisticsVO = new StoreRenewDataStatisticsVO();

        // =============统计门店所有在读正式学员==========
        List<Student> studentList = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                .eq(Student::getStoreId, studentDTO.getStoreId())
                .eq(Student::getIsRegularStudents, StudentRegularEnum.FORMAL.getCode())
                .eq(Student::getStatus, StudentStatusEnum.FORMAL.getCode())
                .ne(Student::getOrigin, StudentOriginEnum.XGJ_IMPORT.getCode())
                .lt(Student::getTransformTime, studentDTO.getSelectDateStart())
        );

        if (CollectionUtils.isEmpty(studentList)) {
            return storeRenewDataStatisticsVO;
        }

        storeRenewDataStatisticsVO.setToBeRenewedCount(0);
        storeRenewDataStatisticsVO.setRenewCount(0);
        storeRenewDataStatisticsVO.setRenewalRate(0.0);

        // =============统计待续费人数(统计周期开始时间之前数据)==========
        List<Long> studentIdList = studentList.stream().map(Student::getUserId).distinct().toList();

        if (CollectionUtils.isNotEmpty(studentIdList)) {
            List<StoreCourseHoursLog> storeCourseHoursLogList = courseHoursLogMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                    .eq(StoreCourseHoursLog::getStoreId, studentDTO.getStoreId())
                    .in(StoreCourseHoursLog::getStudentId, studentIdList)
                    .lt(StoreCourseHoursLog::getCreateTime, studentDTO.getSelectDateStart())
                    .groupBy(StoreCourseHoursLog::getStudentId)
                    .having("sum(course_hours) <= " + studentDTO.getToBeRenewedCourseHours())
            );

            // 数据组装
            storeRenewDataStatisticsVO.setToBeRenewedCount(storeCourseHoursLogList.size());

            // =============统计续费人数(指定时间段)==========
            List<Long> toBeRenewedStudentIdList = storeCourseHoursLogList.stream().map(StoreCourseHoursLog::getStudentId).distinct().toList();
            if (CollectionUtils.isNotEmpty(toBeRenewedStudentIdList)) {
                List<StoreCourseHoursRecord> storeCourseHoursRecordList = courseHoursRecordService.list(Wrappers.lambdaQuery(StoreCourseHoursRecord.class)
                        .eq(StoreCourseHoursRecord::getStoreId, studentDTO.getStoreId())
                        .eq(StoreCourseHoursRecord::getOperationType, CourseHoursOperationEnum.ENROLL.getDesc())
                        .in(StoreCourseHoursRecord::getStudentId, toBeRenewedStudentIdList)
                        .between(StoreCourseHoursRecord::getCreateTime, studentDTO.getSelectDateStart(), studentDTO.getSelectDateEnd())
                        .groupBy(StoreCourseHoursRecord::getStudentId)
                );
                storeRenewDataStatisticsVO.setRenewCount(storeCourseHoursRecordList.size());
                // =============统计续费率==========
                if (CollectionUtils.isNotEmpty(storeCourseHoursLogList)) {
                    storeRenewDataStatisticsVO.setRenewalRate(Double.parseDouble(String.format("%.2f",
                            (double) storeCourseHoursRecordList.size() / storeCourseHoursLogList.size() * 100)));
                }
            }

        }


        return storeRenewDataStatisticsVO;
    }

    @Override
    public List<StudentVO> getStudentListByPhone(Long storeId, String phone) {
        List<Student> list = list(Wrappers.<Student>lambdaQuery()
                .eq(Student::getStoreId, storeId)
                .eq(Student::getPhone, phone));
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException("未查询到学员信息");
        }

        return list.stream().map(student -> {
            StudentVO studentVO = new StudentVO();
            BeanUtils.copyProperties(student, studentVO);
            return studentVO;
        }).collect(Collectors.toList());

    }

    @Override
    public String oldStudentLogin(Long storeId, Long uId) {

        Student student = getOne(Wrappers.<Student>lambdaQuery()
                .eq(Student::getStoreId, storeId)
                .eq(Student::getUserId, uId));

        if (Objects.isNull(student)) {
            throw new BizException("未查询到学员信息");
        }


        try {
            JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                    .subject(JSON.toJSONString(student))
                    .issueTime(new Date())
                    .expirationTime(new Date(System.currentTimeMillis() + 1000 * 60 * 60 * 24))  // 24小时
                    .build();

            JWSHeader jwsHeader = new JWSHeader.Builder(JWSAlgorithm.HS256)
                    .type(JWT)
                    .build();
            SignedJWT signedJWT = new SignedJWT(jwsHeader, claimsSet);
            signedJWT.sign(new MACSigner(secret));
            return signedJWT.serialize();
        } catch (Exception e) {
            log.error("测评token生成失败,{}", e.getMessage());
            throw new BizException("token生成失败");
        }
    }

    @Override
    public void registerSendCode(Long storeId, String phone) {

        Pattern pattern = Pattern.compile("^1[0-9]\\d{9}$");
        if (!pattern.matcher(phone).matches()) {
            throw new BizException("手机号错误！请输入正确的手机号");
        }
        Long schoolId = 0l;

        String key = String.format("%s%s:%s:%s", SecurityConstants.OAUTH_CODE_PREFIX, schoolId, storeId, phone);

        String code = RandomUtil.randomNumbers(6);
        log.info("测评手机号生成验证码成功:校区:{} 门店:{} 手机:{} 验证码:{}", schoolId, storeId, phone, code);

        CompletableFuture.runAsync(() -> {
            R r = remoteMessageService.sendSmsCode(phone, code);
            if (!r.isOk()) {
                log.error("测评远程调用发送验证码失败：校区：{} 门店：{} 手机：{} 验证码：{} 错误：{}", schoolId, storeId, phone, code, r.getMsg());
            }
        });

        redisTemplate.opsForValue().set(key, code, 3 * 60, TimeUnit.SECONDS);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R registerStudent(StudentDTO studentDTO) {
        studentDTO.setSchoolId(0l);

        String key = String.format("%s%s:%s:%s", SecurityConstants.OAUTH_CODE_PREFIX, studentDTO.getSchoolId(), studentDTO.getStoreId(), studentDTO.getPhone());

        String code = (String) redisTemplate.opsForValue().get(key);

        if (testCodeAuth) {
            if (Objects.isNull(code) || !code.equals(studentDTO.getCode())) {
                throw new BizException("验证码错误！请输入正确的验证码");
            }
        }

        List<Student> students = list(Wrappers.<Student>lambdaQuery()
                .eq(Student::getStoreId, studentDTO.getStoreId())
                .eq(Student::getPhone, studentDTO.getPhone())
                .eq(Student::getName, studentDTO.getName()));

        if (CollectionUtils.isNotEmpty(students)) {
            Student student = students.get(0);
            try {
                JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                        .subject(JSON.toJSONString(student))
                        .issueTime(new Date())
                        .expirationTime(new Date(System.currentTimeMillis() + 1000 * 60 * 60 * 24))  // 24小时
                        .build();

                JWSHeader jwsHeader = new JWSHeader.Builder(JWSAlgorithm.HS256)
                        .type(JWT)
                        .build();
                SignedJWT signedJWT = new SignedJWT(jwsHeader, claimsSet);
                signedJWT.sign(new MACSigner(secret));
                return R.ok(signedJWT.serialize(), "登录成功");
            } catch (Exception e) {
                log.error("测评注册token生成失败, 校区：{} 门店：{} 用户：{} 手机：{} 错误：{}", student.getSchoolId(), student.getStoreId(), student.getName(), student.getPhone(), e.getMessage());
                throw new BizException("注册失败，请重新注册！");
            } finally {
                redisTemplate.delete(key);
            }
        }

        CampusEntity campus = campusService.getById(studentDTO.getStoreId());
        if (Objects.isNull(campus)) {
            throw new BizException("未查询到校区信息");
        }

        Student student = new Student();
        student.setSchoolId(Objects.isNull(campus.getSchoolId()) ? null : campus.getSchoolId().longValue());
        student.setStoreId(campus.getId());
        student.setName(studentDTO.getName());
        student.setPinyinPre(PinyinUtil.getFirstLetter(studentDTO.getName(), ""));
        student.setSex(studentDTO.getSex());
        student.setGrade(studentDTO.getGrade());
        student.setPhone(studentDTO.getPhone());
        student.setFulltimeSchool(studentDTO.getFulltimeSchool());
        student.setIsRegularStudents(StudentRegularEnum.INTENTION.getCode());
        student.setSource(StudentSourceEnum.SOURCE_1.getCode());
        student.setOrigin(StudentOriginEnum.TEST_SAVE.getCode());
        student.setBirthday(studentDTO.getBirthday());
        student.setGradeBaseDate(LocalDateTime.now());
        student.setInitRegular(StudentRegularEnum.INTENTION.getCode());
        save(student);
        log.info("测评注册意向会员基本信息保存成功，学员ID：{}, 姓名：{}, 手机号：{}",
                student.getUserId(), student.getName(), student.getPhone());

        // 调用转换统计服务，插入意向学员转换数据到store_student_conversion表
        try {
            studentConversionService.addIntentionMember(student);
            log.info("测评注册意向学员转换统计数据插入成功，学员ID：{}", student.getUserId());
        } catch (Exception e) {
            log.error("插入测评注册意向学员转换统计数据失败，但不影响主流程. 学员ID: {}",
                    student.getUserId(), e);
            // 不抛出异常，确保转换统计失败不影响主业务流程
        }

        // 向store_student_track_record表插入跟踪记录
        StoreStudentTrackRecordDTO trackRecordDTO = new StoreStudentTrackRecordDTO();
        trackRecordDTO.setUserId(student.getUserId().intValue());
        trackRecordDTO.setStoreId(student.getStoreId().intValue());
        trackRecordDTO.setSchoolId(student.getSchoolId() != null ? student.getSchoolId().intValue() : null);
        trackRecordDTO.setCommunicationRecords("测评注册意向会员");

        storeStudentTrackRecordService.add(trackRecordDTO);
        log.info("测评注册意向会员跟踪记录插入成功，学员ID：{}", student.getUserId());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                redisTemplate.delete(key);
            }
        });


        try {
            JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                    .subject(JSON.toJSONString(student))
                    .issueTime(new Date())
                    .expirationTime(new Date(System.currentTimeMillis() + 1000 * 60 * 60 * 24))  // 24小时
                    .build();

            JWSHeader jwsHeader = new JWSHeader.Builder(JWSAlgorithm.HS256)
                    .type(JWT)
                    .build();
            SignedJWT signedJWT = new SignedJWT(jwsHeader, claimsSet);
            signedJWT.sign(new MACSigner(secret));
            return R.ok(signedJWT.serialize(), "注册成功");
        } catch (Exception e) {
            log.error("测评注册token生成失败, 校区：{} 门店：{} 用户：{} 手机：{} 错误：{}", student.getSchoolId(), student.getStoreId(), student.getName(), student.getPhone(), e.getMessage());
            throw new BizException("注册失败，请重新注册！");
        }


    }

    /**
     * 根据班级id列表获取测评学员列表
     *
     * @param studentTestDTO 手机号/姓名
     *                       test_status 测评状态、
     *                       regular学员类型（意向、试听、正式）
     * @return 测评学员列表
     */
    @Override
    public Page<StudentTestVO> getTestByClassId(Page page, StudentTestDTO studentTestDTO) {
        // 处理参数
        String condition = studentTestDTO.getCondition();
        Integer testStatus = studentTestDTO.getTestStatus();
        Integer regular = studentTestDTO.getRegular();
        return studentMapper.getTestStudentList(page, studentTestDTO.getStoreId(), condition, testStatus, regular);
    }

    /**
     * 根据学员id获取测评详情
     *
     * @param stuId 学员id storeId 门店id
     * @return 测评学员列表
     */
    @Override
    public StudentTestDetailVO testDetail(Integer stuId, Long storeId) {
        Student student = getStudentInfo(storeId, Long.valueOf(stuId));
        StudentTestDetailVO studentTestDetailVO = new StudentTestDetailVO();
        BeanUtil.copyProperties(student, studentTestDetailVO);

        // 获取测评信息
        PaperUser paperUser = paperUserMapper.selectOne(Wrappers.lambdaQuery(PaperUser.class)
                .eq(PaperUser::getUserId, student.getUserId())
                .eq(PaperUser::getTerminalId, storeId));
        if (paperUser == null) {
            return studentTestDetailVO;
        }
        //判断时间不为空
        String testTime = "";
        if (paperUser.getTestTime() != null && paperUser.getTestStartTime() != null) {
            testTime = DateUtil.format(paperUser.getTestStartTime(), "yyyy-MM-dd HH:mm") + "-" + DateUtil.format(paperUser.getTestTime(), "HH:mm");
        }
        studentTestDetailVO.setTestNewTime(testTime);
        studentTestDetailVO.setTestScore(paperUser.getTestScore());
        studentTestDetailVO.setIsReport(paperUser.getIsReport());
        studentTestDetailVO.setGrade(StudentUtils.grade(studentTestDetailVO.getGrade(), studentTestDetailVO.getGradeBaseDate()));
        studentTestDetailVO.setTestNumber(paperUser.getTestNumber());
        studentTestDetailVO.setParentTestNumber(paperUser.getParentTestNumber());

        // 获取儿童报告
        List<PaperChildrenAnswer> paperChildrenAnswer = paperChildrenAnswerMapper.selectList(Wrappers.lambdaQuery(PaperChildrenAnswer.class)
                .eq(PaperChildrenAnswer::getPaperUserId, paperUser.getPaperUserId())
                .eq(PaperChildrenAnswer::getTerminalId, storeId));

        // 获取家长报告
        List<PaperParentAnswer> paperParentAnswer = paperParentAnswerMapper.selectList(Wrappers.lambdaQuery(PaperParentAnswer.class)
                .eq(PaperParentAnswer::getPaperUserId, paperUser.getPaperUserId())
                .eq(PaperParentAnswer::getTerminalId, storeId));

        // 合并两个列表
        List<PaperAnswerVO> paperChildAnswer = new ArrayList<>();

        for (PaperChildrenAnswer childrenAnswer : paperChildrenAnswer) {
            PaperAnswerVO paperAnswer = new PaperAnswerVO();
            paperAnswer.setAnswerId(childrenAnswer.getChildrenAnswerId());
            paperAnswer.setPaperId(childrenAnswer.getPaperId());
            paperAnswer.setPaperName(childrenAnswer.getChildrenPaperName());
            paperAnswer.setTestScore(childrenAnswer.getTestScore());
            paperAnswer.setChildrenAnswerId(childrenAnswer.getChildrenAnswerId());
            String testTimeTamp = "";
            if (childrenAnswer.getTestEndtime() != null) {
                testTimeTamp = DateUtil.format(childrenAnswer.getTestEndtime(), "yyyy/MM/dd HH:mm");
            }
            paperAnswer.setTestTime(testTimeTamp);
            paperAnswer.setTestEndtime(childrenAnswer.getTestEndtime());
            paperAnswer.setTestLevel(childrenAnswer.getTestLevel());
            paperAnswer.setIsReport(childrenAnswer.getIsReport());
            paperAnswer.setPaperUserId(childrenAnswer.getPaperUserId());
            paperAnswer.setTerminalId(childrenAnswer.getTerminalId());
            paperAnswer.setIsChildren(true);
            paperChildAnswer.add(paperAnswer);
        }

        for (PaperParentAnswer parentAnswer : paperParentAnswer) {
            PaperAnswerVO paperAnswer = new PaperAnswerVO();
            paperAnswer.setAnswerId(parentAnswer.getParentAnswerId());
            paperAnswer.setPaperId(parentAnswer.getPaperId());
            paperAnswer.setPaperName(parentAnswer.getParentPaperName());
            List<PaperChildrenAnswer> childrenInfo = paperChildrenAnswerMapper.selectList(Wrappers.lambdaQuery(PaperChildrenAnswer.class)
                    .eq(PaperChildrenAnswer::getParentId, parentAnswer.getParentAnswerId())
                    .eq(PaperChildrenAnswer::getTerminalId, storeId));
            if (childrenInfo != null && !childrenInfo.isEmpty()) {
                paperAnswer.setChildrenAnswerId(childrenInfo.get(0).getChildrenAnswerId());
            }
            String testTimeTamps = "";
            if (parentAnswer.getTestEndtime() != null) {
                testTimeTamps = DateUtil.format(parentAnswer.getTestEndtime(), "yyyy/MM/dd HH:mm");
            }
            paperAnswer.setTestTime(testTimeTamps);
            paperAnswer.setTestEndtime(parentAnswer.getTestEndtime());
            paperAnswer.setIsReport(parentAnswer.getIsReport());
            paperAnswer.setPaperUserId(parentAnswer.getPaperUserId());
            paperAnswer.setTerminalId(parentAnswer.getTerminalId());
            paperAnswer.setIsChildren(false);
            paperChildAnswer.add(paperAnswer);
        }
        // 按照 testEndtime 倒序排序
        paperChildAnswer.sort(Comparator.comparing(PaperAnswerVO::getTestEndtime).reversed());

        studentTestDetailVO.setChildrenAnswerList(paperChildAnswer.stream()
                .filter(PaperAnswerVO::getIsChildren)
                .toList());

        studentTestDetailVO.setParentAnswerList(paperChildAnswer.stream()
                .filter(paperAnswer -> !paperAnswer.getIsChildren())
                .toList());

        studentTestDetailVO.setParentChildList(paperChildAnswer);

        return studentTestDetailVO;
    }


    /**
     * (测评)根据学员id获取报告列表
     *
     * @return 学员报告列表
     */
    @Override
    public StudentChildDetailVO getChildList(Long storeId, Integer stuId) {
        Student student = getStudentInfo(storeId, Long.valueOf(stuId));
        StudentChildDetailVO studentTestDetailVO = new StudentChildDetailVO();
        BeanUtil.copyProperties(student, studentTestDetailVO);
        // 获取测评信息
        PaperUser paperUser = paperUserMapper.selectOne(Wrappers.lambdaQuery(PaperUser.class)
                .eq(PaperUser::getUserId, student.getUserId())
                .eq(PaperUser::getTerminalId, storeId));
        if (paperUser == null) {
            return studentTestDetailVO;
        }

        // 获取儿童报告
        List<PaperChildrenAnswer> paperChildrenAnswer = paperChildrenAnswerMapper.selectList(Wrappers.lambdaQuery(PaperChildrenAnswer.class)
                .eq(PaperChildrenAnswer::getPaperUserId, paperUser.getPaperUserId())
                .eq(PaperChildrenAnswer::getTerminalId, storeId)
                .orderByDesc(PaperChildrenAnswer::getTestEndtime));

        List<PaperAnswerVO> paperChildAnswer = new ArrayList<>();

        for (PaperChildrenAnswer childrenAnswer : paperChildrenAnswer) {
//            获取开始跟结束时间的差值
            String formattedDuration = formatDuration(childrenAnswer.getTestStarttime(), childrenAnswer.getTestEndtime());
            PaperAnswerVO paperAnswer = new PaperAnswerVO();
            paperAnswer.setAnswerId(childrenAnswer.getChildrenAnswerId());
            paperAnswer.setPaperId(childrenAnswer.getPaperId());
            paperAnswer.setPaperName(childrenAnswer.getChildrenPaperName());
            paperAnswer.setTestScore(childrenAnswer.getTestScore());
            paperAnswer.setChildrenAnswerId(childrenAnswer.getChildrenAnswerId());
            paperAnswer.setTestEndtime(childrenAnswer.getTestEndtime());
            paperAnswer.setTestTime(formattedDuration);
            paperAnswer.setTestLevel(childrenAnswer.getTestLevel());
            paperAnswer.setIsReport(childrenAnswer.getIsReport());
            paperAnswer.setPaperUserId(childrenAnswer.getPaperUserId());
            paperAnswer.setTerminalId(childrenAnswer.getTerminalId());
            paperAnswer.setIsChildren(true);
            paperChildAnswer.add(paperAnswer);
        }

        studentTestDetailVO.setChildrenAnswerList(paperChildAnswer.stream()
                .filter(PaperAnswerVO::getIsChildren)
                .toList());

        return studentTestDetailVO;
    }

    public String formatDuration(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return "00:00:00";
        }

        Duration duration = Duration.between(startTime, endTime);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        long seconds = duration.getSeconds() % 60;

        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }

    /**
     * (测评)根据学员id获取家长报告列表
     *
     * @return 家长报告列表
     */
    @Override
    public StudentParentDetailVO getParentList(Long storeId, Integer stuId) {
        Student student = getStudentInfo(storeId, Long.valueOf(stuId));
        StudentParentDetailVO studentTestDetailVO = new StudentParentDetailVO();
        BeanUtil.copyProperties(student, studentTestDetailVO);

        // 获取测评信息
        PaperUser paperUser = paperUserMapper.selectOne(Wrappers.lambdaQuery(PaperUser.class)
                .eq(PaperUser::getUserId, student.getUserId())
                .eq(PaperUser::getTerminalId, storeId));
        if (paperUser == null) {
            return studentTestDetailVO;
        }
        // 获取家长报告
        List<PaperParentAnswer> paperParentAnswer = paperParentAnswerMapper.selectList(Wrappers.lambdaQuery(PaperParentAnswer.class)
                .eq(PaperParentAnswer::getPaperUserId, paperUser.getPaperUserId())
                .eq(PaperParentAnswer::getTerminalId, storeId)
                .orderByDesc(PaperParentAnswer::getTestEndtime));

        // 合并两个列表
        List<PaperAnswerVO> paperChildAnswer = new ArrayList<>();
        for (PaperParentAnswer parentAnswer : paperParentAnswer) {
            PaperAnswerVO paperAnswer = new PaperAnswerVO();
            paperAnswer.setAnswerId(parentAnswer.getParentAnswerId());
            paperAnswer.setPaperId(parentAnswer.getPaperId());
            paperAnswer.setPaperName(parentAnswer.getParentPaperName());
            List<PaperChildrenAnswer> childrenInfo = paperChildrenAnswerMapper.selectList(Wrappers.lambdaQuery(PaperChildrenAnswer.class)
                    .eq(PaperChildrenAnswer::getParentId, parentAnswer.getParentAnswerId())
                    .eq(PaperChildrenAnswer::getTerminalId, storeId));
            if (childrenInfo != null && !childrenInfo.isEmpty()) {
                paperAnswer.setChildrenAnswerId(childrenInfo.get(0).getChildrenAnswerId());
            }
            paperAnswer.setTestEndtime(parentAnswer.getTestEndtime());
            paperAnswer.setIsReport(parentAnswer.getIsReport());
            paperAnswer.setPaperUserId(parentAnswer.getPaperUserId());
            paperAnswer.setTerminalId(parentAnswer.getTerminalId());
            paperAnswer.setIsChildren(false);
            paperChildAnswer.add(paperAnswer);
        }
        studentTestDetailVO.setParentAnswerList(paperChildAnswer.stream()
                .filter(paperAnswer -> !paperAnswer.getIsChildren())
                .toList());

        return studentTestDetailVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStore(Long schoolId, Long storeId, Long newStoreId, Long studentId) {

        boolean exists = campusService.exists(Wrappers.lambdaQuery(CampusEntity.class)
                .eq(CampusEntity::getSchoolId, schoolId)
                .eq(CampusEntity::getId, newStoreId));

        if (!exists) {
            throw new BizException("未查询到校区门店信息");
        }

        Student student = getStudentInfo(storeId, studentId);

        //判断学员是否在新门店存在
        Student existsStudent = baseMapper.selectOne(Wrappers.lambdaQuery(Student.class)
                .eq(Student::getName, student.getName())
                .eq(Student::getPhone, student.getPhone())
                .eq(Student::getStoreId, newStoreId));

        if (existsStudent != null) {
            throw new BizException("该手机号该姓名学生已在转校校区存在，请核对后重试");
        }

        courseHoursRecordService.changeStore(newStoreId, studentId);

        int result = baseMapper.update(Wrappers.lambdaUpdate(Student.class)
                .eq(Student::getUserId, student.getUserId())
                .set(Student::getStoreId, newStoreId));

        if (result != StoreConstant.SAVE_SUCCESS_ONE) {
            throw new BizException("操作失败 !");
        }
    }

    /**
     * 学员报表分页
     */
    @Override
    public Page<StudentFormVO> getStudentPage(Page<Student> page, StudentQuery studentQuery) {
        List<Integer> status = new ArrayList<>();
        if (ObjectUtil.isNotNull(studentQuery.getStatus()) && studentQuery.getStatus() == StudentStatusEnum.FORMAL.getCode()) {
            status.add(StudentStatusEnum.FORMAL.getCode());
            status.add(StudentStatusEnum.TRIAL.getCode());
            status.add(StudentStatusEnum.SUSPENSION.getCode());
        }

        if (ObjectUtil.isNotNull(studentQuery.getStatus()) && studentQuery.getStatus() == StudentStatusEnum.LEAVE.getCode()) {
            status.add(StudentStatusEnum.LEAVE.getCode());
        }

        //获取学员报表信息
        Page<Student> studentPage = page(page, Wrappers.<Student>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(studentQuery.getStoreId()), Student::getStoreId, studentQuery.getStoreId())
                .and(CharSequenceUtil.isNotBlank(studentQuery.getName()), wrapper -> wrapper
                        .like(Student::getName, studentQuery.getName())
                        .or()
                        .like(Student::getPinyinPre, studentQuery.getName())
                )
                .eq(ObjectUtil.isNotEmpty(studentQuery.getPhone()), Student::getPhone, studentQuery.getPhone())
                .eq(ObjectUtil.isNotEmpty(studentQuery.getIsRegularStudents()), Student::getIsRegularStudents, studentQuery.getIsRegularStudents())
                .in(ObjectUtil.isNotEmpty(status), Student::getStatus, status)
                .eq(ObjectUtil.isNotEmpty(studentQuery.getStageId()), Student::getStageId, studentQuery.getStageId())
                .eq(ObjectUtil.isNotEmpty(studentQuery.getResponsiblePerson()), Student::getResponsiblePerson, studentQuery.getResponsiblePerson())
                .le(ObjectUtil.isNotEmpty(studentQuery.getCourseHours()), Student::getCourseHours, studentQuery.getCourseHours())
                .le(studentQuery.getEndDate() != null && !studentQuery.getEndDate().isEmpty(), Student::getCreateTime, studentQuery.getEndDate() + " 23:59:59")
                .ge(studentQuery.getEndDate() != null && !studentQuery.getStartDate().isEmpty(), Student::getCreateTime, studentQuery.getStartDate())
                .orderByDesc(Student::getCreateTime)
                .orderByDesc(Student::getUserId)
        );
        R<List<StageVO>> stageList = stageService.getStageList(StageProductEnum.STAGE_PRODUCT_ENUM_1.code);
        Map<Integer, String> stageMap;
        if (stageList != null && stageList.getData() != null) {
            stageMap = stageList.getData().stream()
                    .collect(Collectors.toMap(StageVO::getId, StageVO::getStageName));
        } else {
            stageMap = new HashMap<>();
        }
        Page<StudentFormVO> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<StudentFormVO> resultRecords = studentPage.getRecords().stream()
                .map(entity -> {
                    StudentFormVO vo = new StudentFormVO();
                    BeanUtil.copyProperties(entity, vo);
                    if (ObjectUtil.isNotNull(entity.getStageId())) {
                        vo.setStageName(stageMap.getOrDefault(entity.getStageId(), ""));
                    }
                    BigDecimal amount = entity.getAmount() != null ? entity.getAmount() : BigDecimal.ZERO;
                    BigDecimal truncated = amount.abs().setScale(2, RoundingMode.DOWN);
                    vo.setAmount(truncated);
                    Integer studentStatus = entity.getStatus();
                    if (studentStatus != null) {
                        if (studentStatus == StudentStatusEnum.FORMAL.getCode() || studentStatus == StudentStatusEnum.TRIAL.getCode() || studentStatus == StudentStatusEnum.SUSPENSION.getCode()) {
                            vo.setStatus(StudentStatusEnum.FORMAL.getCode());
                        }
                        if (studentStatus == StudentStatusEnum.LEAVE.getCode()) {
                            vo.setStatus(StudentStatusEnum.LEAVE.getCode());
                        }
                    }
                    return vo;
                }).toList();
        resultPage.setRecords(resultRecords);
        return resultPage;
    }

    /**
     * 学员报表
     */
    @Override
    public List<StudentFormVO> getStudentList(StudentQuery studentQuery) {
        List<Integer> status = new ArrayList<>();
        if (ObjectUtil.isNotNull(studentQuery.getStatus()) && studentQuery.getStatus() == StudentStatusEnum.FORMAL.getCode()) {
            status.add(StudentStatusEnum.FORMAL.getCode());
            status.add(StudentStatusEnum.TRIAL.getCode());
            status.add(StudentStatusEnum.SUSPENSION.getCode());
        }
        if (ObjectUtil.isNotNull(studentQuery.getStatus()) && studentQuery.getStatus() == StudentStatusEnum.LEAVE.getCode()) {
            status.add(StudentStatusEnum.LEAVE.getCode());
        }
        studentQuery.setStatusList(status);
        CampusQuery campusQuery = new CampusQuery();
        campusQuery.setCampusId(studentQuery.getStoreId());
        //获取性别Map
        R<List<SysDictItem>> sexList = remoteDictService.getDictsByType("gender");
        Map<String, String> sexMap = sexList.getData().stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getLabel));
        //获取年级Map
        R<List<SysDictItem>> gradeList = remoteDictService.getDictsByType("student_grade_type");
        Map<String, String> gradeMap = gradeList.getData().stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getLabel));
        //获取负责老师Map
        List<EmployeeVO> teacherList = employeeService.getEmployeeByCampusId(campusQuery, EmployeeCampusStatusEnum.ACTIVE.getCode());
        Map<Long, String> teacherMap = teacherList.stream().collect(Collectors.toMap(EmployeeVO::getUserId, EmployeeVO::getName));
        //获取学员类型Map
        R<List<SysDictItem>> studentTypeList = remoteDictService.getDictsByType("regular_students");
        Map<String, String> studentTypeMap = studentTypeList.getData().stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getLabel));
        //获取学员状态Map
        R<List<SysDictItem>> studentStatusList = remoteDictService.getDictsByType("miniapp_student_status");
        Map<String, String> studentStatusMap = studentStatusList.getData().stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getLabel));
        R<List<StageVO>> stageList = stageService.getStageList(StageProductEnum.STAGE_PRODUCT_ENUM_1.code);
        //获取课程类型
        R<List<CourseTypeDTO>> courseTypeResult = remoteCourseTypeService.getAll();
        Map<Integer, String> courseTypeMap;
        if (courseTypeResult.isOk()) {
            courseTypeMap = courseTypeResult.getData().stream().collect(Collectors.toMap(CourseTypeDTO::getId, CourseTypeDTO::getName));
        } else {
            courseTypeMap = Collections.emptyMap();
        }
        Map<Integer, String> stageMap;
        if (stageList != null && stageList.getData() != null) {
            stageMap = stageList.getData().stream()
                    .collect(Collectors.toMap(StageVO::getId, StageVO::getStageName));
        } else {
            stageMap = new HashMap<>();
        }
        List<StudentFormVO> studentList = handleStudentList(studentQuery);
        return studentList.stream()
                .map(entity -> {
                    StudentFormVO vo = new StudentFormVO();
                    BeanUtil.copyProperties(entity, vo);
                    if (ObjectUtil.isNotNull(entity.getStageId())) {
                        vo.setStageName(stageMap.getOrDefault(entity.getStageId(), ""));
                    }
                    BigDecimal amount = entity.getAmount() != null ? entity.getAmount() : BigDecimal.ZERO;
                    BigDecimal truncated = amount.abs().setScale(2, RoundingMode.DOWN);
                    vo.setAmount(truncated);
                    Integer studentStatus = entity.getStatus();
                    if (studentStatus != null) {
                        if (studentStatus == StudentStatusEnum.FORMAL.getCode() || studentStatus == StudentStatusEnum.TRIAL.getCode() || studentStatus == StudentStatusEnum.SUSPENSION.getCode()) {
                            vo.setStatus(StudentStatusEnum.FORMAL.getCode());
                        }
                        if (studentStatus == StudentStatusEnum.LEAVE.getCode()) {
                            vo.setStatus(StudentStatusEnum.LEAVE.getCode());
                        }
                        vo.setStatusName(studentStatusMap.getOrDefault(vo.getStatus().toString(), "--"));
                    }

                    if (entity.getSex() != null) {
                        vo.setSexName(sexMap.getOrDefault(entity.getSex().toString(), "--"));
                    }
                    if (entity.getGrade() != null) {
                        vo.setGradeName(gradeMap.getOrDefault(entity.getGrade().toString(), "--"));
                    }
                    if (entity.getResponsiblePerson() != null) {
                        vo.setResponsiblePersonName(teacherMap.getOrDefault(entity.getResponsiblePerson(), "--"));
                    }
                    if (entity.getIsRegularStudents() != null) {
                        vo.setIsRegularStudentsName(studentTypeMap.getOrDefault(entity.getIsRegularStudents().toString(), "--"));
                    }
                    vo.setStudentCourseTypeName(courseTypeMap.get(entity.getStudentCourseType()));
                    return vo;
                }).toList();
    }

    @Override
    public List<StudentMemberDTO> getStudentMemberList(List<Long> studentIdList) {
        List<Student> studentList = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                        .in(Student::getUserId, studentIdList)
                .notIn(Student::getStatus, List.of(StudentStatusEnum.LEAVE.getCode(),
                        StudentStatusEnum.SUSPENSION.getCode()))
                .isNotNull(Student::getSchStudentId));

        if (CollectionUtils.isEmpty(studentList)) {
            return Collections.emptyList();
        }
        List<String> schStudentIdList = studentList.stream()
                .map(Student::getSchStudentId)
                .collect(Collectors.toList());

        Map<String, Member> memberMap = memberMapper.selectList(Wrappers.lambdaQuery(Member.class)
                        .in(Member::getSchStudentId, schStudentIdList)
                        .isNotNull(Member::getPublicOpenId))
                .stream()
                .collect(Collectors.toMap(Member::getSchStudentId, Function.identity(), (v1, v2) -> v2));
        return studentList
                .stream()
                .map(student -> {
                    StudentMemberDTO studentMemberDTO = new StudentMemberDTO();
                    BeanUtil.copyProperties(student, studentMemberDTO);
                    Member member = memberMap.get(student.getSchStudentId());
                    if (member != null) {
                        studentMemberDTO.setPublicOpenId(member.getPublicOpenId());
                    }
                    return studentMemberDTO;
                }).toList();
    }

    public List<StudentFormVO> handleStudentList(StudentQuery studentQuery) {
        // 获取学员报表信息
        List<StudentFormVO> studentList = studentMapper.getStudentHoursList(studentQuery);
        //分组
        Map<Long, List<StudentFormVO>> studentFormVoMap = studentList.stream()
                .collect(Collectors.groupingBy(StudentFormVO::getUserId, LinkedHashMap::new, Collectors.toList()));
        //循环studentFormVoMap处理数据逻辑
        List<StudentFormVO> result = new ArrayList<>();
        for (Map.Entry<Long, List<StudentFormVO>> entry : studentFormVoMap.entrySet()) {
            StudentFormVO newVO = new StudentFormVO();
            StudentFormVO firstStudent = entry.getValue().get(0);
            newVO.setUserId(firstStudent.getUserId());
            newVO.setStoreId(firstStudent.getStoreId());
            newVO.setName(firstStudent.getName());
            newVO.setCourseHours(firstStudent.getCourseHours());
            newVO.setAmount(firstStudent.getAmount());
            newVO.setPhone(firstStudent.getPhone());
            newVO.setSex(firstStudent.getSex());
            newVO.setGrade(firstStudent.getGrade());
            newVO.setStageId(firstStudent.getStageId());
            newVO.setResponsiblePerson(firstStudent.getResponsiblePerson());
            newVO.setIsRegularStudents(firstStudent.getIsRegularStudents());
            newVO.setStatus(firstStudent.getStatus());
            newVO.setDescribe(firstStudent.getDescribe());
            newVO.setCreateTime(firstStudent.getCreateTime());
            List<StudentFormVO> records = entry.getValue();
            for (StudentFormVO record : records) {
                if (record.getStudentCourseType() != null) {
                    if (record.getStudentCourseType() == 1) {
                        newVO.setRegularFormal(record.getStudentFormal());
                        newVO.setRegularGift(record.getStudentGift());
                    }
                    if (record.getStudentCourseType() == 2) {
                        newVO.setExpandFormal(record.getStudentFormal());
                        newVO.setExpandGift(record.getStudentGift());
                    }
                    if (record.getStudentCourseType() == 3) {
                        newVO.setComplementaryFormal(record.getStudentFormal());
                        newVO.setComplementaryGift(record.getStudentGift());
                    }
                    if (record.getStudentCourseType() == 4) {
                        newVO.setFaceFormal(record.getStudentFormal());
                        newVO.setFaceGift(record.getStudentGift());
                    }
                    if (record.getStudentCourseType() == 6) {
                        newVO.setDevelopFormal(record.getStudentFormal());
                        newVO.setDevelopGift(record.getStudentGift());
                    }
                    if (record.getStudentCourseType() == 7) {
                        newVO.setResearchFormal(record.getStudentFormal());
                        newVO.setResearchGift(record.getStudentGift());
                    }
                }
            }
            result.add(newVO);
        }
        return result;
    }

    public List<StudentHoursVO> getHoursList(Long storeId, Integer stuId) {
        List<CourseHoursStudent> courseHoursStudentList = courseHoursStudentMapper.selectList(
                Wrappers.lambdaQuery(CourseHoursStudent.class)
                        .select(CourseHoursStudent::getId, CourseHoursStudent::getCourseHours, CourseHoursStudent::getCourseType, CourseHoursStudent::getGift, CourseHoursStudent::getFormal)
                        .eq(CourseHoursStudent::getStudentId, stuId)
                        .eq(CourseHoursStudent::getStoreId, storeId)
        );
        if (CollectionUtils.isEmpty(courseHoursStudentList)) {
            return Collections.emptyList();
        }
        R<List<CourseTypeDTO>> courseTypeResult = remoteCourseTypeService.getAll();
        Map<Integer, String> courseTypeMap = new HashMap<>();
        if (courseTypeResult.isOk() && CollectionUtils.isNotEmpty(courseTypeResult.getData())) {
            courseTypeMap = courseTypeResult.getData().stream().collect(Collectors.toMap(CourseTypeDTO::getId, CourseTypeDTO::getName));
        }
        Map<Integer, String> finalCourseTypeMap = courseTypeMap;
        return courseHoursStudentList.stream()
                .map(courseHoursStudent -> {
                    StudentHoursVO vo = new StudentHoursVO();
                    BeanUtil.copyProperties(courseHoursStudent, vo);
                    vo.setCourseTypeName(finalCourseTypeMap.getOrDefault(courseHoursStudent.getCourseType(), "--"));
                    vo.setCourseHours(courseHoursStudent.getCourseHours());
                    vo.setFormal(courseHoursStudent.getFormal());
                    vo.setGift(courseHoursStudent.getGift());
                    vo.setId(courseHoursStudent.getId());
                    return vo;
                }).toList();
    }

    /**
     * 新增意向会员
     *
     * @param intentionStudentDTO 意向会员信息
     * @return R
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveIntentionStudent(IntentionStudentDTO intentionStudentDTO) {
        log.info("开始新增意向会员，学员信息：name={}, phone={}, grade={}, responsiblePerson={}",
                intentionStudentDTO.getName(), intentionStudentDTO.getPhone(),
                intentionStudentDTO.getGrade(), intentionStudentDTO.getResponsiblePerson());

        // 门店Id
        Long storeId = StoreContextHolder.getStoreId();
        // 学校Id
        Long schoolId = StoreContextHolder.getSchoolId();
        log.info("获取到门店和校区信息：storeId={}, schoolId={}", storeId, schoolId);

        // 学员重复性校验：按照学员姓名+手机号校验
        Student existingStudent = baseMapper.selectOne(Wrappers.lambdaQuery(Student.class)
                .eq(Student::getName, intentionStudentDTO.getName())
                .eq(Student::getPhone, intentionStudentDTO.getPhone())
                .eq(Student::getStoreId, storeId));

        if (ObjectUtil.isNotNull(existingStudent)) {
            // 学员已存在，返回错误提示
            String errorMsg = String.format("学员【%s   %s】已存在，不可重复录入",
                    intentionStudentDTO.getName(), intentionStudentDTO.getPhone());
            log.warn("意向会员新增失败，学员已存在：name={}, phone={}, storeId={}, existingUserId={}",
                    intentionStudentDTO.getName(), intentionStudentDTO.getPhone(), storeId, existingStudent.getUserId());
            throw new BizException(errorMsg);
        }

        // 创建新的意向会员
        Student student = new Student();
        BeanUtil.copyProperties(intentionStudentDTO, student);

        // 设置基本信息
        student.setStoreId(storeId);
        student.setSchoolId(schoolId);
        student.setIsRegularStudents(StudentRegularEnum.INTENTION.getCode()); // 设置为意向会员
        student.setInitRegular(StudentRegularEnum.INTENTION.getCode());
        student.setOrigin(StudentOriginEnum.MANUAL_INPUT.getCode()); // 手动录入
        student.setPinyinPre(PinyinUtil.getFirstLetter(intentionStudentDTO.getName(), ""));

        if (intentionStudentDTO.getGrade() != null) {
            student.setGradeBaseDate(LocalDateTime.now());
        }

        // 保存学员信息
        baseMapper.insert(student);
        log.info("意向会员基本信息保存成功，学员ID：{}, 姓名：{}, 手机号：{}",
                student.getUserId(), student.getName(), student.getPhone());

        // 调用现有接口：向store_student_conversion表插入意向会员转换记录
        studentConversionService.addIntentionMember(student);
        log.info("意向会员转换记录插入成功，学员ID：{}", student.getUserId());

        // 向store_student_track_record表插入跟踪记录
        StoreStudentTrackRecordDTO trackRecordDTO = new StoreStudentTrackRecordDTO();
        trackRecordDTO.setUserId(student.getUserId().intValue());
        trackRecordDTO.setStoreId(storeId.intValue());
        trackRecordDTO.setSchoolId(schoolId.intValue());
        trackRecordDTO.setRecommendTeacher(intentionStudentDTO.getResponsiblePerson() != null ?
                intentionStudentDTO.getResponsiblePerson().intValue() : null);
        trackRecordDTO.setRecommendStudent(intentionStudentDTO.getReferralStudentId() != null ?
                intentionStudentDTO.getReferralStudentId().intValue() : null);
        trackRecordDTO.setCommunicationRecords("新增意向会员：" +
                (intentionStudentDTO.getDescribe() != null ? intentionStudentDTO.getDescribe() : ""));

        storeStudentTrackRecordService.add(trackRecordDTO);
        log.info("意向会员跟踪记录插入成功，学员ID：{}, 推荐老师：{}",
                student.getUserId(), trackRecordDTO.getRecommendTeacher());

        // 发送消息意向会员不同步老飞天
        //studentProducer.sendMessage(student.getUserId());

        log.info("意向会员新增完成，学员ID：{}, 姓名：{}, 手机号：{}",
                student.getUserId(), student.getName(), student.getPhone());
        return R.ok("意向会员新增成功");
    }

    /**
     * 修改意向会员信息
     *
     * @param updateIntentionStudentDTO 修改意向会员信息
     * @return R
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateIntentionStudent(UpdateIntentionStudentDTO updateIntentionStudentDTO) {
        log.info("开始修改意向会员信息，学员ID：{}, 学员信息：name={}, phone={}, grade={}",
                updateIntentionStudentDTO.getUserId(), updateIntentionStudentDTO.getName(),
                updateIntentionStudentDTO.getPhone(), updateIntentionStudentDTO.getGrade());

        // 门店Id
        Long storeId = StoreContextHolder.getStoreId();
        // 学校Id
        Long schoolId = StoreContextHolder.getSchoolId();
        log.info("获取到门店和校区信息：storeId={}, schoolId={}", storeId, schoolId);

        // 验证学员是否存在且为意向会员
        Student existingStudent = baseMapper.selectOne(Wrappers.lambdaQuery(Student.class)
                .eq(Student::getUserId, updateIntentionStudentDTO.getUserId())
                .eq(Student::getStoreId, storeId));

        if (ObjectUtil.isNull(existingStudent)) {
            String errorMsg = "学员不存在或不属于当前门店";
            log.warn("修改意向会员失败，学员不存在：userId={}, storeId={}",
                    updateIntentionStudentDTO.getUserId(), storeId);
            throw new BizException(errorMsg);
        }

        if (!Objects.equals(StudentRegularEnum.INTENTION.getCode(), existingStudent.getIsRegularStudents())) {
            String errorMsg = "该学员不是意向会员，无法修改";
            log.warn("修改意向会员失败，学员类型不匹配：userId={}, currentType={}, expectedType={}",
                    updateIntentionStudentDTO.getUserId(), existingStudent.getIsRegularStudents(),
                    StudentRegularEnum.INTENTION.getCode());
            throw new BizException(errorMsg);
        }

        // 如果修改了姓名或手机号，需要进行重复性校验
        boolean nameOrPhoneChanged = !updateIntentionStudentDTO.getName().equals(existingStudent.getName()) ||
                !updateIntentionStudentDTO.getPhone().equals(existingStudent.getPhone());

        if (nameOrPhoneChanged) {
            // 学员重复性校验：按照学员姓名+手机号校验（排除当前学员）
            Student duplicateStudent = baseMapper.selectOne(Wrappers.lambdaQuery(Student.class)
                    .eq(Student::getName, updateIntentionStudentDTO.getName())
                    .eq(Student::getPhone, updateIntentionStudentDTO.getPhone())
                    .eq(Student::getStoreId, storeId)
                    .ne(Student::getUserId, updateIntentionStudentDTO.getUserId()));

            if (ObjectUtil.isNotNull(duplicateStudent)) {
                String errorMsg = String.format("学员【%s   %s】已存在，不可重复录入",
                        updateIntentionStudentDTO.getName(), updateIntentionStudentDTO.getPhone());
                log.warn("修改意向会员失败，学员信息重复：name={}, phone={}, storeId={}, duplicateUserId={}",
                        updateIntentionStudentDTO.getName(), updateIntentionStudentDTO.getPhone(),
                        storeId, duplicateStudent.getUserId());
                throw new BizException(errorMsg);
            }
        }

        // 更新学员信息
        Student updateStudent = new Student();
        BeanUtil.copyProperties(updateIntentionStudentDTO, updateStudent);
        updateStudent.setUserId(updateIntentionStudentDTO.getUserId());

        // 如果修改了姓名，更新拼音前缀
        if (!updateIntentionStudentDTO.getName().equals(existingStudent.getName())) {
            updateStudent.setPinyinPre(PinyinUtil.getFirstLetter(updateIntentionStudentDTO.getName(), ""));
        }

        // 如果修改了年级，更新年级基准日期
        if (!Objects.equals(updateIntentionStudentDTO.getGrade(), existingStudent.getGrade()) &&
                updateIntentionStudentDTO.getGrade() != null) {
            updateStudent.setGradeBaseDate(LocalDateTime.now());
        }

        // 执行更新
        int updateCount = baseMapper.updateById(updateStudent);
        if (updateCount == 0) {
            String errorMsg = "修改意向会员信息失败";
            log.error("修改意向会员信息失败，更新数据库返回0：userId={}", updateIntentionStudentDTO.getUserId());
            throw new BizException(errorMsg);
        }

        log.info("意向会员基本信息修改成功，学员ID：{}, 姓名：{}, 手机号：{}",
                updateIntentionStudentDTO.getUserId(), updateIntentionStudentDTO.getName(),
                updateIntentionStudentDTO.getPhone());


//        // 向store_student_track_record表插入跟踪记录
//        StoreStudentTrackRecordDTO trackRecordDTO = new StoreStudentTrackRecordDTO();
//        trackRecordDTO.setUserId(updateIntentionStudentDTO.getUserId().intValue());
//        trackRecordDTO.setStoreId(storeId.intValue());
//        trackRecordDTO.setSchoolId(schoolId.intValue());
//        trackRecordDTO.setRecommendTeacher(updateIntentionStudentDTO.getResponsiblePerson() != null ?
//                updateIntentionStudentDTO.getResponsiblePerson().intValue() : null);
//        trackRecordDTO.setRecommendStudent(updateIntentionStudentDTO.getReferralStudentId() != null ?
//                updateIntentionStudentDTO.getReferralStudentId().intValue() : null);
//        trackRecordDTO.setCommunicationRecords("修改意向会员信息：" +
//                (updateIntentionStudentDTO.getDescribe() != null ? updateIntentionStudentDTO.getDescribe() : ""));
//
//        storeStudentTrackRecordService.add(trackRecordDTO);
//        log.info("意向会员修改跟踪记录插入成功，学员ID：{}, 推荐老师：{}",
//                updateIntentionStudentDTO.getUserId(), trackRecordDTO.getRecommendTeacher());

        log.info("意向会员信息修改完成，学员ID：{}, 姓名：{}, 手机号：{}",
                updateIntentionStudentDTO.getUserId(), updateIntentionStudentDTO.getName(),
                updateIntentionStudentDTO.getPhone());
        return R.ok("意向会员信息修改成功");
    }

    /**
     * 获取分享二维码链接
     *
     * @return ShareQrCodeVO
     */
    @Override
    public ShareQrCodeVO getShareQrCodeUrl() {
        // 获取当前登录用户信息
        YdsfUser currentUser = SecurityUtils.getUser();
        if (currentUser == null) {
            throw new BizException("用户未登录");
        }

        // 获取门店和校区信息
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        try {
            // 对用户姓名进行URL编码
            String encodedUserName = URLEncoder.encode(currentUser.getName(), StandardCharsets.UTF_8);

            // 构建分享链接，包含分享人ID、姓名和门店ID
            String qrCodeUrl = String.format("%s?inviterUserId=%d&inviterUserName=%s&storeId=%d&schoolId=%d",
                    intentionInviteLink, currentUser.getId(), encodedUserName, storeId, schoolId);

            ShareQrCodeVO shareQrCodeVO = new ShareQrCodeVO();
            shareQrCodeVO.setQrCodeUrl(qrCodeUrl);
            shareQrCodeVO.setInviterUserId(currentUser.getId());
            shareQrCodeVO.setInviterUserName(currentUser.getName());
            shareQrCodeVO.setStoreId(storeId);
            shareQrCodeVO.setSchoolId(schoolId);

            return shareQrCodeVO;
        } catch (Exception e) {
            log.error("生成分享二维码URL时发生错误", e);
            throw new BizException("生成分享链接失败");
        }
    }

    /**
     * 家长录入意向会员
     *
     * @param parentIntentionStudentDTO 家长录入意向会员信息
     * @return R
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveIntentionStudentByParent(ParentIntentionStudentDTO parentIntentionStudentDTO) {
        log.info("家长开始录入意向会员，学员信息：name={}, phone={}, storeId={}, inviterUserId={}",
                parentIntentionStudentDTO.getName(), parentIntentionStudentDTO.getPhone(),
                parentIntentionStudentDTO.getStoreId(), parentIntentionStudentDTO.getInviterUserId());

        String code = parentIntentionStudentDTO.getCode();
        String key = CacheConstants.ANYONE_MOBILE_CODE_KEY + CacheConstants.WEB_SMS + StringPool.AT + parentIntentionStudentDTO.getPhone();
        log.info("开始验证码校验，手机号：{}", parentIntentionStudentDTO.getPhone());

        // 家长录入意向会员的时候需要校验验证码
        Object codeObj = redisTemplate.opsForValue()
                .get(key);
        if (codeObj == null) {
            log.warn("验证码校验失败，验证码不存在，手机号：{}", parentIntentionStudentDTO.getPhone());
            throw new ValidateCodeException("验证码错误！请输入正确的验证码");
        }
        String saveCode = codeObj.toString();
        if (StrUtil.isBlank(saveCode)) {
            log.warn("验证码校验失败，验证码为空，手机号：{}", parentIntentionStudentDTO.getPhone());
            throw new ValidateCodeException("验证码错误！请输入正确的验证码");
        }
        if (!StrUtil.equals(saveCode, code)) {
            log.warn("验证码校验失败，验证码不匹配，手机号：{}, 输入验证码：{}", parentIntentionStudentDTO.getPhone(), code);
            throw new ValidateCodeException("验证码错误！请输入正确的验证码");
        }
        log.info("验证码校验成功，手机号：{}", parentIntentionStudentDTO.getPhone());

        // 获取门店和校区信息
        Long storeId = parentIntentionStudentDTO.getStoreId();
        CampusEntity campusEntity = campusService.getById(storeId);
        if (campusEntity == null) {
            log.error("家长录入意向会员失败，门店信息不存在，storeId：{}", storeId);
            return R.failed("门店信息不存在");
        }
        Long schoolId = Objects.isNull(campusEntity.getSchoolId()) ? null : campusEntity.getSchoolId().longValue();
        log.info("获取到门店和校区信息：storeId={}, schoolId={}", storeId, schoolId);

        // 学员重复性校验：按照学员姓名+手机号校验
        Student existingStudent = baseMapper.selectOne(Wrappers.lambdaQuery(Student.class)
                .eq(Student::getName, parentIntentionStudentDTO.getName())
                .eq(Student::getPhone, parentIntentionStudentDTO.getPhone())
                .eq(Student::getStoreId, storeId));

        if (existingStudent != null) {
            log.warn("家长录入意向会员失败，学员已存在：name={}, phone={}, storeId={}, existingUserId={}",
                    parentIntentionStudentDTO.getName(), parentIntentionStudentDTO.getPhone(), storeId, existingStudent.getUserId());
            return R.failed("该学员信息已存在，请勿重复提交");
        }

        // 创建新的意向会员
        Student student = new Student();
        BeanUtil.copyProperties(parentIntentionStudentDTO, student);

        // 设置基本信息
        student.setStoreId(storeId);
        student.setSchoolId(schoolId);
        student.setIsRegularStudents(StudentRegularEnum.INTENTION.getCode()); // 设置为意向会员
        student.setInitRegular(StudentRegularEnum.INTENTION.getCode());
        student.setOrigin(StudentOriginEnum.SHARE_INVITATION.getCode()); // 分享邀请来源
        student.setPinyinPre(PinyinUtil.getFirstLetter(parentIntentionStudentDTO.getName(), ""));
        // TODO-LY: liuyi 2025/6/25 Administrator 目前设置的是邀请人即为负责人
        student.setResponsiblePerson(parentIntentionStudentDTO.getInviterUserId()); // 设置邀请老师为负责人

        if (parentIntentionStudentDTO.getGrade() != null) {
            student.setGradeBaseDate(LocalDateTime.now());
        }

        // 保存学员信息
        baseMapper.insert(student);
        log.info("家长录入意向会员基本信息保存成功，学员ID：{}, 姓名：{}, 手机号：{}",
                student.getUserId(), student.getName(), student.getPhone());

        // 调用现有接口：向store_student_conversion表插入意向会员转换记录
        studentConversionService.addIntentionMember(student);
        log.info("家长录入意向会员转换记录插入成功，学员ID：{}", student.getUserId());

        // 向store_student_track_record表插入跟踪记录
        StoreStudentTrackRecordDTO trackRecordDTO = new StoreStudentTrackRecordDTO();
        trackRecordDTO.setUserId(student.getUserId().intValue());
        trackRecordDTO.setStoreId(storeId.intValue());
        trackRecordDTO.setSchoolId(Objects.nonNull(schoolId) ? schoolId.intValue() : null);
        trackRecordDTO.setCommunicationRecords("家长通过分享邀请注册意向会员");
        trackRecordDTO.setRecommendTeacher(parentIntentionStudentDTO.getInviterUserId() != null ?
                parentIntentionStudentDTO.getInviterUserId().intValue() : null);
        storeStudentTrackRecordService.add(trackRecordDTO);
        log.info("家长录入意向会员跟踪记录插入成功，学员ID：{}, 邀请老师：{}",
                student.getUserId(), trackRecordDTO.getRecommendTeacher());

        redisTemplate.delete(key);
        log.info("验证码缓存清理完成，手机号：{}", parentIntentionStudentDTO.getPhone());

        log.info("家长录入意向会员完成，学员ID：{}, 姓名：{}, 手机号：{}, 邀请老师：{}",
                student.getUserId(), student.getName(), student.getPhone(), parentIntentionStudentDTO.getInviterUserId());
        return R.ok("提交成功");
    }

    /**
     * 获取意向会员列表
     *
     * @param page  分页对象
     * @param query 查询条件（支持手机号、姓名或姓名首字母搜索）
     * @return 意向会员列表分页结果
     */
    @Override
    public IPage<IntentionStudentListVO> getIntentionStudentList(Page<IntentionStudentListVO> page, IntentionStudentQuery query) {
        log.info("开始获取意向会员列表，查询条件：{}, 分页参数：current={}, size={}",
                query.getCondition(), page.getCurrent(), page.getSize());

        // 获取当前门店ID
        Long storeId = StoreContextHolder.getStoreId();
        if (storeId == null) {
            log.error("获取意向会员列表失败，门店信息不存在");
            throw new BizException("门店信息不存在");
        }
        log.info("获取到门店ID：{}", storeId);

        // 查询意向会员列表
        Page<IntentionStudentListVO> resultPage = baseMapper.getIntentionStudentList(page, storeId, query.getCondition());
        log.info("查询到意向会员基础数据：{}条记录，总记录数：{}",
                resultPage.getRecords().size(), resultPage.getTotal());

        // 处理业务逻辑
        List<IntentionStudentListVO> records = resultPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            // 收集所有学员ID
            List<Long> userIds = records.stream().map(IntentionStudentListVO::getUserId).collect(Collectors.toList());
            log.info("开始处理意向会员业务数据，学员ID列表：{}", userIds);

            // 批量查询测评记录
            List<PaperUser> paperUsers = paperUserMapper.selectList(Wrappers.lambdaQuery(PaperUser.class)
                    .in(PaperUser::getUserId, userIds)
                    .eq(PaperUser::getTerminalId, storeId));
            log.info("查询到测评记录：{}条", paperUsers.size());

            // 构建用户ID与测评次数的映射
            Map<Long, Integer> testNumberMap = paperUsers.stream()
                    .collect(Collectors.toMap(PaperUser::getUserId,
                            paperUser -> paperUser.getTestNumber() != null ? paperUser.getTestNumber() : 0,
                            (existing, replacement) -> existing));

            // 批量查询跟踪记录（排除初始化记录）
            List<StoreStudentTrackRecord> trackRecords = storeStudentTrackRecordMapper.getTrackRecordsByUserIds(userIds, storeId);
            log.info("查询到跟踪记录：{}条", trackRecords.size());

            // 按学员ID分组跟踪记录，并获取每个学员最新的跟踪记录
            Map<Long, StoreStudentTrackRecord> latestTrackMap = new HashMap<>();
            if (CollUtil.isNotEmpty(trackRecords)) {
                Map<Long, List<StoreStudentTrackRecord>> trackGroupMap = trackRecords.stream()
                        .collect(Collectors.groupingBy(record -> record.getUserId().longValue()));

                // 获取每个学员最新的跟踪记录
                trackGroupMap.forEach((userId, userTrackRecords) -> {
                    if (CollUtil.isNotEmpty(userTrackRecords)) {
                        // 按创建时间倒序排序，取第一个（最新的）
                        userTrackRecords.stream().min((r1, r2) -> r2.getCreateTime().compareTo(r1.getCreateTime())).ifPresent(latestRecord -> latestTrackMap.put(userId, latestRecord));
                    }
                });
                log.info("处理完成最新跟踪记录映射，涉及学员：{}个", latestTrackMap.size());
            }
            // 填充业务字段
            records.forEach(record -> {
                // 从paper_user表获取测评次数并设置测评状态
                Integer testNumber = testNumberMap.getOrDefault(record.getUserId(), 0);
                record.setTestNum(testNumber);
                if (testNumber > 0) {
                    record.setTestStatus(Integer.parseInt(YesNoEnum.YES.getCode())); // 已测评
                } else {
                    record.setTestStatus(Integer.parseInt(YesNoEnum.NO.getCode())); // 未测评
                }

                // 设置意愿度等级和最近跟进时间
                StoreStudentTrackRecord latestTrack = latestTrackMap.get(record.getUserId());
                if (latestTrack != null) {
                    record.setWillingnessLevel(latestTrack.getWillingnessLevel());
                    record.setLastFollowTime(latestTrack.getCreateTime());
                }
                // 设置意愿度等级描述
                if (Objects.nonNull(record.getWillingnessLevel())) {
                    // 根据意愿度等级code获取对应的描述
                    WillingnessLevelEnum willingnessEnum = WillingnessLevelEnum.getByCode(record.getWillingnessLevel());
                    if (willingnessEnum != null) {
                        record.setWillingnessLevelDesc(willingnessEnum.getDesc());
                    }
                }
            });
        }

        // Long allIntentionCount = baseMapper.getIntentionStudentCount(storeId);

        log.info("意向会员列表获取完成，门店ID：{}, 返回记录数：{}, 总记录数：{}",
                storeId, resultPage.getRecords().size(), resultPage.getTotal());
        return resultPage;
    }

    /**
     * 根据意向学生ID获取意向学员详情
     *
     * @param userId 意向学生ID
     * @return 意向学员详情
     */
    @Override
    public IntentionStudentDetailVO getIntentionStudentDetail(Long userId) {
        log.info("开始获取意向学员详情，学员ID：{}", userId);

        // 获取当前门店ID
        Long storeId = StoreContextHolder.getStoreId();
        if (storeId == null) {
            log.error("获取意向学员详情失败，门店信息不存在，学员ID：{}", userId);
            throw new BizException("门店信息不存在");
        }
        log.info("获取到门店ID：{}", storeId);

        // 查询学生基本信息
        Student student = baseMapper.selectOne(Wrappers.lambdaQuery(Student.class)
                .eq(Student::getUserId, userId)
                .eq(Student::getStoreId, storeId)
                .eq(Student::getIsRegularStudents, StudentRegularEnum.INTENTION.getCode()));

        if (student == null) {
            log.warn("意向学员不存在，学员ID：{}, 门店ID：{}", userId, storeId);
            throw new BizException("意向学员不存在");
        }
        log.info("查询到意向学员基本信息：userId={}, name={}, phone={}",
                student.getUserId(), student.getName(), student.getPhone());

        // 构建返回对象
        IntentionStudentDetailVO detailVO = new IntentionStudentDetailVO();
        BeanUtil.copyProperties(student, detailVO);

        // 设置年级信息
        Integer currentGrade = StudentUtils.grade(student.getGrade(), student.getGradeBaseDate());
        detailVO.setGrade(currentGrade);

        // 设置录入来源描述
        if (student.getOrigin() != null) {
            for (StudentOriginEnum originEnum : StudentOriginEnum.values()) {
                if (originEnum.getCode() == student.getOrigin()) {
                    detailVO.setOriginDesc(originEnum.getDesc());
                    break;
                }
            }
        }

        // 设置负责老师信息
        if (student.getResponsiblePerson() != null) {
            detailVO.setResponsiblePerson(student.getResponsiblePerson());
            try {
                List<EmployeeVO> employeeList = employeeService.getEmployeeListByIdList(
                        Collections.singletonList(student.getResponsiblePerson()));
                if (!employeeList.isEmpty()) {
                    detailVO.setResponsiblePersonName(employeeList.get(0).getName());
                    log.info("设置负责老师信息成功：userId={}, responsiblePersonId={}, responsiblePersonName={}",
                            userId, student.getResponsiblePerson(), employeeList.get(0).getName());
                }
            } catch (Exception e) {
                log.warn("获取负责老师信息失败：userId={}, responsiblePersonId={}, error={}",
                        userId, student.getResponsiblePerson(), e.getMessage());
            }
        }


        // 查询跟进记录（排除初始化记录，按创建时间倒序）
        List<StoreStudentTrackRecord> trackRecords = storeStudentTrackRecordMapper.getTrackRecordsByUserIds(
                Collections.singletonList(userId), storeId);
        log.info("查询到意向学员跟进记录：{}条，学员ID：{}", trackRecords.size(), userId);

        // 从跟进记录中获取转介绍学员信息（取第一条跟踪记录，因为推荐学员信息存在第一条记录中）
        Long referralStudentId = null;
        Optional<StoreStudentTrackRecord> firstRecordOpt = storeStudentTrackRecordMapper.selectList(
                Wrappers.lambdaQuery(StoreStudentTrackRecord.class)
                        .eq(StoreStudentTrackRecord::getUserId, userId)
                        .eq(StoreStudentTrackRecord::getStoreId, storeId)
                        .orderByAsc(StoreStudentTrackRecord::getId)
        ).stream().findFirst();

        if (firstRecordOpt.isPresent()) {
            StoreStudentTrackRecord firstRecord = firstRecordOpt.get();
            if (firstRecord.getRecommendStudent() != null && firstRecord.getRecommendStudent() > 0) {
                referralStudentId = firstRecord.getRecommendStudent().longValue();
                log.info("从第一条跟踪记录获取转介绍学员ID：userId={}, firstRecordId={}, referralStudentId={}",
                        userId, firstRecord.getId(), referralStudentId);
            }
        }
        // 设置转介绍学员信息
        if (referralStudentId != null) {
            detailVO.setReferralStudentId(referralStudentId);
            try {
                Student referralStudent = baseMapper.selectOne(Wrappers.lambdaQuery(Student.class)
                        .eq(Student::getUserId, referralStudentId)
                        .eq(Student::getStoreId, storeId));
                if (referralStudent != null) {
                    detailVO.setReferralStudentName(referralStudent.getName());
                    log.info("设置转介绍学员信息成功（来自第一条跟踪记录）：userId={}, referralStudentId={}, referralStudentName={}",
                            userId, referralStudentId, referralStudent.getName());
                }
            } catch (Exception e) {
                log.warn("获取转介绍学员信息失败：userId={}, referralStudentId={}, error={}",
                        userId, referralStudentId, e.getMessage());
            }
        }

        // 转换跟进记录
        List<IntentionStudentTrackRecordVO> trackRecordVOs = trackRecords.stream()
                .map(record -> {
                    IntentionStudentTrackRecordVO trackVO = new IntentionStudentTrackRecordVO();

                    // 设置基本字段
                    trackVO.setId(record.getId());
                    trackVO.setWillingnessLevel(record.getWillingnessLevel());
                    trackVO.setCommunicationRecords(record.getCommunicationRecords());

                    // 跟踪人使用修改人，跟踪时间使用创建时间
                    trackVO.setTracker(record.getUpdateBy());
                    trackVO.setTrackTime(record.getCreateTime());

                    // 设置意愿等级描述
                    if (record.getWillingnessLevel() != null) {
                        for (WillingnessLevelEnum levelEnum : WillingnessLevelEnum.values()) {
                            if (levelEnum.getCode() == record.getWillingnessLevel()) {
                                trackVO.setWillingnessLevelDesc(levelEnum.getDesc());
                                break;
                            }
                        }
                    }
                    // 处理沟通记录：如果为空或空字符串，显示"暂未填写沟通记录"
                    if (org.apache.commons.lang3.StringUtils.isBlank(record.getCommunicationRecords())) {
                        trackVO.setCommunicationRecords("暂未填写沟通记录");
                    }

                    return trackVO;
                })
                .collect(Collectors.toList());

        detailVO.setTrackRecords(trackRecordVOs);

        log.info("意向学员详情获取完成，学员ID：{}, 姓名：{}, 跟进记录数：{}",
                userId, student.getName(), trackRecordVOs.size());
        return detailVO;
    }

}
