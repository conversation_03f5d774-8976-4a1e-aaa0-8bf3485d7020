package com.yuedu.store.mq.consumer;

import com.yuedu.store.mq.dto.AlternateDTO;
import com.yuedu.store.service.CourseHoursPayService;
import com.yuedu.store.utils.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;


/**
 * 课消候补生产者
 * <AUTHOR>
 * @date  2025/5/31 10:20
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = "${rocketmq.topics.student_course_alternate_topic}",
        consumerGroup = "${rocketmq.groups.student_course_alternate_group}", tag = "*")
@ConditionalOnProperty(
        prefix = "rocketmq",
        name = "enabled",
        havingValue = "true",
        matchIfMissing = false)
public class AlternateConsumer implements RocketMQListener {

    private final CourseHoursPayService courseHoursPayService;

    public AlternateConsumer(CourseHoursPayService courseHoursPayService) {
        this.courseHoursPayService = courseHoursPayService;
    }

    @Override
    public ConsumeResult consume(MessageView messageView) {
        log.info("接收到的消息:messageId:{},body:{}", messageView.getMessageId(), messageView.getBody());
        AlternateDTO alternate = null;
        //如果解析消息内容失败，返回ConsumeResult.SUCCESS，避免消息重复消费阻塞其他正常消息消费，原因是消息重试还会是失败。
        try {
            //解析消息内容
            alternate = MqUtils.convertMessageBodyToDTO(messageView, AlternateDTO.class);
            log.info("解析后的消息内容:{}", alternate);
            if (alternate == null) {
                return ConsumeResult.SUCCESS;
            }
        } catch (Exception e) {
            log.error("解析消息内容失败", e);
            return ConsumeResult.SUCCESS;
        }

        try {
            Long count = courseHoursPayService.alternate(alternate.getStudentId(), alternate.getCourseType());
            if (count != null && count > 1L) {
                //重入队列，根据重试机制继续消费
                return ConsumeResult.FAILURE;
            }
        } catch (Exception e) {
            log.error("候补课消,学员ID {},课程类型：{} ", alternate.getStudentId(), alternate.getCourseType(), e);
            return ConsumeResult.FAILURE;
        }
        return ConsumeResult.SUCCESS;
    }
}
