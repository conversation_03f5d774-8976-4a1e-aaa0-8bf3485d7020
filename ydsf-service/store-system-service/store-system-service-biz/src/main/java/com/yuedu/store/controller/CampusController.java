package com.yuedu.store.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.dto.StoreDTO;
import com.yuedu.store.entity.CampusEntity;
import com.yuedu.store.query.AppUserQuery;
import com.yuedu.store.query.CampusPageQuery;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.service.CampusService;
import com.yuedu.store.valid.AppUserQueryValidGroup;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.SsCampusVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 门店
 *
 * <AUTHOR>
 * @date 2024-09-27 16:59:19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/campus")
@Tag(description = "campus", name = "门店管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CampusController {

    private final CampusService campusService;

    /**
     * 查询全部部门
     *
     * @return 全部门店列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询全部门店", description = "查询全部门店")
    public R list(@RequestParam(name = "query", required = false, defaultValue = "") String name) {
        return R.ok(campusService.listCampus(name));
    }

    /**
     * 根据门店id查询门店信息
     *
     * @param schoolId 门店id
     * @return 门店信息
     */
    @GetMapping("/info/{schoolId}")
    @Operation(summary = "根据门店id查询门店信息", description = "根据门店id查询门店信息")
    @Inner
    public R getOneSchoolBySchoolId(@PathVariable Long schoolId) {
        return R.ok(campusService.selectOneBySchoolId(schoolId));
    }

    /**
     * 根据门店id列表查询门店列表信息
     *
     * @param campusDTO 门店id集合
     * @return 门店信息列表
     */
    @PostMapping("/campusList")
    @Operation(summary = "根据门店id列表查询门店列表信息", description = "根据门店id列表查询门店列表信息")
    @Inner
    public R<List<CampusVO>> getSchoolListById(@RequestBody CampusDTO campusDTO) {
        return R.ok(campusService.selectBySchoolId(campusDTO.getSchoolIdList(), campusDTO.getCampusName()));
    }

    /**
     * 分页查询
     *
     * @param page   分页对象
     * @param params 门店表
     * @return 门店分页列表
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
//    @HasPermission("store_campus_view")
    public R<Page<CampusVO>> getCampusPage(@ParameterObject Page page, @ParameterObject CampusPageQuery params) {
        return R.ok(campusService.pageQuery(page, params));
    }

    /**
     * 新增门店表
     *
     * @param campus 门店信息
     * @return R
     */
    @Operation(summary = "新增门店表", description = "新增门店表")
    @SysLog("新增门店表")
    @PostMapping
//    @HasPermission("store_campus_add")
    public R save(@Valid @RequestBody StoreDTO campus) {

        if(Objects.isNull(campus.getStudentPrice()) || campus.getStudentPrice().compareTo(BigDecimal.ZERO) < 0 || campus.getStudentPrice().scale() > 2){
            return R.failed("学生价格不能为空并且价格不能小于0且精度不能超过两位");
        }

        campusService.addStore(campus);
        return R.ok();
    }


    /**
     * 修改门店表
     *
     * @param campus 门店表
     * @return R
     */
    @Operation(summary = "修改门店表", description = "修改门店表")
    @SysLog("修改门店表")
    @PutMapping
//    @HasPermission("store_campus_edit")
    public R updateById(@Valid @RequestBody StoreDTO campus) {
        campusService.editStore(campus);
        return R.ok();
    }

    /**
     * 通过id查询校区表
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
//    @HasPermission("store_campus_view")
    public R<CampusVO> getById(@PathVariable("id") Long id) {
        return R.ok(campusService.details(id));
    }


    /**
     * 内部调用:通过用户信息查询门店信息
     *
     * @param appUserQuery 用户信息
     * @return List<SsCampusVO>
     */
    @Operation(summary = "内部调用:通过用户信息查询门店信息", description = "内部调用:通过用户信息查询门店信息")
    @PostMapping("/getCampusInfoByUser")
    @Inner
    public R<List<SsCampusVO>> getCampusInfoByUser(@Validated({AppUserQueryValidGroup.GetCampusInfoByUser.class})
                                                   @RequestBody AppUserQuery appUserQuery) {
        return R.ok(campusService.getCampusInfoByUser(appUserQuery));
    }

    /**
     * 小程序:通过用户信息查询门店信息
     *
     * @return List<SsCampusVO>
     */
    @Operation(summary = "小程序:通过用户信息查询门店信息", description = "小程序:通过用户信息查询门店信息")
    @GetMapping("/getCampusByUser")
    public R<List<SsCampusVO>> getCampusByUser() {
        AppUserQuery appUserQuery = new AppUserQuery();
        appUserQuery.setUserId(SecurityUtils.getUser().getId());
        return R.ok(campusService.getCampusInfoByUser(appUserQuery));
    }

    /**
     * 内部调用:查询所有校区
     *
     * @param campusDTO
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.store.vo.CampusVO>>
     * <AUTHOR>
     * @date 2025/2/10 11:10
     */
    @Operation(summary = "内部调用:查询所有校区", description = "内部调用:查询所有校区")
    @PostMapping("/getCampusAll")
    @Inner
    public R<List<CampusVO>> getCampusAll(@RequestBody CampusDTO campusDTO) {
        return R.ok(campusService.getCampusAll(campusDTO));
    }

    /**
     * 内部调用:批量保存/更新校区
     *
     * @param campusEntityList
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2025/2/10 11:39
     */
    @Operation(summary = "内部调用:批量保存/更新校区", description = "内部调用:批量保存/更新校区")
    @PostMapping("/saveOrUpdateBatchCampus")
    @Inner
    public R saveOrUpdateBatchCampus(@RequestBody List<CampusEntity> campusEntityList) {
        campusService.saveOrUpdateBatch(campusEntityList);
        return R.ok();
    }

    /**
     * 根据校区编码查询校区信息
     */
    @Operation(summary = "内部调用：根据校区编码查询校区信息", description = "内部调用：根据校区编码查询校区信息")
    @PostMapping("/getByCampusNo")
    @Inner
    public R<CampusVO> getByCampusNo(@Validated @NotNull @RequestBody CampusQuery campusQuery) {
        return R.ok(campusService.getByCampusNo(campusQuery.getCampusNo()));
    }

    /**
     * 批量门店ID查询门店信息
     */
    @Operation(summary = "内部调用：批量门店ID查询门店信息", description = "内部调用：批量门店ID查询门店信息")
    @PostMapping("/getByStoreIdList")
    @Inner
    public R<Map<Long, CampusVO>> getByStoreIdList(@Validated @NotNull @RequestBody List<Long> storeIdList) {
        return R.ok(campusService.mapByStoreIdList(storeIdList));
    }

    /**
     * 根据门店ID获取店长ID
     */
    @Operation(summary = "根据门店ID获取店长ID", description = "根据门店ID获取店长ID")
    @PostMapping("/getStoreManagerIdByStoreId")
    @Inner
    public R<Integer> getManagerId(@NotNull @RequestBody StoreDTO storeDTO) {
        return R.ok(campusService.getStoreManagerId(storeDTO.getId()));
    }
}