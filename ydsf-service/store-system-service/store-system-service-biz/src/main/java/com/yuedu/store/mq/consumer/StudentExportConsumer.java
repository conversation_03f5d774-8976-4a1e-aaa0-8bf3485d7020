package com.yuedu.store.mq.consumer;

import com.yuedu.store.mq.dto.CourseExportDTO;
import com.yuedu.store.service.CourseExportService;
import com.yuedu.store.utils.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * 学员明细导出
 * <AUTHOR>
 * @date  2025/6/25
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = "${rocketmq.topics.student_hours_log_export_topic}",
        consumerGroup = "${rocketmq.groups.student_hours_log_export_group}", tag = "*")
@ConditionalOnProperty(
        prefix = "rocketmq",
        name = "enabled",
        havingValue = "true",
        matchIfMissing = false)
public class StudentExportConsumer implements RocketMQListener {

    private final CourseExportService courseExportService;

    public StudentExportConsumer(CourseExportService courseExportService) {
        this.courseExportService = courseExportService;
    }

    @Override
    public ConsumeResult consume(MessageView messageView) {
        log.info("接收到的消息:messageId:{},body:{}", messageView.getMessageId(), messageView.getBody());
        CourseExportDTO courseExport = null;
        try {
            //解析消息内容
            courseExport = MqUtils.convertMessageBodyToDTO(messageView, CourseExportDTO.class);
            log.info("解析后的消息内容:{}", courseExport);
            if (courseExport == null) {
                return ConsumeResult.SUCCESS;
            }
        } catch (Exception e) {
            log.error("解析消息内容失败", e);
            return ConsumeResult.SUCCESS;
        }

        try {
            Boolean count = courseExportService.studentExport(courseExport.getExportId());
            if (Objects.equals(count, Boolean.FALSE)) {
                //重入队列，根据重试机制继续消费
                return ConsumeResult.FAILURE;
            }
        } catch (Exception e) {
            log.error("标识:{}学员导出 ",courseExport.getExportId(), e);
            return ConsumeResult.FAILURE;
        }
        return ConsumeResult.SUCCESS;
    }
}
