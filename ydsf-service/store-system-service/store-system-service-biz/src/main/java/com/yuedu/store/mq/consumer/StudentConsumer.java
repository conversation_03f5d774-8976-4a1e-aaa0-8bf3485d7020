package com.yuedu.store.mq.consumer;

import com.yuedu.store.mq.dto.StudentDTO;
import com.yuedu.store.service.StudentMigrateService;
import com.yuedu.store.utils.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;


/**
 * 生产者
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = "${rocketmq.topics.student_ss_sync_ft_topic}",
    consumerGroup = "${rocketmq.groups.student_ss_sync_ft_group}", tag = "*")
@ConditionalOnProperty(
        prefix = "rocketmq",
        name = "enabled",
        havingValue = "true",
        matchIfMissing = false)
public class StudentConsumer implements RocketMQListener {

    private final StudentMigrateService studentMigrateService;


    public StudentConsumer(StudentMigrateService studentMigrateService) {
        this.studentMigrateService = studentMigrateService;
    }

    @Override
    public ConsumeResult consume(MessageView messageView) {
        log.info("接收到的消息:messageId:{},body:{}", messageView.getMessageId(), messageView.getBody());
        StudentDTO studentDto = null;
        //如果解析消息内容失败，返回ConsumeResult.SUCCESS，避免消息重复消费阻塞其他正常消息消费，原因是消息重试还会是失败。
        try {
            //解析消息内容
            studentDto = MqUtils.convertMessageBodyToDTO(messageView, StudentDTO.class);
            log.info("解析后的消息内容:{}", studentDto);
            if (studentDto == null) {
                return ConsumeResult.SUCCESS;
            }
        } catch (Exception e) {
            log.error("解析消息内容失败", e);
            return ConsumeResult.SUCCESS;
        }

        try {
            studentMigrateService.sync(studentDto.getUserId());
        } catch (Exception e) {
            log.error("学员同步失败,学员ID {} ", studentDto.getUserId(), e);
            return ConsumeResult.FAILURE;
        }

        return ConsumeResult.SUCCESS;
    }
}
