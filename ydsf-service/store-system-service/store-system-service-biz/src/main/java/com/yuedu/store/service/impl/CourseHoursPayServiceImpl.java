package com.yuedu.store.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.config.okhttp.OkHttpFinanceClient;
import com.yuedu.store.constant.cst.StoreConstant;
import com.yuedu.store.constant.enums.*;
import com.yuedu.store.dto.*;
import com.yuedu.store.entity.*;
import com.yuedu.store.mapper.*;
import com.yuedu.store.mapper.CampusJointMapper;
import com.yuedu.store.mq.producer.AlternateProducer;
import com.yuedu.store.mq.producer.JointBillProducer;
import com.yuedu.store.mq.producer.RefundProducer;
import com.yuedu.store.proxy.dto.*;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.query.CourseHoursRefundQuery;
import com.yuedu.store.query.StoreCourseHoursPayQuery;
import com.yuedu.store.service.EmployeeService;
import com.yuedu.store.vo.CourseHoursPayVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.store.vo.StoreCourseHoursPayVO;
import com.yuedu.store.service.CourseHoursPayService;
import com.yuedu.teaching.api.feign.RemoteCourseTypeService;
import com.yuedu.teaching.dto.CourseTypeDTO;
import com.yuedu.store.service.StudentConversionService;
import com.yuedu.ydsf.admin.api.entity.SysDictItem;
import com.yuedu.ydsf.admin.api.feign.RemoteDictService;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.data.resolver.ParamResolver;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteTimetableChangeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class CourseHoursPayServiceImpl extends ServiceImpl<CourseHoursPayMapper, CourseHoursPay> implements CourseHoursPayService {


    private final AlternateProducer alternateProducer;
    private final CourseHoursLogMapper courseHoursLogMapper;
    private final CourseHoursRecordMapper courseHoursRecordMapper;
    private final CourseHoursPayMapper courseHoursPayMapper;
    private final CourseHoursStudentMapper courseHoursStudentMapper;
    private final StudentMapper studentMapper;
    private final RemoteTimetableChangeService remoteTimetableChangeService;
    private final JointBillProducer jointBillProducer;
    private final RefundProducer refundProducer;
    private final CampusMapper campusMapper;
    private final CampusJointMapper campusJointMapper;
    private final OkHttpFinanceClient okHttpFinanceClient;
    private final JointBillMapper jointBillMapper;
    private final RemoteCourseTypeService remoteCourseTypeService;
    private final EmployeeMapper employeeMapper;

    private final StudentConversionService studentConversionService;
    private final RemoteDictService remoteDictService;
    private final EmployeeService employeeService;


    /**
     * 为学员录入课次
     *
     * @param params 课次信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCourseHour(CourseHoursPayDTO params) {
        //查询是否存在学员
        Long studentId = params.getUserId();
        Long storeId = params.getStoreId();
        Long schoolId = params.getSchoolId();
        Integer courseType = params.getCourseType();

        boolean isCourseHoursZero = params.getCourseHoursGift() <= StoreConstant.CODE && params.getCourseHoursFormal() <= StoreConstant.CODE;

        Student student = getStudentInfo(schoolId, studentId);

        // 记录学员转换前的状态，用于后续判断是否发生了转换
        Integer originalRegularType = student.getIsRegularStudents();

        //最大引流课数量节数限制
        if (params.getCourseType() == StoreConstant.TRIAL_COURSE_TYPE && courseHoursRecordMapper.sumCountByStudentAndType(studentId, params.getCourseType()) + params.getCourseHoursFormal() + params.getCourseHoursGift() > getDefaultTrialMaxNumber()) {
            throw new BizException("已超过今年最大引流课添加次数!");
        }

        // 意向会员转换逻辑：根据课次类型判断转为试听还是正式会员
        if (student.getIsRegularStudents().equals(StudentRegularEnum.INTENTION.getCode())) {
            log.info("检测到意向会员，开始根据课次类型转换. 学员ID: {}, 课次类型: {}", studentId, courseType);

            if (courseType.equals(StoreConstant.TRIAL_COURSE_TYPE)) {
                // 课次类型为2，转为试听会员
                student.setIsRegularStudents(StudentRegularEnum.TRIAL.getCode());
                student.setStatus(StudentRegularEnum.TRIAL.getCode());
                log.info("课次类型为2，将意向会员转为试听会员. 学员ID: {}", studentId);
            } else {
                // 课次类型不为2，转为正式会员
                student.setIsRegularStudents(StudentRegularEnum.FORMAL.getCode());
                student.setStatus(StudentRegularEnum.FORMAL.getCode());
                log.info("课次类型为{}，将意向会员转为正式会员. 学员ID: {}", courseType, studentId);
            }
        }

        if (params.getCourseType() != StoreConstant.TRIAL_COURSE_TYPE && student.getIsRegularStudents() != StudentRegularEnum.FORMAL.getCode()) {
            student.setIsRegularStudents(StudentRegularEnum.FORMAL.getCode());
            student.setOrigin(StudentOriginEnum.TRIAL_CHANGE_ENROLL.getCode());
            student.setTransformTime(LocalDateTime.now());
        }

        if (params.getCourseType() != StoreConstant.TRIAL_COURSE_TYPE) {
            student.setStatus(StudentStatusEnum.FORMAL.getCode());
        }

        if (params.getCourseType() == StoreConstant.TRIAL_COURSE_TYPE && student.getIsRegularStudents() == StudentRegularEnum.TRIAL.getCode()) {
            student.setStatus(StudentStatusEnum.TRIAL.getCode());
        }

        if (params.getCourseType() == StoreConstant.TRIAL_COURSE_TYPE && student.getIsRegularStudents() == StudentRegularEnum.FORMAL.getCode()) {
            student.setStatus(StudentStatusEnum.FORMAL.getCode());
        }

        student.setCourseHours(student.getCourseHours() + params.getCourseHoursGift() + params.getCourseHoursFormal());
        student.setAmount(student.getAmount().add(params.getTotalAmount()));

        //学生课次类型汇总
        CourseHoursStudent courseHoursStudent = getCourseHoursStudent(schoolId, storeId, studentId, courseType);

        courseHoursStudent.setCourseHours(courseHoursStudent.getCourseHours() + params.getCourseHoursGift() + params.getCourseHoursFormal());
        courseHoursStudent.setFormal(courseHoursStudent.getFormal() + params.getCourseHoursFormal());
        courseHoursStudent.setGift(courseHoursStudent.getGift() + params.getCourseHoursGift());
        courseHoursStudent.setTotalAmount(courseHoursStudent.getTotalAmount().add(params.getTotalAmount()));
        courseHoursStudentMapper.updateById(courseHoursStudent);

        //批次号 雪花ID
        Long batchNo = IdUtil.getSnowflakeNextId();

        CourseHoursPay courseHoursPay = new CourseHoursPay();
        BeanUtil.copyProperties(params, courseHoursPay);
        courseHoursPay.setId(batchNo);
        courseHoursPay.setStoreId(params.getStoreId());
        courseHoursPay.setSchoolId(params.getSchoolId());
        courseHoursPay.setStudentId(params.getUserId());
        courseHoursPay.setGift(params.getCourseHoursGift());
        courseHoursPay.setFormal(params.getCourseHoursFormal());
        courseHoursPay.setCourseType(params.getCourseType());
        //添加课次购买记录
        if(!isCourseHoursZero){
            baseMapper.insert(courseHoursPay);
        }

        List<CourseHoursRecordDTO> records = new ArrayList<>();

        if (params.getCourseHoursFormal() != null && params.getCourseHoursFormal() > StoreConstant.CODE) {
            records.add(initCourseHours(courseType, schoolId, storeId, studentId, CourseHoursOperationEnum.ENROLL.getDesc(), params.getCourseHoursFormal(), params.getTotalAmount()));
        }

        if (params.getCourseHoursGift() != null && params.getCourseHoursGift() > StoreConstant.CODE) {
            records.add(initCourseHours(courseType, schoolId, storeId, studentId, CourseHoursOperationEnum.GIFT.getDesc(), params.getCourseHoursGift(), BigDecimal.valueOf(0)));
        }

        if(!records.isEmpty()){
            addRecord(batchNo, records, CourseHoursLogTypeEnum.ENROLL.getCode());
        }

        int result = studentMapper.update(student,
                Wrappers.lambdaUpdate(Student.class).eq(Student::getUserId, student.getUserId()).eq(Student::getUpdateTime, student.getUpdateTime()));

        if (result != StoreConstant.SAVE_SUCCESS_ONE) {
            throw new BizException("操作失败 !");
        }

        // 统一处理学员类型转换统计：检查学员类型是否发生变化，如有变化则插入转换统计表
        if (!originalRegularType.equals(student.getIsRegularStudents())) {
            log.info("检测到学员类型发生变化，开始插入转换统计. 学员ID: {}, 原类型: {}, 新类型: {}",
                    studentId, originalRegularType, student.getIsRegularStudents());

            try {
                StudentRegularEnum targetRegularEnum = StudentRegularEnum.getByCode(student.getIsRegularStudents());
                if (targetRegularEnum != null) {
                    studentConversionService.convertMember(student, targetRegularEnum);
                    log.info("学员转换统计数据插入成功. 学员ID: {}, 原类型: {}, 目标类型: {}",
                            studentId, originalRegularType, targetRegularEnum.getDesc());
                } else {
                    log.warn("无法找到对应的学员类型枚举. 学员ID: {}, 类型代码: {}",
                            studentId, student.getIsRegularStudents());
                }
            } catch (Exception e) {
                log.error("插入学员转换统计数据失败，但不影响主流程. 学员ID: {}, 原类型: {}, 新类型: {}",
                        studentId, originalRegularType, student.getIsRegularStudents(), e);
                // 不抛出异常，确保转换统计失败不影响主业务流程
            }
        }

        //往队列发送候补消息
        alternateProducer.sendMessage(studentId, courseType);
        //往队列发送收费单信息
        if (isJoint(storeId) && !isCourseHoursZero && courseHoursPay.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            jointBillProducer.sendMessage(batchNo);
        }
    }


    /**
     * 判断门店是否是联营
     *
     * @param storeId 门店ID
     * @return boolean
     */
    @Override
    public boolean isJoint(Long storeId) {
        CampusEntity campus = campusMapper.selectById(storeId);

        if (campus == null) {
            return false;
        }
        return Objects.equals(campus.getCampusMode(), StoreConstant.JOINT_CAMPUS_MODE);
    }

    /**
     * 创建联营收费单
     *
     * @param batchNo 批次号
     */
    @Override
    public void createJointBill(Long batchNo) {
        //获取收费单
        CourseHoursPay courseHoursPay = baseMapper.selectById(batchNo);
        if (courseHoursPay == null) {
            log.error("创建联营收费单失败, 收费单不存在, batchNo:{}", batchNo);
            return;
        }

        if (Objects.equals(courseHoursPay.getNullify(), NullifyEnum.CANCEL.getCode())) {
            log.error("该联营收费单已作废,禁止操作! batchNo:{}", batchNo);
            return;
        }
        //获取门店联营附属信息
        CampusJoint campusJoint = campusJointMapper.selectById(courseHoursPay.getStoreId());
        if (campusJoint == null) {
            log.error("创建联营收费单失败, 门店附属信息不存在, storeId:{}", courseHoursPay.getStoreId());
            return;
        }

        //判断已经制单
        JointBill jointBill = jointBillMapper.selectById(batchNo);
        if (jointBill != null && Objects.equals(jointBill.getStatus(), JointBillStatusEnum.SUCCESS.getCode())) {
            log.error("该联营收费单已制单,禁止操作! batchNo:{}", batchNo);
            return;
        }

        // 获取所有课程类型信息
        Map<Integer, Integer> courseTypeMap;

        R<List<CourseTypeDTO>> courseTypeResult = remoteCourseTypeService.getAll();
        if (courseTypeResult.isOk() && CollUtil.isNotEmpty(courseTypeResult.getData())) {
            courseTypeMap = courseTypeResult.getData().stream()
                    .filter(item ->item.getJointPayType() != null)
                    .collect(Collectors.toMap(CourseTypeDTO::getId, CourseTypeDTO::getJointPayType));
        } else {
            log.error("获取课程类型信息失败或为空, result: {}", courseTypeResult);
            return;
        }

        if (jointBill == null){
            jointBill = new JointBill();
            BeanUtil.copyProperties(courseHoursPay, jointBill);
            jointBillMapper.insert(jointBill);
        }

        Student student = getStudentInfo(courseHoursPay.getSchoolId(), courseHoursPay.getStudentId());
        String employeeName = "";
        if(courseHoursPay.getAdvisorId() != null){
            Employee employee = employeeMapper.selectById(courseHoursPay.getAdvisorId());
            employeeName = employee.getName();
        }
        String notes = String.format("%s+%s+%s", student.getName(), student.getPhone(), employeeName);
        jointBill.setNotes(notes);

        jointBill.setJointPayType(courseTypeMap.get(courseHoursPay.getCourseType()));

        final JointBillDTO createJointBill = getJointBillDTO(jointBill, campusJoint);

        String response = okHttpFinanceClient.executePost(
                OkHttpFinanceClient.FinanceEndpoint.CREATE_BILL,
                JSON.toJSONString(createJointBill)
        );

        CreateBillResp result = JSONUtil.toBean(response, CreateBillResp.class);
        log.info("创建联营收费单结果返回, result: {}", result);
        if (result.getCode() != HttpStatus.HTTP_OK ) {
            log.error("创建联营收费单失败, result: {}", result);
            jointBill.setStatus(JointBillStatusEnum.FAIL.getCode());
            jointBill.setMessage(result.getMsg());
        } else {
            jointBill.setStatus(JointBillStatusEnum.SUCCESS.getCode());
            jointBill.setFinanceId(result.getData().getId());
            jointBill.setMessage("");
        }
        jointBillMapper.updateById(jointBill);
    }

    @NotNull
    private static JointBillDTO getJointBillDTO(JointBill jointBill, CampusJoint campusJoint) {
        JointBillDTO createJointBill = new JointBillDTO();
        createJointBill.setCategory(jointBill.getJointPayType());
        createJointBill.setCustomername(campusJoint.getCustomerName());
        createJointBill.setPhone(campusJoint.getSigningSubjectPhone());
        createJointBill.setMoney(jointBill.getTotalAmount().multiply(new BigDecimal(100)).setScale(0, RoundingMode.DOWN));
        createJointBill.setPaytype(jointBill.getPayType());
        createJointBill.setPaymethod(jointBill.getPayMethod());
        createJointBill.setPayDate(jointBill.getFeeDate());
        createJointBill.setMode(jointBill.getPayMode());
        createJointBill.setChargeorg(campusJoint.getOrg());
        createJointBill.setDepartment(campusJoint.getDepartmentId());
        createJointBill.setSerial(jointBill.getId().toString());
        createJointBill.setRemark(jointBill.getNotes());
        return createJointBill;
    }


    /**
     * 获取默认引流课最大节数
     *
     * @return Integer
     */
    private Integer getDefaultTrialMaxNumber() {
        String pictureBookTemplateId = ParamResolver.getStr(StoreConstant.TRIAL_MAX_NUMBER);
        if (pictureBookTemplateId.isBlank()){
            throw new BizException("参数配置 默认教学页ID为空！");
        }
        return Integer.valueOf(pictureBookTemplateId);
    }


    /**
     * 获取学员信息
     *
     * @param studentId 学员ID
     * @return Student
     */
    private Student getStudentInfo(Long schoolId, Long studentId) {

        Student oldStudent = studentMapper.selectOne(Wrappers.lambdaQuery(Student.class)
                .eq(Student::getUserId, studentId)
                .eq(schoolId != null, Student::getSchoolId, schoolId));

        if (oldStudent == null) {
            throw new BizException("该学员不存在,操作失败!");
        }
        return oldStudent;
    }


    /**
     * 获取学员单类型剩余课时
     *
     * @param storeId   门店ID
     * @param studentId 学员ID
     * @return Student
     */
    private CourseHoursStudent getCourseHoursStudent(Long schoolId, Long storeId, Long studentId, Integer courseType) {

        CourseHoursStudent courseHoursStudent = courseHoursStudentMapper.selectOne(Wrappers.<CourseHoursStudent>lambdaQuery()
                .eq(CourseHoursStudent::getSchoolId, schoolId)
                .eq(CourseHoursStudent::getStudentId, studentId)
                .eq(CourseHoursStudent::getCourseType, courseType)
                .last("LIMIT 1"));

        if (courseHoursStudent == null) {
            courseHoursStudent = new CourseHoursStudent();
            courseHoursStudent.setSchoolId(schoolId);
            courseHoursStudent.setStoreId(storeId);
            courseHoursStudent.setStudentId(studentId);
            courseHoursStudent.setCourseType(courseType);
            courseHoursStudent.setCourseHours(StoreConstant.CODE);
            courseHoursStudent.setFormal(StoreConstant.CODE);
            courseHoursStudent.setGift(StoreConstant.CODE);
            courseHoursStudent.setTotalAmount(BigDecimal.ZERO);
            courseHoursStudentMapper.insert(courseHoursStudent);
        }
        return courseHoursStudent;
    }


    /**
     * 添加课时
     */
    private CourseHoursRecordDTO initCourseHours(Integer courseType, Long schoolId, Long storeId, Long studentId, String operationType, Integer courseHours, BigDecimal amount) {
        CourseHoursRecordDTO item = new CourseHoursRecordDTO();
        item.setSchoolId(schoolId);
        item.setStoreId(storeId);
        item.setStudentId(studentId);
        item.setCourseType(courseType);
        item.setOperationType(operationType);
        item.setCount(courseHours);
        item.setQuantity(courseHours);
        item.setTotalAmount(amount);
        item.setResidueAmount(amount);

        if (amount.compareTo(BigDecimal.ZERO) > 0 && courseHours > 0) {
            BigDecimal unitPrice = amount.divide(new BigDecimal(courseHours), 4, RoundingMode.DOWN);
            item.setUnitPrice(unitPrice);
        }
        return item;
    }


    /**
     * 保存课次记录
     *
     * @param records 记录信息
     * @param type    添加类型
     */
    private void addRecord(Long batchNo, List<CourseHoursRecordDTO> records, Integer type) {

        //日志列表
        List<StoreCourseHoursLog> logList = new ArrayList<>();
        for (CourseHoursRecordDTO operation : records) {
            //设置课次信息(增加课次)
            StoreCourseHoursRecord createRecord = new StoreCourseHoursRecord();
            BeanUtil.copyProperties(operation, createRecord);
            createRecord.setBatchNo(batchNo);
            //添加课次记录
            courseHoursRecordMapper.insert(createRecord);

            StoreCourseHoursLog courseHoursLog = BeanUtil.copyProperties(createRecord, StoreCourseHoursLog.class, "id");
            courseHoursLog.setCourseHours(operation.getCount());
            courseHoursLog.setRelatedId(createRecord.getId());
            courseHoursLog.setLogType(type);
            logList.add(courseHoursLog);
        }

        //记录课次操作日志
        courseHoursLogMapper.insert(logList);
    }


    /**
     * 全部退费
     *
     * @param schoolId  校区ID
     * @param storeId   门店ID
     * @param studentId 学员ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundStudent(Long schoolId, Long storeId, Long studentId, LocalDate refundDate) {

        Student oldStudent = getStudentInfo(schoolId, studentId);

        if (oldStudent.getStatus() == StudentStatusEnum.LEAVE.getCode()) {
            throw new BizException("该学员已经已经退费,无需重复退费!");
        }

        if (oldStudent.getCourseHours() < StoreConstant.CODE) {
            throw new BizException("已欠费，无法退费，如若退费，请先补齐课次!");
        }

        fullRefund(schoolId, storeId, studentId, oldStudent,refundDate);

    }

    /**
     * 部分可退费列表
     *
     * @param params 学员信息**
     * 全部退费
     */
    @Override
    public List<CourseHoursPayVO> refundList(CourseHoursRefundQuery params) {

        List<StoreCourseHoursRecord> courseHoursRecord = courseHoursRecordMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursRecord.class)
                .eq(StoreCourseHoursRecord::getSchoolId, params.getSchoolId())
                .eq(StoreCourseHoursRecord::getStudentId, params.getStudentId())
                .eq(StoreCourseHoursRecord::getCourseType, params.getCourseType())
                .gt(StoreCourseHoursRecord::getQuantity, StoreConstant.CODE)
        );

        if (courseHoursRecord.isEmpty()){
            return Collections.emptyList();
        }

        Map<String, StoreCourseHoursRecord> batchMap = courseHoursRecord.stream()
                .collect(Collectors.toMap(
                        item -> item.getBatchNo() + "_" + item.getOperationType(),
                        item -> item,
                        (existing, replacement) -> existing
                ));

        //获取所有批次ID
        List<Long> batchNos = courseHoursRecord.stream().map(StoreCourseHoursRecord::getBatchNo).distinct().toList();

        return courseHoursPayMapper.selectList(Wrappers.lambdaQuery(CourseHoursPay.class)
                .in(CourseHoursPay::getId, batchNos)
                .eq(CourseHoursPay::getSchoolId, params.getSchoolId())
                .eq(CourseHoursPay::getStudentId, params.getStudentId())
                .eq(CourseHoursPay::getCourseType, params.getCourseType())
        ).stream().map(item -> {
            CourseHoursPayVO courseHoursPayVO = new CourseHoursPayVO();
            BeanUtil.copyProperties(item, courseHoursPayVO);

            StoreCourseHoursRecord giftRecord = batchMap.get(item.getId() + "_" + CourseHoursOperationEnum.GIFT.getDesc());
            StoreCourseHoursRecord enrollRecord = batchMap.get(item.getId() + "_" + CourseHoursOperationEnum.ENROLL.getDesc());

            courseHoursPayVO.setCourseHoursGift(giftRecord != null ? giftRecord.getQuantity() : 0);
            courseHoursPayVO.setCourseHoursFormal(enrollRecord != null ? enrollRecord.getQuantity() : 0);

            return courseHoursPayVO;
        }).toList();
    }


    /**
     * 退费
     *
     * @param storeId    校区
     * @param studentId  学员
     * @param oldStudent 原数据信息
     */
    private void fullRefund(Long schoolId, Long storeId, Long studentId, Student oldStudent,LocalDate refundDate) {

        //更改剩余课时
        int result = studentMapper.update(Wrappers.lambdaUpdate(Student.class).eq(Student::getUserId, studentId)
                .eq(Student::getUpdateTime, oldStudent.getUpdateTime())
                .set(Student::getCourseHours, StoreConstant.CODE)
                .set(Student::getAmount, BigDecimal.ZERO)
                .set(Student::getStatus, StudentStatusEnum.LEAVE.getCode()));

        if (result != StoreConstant.SAVE_SUCCESS_ONE) {
            throw new BizException("操作失败!");
        }

        //添加课次记录+课次日志
        refund(schoolId, storeId, studentId, refundDate);

        //清空未开始的调课课程
        remoteTimetableChangeService.missedClassRescheduleClear(studentId, storeId);
    }


    /**
     * 全部退费
     *
     * @param schoolId   校区
     * @param studentId 学生
     */
    private void refund(Long schoolId, Long storeId, Long studentId, LocalDate refundDate) {

        courseHoursStudentMapper.update(Wrappers.lambdaUpdate(CourseHoursStudent.class)
                .set(CourseHoursStudent::getFormal, StoreConstant.CODE)
                .set(CourseHoursStudent::getGift, StoreConstant.CODE)
                .set(CourseHoursStudent::getCourseHours, StoreConstant.CODE)
                .set(CourseHoursStudent::getTotalAmount, BigDecimal.ZERO)
                .eq(CourseHoursStudent::getSchoolId, schoolId)
                .eq(CourseHoursStudent::getStudentId, studentId)
                .gt(CourseHoursStudent::getCourseHours, StoreConstant.CODE));

        // 获取需要退费的课次记录
        List<StoreCourseHoursRecord> records = courseHoursRecordMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursRecord.class)
                .eq(StoreCourseHoursRecord::getSchoolId, schoolId)
                .eq(StoreCourseHoursRecord::getStudentId, studentId)
                .gt(StoreCourseHoursRecord::getQuantity, StoreConstant.CODE));

        //联营退费
        List<JointBillRefundOrderDTO> jonitBillRefundList  = new ArrayList<>();

        // 记录课次操作日志
        List<StoreCourseHoursLog> logList = new ArrayList<>();

        //批次号 雪花ID
        Long batchNo = IdUtil.getSnowflakeNextId();

        // 批量更新 Quantity 为 0
        if (!records.isEmpty()) {

            List<Long> ids = records.stream().map(StoreCourseHoursRecord::getId).toList();
            courseHoursRecordMapper.update(Wrappers.lambdaUpdate(StoreCourseHoursRecord.class)
                    .set(StoreCourseHoursRecord::getQuantity, StoreConstant.CODE)
                    .set(StoreCourseHoursRecord::getResidueAmount, StoreConstant.CODE)
                    .in(StoreCourseHoursRecord::getId, ids));

            records.forEach(item -> logList.add(createRefundLog(item, CourseHoursLogTypeEnum.REFUND.getCode(), batchNo, jonitBillRefundList)));

            // 批量插入日志
            courseHoursLogMapper.insert(logList);
        }
        if (!jonitBillRefundList.isEmpty() && isJoint(storeId)) {
            jointBillRefund(jonitBillRefundList,refundDate);
        }

        if (!logList.isEmpty()){
            //记录退费单
            refundProducer.sendMessage(batchNo, refundDate, SecurityUtils.getUser().getId());
        }
    }

    private void jointBillRefund(List<JointBillRefundOrderDTO> jonitBillRefundList,LocalDate refundDate) {

        if (refundDate == null){
            throw new BizException("退费日期不能为空!");
        }
        //获取jointBillId
        Map<Long,String> financeIdMap = jointBillMapper.selectBatchIds(jonitBillRefundList.stream().map(JointBillRefundOrderDTO::getBatchNo).toList()).stream() .collect(Collectors.toMap(
                JointBill::getId,
                JointBill::getFinanceId
        ));

        if (jonitBillRefundList.size() != financeIdMap.size()) {
            throw new BizException("需退费单数量不等于收费单数量，退费失败。请联系技术人员!");
        }

        jonitBillRefundList.forEach(item -> {
            String financeId = financeIdMap.get(item.getBatchNo());
            if (financeId == null || financeId.isEmpty()) {
                throw new BizException("有未生成收费单的批次!");
            }
            item.setId(financeId);
        });
        JointBillRefundDTO jointBillRefund = new JointBillRefundDTO();

        jointBillRefund.setPayDate(refundDate.toString());
        jointBillRefund.setOrders(jonitBillRefundList);

        String response = okHttpFinanceClient.executePost(
                OkHttpFinanceClient.FinanceEndpoint.BATCH_REFUND,
                JSON.toJSONString(jointBillRefund)
        );

        CreateBillResp result = JSONUtil.toBean(response, CreateBillResp.class);
        log.info("退费联营收费单结果返回, result: {}", result);
        if (result.getCode() != HttpStatus.HTTP_OK ) {
            throw new BizException("财务系统错误提示:" + result.getMsg());
        }

    }

    /**
     * 给学员部分退费
     *
     * @param params 退费信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal refundAmount(CoursePartRefundDTO params, Boolean isCompute) {

        //查询是否存在学员
        Long studentId = params.getStudentId();
        Long schoolId = params.getSchoolId();
        Long storeId = params.getStoreId();
        LocalDate refundDate = params.getRefundDate();

        Student oldStudent = getStudentInfo(schoolId, studentId);

        if (oldStudent.getStatus() == StudentStatusEnum.LEAVE.getCode()) {
            throw new BizException("该学员已经已经退费,无需重复退费!");
        }

        if ((oldStudent.getCourseHours() < StoreConstant.CODE)) {
            throw new BizException("已欠费，无法退费，如若退费，请先补齐课次!");
        }

        //根据批次查询所有需要退款的记录
        List<Long> batchNoList = params.getCoursePackage().stream().map(CoursePackageDTO::getBatchNo).toList();
        List<StoreCourseHoursRecord> records = courseHoursRecordMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursRecord.class)
                .eq(StoreCourseHoursRecord::getSchoolId, schoolId)
                .eq(StoreCourseHoursRecord::getStudentId, studentId)
                .in(StoreCourseHoursRecord::getBatchNo, batchNoList)
                .gt(StoreCourseHoursRecord::getQuantity, StoreConstant.CODE)
        );

        if (records.isEmpty()) {
            throw new BizException("该批次没有可退的课次，请核实后操作!");
        }

        Map<String, StoreCourseHoursRecord> batchMap = records.stream()
                .collect(Collectors.toMap(
                        item -> item.getBatchNo() + "_" + item.getOperationType(),
                        item -> item
                ));

        // 记录课次操作日志
        List<Long> zero = new ArrayList<>();
        List<StoreCourseHoursLog> logList = new ArrayList<>();
        List<StoreCourseHoursRecord> recordList = new ArrayList<>();
        List<JointBillRefundOrderDTO> jonitBillRefundList  = new ArrayList<>();

        Map<Integer, CourseHoursStudentDTO> courseHoursStudentMap = new HashMap<>();

        //批次号 雪花ID
        Long refundBatchNo = IdUtil.getSnowflakeNextId();

        params.getCoursePackage().forEach(coursePackage -> {

            String batchNo = coursePackage.getBatchNo().toString();
            String keyEnroll = batchNo + "_" + CourseHoursOperationEnum.ENROLL.getDesc();
            String keyGift = batchNo + "_" + CourseHoursOperationEnum.GIFT.getDesc();
            StoreCourseHoursRecord courseHoursRecordEnroll = batchMap.get(keyEnroll);
            StoreCourseHoursRecord courseHoursRecordGift = batchMap.get(keyGift);

            if (courseHoursRecordEnroll == null && coursePackage.getCourseHoursFormal() > StoreConstant.CODE) {
                throw new BizException("批次：" + "batchNo" + "超出可退正式课次，请核实后操作!");
            }

            if (courseHoursRecordGift == null && coursePackage.getCourseHoursGift() > StoreConstant.CODE) {
                throw new BizException("批次：" + "batchNo" + "超出可退赠送课次，请核实后操作!");
            }

            if (courseHoursRecordEnroll != null && coursePackage.getCourseHoursFormal() > courseHoursRecordEnroll.getQuantity()) {
                throw new BizException("批次：" + "batchNo" + "超出可退正式课次，请核实后操作!");
            }

            if (courseHoursRecordGift != null && coursePackage.getCourseHoursGift() > courseHoursRecordGift.getQuantity()) {
                throw new BizException("批次：" + "batchNo" + "超出可退赠送课次，请核实后操作!");
            }

            if (courseHoursRecordEnroll != null && coursePackage.getCourseHoursFormal() >= StoreConstant.CODE) {
                updateRecordAndLog(courseHoursRecordEnroll, coursePackage.getCourseHoursFormal(), zero, logList, recordList, CourseHoursLogTypeEnum.PART_REFUND.getCode(), courseHoursStudentMap,jonitBillRefundList);
            }

            if (courseHoursRecordGift != null && coursePackage.getCourseHoursGift() >= StoreConstant.CODE) {
                updateRecordAndLog(courseHoursRecordGift, coursePackage.getCourseHoursGift(), zero, logList, recordList, CourseHoursLogTypeEnum.PART_REFUND.getCode(), courseHoursStudentMap,jonitBillRefundList);
            }

        });

        if (Boolean.TRUE.equals(isCompute)) {
            return courseHoursStudentMap.values().stream().map(CourseHoursStudentDTO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }


        if (!zero.isEmpty()) {
            courseHoursRecordMapper.update(Wrappers.lambdaUpdate(StoreCourseHoursRecord.class)
                    .set(StoreCourseHoursRecord::getQuantity, StoreConstant.CODE)
                    .set(StoreCourseHoursRecord::getResidueAmount, BigDecimal.ZERO)
                    .in(StoreCourseHoursRecord::getId, zero));
        }
        if (!recordList.isEmpty()) {
            recordList.forEach(courseHoursRecordMapper::updateById);
        }
        if (!logList.isEmpty()) {
            //给所有logList赋值退费批次编号batchNo
            logList.forEach(item -> item.setBatchNo(refundBatchNo));
            courseHoursLogMapper.insert(logList);
        }
        if (!courseHoursStudentMap.isEmpty()) {

            List<CourseHoursStudent> courseHoursStudentList = courseHoursStudentMapper.selectList(Wrappers.lambdaUpdate(CourseHoursStudent.class)
                    .eq(CourseHoursStudent::getSchoolId, schoolId)
                    .eq(CourseHoursStudent::getStudentId, studentId)
                    .in(CourseHoursStudent::getCourseType, courseHoursStudentMap.keySet().stream().toList()));
            //更新课程类型剩余课次记录
            courseHoursStudentList.forEach(item -> {
                CourseHoursStudentDTO courseHoursStudent = courseHoursStudentMap.get(item.getCourseType());

                item.setCourseHours(item.getCourseHours() - courseHoursStudent.getCourseHours());
                item.setFormal(item.getFormal() - courseHoursStudent.getFormal());
                item.setGift(item.getGift() - courseHoursStudent.getGift());
                item.setTotalAmount(item.getTotalAmount().subtract(courseHoursStudent.getTotalAmount()));
                courseHoursStudentMapper.updateById(item);
            });
        }

        BigDecimal money = courseHoursStudentMap.values().stream().map(CourseHoursStudentDTO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        int courseHours = courseHoursStudentMap.values().stream().map(CourseHoursStudentDTO::getCourseHours).reduce(StoreConstant.CODE, Integer::sum);

        //判断是否全部退费
        if (courseHoursStudentMapper.getCourseHoursSumByStudentId(studentId) == StoreConstant.CODE) {
            oldStudent.setStatus(StudentStatusEnum.LEAVE.getCode());
        }

        //更新学员信息
        int result = studentMapper.update(Wrappers.lambdaUpdate(Student.class)
                .eq(Student::getUserId, studentId)
                .eq(Student::getUpdateTime, oldStudent.getUpdateTime())
                .set(Student::getCourseHours, oldStudent.getCourseHours() - courseHours)
                .set(Student::getStatus, oldStudent.getStatus())
                .set(Student::getAmount, oldStudent.getAmount().subtract(money)));

        if (result != StoreConstant.SAVE_SUCCESS_ONE) {
            throw new BizException("操作失败!");
        }
        if (!jonitBillRefundList.isEmpty() && isJoint(storeId)) {
            jointBillRefund(jonitBillRefundList,refundDate);
        }
        if (!logList.isEmpty()) {
            //记录退费单
            refundProducer.sendMessage(refundBatchNo, refundDate, SecurityUtils.getUser().getId());
        }
        if (oldStudent.getStatus() == StudentStatusEnum.LEAVE.getCode()) {
            //清空未开始的调课课程
            remoteTimetableChangeService.missedClassRescheduleClear(studentId, storeId);
        }
        return money;
    }


    /**
     * 课消
     */
    @Override
    @Transactional (rollbackFor = Exception.class)
    public void reduction(CourseHoursRidDTO ridStudent) {

        Student student = getStudentInfo(ridStudent.getSchoolId(), ridStudent.getStudentId());

        //判断这节课这个学生是否课消 已课消直接返回正确
        StoreCourseHoursLog storeCourseHoursLog = courseHoursLogMapper.selectOne(Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                .eq(StoreCourseHoursLog::getSchoolId, ridStudent.getSchoolId())
                .eq(StoreCourseHoursLog::getStudentId, ridStudent.getStudentId())
                .eq(StoreCourseHoursLog::getTimetableId, ridStudent.getTimetableId())
                .orderByDesc(StoreCourseHoursLog::getId)
                .last("limit 1")
        );

        if (storeCourseHoursLog != null && storeCourseHoursLog.getLogType() == CourseHoursLogTypeEnum.CONSUME.getCode()) {
            return;
        }

        CourseHoursStudent courseHoursStudent = courseHoursStudentMapper.selectOne(Wrappers.lambdaQuery(CourseHoursStudent.class)
                .eq(CourseHoursStudent::getSchoolId, ridStudent.getSchoolId())
                .eq(CourseHoursStudent::getStudentId, ridStudent.getStudentId())
                .eq(CourseHoursStudent::getCourseType, ridStudent.getCourseType())
                .last("limit 1"));

        if (courseHoursStudent == null) {
            throw new BizException("该学生没有该课程类型的课次记录，课消失败!");
        }

        StoreCourseHoursRecord item = courseHoursRecordMapper.selectOne(Wrappers.lambdaQuery(StoreCourseHoursRecord.class)
                .eq(StoreCourseHoursRecord::getSchoolId, ridStudent.getSchoolId())
                .eq(StoreCourseHoursRecord::getStudentId, ridStudent.getStudentId())
                .eq(StoreCourseHoursRecord::getCourseType, ridStudent.getCourseType())
                .gt(StoreCourseHoursRecord::getQuantity, StoreConstant.CODE)
                .orderByAsc(StoreCourseHoursRecord::getId)
                .last("limit 1"));


        StoreCourseHoursLog courseHoursLog;


        BigDecimal reductionAmount = BigDecimal.ZERO;

        //扣一次课时
        student.setCourseHours(student.getCourseHours() - StoreConstant.CONSUME_COURSE_HOUR);
        courseHoursStudent.setCourseHours(courseHoursStudent.getCourseHours() - StoreConstant.CONSUME_COURSE_HOUR);

        if (item != null) {
            reductionAmount = item.getUnitPrice();

            // 更新课次记录
            courseHoursLog = createRefundLog(item, CourseHoursLogTypeEnum.CONSUME.getCode(), StoreConstant.CONSUME_COURSE_HOUR, ridStudent.getTimetableId());

            if (item.getQuantity() == StoreConstant.CONSUME_COURSE_HOUR) {
                reductionAmount = item.getResidueAmount();
                item.setResidueAmount(BigDecimal.ZERO);
            } else {
                item.setResidueAmount(item.getResidueAmount().subtract(reductionAmount));
            }
            item.setQuantity(item.getQuantity() - StoreConstant.CONSUME_COURSE_HOUR);

            if (Objects.equals(item.getOperationType(), CourseHoursOperationEnum.ENROLL.getDesc())) {
                courseHoursStudent.setFormal(courseHoursStudent.getFormal() - StoreConstant.CONSUME_COURSE_HOUR);
            } else {
                courseHoursStudent.setGift(courseHoursStudent.getGift() - StoreConstant.CONSUME_COURSE_HOUR);
            }

            // 更新课次记录
            courseHoursRecordMapper.updateById(item);
        } else {
            //没有记录 扣一次正式课次
            if (student.getIsRegularStudents() == StudentRegularEnum.TRIAL.getCode()) {
                throw new BizException("该学员没有课次记录,操作失败!");
            }
            // 处理无记录的情况
            courseHoursLog = createDefaultRefundLog(student, ridStudent.getStoreId(), ridStudent.getCourseType(), ridStudent.getTimetableId());
            courseHoursStudent.setFormal(courseHoursStudent.getFormal() - StoreConstant.CONSUME_COURSE_HOUR);
        }

        courseHoursLogMapper.insert(courseHoursLog);

        student.setAmount(student.getAmount().subtract(reductionAmount));
        courseHoursStudent.setTotalAmount(courseHoursStudent.getTotalAmount().subtract(reductionAmount));

        courseHoursStudentMapper.updateById(courseHoursStudent);
        int result = studentMapper.update(student,
                Wrappers.lambdaUpdate(Student.class)
                        .eq(Student::getUserId, student.getUserId())
                        .eq(Student::getUpdateTime, student.getUpdateTime()));

        if (result != StoreConstant.SAVE_SUCCESS_ONE) {
            throw new BizException("操作失败!");
        }
    }


    /**
     * 取消考勤
     *
     * @param params 取消考勤
     */
    @Override
    @Transactional (rollbackFor = Exception.class)
    public void courseHoursCancel(CourseHoursCancelDTO params) {

        Long storeId = params.getStoreId();
        Long timetableId = params.getTimetableId();
        Long schoolId = params.getSchoolId();
        Long studentId = params.getStudentId();

        Student student = getStudentInfo(schoolId, studentId);

        Long count = courseHoursLogMapper.selectCount(Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                .eq(StoreCourseHoursLog::getSchoolId, schoolId)
                .eq(StoreCourseHoursLog::getStudentId, studentId)
                .eq(StoreCourseHoursLog::getTimetableId, timetableId)
                .orderByDesc(StoreCourseHoursLog::getId)
        );

        //判断这节课这个学生是否课消 已课消直接返回正确
        if (count % 2 == 0) {
            return; // 偶数，无需处理
        }

        StoreCourseHoursLog item = courseHoursLogMapper.selectOne(Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                .eq(StoreCourseHoursLog::getSchoolId, schoolId)
                .eq(StoreCourseHoursLog::getStudentId, studentId)
                .eq(StoreCourseHoursLog::getTimetableId, timetableId)
                .eq(StoreCourseHoursLog::getNullify, NullifyEnum.DEFAULT.getCode())
                .orderByDesc(StoreCourseHoursLog::getId)
                .last("limit 1")
        );

        if (item == null) {
            throw new BizException("课消记录不存在，无法取消考勤,操作失败!");
        }

        if (item.getLogType() != CourseHoursLogTypeEnum.CONSUME.getCode()) {
            throw new BizException("取消考勤,数据类型错误,操作失败!");
        }

        CourseHoursStudent courseHoursStudent = courseHoursStudentMapper.selectOne(Wrappers.lambdaQuery(CourseHoursStudent.class)
                .eq(CourseHoursStudent::getSchoolId, schoolId)
                .eq(CourseHoursStudent::getStudentId, studentId)
                .eq(CourseHoursStudent::getCourseType, item.getCourseType())
                .last("limit 1"));

        if (courseHoursStudent == null) {
            throw new BizException("该学生没有该课程类型的课次记录，取消课消失败!");
        }

        student.setCourseHours(student.getCourseHours() + Math.abs(item.getCourseHours()));
        student.setAmount(student.getAmount().add(item.getTotalAmount().abs()));
        courseHoursStudent.setCourseHours(courseHoursStudent.getCourseHours() + Math.abs(item.getCourseHours()));
        courseHoursStudent.setTotalAmount(courseHoursStudent.getTotalAmount().add(item.getTotalAmount().abs()));

        if (item.getRelatedId() != null) {
            // 获取当前扣费课次
            StoreCourseHoursRecord courseHoursRecord = courseHoursRecordMapper.selectOne(Wrappers.lambdaQuery(StoreCourseHoursRecord.class)
                    .eq(StoreCourseHoursRecord::getStudentId, student.getUserId())
                    .eq(StoreCourseHoursRecord::getId, item.getRelatedId())
                    .last("limit 1")
            );

            if (courseHoursRecord != null) {
                // 计算新的课次数量
                int newQuantity = courseHoursRecord.getQuantity() + Math.abs(item.getCourseHours());
                // 计算新的剩余课次金额
                BigDecimal newResidueAmount = courseHoursRecord.getResidueAmount().add(item.getTotalAmount().abs());
                // 更新课次记录
                courseHoursRecordMapper.update(Wrappers.lambdaUpdate(StoreCourseHoursRecord.class)
                        .eq(StoreCourseHoursRecord::getStudentId, student.getUserId())
                        .eq(StoreCourseHoursRecord::getId, item.getRelatedId())
                        .eq(StoreCourseHoursRecord::getUpdateTime, courseHoursRecord.getUpdateTime())
                        .set(StoreCourseHoursRecord::getResidueAmount, newResidueAmount)
                        .set(StoreCourseHoursRecord::getQuantity, newQuantity));

            }
        }
        //回补课程类型剩余课次
        if (item.getOperationType().equals(CourseHoursOperationEnum.ENROLL.getDesc())) {
            courseHoursStudent.setFormal(courseHoursStudent.getFormal() + Math.abs(item.getCourseHours()));
        } else {
            courseHoursStudent.setGift(courseHoursStudent.getGift() + Math.abs(item.getCourseHours()));
        }

        courseHoursStudentMapper.updateById(courseHoursStudent);

        StoreCourseHoursLog courseHoursLog = new StoreCourseHoursLog();
        courseHoursLog.setCourseType(item.getCourseType());
        courseHoursLog.setCourseHours(Math.abs(item.getCourseHours()));
        courseHoursLog.setLogType(CourseHoursLogTypeEnum.CANCEL.getCode());
        courseHoursLog.setSchoolId(student.getSchoolId());
        courseHoursLog.setStoreId(storeId);
        courseHoursLog.setStudentId(student.getUserId());
        courseHoursLog.setOperationType(item.getOperationType());
        courseHoursLog.setTimetableId(timetableId);
        courseHoursLog.setRelatedId(item.getId());
        courseHoursLog.setUnitPrice(item.getUnitPrice());
        courseHoursLog.setTotalAmount(item.getUnitPrice());

        courseHoursLogMapper.insert(courseHoursLog);

        item.setNullify(NullifyEnum.CANCEL.getCode());
        courseHoursLogMapper.update(item, Wrappers.lambdaUpdate(StoreCourseHoursLog.class)
                .eq(StoreCourseHoursLog::getStudentId, student.getUserId())
                .eq(StoreCourseHoursLog::getId, item.getId()));

        int result = studentMapper.update(student, Wrappers.lambdaUpdate(Student.class)
                .eq(Student::getUserId, student.getUserId())
                .eq(Student::getUpdateTime, student.getUpdateTime()));

        if (result != StoreConstant.SAVE_SUCCESS_ONE) {
            throw new BizException("操作失败!");
        }
        //往队列发送候补消息
        alternateProducer.sendMessage(studentId,item.getCourseType());
    }

    /**
     * 作废收费单
     *
     * @param params 作废收费单
     */
    @Override
    @Transactional (rollbackFor = Exception.class)
    public void revoke(CourseHoursRevokeDTO params) {

        Long schoolId = params.getSchoolId();
        Long storeId =  params.getStoreId();
        Long studentId = params.getStudentId();
        Long batchNo = params.getBatchNo();
        Student oldStudent = getStudentInfo(schoolId, studentId);

        CourseHoursPay courseHoursPay = baseMapper.selectOne(Wrappers.lambdaQuery(CourseHoursPay.class)
                .eq(CourseHoursPay::getSchoolId, schoolId)
                .eq(CourseHoursPay::getStudentId, studentId)
                .eq(CourseHoursPay::getId, batchNo)
                .last("limit 1")
        );

        if (courseHoursPay == null) {
            throw new BizException("该批次收费单不存在,操作失败!");
        }

        if(Objects.equals(courseHoursPay.getNullify(), NullifyEnum.CANCEL.getCode())){
            throw new BizException("该批次收费单已作废,禁止操作!");
        }


        List<StoreCourseHoursRecord> courseHoursRecord = courseHoursRecordMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursRecord.class)
                .eq(StoreCourseHoursRecord::getSchoolId, schoolId)
                .eq(StoreCourseHoursRecord::getStudentId, studentId)
                .eq(StoreCourseHoursRecord::getBatchNo, batchNo)
        );

        if (courseHoursRecord.isEmpty()){
             throw new BizException("该收费单详情不存在,操作失败!");
        }

        // 记录课次操作日志
        List<StoreCourseHoursLog> logList = new ArrayList<>();

        courseHoursRecord.forEach(item -> {
            if (!Objects.equals(item.getQuantity(), item.getCount())) {
                throw new BizException("该收费单已课消或部分退费,不允许作废!");
            }
            logList.add(createRefundLog(item, CourseHoursLogTypeEnum.REVOKE.getCode()));
        });

        courseHoursRecordMapper.update(Wrappers.lambdaUpdate(StoreCourseHoursRecord.class)
                .set(StoreCourseHoursRecord::getQuantity, StoreConstant.CODE)
                .set(StoreCourseHoursRecord::getResidueAmount, BigDecimal.ZERO)
                .eq(StoreCourseHoursRecord::getStudentId, studentId)
                .in(StoreCourseHoursRecord::getId, courseHoursRecord.stream().map(StoreCourseHoursRecord::getId).toList()));

        courseHoursLogMapper.insert(logList);

        CourseHoursStudent courseHoursStudent = getCourseHoursStudent(schoolId, oldStudent.getStoreId(), studentId, courseHoursPay.getCourseType());

        courseHoursStudent.setCourseHours(courseHoursStudent.getCourseHours() - courseHoursPay.getGift() - courseHoursPay.getFormal());
        courseHoursStudent.setFormal(courseHoursStudent.getFormal() - courseHoursPay.getFormal());
        courseHoursStudent.setGift(courseHoursStudent.getGift() - courseHoursPay.getGift());
        courseHoursStudent.setTotalAmount(courseHoursStudent.getTotalAmount().subtract(courseHoursPay.getTotalAmount()));
        courseHoursStudentMapper.updateById(courseHoursStudent);

        courseHoursPay.setNullify(NullifyEnum.CANCEL.getCode());
        courseHoursPayMapper.updateById(courseHoursPay);
        //更新学员信息
        int result = studentMapper.update(Wrappers.lambdaUpdate(Student.class)
                .eq(Student::getUserId, studentId)
                .eq(Student::getUpdateTime, oldStudent.getUpdateTime())
                .set(Student::getCourseHours, oldStudent.getCourseHours() - courseHoursPay.getGift() - courseHoursPay.getFormal())
                .set(Student::getAmount, oldStudent.getAmount().subtract(courseHoursPay.getTotalAmount())));

        if (result != StoreConstant.SAVE_SUCCESS_ONE) {
            throw new BizException("操作失败!");
        }

        if (isJoint(storeId) && courseHoursPay.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            cancelJointBill(batchNo);
        }
    }

    private void cancelJointBill(Long batchNo) {
        
        //获取收费单
        JointBill jointBill = jointBillMapper.selectById(batchNo);
        if (jointBill == null){
            throw new BizException("未找到联营收费单,操作失败!");
        }
        if(!Objects.equals(jointBill.getStatus(), JointBillStatusEnum.SUCCESS.getCode())){
            throw new BizException("联营收费单制单状态不是成功状态,操作失败!");
        }

        JointBillCancelDTO jointBillCancel = new JointBillCancelDTO();
        jointBillCancel.setId(jointBill.getFinanceId());
        String response = okHttpFinanceClient.executePost(
                OkHttpFinanceClient.FinanceEndpoint.ORDER_CANCEL,
                JSON.toJSONString(jointBillCancel)
        );

        CreateBillResp result = JSONUtil.toBean(response, CreateBillResp.class);
        log.info("作废联营收费单结果返回, result: {}", result);
        if (result.getCode() != HttpStatus.HTTP_OK ) {
            throw new BizException("财务系统错误提示:" + result.getMsg());
        }
    }

    /**
     * 候补课消
     * @param studentId   学员ID
     * @param courseType  课次类型
     */
    @Override
    @Transactional (rollbackFor = Exception.class)
    public Long alternate(Long studentId, Integer courseType) {
        Student student = getStudentInfo(null, studentId);

        //查询是否有需要候补的订单
        StoreCourseHoursLog charge = courseHoursLogMapper.selectOne(Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                .eq(StoreCourseHoursLog::getStudentId, studentId)
                .eq(StoreCourseHoursLog::getLogType, CourseHoursLogTypeEnum.CONSUME.getCode())
                .eq(StoreCourseHoursLog::getNullify, NullifyEnum.DEFAULT.getCode())
                .eq(StoreCourseHoursLog::getCourseType, courseType)
                .isNull(StoreCourseHoursLog::getRelatedId)
                .orderByAsc(StoreCourseHoursLog::getId)
                .last("limit 1")
        );
        if (charge == null) {
            return 0L;
        }

        //查询是否有可扣减的批次
        StoreCourseHoursRecord courseHoursRecord = courseHoursRecordMapper.selectOne(Wrappers.lambdaUpdate(StoreCourseHoursRecord.class)
                .eq(StoreCourseHoursRecord::getStudentId, studentId)
                .eq(StoreCourseHoursRecord::getCourseType , courseType)
                .gt(StoreCourseHoursRecord::getQuantity, StoreConstant.CODE)
                .orderByAsc(StoreCourseHoursRecord::getId)
                .last("limit 1")
        );

        if (courseHoursRecord == null) {
            return 0L;
        }

        //查询学生该类型下是否有剩余课次记录
         CourseHoursStudent courseHoursStudent = courseHoursStudentMapper.selectOne(Wrappers.lambdaQuery(CourseHoursStudent.class)
                .eq(CourseHoursStudent::getStudentId, studentId)
                .eq(CourseHoursStudent::getCourseType, courseType)
                .last("limit 1")
        );

        if (courseHoursStudent == null){
            log.error("候补订单，学生: {}该类型: {}课次记录不存在",studentId,courseType);
            return null;
        }

        //查询是否多条数据
        Long count = courseHoursLogMapper.selectCount(Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                .eq(StoreCourseHoursLog::getStudentId, studentId)
                .eq(StoreCourseHoursLog::getLogType, CourseHoursLogTypeEnum.CONSUME.getCode())
                .eq(StoreCourseHoursLog::getNullify, NullifyEnum.DEFAULT.getCode())
                .eq(StoreCourseHoursLog::getCourseType, courseType)
                .isNull(StoreCourseHoursLog::getRelatedId));


        BigDecimal reduceAmount;
        // 更新课次记录的数量
        int absCourseHours = Math.abs(charge.getCourseHours());
        charge.setOperationType(courseHoursRecord.getOperationType());
        if (courseHoursRecord.getQuantity() <= absCourseHours) {
            charge.setCourseHours(-courseHoursRecord.getQuantity());
            charge.setUnitPrice(courseHoursRecord.getUnitPrice());
            reduceAmount = courseHoursRecord.getResidueAmount();
            charge.setTotalAmount(reduceAmount.negate());
            courseHoursRecord.setQuantity(StoreConstant.CODE);
            courseHoursRecord.setResidueAmount(BigDecimal.ZERO);
        } else {
            charge.setCourseHours(-absCourseHours);
            charge.setUnitPrice(courseHoursRecord.getUnitPrice());
            reduceAmount = BigDecimal.valueOf(absCourseHours).multiply(courseHoursRecord.getUnitPrice());
            charge.setTotalAmount(reduceAmount.negate());
            courseHoursRecord.setQuantity(courseHoursRecord.getQuantity() - absCourseHours);
            courseHoursRecord.setResidueAmount(courseHoursRecord.getResidueAmount().subtract(reduceAmount));
        }

        // 设置日志与课次记录的关联关系
        charge.setRelatedId(courseHoursRecord.getId());

        courseHoursLogMapper.updateById(charge);
        courseHoursRecordMapper.updateById(courseHoursRecord);

        courseHoursStudent.setTotalAmount(courseHoursStudent.getTotalAmount().subtract(reduceAmount));
        courseHoursStudentMapper.updateById(courseHoursStudent);

        //减少剩余金额
        student.setAmount(student.getAmount().subtract(reduceAmount));

        int result = studentMapper.update(student, Wrappers.lambdaUpdate(Student.class)
                .eq(Student::getUserId, student.getUserId())
                .eq(Student::getUpdateTime, student.getUpdateTime()));

        if (result != StoreConstant.SAVE_SUCCESS_ONE) {
            throw new BizException("操作失败 !");
        }

        return count;
    }

    private StoreCourseHoursLog createDefaultRefundLog(Student student, Long storeId, Integer courseType, Long timetableId) {
        StoreCourseHoursLog courseHoursLog = new StoreCourseHoursLog();
        courseHoursLog.setCourseType(courseType);
        courseHoursLog.setCourseHours(-StoreConstant.CONSUME_COURSE_HOUR);
        courseHoursLog.setLogType(CourseHoursLogTypeEnum.CONSUME.getCode());
        courseHoursLog.setSchoolId(student.getSchoolId());
        courseHoursLog.setStoreId(storeId);
        courseHoursLog.setStudentId(student.getUserId());
        courseHoursLog.setOperationType(CourseHoursOperationEnum.ENROLL.getDesc());
        courseHoursLog.setTimetableId(timetableId);
        return courseHoursLog;
    }

    private void updateRecordAndLog(StoreCourseHoursRecord item, Integer remainingHours, List<Long> zero, List<StoreCourseHoursLog> logList, List<StoreCourseHoursRecord> recordList, Integer logType, Map<Integer, CourseHoursStudentDTO> courseHoursStudentMap,List<JointBillRefundOrderDTO> jonitBillRefundList) {

        int deductCourseHours;
        BigDecimal residueAmount;
        if (remainingHours == StoreConstant.CODE) {
            deductCourseHours = item.getQuantity();
            zero.add(item.getId());
            logList.add(createRefundLog(item, logType));
            residueAmount = item.getResidueAmount();

        } else {

            deductCourseHours = item.getQuantity() - remainingHours;
            //相等不扣费
            if (deductCourseHours == StoreConstant.CODE){
                return;
            }
            // 剩余可退课次小于剩余课次
            item.setQuantity(remainingHours);
            residueAmount = BigDecimal.valueOf(deductCourseHours).multiply(item.getUnitPrice());
            item.setResidueAmount(item.getResidueAmount().subtract(residueAmount));
            recordList.add(item);

            logList.add(createRefundLog(item, logType, deductCourseHours));
        }

        if (residueAmount.compareTo(BigDecimal.ZERO) > 0) {
            JointBillRefundOrderDTO jointBillRefundOrder = new JointBillRefundOrderDTO();
            jointBillRefundOrder.setBatchNo(item.getBatchNo());
            jointBillRefundOrder.setMoney(residueAmount.multiply(new BigDecimal(100)).setScale(0, RoundingMode.DOWN));
            jonitBillRefundList.add(jointBillRefundOrder);
        }

        int deductCourseHoursFormal = 0;
        int deductCourseHoursGift = 0;
        if (Objects.equals(item.getOperationType(), CourseHoursOperationEnum.GIFT.getDesc())) {
            deductCourseHoursGift = deductCourseHours;
        } else {
            deductCourseHoursFormal = deductCourseHours;
        }
        CourseHoursStudentDTO courseHoursStudent = courseHoursStudentMap.get(item.getCourseType());
        if (courseHoursStudent == null) {
            courseHoursStudent = CourseHoursStudentDTO.builder()
                    .courseType(item.getCourseType())
                    .courseHours(deductCourseHoursFormal + deductCourseHoursGift)
                    .formal(deductCourseHoursFormal)
                    .gift(deductCourseHoursGift)
                    .totalAmount(residueAmount)
                    .build();
        } else {
            courseHoursStudent.setFormal(courseHoursStudent.getFormal() + deductCourseHoursFormal);
            courseHoursStudent.setGift(courseHoursStudent.getGift() + deductCourseHoursGift);
            courseHoursStudent.setCourseHours(courseHoursStudent.getCourseHours() + deductCourseHoursFormal + deductCourseHoursGift);
            courseHoursStudent.setTotalAmount(courseHoursStudent.getTotalAmount().add(residueAmount));
        }

        courseHoursStudentMap.put(item.getCourseType(), courseHoursStudent);
    }

    private StoreCourseHoursLog createRefundLog(StoreCourseHoursRecord item, Integer type, Long batchNo, List<JointBillRefundOrderDTO> jonitBillRefundList) {
        StoreCourseHoursLog courseHoursLog = BeanUtil.copyProperties(item, StoreCourseHoursLog.class, "id");
        courseHoursLog.setCourseHours(-item.getQuantity());
        courseHoursLog.setTotalAmount(item.getResidueAmount().negate());
        courseHoursLog.setRelatedId(item.getId());
        courseHoursLog.setLogType(type);
        courseHoursLog.setBatchNo(batchNo);

        if (item.getResidueAmount().compareTo(BigDecimal.ZERO) > 0) {
            JointBillRefundOrderDTO jointBillRefundOrder = new JointBillRefundOrderDTO();
            jointBillRefundOrder.setBatchNo(item.getBatchNo());
            jointBillRefundOrder.setMoney(item.getResidueAmount().multiply(new BigDecimal(100)).setScale(0, RoundingMode.DOWN));
            jonitBillRefundList.add(jointBillRefundOrder);
        }

        return courseHoursLog;
    }
    private StoreCourseHoursLog createRefundLog(StoreCourseHoursRecord item, Integer type) {
        StoreCourseHoursLog courseHoursLog = BeanUtil.copyProperties(item, StoreCourseHoursLog.class, "id");
        courseHoursLog.setCourseHours(-item.getQuantity());
        courseHoursLog.setTotalAmount(item.getResidueAmount().negate());
        courseHoursLog.setRelatedId(item.getId());
        courseHoursLog.setLogType(type);
        return courseHoursLog;
    }

    private StoreCourseHoursLog createRefundLog(StoreCourseHoursRecord item, Integer type, Integer courseHours) {
        StoreCourseHoursLog courseHoursLog = BeanUtil.copyProperties(item, StoreCourseHoursLog.class, "id");
        courseHoursLog.setCourseHours(-courseHours);
        // 计算总金额
        BigDecimal quantity = new BigDecimal(courseHours);
        BigDecimal totalAmount = quantity.multiply(item.getUnitPrice());
        courseHoursLog.setTotalAmount(totalAmount.negate());
        courseHoursLog.setRelatedId(item.getId());
        courseHoursLog.setLogType(type);
        return courseHoursLog;
    }

    private StoreCourseHoursLog createRefundLog(StoreCourseHoursRecord item, Integer type, Integer courseHours, Long relatedId) {
        StoreCourseHoursLog courseHoursLog = BeanUtil.copyProperties(item, StoreCourseHoursLog.class, "id");
        courseHoursLog.setCourseHours(-courseHours);

        BigDecimal totalAmount;
        if (Objects.equals(item.getQuantity(), courseHours)) {
            totalAmount = item.getResidueAmount();
        } else {
            // 计算总金额
            totalAmount = (new BigDecimal(courseHours)).multiply(item.getUnitPrice());
        }

        courseHoursLog.setTotalAmount(totalAmount.negate());
        courseHoursLog.setUnitPrice(item.getUnitPrice());

        courseHoursLog.setRelatedId(item.getId());
        courseHoursLog.setLogType(type);
        courseHoursLog.setTimetableId(relatedId);
        return courseHoursLog;
    }

    /**
     * 收费分页查询
     * @param page
     * @param storeCourseHoursPayQuery
     */
    @Override
    public Page<StoreCourseHoursPayVO> getBillPage(Page page, StoreCourseHoursPayQuery storeCourseHoursPayQuery) {
        Map<Long, String> studentNameMap = new HashMap<>();
        Map<Long, String> studentPhoneMap = new HashMap<>();
        Map<Long, Integer> studentGradeMap = new HashMap<>();
        List<Long> userIds =  new ArrayList<>();
            List<Student> studentList = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                    .select(Student::getUserId, Student::getName, Student::getPhone, Student::getGrade)
                    .eq(storeCourseHoursPayQuery.getStudentId() != null, Student::getUserId, storeCourseHoursPayQuery.getStudentId())
                    .and(CharSequenceUtil.isNotBlank(storeCourseHoursPayQuery.getStudentName()), wrapper -> wrapper
                            .like(Student::getName, storeCourseHoursPayQuery.getStudentName())
                            .or()
                            .like(Student::getPinyinPre, storeCourseHoursPayQuery.getStudentName())
                    )
                    .eq(StringUtils.isNotBlank(storeCourseHoursPayQuery.getStudentPhone()), Student::getPhone, storeCourseHoursPayQuery.getStudentPhone())
                    .eq(storeCourseHoursPayQuery.getSchoolId() != null, Student::getSchoolId, storeCourseHoursPayQuery.getSchoolId()));

            if (CollectionUtils.isEmpty(studentList) && (storeCourseHoursPayQuery.getStudentName() != null || storeCourseHoursPayQuery.getStudentPhone() != null)) {
                return new Page<>(page.getCurrent(), page.getSize());
            }
            for (Student student : studentList) {
                Long userId = student.getUserId();
                studentNameMap.put(userId, student.getName());
                studentPhoneMap.put(userId, student.getPhone());
                studentGradeMap.put(userId, student.getGrade());
                userIds.add(userId);
            }
        CampusQuery campusQuery = new CampusQuery();
        campusQuery.setCampusId(storeCourseHoursPayQuery.getStoreId());
        //获取年级Map
        R<List<SysDictItem>> gradeList = remoteDictService.getDictsByType("student_grade_type");
        Map<String, String> gradeMap = gradeList.getData().stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getLabel));
        //获取收费类型Map
        R<List<SysDictItem>> studentTypeList = remoteDictService.getDictsByType("bill_type");
        Map<String, String> billTypeMap = studentTypeList.getData().stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getLabel));
        //业绩归属人Map
        List<EmployeeVO> teacherList = employeeService.getEmployeeByCampusId(campusQuery, EmployeeCampusStatusEnum.ACTIVE.getCode());
        Map<Long, String> teacherMap = teacherList.stream().collect(Collectors.toMap(EmployeeVO::getUserId, EmployeeVO::getName));
        //获取课程类型
        R<List<CourseTypeDTO>> courseTypeResult = remoteCourseTypeService.getAll();
        Map<Integer, String> courseTypeMap;
        if (courseTypeResult.isOk()) {
            courseTypeMap = courseTypeResult.getData().stream().collect(Collectors.toMap(CourseTypeDTO::getId, CourseTypeDTO::getName));
        } else {
            courseTypeMap = Collections.emptyMap();
        }
        //获取收费信息
        Page<CourseHoursPay> courseHoursPayPage = page(page, Wrappers.<CourseHoursPay>lambdaQuery()
                .in(ObjectUtil.isNotEmpty(userIds) && (storeCourseHoursPayQuery.getStudentId() != null || StringUtils.isNotBlank(storeCourseHoursPayQuery.getStudentName()) || StringUtils.isNotBlank(storeCourseHoursPayQuery.getStudentPhone())),CourseHoursPay::getStudentId, userIds)
                .eq(storeCourseHoursPayQuery.getStudentId() != null, CourseHoursPay::getStudentId, storeCourseHoursPayQuery.getStudentId())
                .eq(storeCourseHoursPayQuery.getCourseType() != null, CourseHoursPay::getCourseType, storeCourseHoursPayQuery.getCourseType())
                .eq(storeCourseHoursPayQuery.getSchoolId() != null, CourseHoursPay::getSchoolId, storeCourseHoursPayQuery.getSchoolId())
                .eq(storeCourseHoursPayQuery.getStoreId() != null, CourseHoursPay::getStoreId, storeCourseHoursPayQuery.getStoreId())
                .eq(storeCourseHoursPayQuery.getAdvisorId() != null, CourseHoursPay::getAdvisorId, storeCourseHoursPayQuery.getAdvisorId())
                .like(storeCourseHoursPayQuery.getCreateBy() != null && !storeCourseHoursPayQuery.getCreateBy().isEmpty(),
                        CourseHoursPay::getCreateBy, storeCourseHoursPayQuery.getCreateBy())
                .eq(storeCourseHoursPayQuery.getNullify() != null, CourseHoursPay::getNullify, storeCourseHoursPayQuery.getNullify())
                .eq(storeCourseHoursPayQuery.getFeeType() != null, CourseHoursPay::getFeeType, storeCourseHoursPayQuery.getFeeType())
                .le(storeCourseHoursPayQuery.getFeeEndDate() != null && !storeCourseHoursPayQuery.getFeeEndDate().isEmpty(), CourseHoursPay::getFeeDate, storeCourseHoursPayQuery.getFeeEndDate()+" 23:59:59")
                .ge(storeCourseHoursPayQuery.getFeeStartDate() != null && !storeCourseHoursPayQuery.getFeeStartDate().isEmpty(), CourseHoursPay::getFeeDate, storeCourseHoursPayQuery.getFeeStartDate())
                .orderByDesc(CourseHoursPay::getUpdateTime));
        Page<StoreCourseHoursPayVO> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<StoreCourseHoursPayVO> resultRecords = courseHoursPayPage.getRecords().stream()
                .map(entity -> {
                    StoreCourseHoursPayVO vo = new StoreCourseHoursPayVO();
                    BeanUtil.copyProperties(entity, vo);
                    Long studentId = entity.getStudentId();
                    vo.setNum(String.valueOf(entity.getId()));
                    vo.setName(studentNameMap.getOrDefault(studentId, ""));
                    vo.setPhone(studentPhoneMap.getOrDefault(studentId, ""));
                    vo.setGrade(studentGradeMap.getOrDefault(studentId, 0));
                    BigDecimal amount = entity.getTotalAmount() != null ? entity.getTotalAmount() : BigDecimal.ZERO;
                    BigDecimal truncated = amount.abs().setScale(2, RoundingMode.DOWN);
                    vo.setTotalAmount(truncated);
                    vo.setGradeName(gradeMap.getOrDefault(String.valueOf(vo.getGrade()), ""));
                    vo.setCourseTypeName(courseTypeMap.getOrDefault(vo.getCourseType(), ""));
                    vo.setFeeTypeName(billTypeMap.getOrDefault(String.valueOf(vo.getFeeType()), ""));
                    Integer formalGift = vo.getFormal()+vo.getGift();
                    vo.setCourseHoursContent(formalGift+"("+vo.getFormal() + "+" + vo.getGift()+")");
                    vo.setNullifyName(entity.getNullify() == 1 ? "作废" : "正常");
                    vo.setAdvisorName(teacherMap.getOrDefault(entity.getAdvisorId(), ""));
                    return vo;
                }).toList();
        resultPage.setRecords(resultRecords);
        return resultPage;
    }

    /**
     * 通过id查询收费信息
     * @param id
     */
    @Override
    public StoreCourseHoursPayVO getBillById(Long id) {
        CourseHoursPay courseHoursPay = courseHoursPayMapper.selectById(id);
        StoreCourseHoursPayVO storeCourseHoursPayVO = new StoreCourseHoursPayVO();
        BeanUtils.copyProperties(courseHoursPay, storeCourseHoursPayVO);
        Student studentInfo = studentMapper.selectById(courseHoursPay.getStudentId());
        storeCourseHoursPayVO.setName(studentInfo.getName());
        storeCourseHoursPayVO.setPhone(studentInfo.getPhone());
        storeCourseHoursPayVO.setGrade(studentInfo.getGrade());
        BigDecimal amount = courseHoursPay.getTotalAmount() != null ? courseHoursPay.getTotalAmount() : BigDecimal.ZERO;
        BigDecimal truncated = amount.abs().setScale(2, RoundingMode.DOWN);
        storeCourseHoursPayVO.setTotalAmount(truncated);
        return storeCourseHoursPayVO;
    }

    /**
     * 编辑收费单
     *
     * @param params 编辑收费单
     */
    @Override
    public void edit(CourseHoursEditDTO params) {

        Long schoolId = params.getSchoolId();
        Long batchNo = params.getId();
        CourseHoursPay courseHoursPay = baseMapper.selectOne(Wrappers.lambdaQuery(CourseHoursPay.class)
                .eq(CourseHoursPay::getSchoolId, schoolId)
                .eq(CourseHoursPay::getId, batchNo)
                .last("limit 1")
        );

        if (courseHoursPay == null) {
            throw new BizException("该收费单不存在,操作失败!");
        }
        if(Objects.equals(courseHoursPay.getNullify(), NullifyEnum.CANCEL.getCode())){
            throw new BizException("该收费单已作废,禁止操作!");
        }
        //更新收费信息
        int result = baseMapper.update(Wrappers.lambdaUpdate(CourseHoursPay.class)
                .eq(CourseHoursPay::getId, batchNo)
                .eq(CourseHoursPay::getSchoolId, schoolId)
                .set(CourseHoursPay::getFeeDate, params.getFeeDate())
                .set(CourseHoursPay::getFeeType, params.getFeeType())
                .set(CourseHoursPay::getAdvisorId, params.getAdvisorId())
                .set(CourseHoursPay::getNotes, params.getNotes())
                .set(CourseHoursPay::getUpdateBy, "1:"+SecurityUtils.getUser().getId()+":"+SecurityUtils.getUser().getName())
                .set(CourseHoursPay::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
        );
        if (result != StoreConstant.SAVE_SUCCESS_ONE) {
            throw new BizException("操作失败!");
        }
    }
}













    