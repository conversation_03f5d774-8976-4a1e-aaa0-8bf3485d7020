package com.yuedu.store.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuedu.store.entity.CourseHoursStudent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
@Mapper
public interface CourseHoursStudentMapper extends BaseMapper<CourseHoursStudent> {

    @Select("SELECT IFNULL(SUM(course_hours), 0) FROM store_course_hours_student WHERE student_id = #{studentId}")
    Integer getCourseHoursSumByStudentId(Long studentId);

}
