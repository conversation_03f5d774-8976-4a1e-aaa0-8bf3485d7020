package com.yuedu.store.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName IntentionStudentDetailVO
 * @Description 意向学员详情VO类
 * <AUTHOR>
 * @Date 2025/06/25
 * @Version v0.0.1
 */
@Data
@Schema(description = "意向学员详情VO")
public class IntentionStudentDetailVO {

    /**
     * 学员ID
     */
    @Schema(description = "学员ID")
    private Long userId;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;

    /**
     * 性别 1男 0女
     */
    @Schema(description = "性别 1男 0女")
    private Integer sex;

    /**
     * 性别描述
     */
    @Schema(description = "性别描述")
    private String sexDesc;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 年级
     */
    @Schema(description = "年级")
    private Integer grade;

    /**
     * 录入来源
     */
    @Schema(description = "录入来源")
    private Integer origin;

    /**
     * 录入来源描述
     */
    @Schema(description = "录入来源描述")
    private String originDesc;

    /**
     * 阶段Id
     */
    @Schema(description = "阶段Id")
    private Integer stageId;

    /**
     * 学校
     */
    @Schema(description = "学校")
    private String fulltimeSchool;

    /**
     * 负责老师ID
     */
    @Schema(description = "负责老师ID")
    private Long responsiblePerson;

    /**
     * 负责老师姓名
     */
    @Schema(description = "负责老师姓名")
    private String responsiblePersonName;

    /**
     * 转介绍学员ID
     */
    @Schema(description = "转介绍学员ID")
    private Long referralStudentId;

    /**
     * 转介绍学员姓名
     */
    @Schema(description = "转介绍学员姓名")
    private String referralStudentName;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String describe;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 跟进记录列表
     */
    @Schema(description = "跟进记录列表")
    private List<IntentionStudentTrackRecordVO> trackRecords;
}
