package com.yuedu.store.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * @author: 邵子珍
 * @date: 2025-02-11
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClassQuery {

    /**
     * 校区ID
     */
    @Schema(description = "校区ID")
    private Long campusId;

    /**
     * 校管家校区ID
     */
    @Schema(description = "校管家校区ID")
    private String xgjCampusId;

    /**
     * 状态 0未结业
     */
    @Schema(description = "状态")
    private Integer isFinished = 0;

    /**
     * 代课老师id
     */
    @Schema(description = "带课老师id")
    private Long headTeacherId;

    /**
     * 班级名称
     */
    @Schema(description = "班级名称")
    private String name;

    /**
     * 当前班级
     */
    @Schema(description = "当前班级")
    private Integer classId;

}
