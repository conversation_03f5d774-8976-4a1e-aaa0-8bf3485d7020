package com.yuedu.store.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: yuxingkui
 * @date: 2025/2/20
 **/
@Getter
@AllArgsConstructor
public enum StudentRegularEnum {


    /**
     * 意向
     */
    INTENTION(2, "意向"),

    /**
     * 正式
     */
    FORMAL(1, "正式"),

    /**
     * 试听
     */
    TRIAL(0, "试听");

    /**
     * 类型
     */
    private final int code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return StudentRegularEnum
     */
    public static StudentRegularEnum getByCode(int code) {
        for (StudentRegularEnum value : StudentRegularEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
