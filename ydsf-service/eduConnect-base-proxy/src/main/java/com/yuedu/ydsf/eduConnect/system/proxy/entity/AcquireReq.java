package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Builder;
import lombok.Data;

/**
 * 声网资源请求类
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@Data
@Builder
public class AcquireReq {

    /**
     * 频道名
     */
    private String cname;
    /**
     * 频道内使用的 UID
     */
    private String uid;
    /**
     * 客户端请求
     */
    private ClientRequest clientRequest;


    @Data
    @Builder
    public static class ClientRequest {
        /**
         * 云端录制资源使用场景:
         * 0: 除去页面录制、单流录制且开启延时转码/混音的场景
         * 1: 页面录制
         * 2: 单流录制
         */
        @Builder.Default
        private Integer scene = 0;
        /**
         * 云端录制 RESTful API 的调用时效 单位为小时
         */
        private Integer resourceExpiredHour;
    }

}
