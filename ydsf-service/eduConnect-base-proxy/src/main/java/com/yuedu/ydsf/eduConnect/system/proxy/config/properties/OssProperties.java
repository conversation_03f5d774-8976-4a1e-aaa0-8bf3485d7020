package com.yuedu.ydsf.eduConnect.system.proxy.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/02/07
 **/
@Data
@ConfigurationProperties(prefix = "oss")
public class OssProperties {


    private String endpoint;


    private String region;


    private String accessKeyId;


    private String accessKeySecret;

    private String bucketName;

    @NestedConfigurationProperty
    private OssBizProperties live;

    @Data
    public static class OssBizProperties{

        private String prefix;

        private String domain;

    }
}
