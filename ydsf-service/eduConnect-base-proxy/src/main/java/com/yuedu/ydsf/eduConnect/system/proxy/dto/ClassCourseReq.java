package com.yuedu.ydsf.eduConnect.system.proxy.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;

/**
 * 保存校管家创建班级/排课类
 * <AUTHOR>
 * @date 2024年01月19日 09时57分
 */
@Data
public class ClassCourseReq {

    /**
     * 三方id(校区授权班级信息ID)
     */
    @JsonProperty("tripartiteId")
    private String tripartiteId;
    /**
     * 课程ID
     */
    @JsonProperty("cShiftID")
    private String cShiftID;
    /**
     * 校区ID
     */
    @JsonProperty("cCampusID")
    private String cCampusID;
    /**
     * 班级名称
     */
    @JsonProperty("cName")
    private String cName;
    /**
     * -1-无操作; 0-新增; 1-修改; 2-删除; 3-结业;
     */
    @JsonProperty("type")
    private Integer type;

    @JsonIgnore
    private Long classTimeId;

    /**
     * 新增排课
     */
    private List<CreateCourseReq> createCourseList = new ArrayList<>();

    /**
     * 编辑排课
     */
    private List<UpdateCourseReq> updateCourseList = new ArrayList<>();

    /**
     * 删除排课
     */
    private List<DeleteCourseReq> deleteCourseList = new ArrayList<>();


    @AllArgsConstructor
    public enum CreateClassType {
        /**
         * 无操作
         */
        CREATE_CLASS_TYPE(-1,"无操作"),
        /**
         * 新增
         */
        CREATE_CLASS_TYPE_0(0,"新增"),
        /**
         * 修改
         */
        CREATE_CLASS_TYPE_1(1,"修改"),
        /**
         * 删除
         */
        CREATE_CLASS_TYPE_2(2,"删除"),
        /**
         * 结业
         */
        CREATE_CLASS_TYPE_3(3,"结业");

        public final Integer CODE;

        public final String MSG;
    }

    public static Object getKey(ClassCourseReq classCourseReq) {
        return new AbstractMap.SimpleEntry<>(classCourseReq.getTripartiteId(), classCourseReq.getType());
    }

}
