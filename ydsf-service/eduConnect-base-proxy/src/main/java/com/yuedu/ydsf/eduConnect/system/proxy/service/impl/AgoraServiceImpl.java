package com.yuedu.ydsf.eduConnect.system.proxy.service.impl;

import com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AgoraTokenGenerator;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.AgoraConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.AgoraRoomType;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.*;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.*;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RoomReq.RoomProperties;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RoomReq.RoomProperties.Processes;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RoomReq.RoomProperties.Processes.HandsUp;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RoomReq.RoomProperties.RoleConfig;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RoomReq.RoomProperties.RoleConfig.DefaultStream;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RoomReq.RoomProperties.RoleConfig.Two;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RoomReq.RoomProperties.Schedule;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.AgoraEducationApi;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.AgoraRecordApi;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.AgoraRtmApi;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import com.yuedu.ydsf.eduConnect.system.proxy.util.RtcTokenBuilder;
import com.yuedu.ydsf.eduConnect.system.proxy.util.SignUtils;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.UUID;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/09/30
 **/
@Slf4j
public class AgoraServiceImpl implements AgoraService {

    private final AgoraTokenGenerator agoraTokenGenerator;

    private final AgoraEducationApi agoraEducationApi;

    private final AgoraRecordApi agoraRecordApi;

    private final AgoraRtmApi agoraRtmApi;

    @Override
    public boolean handlerEvent(AgoraEventDTO agoraEventDTO,
        Function<AgoraUploadEventDTO, Boolean> function) {
        log.info("声网推送处理: {}", agoraEventDTO);
        try {
            return switch (agoraEventDTO.getEventType()) {
                case AgoraConstant.CLOUD_FILE_UPLOAD_FINISH -> {
                    log.info("处理云端文件上传完成事件: {}", agoraEventDTO);
                    yield Objects.isNull(function) ? true
                        : function.apply(AgoraUploadEventDTO.builder()
                            .recordFileUpload(
                                agoraEventDTO.getPayload().toJavaObject(RecordFileUpload.class))
                            .templateId(agoraTokenGenerator.getAgoraVodTemplateId())
                            .build());
                }
                default -> true;
            };
        } catch (Exception e) {
            log.error("声网推送处理错误：body: {}  error: {}", agoraEventDTO,
                e.getMessage());
        }

        return false;
    }

    @Override
    public boolean verifySign(String body, String sign) {
        log.info("===============声网消息推送===============");
        try {
            log.info("声网验签:{} {} ", body, sign);
            return SignUtils.verifySign(sign, body, agoraTokenGenerator.getAgoraSignSecret());
        } catch (Exception e) {
            log.info("声网验签失败: body:{}  sign:{}  error: {}", body, sign, e.getMessage());
        }

        return false;
    }

    @Override
    public String syncAgoraClassRoom(CreateAgoraClassRoomDTO createAgoraClassRoomDTO) {
        log.debug("同步创建声网教室信息: {}", createAgoraClassRoomDTO);
        RoomResp roomResp;
        try {
            // 上课开始时间(yyyy-MM-dd HH:mm)
            LocalDateTime attendClassDateStartTime = LocalDateTime.of(
                createAgoraClassRoomDTO.getAttendClassDate(),
                createAgoraClassRoomDTO.getAttendClassStartTime());
            // 上课结束时间(yyyy-MM-dd HH:mm)
            LocalDateTime attendClassDateEndTime = LocalDateTime.of(
                createAgoraClassRoomDTO.getAttendClassDate(),
                createAgoraClassRoomDTO.getAttendClassEndTime());
            // 校验当前时间距离开课时间时长(单位:分钟)
            // 开课时间在开课前24小时内, 则同步至声网创建课堂, 大于24小时则暂时不同步至声网
            Duration duration = Duration.between(LocalDateTime.now(), attendClassDateStartTime);
            if (duration.toMinutes() > AgoraConstant.DEFAULT_TIME_MINUTE) {
                return null;
            }
            // 此课次声网UUID
            createAgoraClassRoomDTO.setRoomUuid(UUID.randomUUID().toString());
            // 计算上课结束时间与上课开始时间持续时长(单位:秒)
            long seconds = Duration.between(attendClassDateStartTime, attendClassDateEndTime).getSeconds();
            // 同步声网课堂信息
            RoomReq roomReq = RoomReq.builder()
                .roomName(createAgoraClassRoomDTO.getBooksName())
                .roomType(AgoraRoomType.ROOM_TYPE_4.code())
                .roomProperties(RoomProperties.builder()
                    .schedule(Schedule.builder()
                        .startTime(
                            attendClassDateStartTime.atZone(ZoneId.systemDefault()).toInstant()
                                .toEpochMilli())
                        .duration((int) seconds)
                        .build())
                    .processes(Processes.builder()
                        .handsUp(HandsUp.builder()
                            .maxAccept(AgoraConstant.MAX_ACCEPT)
                            .build())
                        .build())
                    .roleConfig(RoleConfig.builder()
                        .two(Two.builder()
                            .defaultStream(DefaultStream.builder()
                                .audioState(AgoraConstant.AUDIO_STATE_DISABLED)
                                .build())
                            .build())
                        .build())
                    .build()).build();

            roomResp = agoraEducationApi.createRoom(createAgoraClassRoomDTO.getRoomUuid(),
                roomReq);
            log.debug("同步声网创建课堂信息: {}", roomResp);
        } catch (Exception e) {
            log.error("同步声网创建课堂信息异常: {}", e.getMessage(), e);
            throw new BizException("同步声网创建房间失败！");
        }
        if (Objects.isNull(roomResp)) {
            log.error("同步声网创建课堂信息失败: {}", "roomResp is null");
            throw new BizException("同步声网创建房间失败！");
        } else if (!AgoraConstant.RESULT_SUCCESS.equals(roomResp.getCode())) {
            log.error("同步声网创建课堂信息失败: {}, errorCode={}", roomResp.getMsg(),
                roomResp.getCode());
            throw new BizException("同步声网创建房间失败！");
        } else {
            log.debug("同步声网创建课堂信息成功: RoomUuid={}",
                createAgoraClassRoomDTO.getRoomUuid());
        }
        return createAgoraClassRoomDTO.getRoomUuid();
    }

    @Override
    public void startRecording(StartRecordingDTO startRecordingDTO) {
        log.info("开始录制请求: {}", startRecordingDTO);
        Long randUid = null;
        do {
            randUid = new Random().nextLong(10000000) + 1;
        } while (randUid.equals(startRecordingDTO.getDeviceId()));

        StreamInfoResp streamInfoResp = null;
        try {
            //获取流信息
            streamInfoResp = agoraEducationApi.streamInfo(
                    startRecordingDTO.getRoomUuid(),
                    String.valueOf(startRecordingDTO.getDeviceId()));
            log.info("获取流信息: {}", streamInfoResp);
            if (Objects.isNull(streamInfoResp)) {
                throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "声网接口返回空！");
            }
        } catch (Exception e) {
            log.error("获取流信息异常", e.getMessage());
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "获取流信息网络错误:" + e.getMessage());
        }

        if (!streamInfoResp.isSuccess()) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "获取流信息错误:" + streamInfoResp.getMsg());
        }

        if (Integer.valueOf(0).equals(streamInfoResp.getData().getState())) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "未检测到流信息");
        }

        AcquireResp acquire = null;
        try {
            //获取资源
            acquire = agoraRecordApi.acquire(AcquireReq.builder()
                    .uid(randUid.toString())
                    .cname(startRecordingDTO.getRoomUuid())
                    .clientRequest(AcquireReq.ClientRequest.builder().build())
                    .build());
            log.info("获取录制资源: {}", acquire);
            if (Objects.isNull(acquire)) {
                throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "声网接口返回空！");
            }
        } catch (Exception e) {
            log.error("获取录制资源异常", e.getMessage());
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "获取录制资源网络错误:" + e.getMessage());
        }

        if (!acquire.isSuccess()) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "创建录制资源错误:" + acquire.getReason());
        }

        StartResp startResp = null;
        try {
            //开始录制
            startResp = agoraRecordApi.start(acquire.getResourceId(),
                    AgoraConstant.AGORA_RECORDING_MODE_MIX,
                    StartReq.builder()
                            .uid(acquire.getUid())
                            .cname(startRecordingDTO.getRoomUuid())
                            .clientRequest(StartReq.ClientRequest.builder()
                                    .token(agoraTokenGenerator.getAgoraRtcToken(startRecordingDTO.getRoomUuid(),
                                            acquire.getUid(), RtcTokenBuilder.Role.Role_Subscriber))
                                    .recordingConfig(StartReq.ClientRequest.RecordingConfig.builder()
                                            .subscribeVideoUids(List.of(streamInfoResp.getData().getStreamUuid()))
                                            .subscribeAudioUids(List.of(streamInfoResp.getData().getStreamUuid()))
                                            .transcodingConfig(
                                                    StartReq.ClientRequest.RecordingConfig.TranscodingConfig.builder()
                                                            .build())
                                            .extensionParams(
                                                    StartReq.ClientRequest.RecordingConfig.ExtensionParams.builder()
                                                            .build())
                                            .build())
                                    .recordingFileConfig(StartReq.ClientRequest.RecordingFileConfig.builder()
                                            .avFileType(List.of("hls", "mp4"))
                                            .build())
                                    .storageConfig(StartReq.ClientRequest.StorageConfig.builder()
                                            .accessKey(agoraTokenGenerator.getStorageKey())
                                            .secretKey(agoraTokenGenerator.getStorageSecret())
                                            .bucket(agoraTokenGenerator.getStorageBucket())
                                            .fileNamePrefix(List.of(agoraTokenGenerator.getAppId(),
                                                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                                                    startRecordingDTO.getRoomUuid().replace("-", "").toLowerCase()))
                                            .build())
                                    .build())

                            .build());

            log.info("开始录制: {}", startResp);
            if (Objects.isNull(startResp)) {
                throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "声网接口返回空！");
            }
        } catch (Exception e) {
            log.error("开始录制异常", e.getMessage());
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "开始录制网络错误:" + e.getMessage());
        }

        if (!startResp.isSuccess()) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "开启录制错误:" + acquire.getReason());
        }

        startRecordingDTO.setAgoraCloudRecordId(startResp.getSid());
        startRecordingDTO.setCloudRecordingResources(startResp.getResourceId());
        startRecordingDTO.setAgoraCloudRecordIndividualId(startResp.getUid());

    }

    @Override
    public void stopRecording(StopRecordingDTO stopRecordingDTO) {

        StopResp stopResp = null;
        try {
            log.info("停止录制请求: {}", stopRecordingDTO);
            stopResp = agoraRecordApi.stop(stopRecordingDTO.getCloudRecordingResources(),
                    stopRecordingDTO.getAgoraCloudRecordId(),
                    AgoraConstant.AGORA_RECORDING_MODE_MIX,
                    StopReq.builder()
                            .uid(stopRecordingDTO.getAgoraCloudRecordIndividualId())
                            .cname(stopRecordingDTO.getRoomUuid())
                            .clientRequest(StopReq.ClientRequest.builder().build())
                            .build());
            log.info("停止录制: {}", stopResp);
            if (Objects.isNull(stopResp)) {
                throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "声网接口返回空！");
            }
        } catch (Exception e) {
            log.warn("停止录制网络错误:{}", e.getMessage());
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "停止录制网络错误:" + e.getMessage());
        }

        if (!stopResp.isSuccess()) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "停止录制错误:" + stopResp.getReason());
        }

    }

    @Override
    public void createRecordingRoom(CreateRecordingRoomDTO createRecordingRoomDTO) {

        RoomResp room = null;
        try {
            log.info("创建录制房间: {}", createRecordingRoomDTO);
            room = agoraEducationApi.createRoom(createRecordingRoomDTO.getRoomId(),
                    RoomReq.builder()
                            .roomName(createRecordingRoomDTO.getRoomName())
                            .roomProperties(RoomReq.RoomProperties.builder()
                                    .schedule(RoomReq.RoomProperties.Schedule.builder()
                                            .startTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli())
                                            .build())
                                    .processes(RoomReq.RoomProperties.Processes.builder()
                                            .handsUp(RoomReq.RoomProperties.Processes.HandsUp.builder().build())
                                            .build())
                                    .roleConfig(RoomReq.RoomProperties.RoleConfig.builder()
                                            .two(RoomReq.RoomProperties.RoleConfig.Two.builder()
                                                    .defaultStream(RoomReq.RoomProperties.RoleConfig.DefaultStream.builder().build())
                                                    .build())
                                            .build())
                                    .build())
                            .build());
            log.info("创建录制房间: {}", room);
            if (Objects.isNull(room)) {
                throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "声网接口返回空！");
            }
        } catch (Exception e) {
            log.error("创建录制房间网络错误:{}", e.getMessage());
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "创建录制房间网络错误:" + e.getMessage());
        }

        if (!room.isSuccess()) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "创建录制房间响应错误:" + room.getMsg());
        }


    }

    @Override
    public void removeUser(RemoveRoomUserDTO removeRoomUserDTO) {
        RemoveUserResp removeUserResp = null;
        try {
            log.info("移除用户: {}", removeRoomUserDTO);
            removeUserResp = agoraEducationApi.removeUser(removeRoomUserDTO.getRoomId(), removeRoomUserDTO.getUserId().toString(),
                    RemoveUserReq.builder()
                            .dirty(RemoveUserReq.Dirty.builder().build())
                            .build());
            log.info("移除用户: {}", removeUserResp);
            if (Objects.isNull(removeUserResp)) {
                throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "声网接口返回空！");
            }
        } catch (Exception e) {
            log.error("移除用户网络错误:{}", e.getMessage());
        }

        if (!removeUserResp.isSuccess()) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "移除用户错误:" + removeUserResp.getMsg());
        }

    }

  /**
   * 声网更新自定义房间属性
   *
   * <AUTHOR>
   * @date 2024/11/13 8:57
   * @param eduUpdatePropertiesDTO
   * @return void
   */
  @Override
  public void eduUpdateRoomProperties(EduUpdateRoomPropertiesDTO eduUpdatePropertiesDTO) {
    log.info("声网更新自定义房间属性请求: {}", eduUpdatePropertiesDTO);
    RoomPropertiesResp roomPropertiesResp = null;
    try {
      roomPropertiesResp =
          agoraEducationApi.roomProperties(
              eduUpdatePropertiesDTO.getRoomUuid(),
              RoomPropertiesReq.builder().properties(eduUpdatePropertiesDTO.getMessage()).build());
      log.info("声网更新自定义房间属性响应: {}", roomPropertiesResp);
      if (Objects.isNull(roomPropertiesResp)) {
        throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "声网接口返回空！");
      }
    } catch (Exception e) {
      log.error("声网更新自定义房间属性异常: {}", e.getMessage());
      throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "声网更新自定义房间属性网络错误:" + e.getMessage());
    }

    if (!roomPropertiesResp.isSuccess()) {
      throw new BizException(
          BizErrorCodeEnum.REQUEST_ERROR, "声网更新自定义房间属性消息错误:" + roomPropertiesResp.getMsg());
    }
  }

    @Override
    public void publishChannelMessage(PublishChannelDTO publishChannelDTO) {
        log.info("发送频道消息请求: {}", publishChannelDTO);
        RtmChannelResp rtmChannelResp = null;
        try {
            rtmChannelResp =
                    agoraRtmApi.channelMessage(
                            String.valueOf(Integer.MAX_VALUE),
                            RtmChannelReq.builder()
                                    .channelName(publishChannelDTO.getRoomUuid())
                                    .payload(publishChannelDTO.getMessage())
                                    .build());
            log.info("发送频道消息响应: {}", rtmChannelResp);
            if (Objects.isNull(rtmChannelResp)) {
                throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "声网接口返回空！");
            }
        } catch (Exception e) {
            log.error("发送频道消息异常: {}", e.getMessage());
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "发送频道消息网络错误:" + e.getMessage());
        }

        if (!rtmChannelResp.isSuccess()) {
            throw new BizException(
                    BizErrorCodeEnum.REQUEST_ERROR, "发送频道消息错误:" + rtmChannelResp.getResult());
        }

    }

    @Override
    public void publishPeerMessage(PublishPeerDTO publishPeerDTO) {
        log.info("发送点对点消息请求: {}", publishPeerDTO);
        RtmPeerResp rtmPeerResp = null;
        try {
            rtmPeerResp =
                    agoraRtmApi.peerToPeer(
                            String.valueOf(Integer.MAX_VALUE),
                            false,
                            RtmPeerReq.builder()
                                    .destination(publishPeerDTO.getDeviceId())
                                    .enableOfflineMessaging(Boolean.TRUE)
                                    .payload(publishPeerDTO.getMessage())
                                    .build());
            log.info("发送点对点消息响应: {}", rtmPeerResp);
            if (Objects.isNull(rtmPeerResp)) {
                throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "声网接口返回空！");
            }
        } catch (Exception e) {
            log.error("发送点对点消息异常: {}", e.getMessage());
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "发送点对点消息网络错误:" + e.getMessage());
        }

        if (!rtmPeerResp.isSuccess()) {
            throw new BizException(
                    BizErrorCodeEnum.REQUEST_ERROR, "发送点对点消息错误:" + rtmPeerResp.getResult());
        }

    }

    public AgoraServiceImpl(AgoraTokenGenerator agoraTokenGenerator,
                            AgoraEducationApi agoraEducationApi,
                            AgoraRecordApi agoraRecordApi, AgoraRtmApi agoraRtmApi
    ) {
        this.agoraTokenGenerator = agoraTokenGenerator;
        this.agoraEducationApi = agoraEducationApi;
        this.agoraRecordApi = agoraRecordApi;
        this.agoraRtmApi = agoraRtmApi;
    }

}
