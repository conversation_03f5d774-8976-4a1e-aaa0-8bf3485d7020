package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.constant.AppointmentEnum;
import com.yuedu.ydsf.eduConnect.api.constant.AttendClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncXiaogjEnum;
import com.yuedu.ydsf.eduConnect.api.constant.ScheduleEnum;
import com.yuedu.ydsf.eduConnect.api.constant.SysErrorCodeEnum;
import com.yuedu.ydsf.eduConnect.api.vo.ClassCourseScheduleVO;
import com.yuedu.ydsf.eduConnect.api.vo.CreateCourseScheduleVO;
import com.yuedu.ydsf.eduConnect.api.vo.CreateVodScheduleVO;
import com.yuedu.ydsf.eduConnect.convert.CreateClassSessionConvert;
import com.yuedu.ydsf.eduConnect.entity.SsClass;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsCourseSchedule;
import com.yuedu.ydsf.eduConnect.entity.SsCourseScheduleBooks;
import com.yuedu.ydsf.eduConnect.entity.SsCourseScheduleRule;
import com.yuedu.ydsf.eduConnect.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.manager.SsCourseScheduleManager;
import com.yuedu.ydsf.eduConnect.mapper.SsCourseScheduleMapper;
import com.yuedu.ydsf.eduConnect.service.SsClassAuthRoomService;
import com.yuedu.ydsf.eduConnect.service.SsClassService;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeAuthRoomService;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeService;
import com.yuedu.ydsf.eduConnect.service.SsCourseScheduleBooksService;
import com.yuedu.ydsf.eduConnect.service.SsCourseScheduleRuleService;
import com.yuedu.ydsf.eduConnect.service.SsCourseScheduleService;
import com.yuedu.ydsf.eduConnect.service.SsDeviceService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 排课表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:10:16
 */
@Slf4j
@Service
@AllArgsConstructor
public class SsCourseScheduleServiceImpl extends
    ServiceImpl<SsCourseScheduleMapper, SsCourseSchedule> implements SsCourseScheduleService {

    private final SsClassTimeService ssClassTimeService;
    private final SsClassService ssClassService;
    private final SsClassAuthRoomService ssClassAuthRoomService;
    private final SsCourseScheduleRuleService ssCourseScheduleRuleService;
    private final SsCourseScheduleBooksService ssCourseScheduleBooksService;
    private final SsClassTimeAuthRoomService ssClassTimeAuthRoomService;
    private final SsDeviceService ssDeviceService;
    private final SsCourseScheduleManager ssCourseScheduleManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#createCourseScheduleVo.lecturerId"})
    public void saveCourseSchedule(CreateCourseScheduleVO createCourseScheduleVo) {
        log.info("saveCourseSchedule createCourseScheduleVo:{}", createCourseScheduleVo);
        checkDeviceScheduleConflict(createCourseScheduleVo);
        // 验证班级是否是可用状态
        SsClass ssClass = ssClassService.getAvailableClass(createCourseScheduleVo.getClassId());
        SsDevice ssDevice = null;
        if (Objects.equals(createCourseScheduleVo.getAttendClassType(),
            AttendClassTypeEnum.ATTEND_CLASS_TYPE_0
                .CODE)) {
            // 主讲端教室是否绑定了设备
            Long liveRoomId = createCourseScheduleVo.getClassCourseScheduleVOList().get(0)
                .getClassRoomId();
            ssDevice = ssDeviceService.getDeviceByClassRoomId(liveRoomId);
        }

        // 1.保存排课基础信息
        SsCourseSchedule ssCourseSchedule = CreateClassSessionConvert.INSTANCE.toEntity(
            createCourseScheduleVo);
        this.save(ssCourseSchedule);
        //如果是点播课获取视频库ID
        Long recordingId = null;
        if (AttendClassTypeEnum.ATTEND_CLASS_TYPE_1.CODE.equals(
            createCourseScheduleVo.getAttendClassType())) {
            CreateVodScheduleVO createVodScheduleVo = (CreateVodScheduleVO) createCourseScheduleVo;
            recordingId = createVodScheduleVo.getRecordingId();
        }
        List<SsCourseScheduleRule> ssCourseScheduleRuleEntityList = List.of();
        ScheduleEnum scheduleEnum = ScheduleEnum.getEnumByCode(
            createCourseScheduleVo.getClassTimeMethod());
        if (Objects.isNull(scheduleEnum)) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "未知的排课方式");
        }
        // 只有按周排课，需要保持排课规则（用作记录，后续的编辑不会使用）
        if (ScheduleEnum.CLASSTIMEMETHOD_0 == scheduleEnum) {
            ssCourseScheduleRuleEntityList = saveCourseScheduleRules(createCourseScheduleVo,
                Objects.isNull(ssDevice) ? null : ssDevice.getId(), ssCourseSchedule);
        }
        //保存书籍以及课次表
        saveBooksAndClassTime(createCourseScheduleVo, ssCourseSchedule.getId(),
            ssClass, Objects.isNull(ssDevice) ? null : ssDevice.getId(),
            ssCourseScheduleRuleEntityList, recordingId);
        ssCourseScheduleManager.sendCreatedCourseScheduleMessage(ssCourseSchedule.getId());
    }

    /**
     * 保存排课规则
     *
     * @param createCourseScheduleVo 创建排课信息
     * @param deviceId               设备信息
     * @param ssCourseSchedule       排课信息
     * @return java.util.List<com.yuedushufang.ss.api.domain.SsCourseScheduleRule>
     */
    private List<SsCourseScheduleRule> saveCourseScheduleRules(
        CreateCourseScheduleVO createCourseScheduleVo, Long deviceId,
        SsCourseSchedule ssCourseSchedule) {
        if (Objects.nonNull(createCourseScheduleVo.getScheduleCap())
            && !createCourseScheduleVo.getScheduleCap()
            .equals(createCourseScheduleVo.getClassCourseScheduleVOList().size())) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR,
                "排课上限次数与上课时间安排不一致，请手动修改后再提交");
        }
        List<SsCourseScheduleRule> ssCourseScheduleRuleEntityList = createCourseScheduleVo.getClassCourseScheduleVOList()
            .stream().map(e -> {
                int dayOfWeek = e.getAttendClassDate().getDayOfWeek().getValue();
                SsCourseScheduleRule ssCourseScheduleRuleEntity = new SsCourseScheduleRule();
                ssCourseScheduleRuleEntity.setClassId(createCourseScheduleVo.getClassId());
                ssCourseScheduleRuleEntity.setDeviceId(deviceId);
                ssCourseScheduleRuleEntity.setCourseScheduleId(ssCourseSchedule.getId());
                ssCourseScheduleRuleEntity.setAttendClassWeek(dayOfWeek);
                ssCourseScheduleRuleEntity.setAttendClassStartTime(e.getAttendClassStartTime());
                ssCourseScheduleRuleEntity.setAttendClassEndTime(e.getAttendClassEndTime());
                return ssCourseScheduleRuleEntity;
            }).collect(Collectors.toList());
        ssCourseScheduleRuleService.saveBatch(ssCourseScheduleRuleEntityList);
        return ssCourseScheduleRuleEntityList;
    }


    /**
     * 检查设备排课冲突
     *
     * @param createCourseScheduleVo 创建排课信息
     */
    private void checkDeviceScheduleConflict(CreateCourseScheduleVO createCourseScheduleVo) {
        if (createCourseScheduleVo.getIsOutCollide()) {
            return;
        }
        Set<ClassCourseScheduleVO> uniqueSchedules = new HashSet<>(
            createCourseScheduleVo.getClassCourseScheduleVOList());
        if (uniqueSchedules.size() != createCourseScheduleVo.getClassCourseScheduleVOList()
            .size()) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "排课规则存在重复");
        }
        if (Objects.nonNull(createCourseScheduleVo.getScheduleCap())
            && createCourseScheduleVo.getScheduleCap() < CollectionUtils.size(
            createCourseScheduleVo.getClassCourseScheduleVOList())) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "排课规则超过上限");
        }
        createCourseScheduleVo.getClassCourseScheduleVOList().forEach(classCourseScheduleVo -> {
            Boolean count = ssClassTimeService.countByClassIdAndTimeRange(
                classCourseScheduleVo.getClassRoomId(), classCourseScheduleVo.getAttendClassDate(),
                classCourseScheduleVo.getAttendClassStartTime(),
                classCourseScheduleVo.getAttendClassEndTime());
            if (count) {
                throw new BizException(SysErrorCodeEnum.COURSE_SCHEDULE_CONFLICT,
                    "主讲端设备排课时间冲突");
            }
        });
    }

    /**
     * 保存书籍以及课次表
     *
     * @param createCourseScheduleVo         创建排课信息
     * @param courseScheduleId               排课ID
     * @param ssClass                        班级信息
     * @param deviceId                       设备ID
     * @param ssCourseScheduleRuleEntityList 排课规则
     * @param recordingId                    录播课资源ID
     * @return void
     */
    private void saveBooksAndClassTime(
        CreateCourseScheduleVO createCourseScheduleVo,
        Long courseScheduleId,
        SsClass ssClass,
        Long deviceId,
        List<SsCourseScheduleRule> ssCourseScheduleRuleEntityList,
        Long recordingId) {
        List<SsCourseScheduleBooks> courseScheduleBooksList = createCourseScheduleBooksList(
            createCourseScheduleVo, courseScheduleId);
        ssCourseScheduleBooksService.saveBatch(courseScheduleBooksList);
        List<SsClassTime> classTimeList = createClassTimeList(createCourseScheduleVo,
            courseScheduleId, deviceId, ssCourseScheduleRuleEntityList, recordingId,
            courseScheduleBooksList);
        ssClassTimeService.saveBatch(classTimeList);
        saveClassTimeToAuthRoom(ssClass, classTimeList);
    }

    /**
     * 创建课次书籍
     *
     * @param createCourseScheduleVo 创建排课信息
     * @param courseScheduleId       排课ID
     * @return java.util.List<com.yuedushufang.ss.api.domain.SsCourseScheduleBooks>
     */
    private List<SsCourseScheduleBooks> createCourseScheduleBooksList(
        CreateCourseScheduleVO createCourseScheduleVo, Long courseScheduleId) {
        return createCourseScheduleVo.getClassCourseScheduleVOList()
            .stream().map(classCourseScheduleVo -> {
                int index = createCourseScheduleVo.getClassCourseScheduleVOList()
                    .indexOf(classCourseScheduleVo) + 1;
                SsCourseScheduleBooks courseScheduleBooks = new SsCourseScheduleBooks();
                courseScheduleBooks.setClassId(createCourseScheduleVo.getClassId());
                courseScheduleBooks.setCourseScheduleId(courseScheduleId);
                courseScheduleBooks.setBooksId(classCourseScheduleVo.getBooksId());
                courseScheduleBooks.setBooksName(classCourseScheduleVo.getBooksName());
                courseScheduleBooks.setHowManyTimes(index);
                return courseScheduleBooks;
            }).collect(Collectors.toList());
    }

    /**
     * 创建课次信息
     *
     * @param createCourseScheduleVo         创建排课信息
     * @param courseScheduleId               排课ID
     * @param deviceId                       设备ID
     * @param ssCourseScheduleRuleEntityList 排课规则
     * @param recordingId                    录播课资源ID
     * @param courseScheduleBooksList        课次书籍
     * @return java.util.List<com.yuedushufang.ss.api.domain.SsClassTime>
     */
    private List<SsClassTime> createClassTimeList(CreateCourseScheduleVO createCourseScheduleVo,
        Long courseScheduleId, Long deviceId,
        List<SsCourseScheduleRule> ssCourseScheduleRuleEntityList, Long recordingId,
        List<SsCourseScheduleBooks> courseScheduleBooksList) {
        return createCourseScheduleVo.getClassCourseScheduleVOList()
            .stream()
            .map(classCourseScheduleVo -> {
                SsCourseScheduleBooks ssCourseScheduleBooks = courseScheduleBooksList.stream()
                    .filter(e -> e.getBooksId().equals(classCourseScheduleVo.getBooksId()))
                    .findFirst().get();
                SsCourseScheduleRule courseScheduleRule =
                    CollectionUtils.isEmpty(ssCourseScheduleRuleEntityList) ? null
                        : ssCourseScheduleRuleEntityList.stream()
                            .filter(ssCourseScheduleRule ->
                                ssCourseScheduleRule.getAttendClassStartTime()
                                    .equals(classCourseScheduleVo.getAttendClassStartTime())
                                    && ssCourseScheduleRule.getAttendClassEndTime()
                                    .equals(classCourseScheduleVo.getAttendClassEndTime()))
                            .findFirst()
                            .get();
                SsClassTime ssClassTime = CreateClassSessionConvert.INSTANCE.toEntity(
                    classCourseScheduleVo);
                ssClassTime.setCourseScheduleRuleId(
                    Objects.nonNull(courseScheduleRule) ? courseScheduleRule.getId() : null);
                ssClassTime.setClassId(createCourseScheduleVo.getClassId());
                ssClassTime.setCourseScheduleId(courseScheduleId);
                ssClassTime.setCourseScheduleBooksId(ssCourseScheduleBooks.getId());
                ssClassTime.setDeviceId(deviceId);
                ssClassTime.setAttendClassType(createCourseScheduleVo.getAttendClassType());
                ssClassTime.setRecordingId(recordingId);
                ssClassTime.setLecturerId(createCourseScheduleVo.getLecturerId());
                LocalDateTime attendTimeStartTime = LocalDateTime.of(
                    classCourseScheduleVo.getAttendClassDate(),
                    classCourseScheduleVo.getAttendClassStartTime());
                LocalDateTime attendTimeEndTime = LocalDateTime.of(
                    classCourseScheduleVo.getAttendClassDate(),
                    classCourseScheduleVo.getAttendClassEndTime());
                ssClassTime.setAttendTimeStartTime(attendTimeStartTime);
                ssClassTime.setAttendTimeEndTime(attendTimeEndTime);
                return ssClassTime;
            }).collect(Collectors.toList());
    }

    /**
     * 课次与此次的班级对应的授权教室进行关联
     *
     * @param ssClass         班级信息
     * @param ssClassTimeList 课次信息
     * @return java.util.List<com.yuedushufang.ss.api.domain.dto.ClassCourseReq>
     */
    public void saveClassTimeToAuthRoom(SsClass ssClass, List<SsClassTime> ssClassTimeList) {
        // 查询班级下得授权教室，过滤单课次授权的校区
        List<SsClassAuthRoom> classAuthRoomList = getClassAuthRoomList(ssClass);
        saveClassTimeAuthRoom(ssClassTimeList, classAuthRoomList, ssClass);
    }

    /**
     * 获取班级授权教室
     *
     * @param ssClass 班级信息
     * @return java.util.List<com.yuedushufang.ss.eduConnect.entity.SsClassAuthRoom>
     */
    private List<SsClassAuthRoom> getClassAuthRoomList(SsClass ssClass) {
        List<SsClassAuthRoom> classAuthRoomList = ssClassAuthRoomService.listNotAloneAuthRoom(
            ssClass.getId(), AppointmentEnum.TYPE_1);
        if (CollectionUtils.isEmpty(classAuthRoomList)) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "班级未授权教室");
        }
        return classAuthRoomList;
    }

    /**
     * 保存课次授权教室
     *
     * @param ssClassTimeList   课次信息
     * @param classAuthRoomList 授权教室信息
     * @param ssClass           班级信息
     * @return void
     */
    private void saveClassTimeAuthRoom(List<SsClassTime> ssClassTimeList,
        List<SsClassAuthRoom> classAuthRoomList, SsClass ssClass) {
        List<SsClassTimeAuthRoom> allClassTimeAuthRoomList = new ArrayList<>();
        for (SsClassTime ssClassTime : ssClassTimeList) {
            LocalDateTime attendClassDateTime = ssClassTime.getAttendClassDate()
                .atTime(ssClassTime.getAttendClassEndTime());
            if (attendClassDateTime.isBefore(LocalDateTime.now())) {
                continue;
            }
            List<SsClassTimeAuthRoom> classTimeAuthRoomList = classAuthRoomList.stream()
                .map(classAuthRoom -> {
                    SsClassTimeAuthRoom classTimeAuthRoom = new SsClassTimeAuthRoom();
                    classTimeAuthRoom.setClassId(ssClassTime.getClassId());
                    classTimeAuthRoom.setClassTimeId(ssClassTime.getId());
                    if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(
                        ssClass.getIsSyncXiaogj())) {
                        classTimeAuthRoom.setXgjClassTimeId(UUID.randomUUID().toString());
                    }
                    classTimeAuthRoom.setCampusId(classAuthRoom.getCampusId());
                    classTimeAuthRoom.setClassRoomId(classAuthRoom.getClassRoomId());
                    classTimeAuthRoom.setDeviceId(classAuthRoom.getDeviceId());
                    classTimeAuthRoom.setXgjCampusId(classAuthRoom.getXgjCampusId());
                    classTimeAuthRoom.setXgjClassRoomId(classAuthRoom.getXgjClassRoomId());
                    return classTimeAuthRoom;
                }).toList();
            allClassTimeAuthRoomList.addAll(classTimeAuthRoomList);
        }
        boolean saved = ssClassTimeAuthRoomService.saveBatch(allClassTimeAuthRoomList);
        log.info("saveClassTimeAuthRoom saved:{},插入条数:{}", saved, allClassTimeAuthRoomList.size());
    }

}
