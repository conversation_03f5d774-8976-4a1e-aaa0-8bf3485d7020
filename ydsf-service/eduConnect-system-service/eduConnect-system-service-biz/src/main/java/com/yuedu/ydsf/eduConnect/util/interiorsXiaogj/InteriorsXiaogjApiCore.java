package com.yuedu.ydsf.eduConnect.util.interiorsXiaogj;

import java.io.IOException;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 对接内部校管家接口核心类
 * <AUTHOR>
 * @date 2024年01月19日 10时14分
 */
public class InteriorsXiaogjApiCore {

    /**
     * 内部校管家get请求工具类
     * <AUTHOR>
     * @date 2023年12月29日 09时53分
     * @param url 接口请求路径
     * @return String
     */
    public static String okHttpGetUtils(String url) throws IOException {

        String responseBody = null;
        Response response = null;

        try{

            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .method("GET", null)
                    .build();
            response = client.newCall(request).execute();
            responseBody = response.body().string();

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return responseBody;

    }

}
