package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanPubQuery;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanToLiveRoomVO;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanPub;
import java.util.List;

/**
 * 已发布的教学计划表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-29 09:26:30
 */
public interface TeachingPlanPubService extends IService<TeachingPlanPub> {

    /**
     * 通过指定条件查询教学计划列表
     *
     * @param teachingPlanQuery 查询条件
     * @return 结果
     */
    List<TeachingPlanPubVO> getTeachingPlanList(TeachingPlanQuery teachingPlanQuery);

    /**
     * 根据教学计划id查询直播间计划名称
     *
     * @param teachingPlanIdList 教学计划id集合
     * @return List<TeachingPlanToLiveRoomVO>
     */
    List<TeachingPlanToLiveRoomVO> getTeachingPlanToLiveRoom(List<Long> teachingPlanIdList);

    /**
     * 查询小程序约点播课-选择课程列表
     *
     * @param teachingPlanPubQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>
     * <AUTHOR>
     * @date 2024/12/9 11:24
     */
    List<TeachingPlanPubVO> getTeachingPlanCourseList(TeachingPlanPubQuery teachingPlanPubQuery);


    /**
     * 查询小程序约直播播课-选择课程列表
     *
     * @param teachingPlanPubQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>
     * <AUTHOR>
     * @date 2024/12/17 10:16
     */
    List<TeachingPlanPubVO> getTeachingPlanCourseLiveList(
        TeachingPlanPubQuery teachingPlanPubQuery);


    /**
     * 根据教学计划发布表的id集合批量获取对应的信息
     * <AUTHOR>
     * @date 2024/12/19 10:20
     * @param teachingPlanIds
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>
     */
    List<TeachingPlanPubVO> getTeachingPlanPubByTeachingPlanIds(List<Long> teachingPlanIds);

      /**
       * 功能描述
       *
       * <AUTHOR>
       * @date 2024/12/24 15:50
       * @return void
       */
      void updateTeachingPlanPubClose();

    /**
     * 根据门店id和课程类型查询教学计划发布表的id集合
     * @param storeId 门店ID
     * @param courseType 课程类型
     * @return java.util.List<java.lang.Long>
     */
      List<Long> getByStoreIdAndCourseType(Long storeId,  Integer courseType);
}
