package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.api.query.CourseLiveQuery;
import com.yuedu.ydsf.eduConnect.api.vo.CourseLiveVO;
import com.yuedu.ydsf.eduConnect.entity.CourseLive;
import com.yuedu.ydsf.eduConnect.service.CourseLiveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 门店已约直播课 控制类
 *
 * <AUTHOR>
 * @date 2024-12-26 14:41:20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/CourseLive" )
@Tag(description = "b_course_live" , name = "门店已约直播课管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseLiveController {

    private final  CourseLiveService courseLiveService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param courseLive 门店已约直播课
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("edusystem_CourseLive_view")
    public R getCourseLivePage(@ParameterObject Page page, @ParameterObject CourseLive courseLive) {
        LambdaQueryWrapper<CourseLive> wrapper = Wrappers.lambdaQuery();
        return R.ok(courseLiveService.page(page, wrapper));
    }


//    /**
//     *  双师直播约课后台分页查询
//     *
//     * <AUTHOR>
//     * @date 2025年02月25日 08时57分
//     */
//    @Operation(summary = "直播约课分页查询" , description = "直播约课分页查询" )
//    @GetMapping("/live/page" )
//   // @HasPermission("edusystem_CourseLive_view")
//    public R<IPage<CourseLiveVO>> courseLivePage(@ParameterObject Page page, @ParameterObject CourseLiveQuery query) {
//        return R.ok(courseLiveService.livePage(page, query));
//    }



    /**
     * 通过条件查询门店已约直播课
     * @param courseLive 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("edusystem_CourseLive_view")
    public R getDetails(@ParameterObject CourseLive courseLive) {
        return R.ok(courseLiveService.list(Wrappers.query(courseLive)));
    }

    /**
     * 新增门店已约直播课
     * @param courseLive 门店已约直播课
     * @return R
     */
    @Operation(summary = "新增门店已约直播课" , description = "新增门店已约直播课" )
    @PostMapping("/add")
    @HasPermission("edusystem_CourseLive_add")
    public R save(@RequestBody CourseLive courseLive) {
        return R.ok(courseLiveService.save(courseLive));
    }

    /**
     * 修改门店已约直播课
     * @param courseLive 门店已约直播课
     * @return R
     */
    @Operation(summary = "修改门店已约直播课" , description = "修改门店已约直播课" )
    @PutMapping("/edit")
    @HasPermission("edusystem_CourseLive_edit")
    public R updateById(@RequestBody CourseLive courseLive) {
        return R.ok(courseLiveService.updateById(courseLive));
    }

    /**
     * 通过id删除门店已约直播课
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除门店已约直播课" , description = "通过id删除门店已约直播课" )
    @DeleteMapping("/delete")
    @HasPermission("edusystem_CourseLive_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(courseLiveService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param courseLive 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_CourseLive_export")
    public List<CourseLive> exportExcel(CourseLive courseLive,Long[] ids) {
        return courseLiveService.list(Wrappers.lambdaQuery(courseLive).in(ArrayUtil.isNotEmpty(ids), CourseLive::getId, ids));
    }

    /**
     * 导入excel 表
     * @param courseLiveList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_CourseLive_export")
    public R importExcel(@RequestExcel List<CourseLive> courseLiveList, BindingResult bindingResult) {
        return R.ok(courseLiveService.saveBatch(courseLiveList));
    }
}
