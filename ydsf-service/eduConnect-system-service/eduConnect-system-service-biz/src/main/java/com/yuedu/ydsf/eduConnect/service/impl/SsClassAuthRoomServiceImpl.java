package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.eduConnect.api.constant.AppointmentEnum;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.service.SsClassAuthRoomService;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 班级授权教室表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 09:06:41
 */
@Service
public class SsClassAuthRoomServiceImpl extends
    ServiceImpl<SsClassAuthRoomMapper, SsClassAuthRoom> implements SsClassAuthRoomService {

    @Override
    public List<SsClassAuthRoom> listNotAloneAuthRoom(Long classId,
        AppointmentEnum appointmentEnum) {
        QueryWrapper<SsClassAuthRoom> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("class_id", classId).eq("appointment_status", appointmentEnum.CODE);
        return this.list(queryWrapper).stream().filter(
                ssClassAuthRoomEntity -> StringUtils.isBlank(ssClassAuthRoomEntity.getClassTimeIds()))
            .toList();
    }
}
