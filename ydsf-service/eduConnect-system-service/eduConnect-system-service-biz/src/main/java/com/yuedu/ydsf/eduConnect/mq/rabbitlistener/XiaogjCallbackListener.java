package com.yuedu.ydsf.eduConnect.mq.rabbitlistener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.store.dto.XiaogjBusinessIdDTO;
import com.yuedu.ydsf.eduConnect.config.RabbitMqConfig;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjLog;
import com.yuedu.ydsf.eduConnect.mq.dto.SsPushXiaogjEventDto;
import com.yuedu.ydsf.eduConnect.mq.dto.XiaogjPushEventDTO;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeStudentService;
import com.yuedu.ydsf.eduConnect.service.SsXiaogjLogService;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.MqConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.XiaogjEventKeyGroupConstant;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 校管家回调队列监听类
 * <AUTHOR>
 * @date 2025/2/13 11:53
 */
@Slf4j
@Component
@AllArgsConstructor
public class XiaogjCallbackListener {


    private final SsClassTimeStudentService ssClassTimeStudentService;

    private final RabbitMqConfig rabbitMqConfig;

    private final SsXiaogjLogService ssXiaogjLogService;


    /**
     * 监听校管家交换机队列
     * @param xiaogjPushEventDTO
     * @return void
     * <AUTHOR>
     * @date 2025/2/12 13:48
     */
    @RabbitListener(bindings = @QueueBinding(exchange = @Exchange(value = MqConstant.XIAOGJ_PUSH_EXCHANGE, durable = "true", type = ExchangeTypes.FANOUT),
        value = @Queue(value = "${rabbitmq.queue.xiaogj_push_default_queue_sp1}", durable = "true", autoDelete = "false"), key = MqConstant.XIAOGJ_PUSH_EVENT_ROUTER))
    public void xiaogjPushEventListener(@RequestBody XiaogjPushEventDTO xiaogjPushEventDTO)
        throws InterruptedException {

        log.debug("接收校管家推送消息: {}", JSON.toJSONString(xiaogjPushEventDTO));

        if (StringUtils.isBlank(xiaogjPushEventDTO.getEventkey())) {
            return;
        }

        XiaogjBusinessIdDTO xiaogjBusinessIdDTO = new XiaogjBusinessIdDTO();
        xiaogjBusinessIdDTO.setEventkey(xiaogjPushEventDTO.getEventkey());

        // eventId为排课记录id监听事件分组
        if (XiaogjEventKeyGroupConstant.courseIdEventKeyList.contains(xiaogjPushEventDTO.getEventkey())) {

            xiaogjBusinessIdDTO.setCourseId(xiaogjPushEventDTO.getEventid());

        }
        // eventId为班级id监听事件分组
        else if (XiaogjEventKeyGroupConstant.classIdEventKeyList.contains(xiaogjPushEventDTO.getEventkey())) {

            xiaogjBusinessIdDTO.setClassId(xiaogjPushEventDTO.getEventid());

        }

        // 双师排课学生监听事件分组
        if(XiaogjEventKeyGroupConstant.ssClassCourseStudentEventKeyList.contains(xiaogjPushEventDTO.getEventkey())){
            // sp1校管家排课学员回调消费开关: true-启用; false-禁用;
            if(rabbitMqConfig.getCourseStudentEnabled()){

                // 保证校管家数据同步完整, 延迟执行校管家相关操作
                TimeUnit.SECONDS.sleep(rabbitMqConfig.getListenerSleep());

                // 校管家排课学生信息同步双师系统
                ssClassTimeStudentService.syncXiaogjStudent(xiaogjBusinessIdDTO);
            }

        }


    }

    /**
     * 监听双师系统校管家排课回调队列
     * @param ssPushXiaogjEventDto
     * @return void
     * <AUTHOR>
     * @date 2025/2/12 13:48
     */
    @RabbitListener(bindings = @QueueBinding(exchange = @Exchange(value = MqConstant.SS_XIAOGJ_PUSH_CALLBACK_EXCHANGE, durable = "true", type = ExchangeTypes.FANOUT),
        value = @Queue(value = "${rabbitmq.queue.ss_xiaogj_push_callback_default_queue_sp1}", durable = "true", autoDelete = "false"), key = MqConstant.SS_XIAOGJ_PUSH_CALLBACK_EVENT_ROUTER))
    public void xiaogjPushEventListener(@RequestBody SsPushXiaogjEventDto ssPushXiaogjEventDto) {

        // sp1双师系统校管家排课回调消费开关: true-启用; false-禁用;
        if(!rabbitMqConfig.getCourseCallbackEnabled()){
            return;
        }

        log.debug("消费推送校管家排课回调消息:{}", JSON.toJSONString(ssPushXiaogjEventDto));
        log.debug("消费推送校管家排课回调eventId:{}", ssPushXiaogjEventDto.getEventId());

        if(StringUtils.isBlank(ssPushXiaogjEventDto.getEventId())){
            log.info("推送消息ID为空!");
            return;
        }

        // 更新消息返回状态
        ssXiaogjLogService.update(Wrappers.lambdaUpdate(SsXiaogjLog.class)
            .eq(SsXiaogjLog::getRequestId, ssPushXiaogjEventDto.getEventId())
            .set(SsXiaogjLog::getResponseCode, ssPushXiaogjEventDto.getResponseCode())
            .set(SsXiaogjLog::getResponseParam, ssPushXiaogjEventDto.getData())
            .set(SsXiaogjLog::getMtime, LocalDateTime.now())
        );

    }


}
