package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.query.InformationAuthStoreQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationAuthStoreDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationAuthStoreVO;
import com.yuedu.ydsf.eduConnect.entity.InformationAuthStore;

import java.io.Serializable;
import java.util.List;

/**
* 资料授权服务接口
*
* <AUTHOR>
* @date  2025/07/22
*/
public interface InformationAuthStoreService extends IService<InformationAuthStore> {



    /**
     * 资料授权分页查询
     *
     * @param page 分页对象
     * @param informationAuthStoreQuery 资料授权
     * @return IPage 分页结果
     */
    IPage<InformationAuthStoreVO> page(Page page, InformationAuthStoreQuery informationAuthStoreQuery);


    /**
     * 根据ID获得资料授权信息
     *
     * @param id id
     * @return InformationAuthStoreVO 详细信息
     */
    InformationAuthStoreVO getInfoById(Serializable id);


    /**
     * 新增资料授权
     *
     * @param informationAuthStoreDTO 资料授权
     * @return boolean 执行结果
     */
    boolean add(InformationAuthStoreDTO informationAuthStoreDTO);


    /**
     * 修改资料授权
     *
     * @param informationAuthStoreDTO 资料授权
     * @return boolean 执行结果
     */
    boolean edit(InformationAuthStoreDTO informationAuthStoreDTO);


    /**
     * 导出excel 资料授权表格
     *
     * @param informationAuthStoreQuery 查询条件
     * @param ids 导出指定ID
     * @return List<InformationAuthStoreVO> 结果集合
     */
    List<InformationAuthStoreVO> export(InformationAuthStoreQuery informationAuthStoreQuery, Long[] ids);
}
