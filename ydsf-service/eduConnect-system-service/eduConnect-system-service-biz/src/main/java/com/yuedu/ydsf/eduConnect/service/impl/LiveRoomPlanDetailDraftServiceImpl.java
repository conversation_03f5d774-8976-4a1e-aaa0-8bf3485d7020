package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.PlanStatusEnum;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanDetailDraftAddQuery;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanDetailDraftQuery;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailDraftVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailDraftVO;
import com.yuedu.ydsf.eduConnect.config.SsProperty;
import com.yuedu.ydsf.eduConnect.entity.ClassTime;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.manager.LiveRoomPlanDraftManager;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.service.ClassTimeService;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDetailDraftService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.function.Function;

/**
 * 直播间计划明细草稿表 服务类
 *
 * <AUTHOR>
 * @date 2024-12-03 09:46:17
 */
@Slf4j
@Service
@AllArgsConstructor
public class LiveRoomPlanDetailDraftServiceImpl extends ServiceImpl<LiveRoomPlanDetailDraftMapper, LiveRoomPlanDetailDraft> implements LiveRoomPlanDetailDraftService {

    private final ClassTimeService classTimeService;

    private final LiveRoomPlanDraftMapper roomPlanDraftMapper;

    private final LiveRoomPlanDraftManager liveRoomPlanDraftManager;

    private SsProperty ssProperty;

    private final TeachingPlanDraftMapper teachingPlanDraftMapper;

    /**
     * 查询全部直播间明细计划
     *
     * @return List<LiveRoomPlanVO>
     */
    @Override
    public List<LiveRoomPlanDetailDraftVO> listPlans(Integer planId) {
        List<LiveRoomPlanDetailDraftVO> drafts = baseMapper.selectList(Wrappers.lambdaQuery(
                LiveRoomPlanDetailDraft.class)
            .eq(LiveRoomPlanDetailDraft::getPlanId, planId)
            .orderByAsc(LiveRoomPlanDetailDraft::getClassStartDateTime)
        ).stream().map(liveRoomPlanDetailDraft -> {
            LiveRoomPlanDetailDraftVO liveRoomPlanDetailDraftVo = new LiveRoomPlanDetailDraftVO();
            BeanUtils.copyProperties(liveRoomPlanDetailDraft, liveRoomPlanDetailDraftVo);
            return liveRoomPlanDetailDraftVo;
        }).toList();
        if (drafts.isEmpty()) {
            return Collections.emptyList();
        }
        List<ClassTime> timeSlots = classTimeService.list();
        Map<Long, String> timeSlotMap = timeSlots.stream().collect(Collectors.toMap(ClassTime::getId, ClassTime::getName));
        for (int i = 0; i < drafts.size(); i++) {
            String classWeek = String.valueOf(drafts.get(i).getClassStartDateTime().toLocalDate().getDayOfWeek().getValue());
            drafts.get(i).setClassWeek(classWeek);
            drafts.get(i).setDateName(timeSlotMap.get(drafts.get(i).getTimeSlotId()));
        }
        return drafts;
    }

    /**
     * 根据ids集合返回总数
     *
     * @param ids 计划ID集合
     * @return 列表
     */
    @Override
    public Map<Long, Integer> countPlans(List<Long> ids) {
        // 查询符合条件的数据
        List<LiveRoomPlanDetailDraft> drafts = baseMapper.selectList(Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
            .in(ObjectUtil.isNotEmpty(ids),LiveRoomPlanDetailDraft::getPlanId, ids));
        return drafts.stream()
            .collect(Collectors.groupingBy(LiveRoomPlanDetailDraft::getPlanId,
                Collectors.collectingAndThen(Collectors.counting(), Long::intValue)));
    }

  /**
   * 新增直播间计划明细
   *
   * <AUTHOR>
   * @date 2024/12/4 11:51
   * @param detailDraftAddQuery
   * @return void
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void savePlanDetail(LiveRoomPlanDetailDraftAddQuery detailDraftAddQuery) {
    log.info("开始保存直播间计划明细, 入参:{}", JSON.toJSONString(detailDraftAddQuery));
    List<LiveRoomPlanDetailDraftQuery> detailList = detailDraftAddQuery.getDetailList();

    if (CollectionUtils.isEmpty(detailList)) {
      log.warn("直播间计划明细列表为空");
      return;
    }

    try {
      Long planId = detailList.get(0).getPlanId();

      // 检查是否存在关联的教学计划
      List<TeachingPlanDraft> teachingPlanDrafts =
          teachingPlanDraftMapper.selectList(
              Wrappers.lambdaQuery(TeachingPlanDraft.class)
                  .eq(TeachingPlanDraft::getLiveRoomPlanId, planId));

      if (!CollectionUtils.isEmpty(teachingPlanDrafts)) {
        log.info("直播间计划已关联教学计划，可以增加排期");
        // 存在关联的教学计划，检查新增排期是否在合理区间
        // 获取已有排期中最后一节课的结束时间
        List<LiveRoomPlanDetailDraft> existingDetails =
            baseMapper.selectList(
                Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                    .eq(LiveRoomPlanDetailDraft::getPlanId, planId)
                    .orderByDesc(LiveRoomPlanDetailDraft::getClassEndDateTime)
                    .last("LIMIT 1"));

        if (CollectionUtils.isNotEmpty(existingDetails)) {
          LocalDateTime lastClassEndTime = existingDetails.get(0).getClassEndDateTime();

          // 1. 批量获取时间段信息
          Map<Long, ClassTime> timeSlotMap = getTimeSlotMap(detailList);

          // 2. 检查新增排期的开始时间是否晚于最后一节课的结束时间
          for (LiveRoomPlanDetailDraftQuery detail : detailList) {
            ClassTime timeSlot = timeSlotMap.get(detail.getTimeSlotId());
            if (timeSlot == null) {
              throw new BizException("未找到对应的时间段信息");
            }

            LocalDateTime newClassStartDateTime =
                LocalDateTime.of(detail.getClassDate(), timeSlot.getStartTime());
            if (newClassStartDateTime.isBefore(lastClassEndTime)) {
              throw new BizException("新增排期的开始时间不能早于已有排期的最后一节课结束时间");
            }
          }
        }
      }

      // 1. 批量获取时间段信息
      Map<Long, ClassTime> timeSlotMap = getTimeSlotMap(detailList);

      // 2. 组装并保存数据
      List<LiveRoomPlanDetailDraft> saveList = buildSaveList(detailList, timeSlotMap);
      saveBatchWithCheck(saveList);

      // 3. 更新课程序号
      updateLessonOrders(planId);

      // 4. 更新直播间计划状态
      updatePlanStatus(planId);

      log.info("保存直播间计划明细成功");
    } catch (BizException e) {
      log.warn("保存直播间计划明细业务异常", e);
      throw e;
    } catch (Exception e) {
      log.error("保存直播间计划明细异常", e);
      throw new BizException("保存失败:" + e.getMessage());
    }
  }

  /**
   * 获取时间段信息Map
   *
   * <AUTHOR>
   * @date 2024/12/5 10:02
   * @param detailList
   * @return java.util.Map<java.lang.Long,com.yuedu.ydsf.eduConnect.entity.ClassTime>
   */
  private Map<Long, ClassTime> getTimeSlotMap(List<LiveRoomPlanDetailDraftQuery> detailList) {
    List<Long> timeSlotIds =
        detailList.stream().map(LiveRoomPlanDetailDraftQuery::getTimeSlotId).toList();
    log.info("查询时间段信息, timeSlotIds:{}", timeSlotIds);

    List<ClassTime> timeSlots = classTimeService.listByIds(timeSlotIds);
    return timeSlots.stream().collect(Collectors.toMap(ClassTime::getId, Function.identity()));
  }

  /**
   * 构建待保存的数据列表
   *
   * <AUTHOR>
   * @date 2024/12/5 10:02
   * @param detailList
   * @param timeSlotMap
   * @return java.util.List<com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft>
   */
  private List<LiveRoomPlanDetailDraft> buildSaveList(
      List<LiveRoomPlanDetailDraftQuery> detailList, Map<Long, ClassTime> timeSlotMap) {
    List<LiveRoomPlanDetailDraft> saveList = new ArrayList<>(detailList.size());
    LocalDateTime now = LocalDateTime.now();
    for (LiveRoomPlanDetailDraftQuery detail : detailList) {
      ClassTime timeSlot = timeSlotMap.get(detail.getTimeSlotId());
      if (timeSlot == null) {
        log.warn("未找到对应的时间段信息, timeSlotId:{}", detail.getTimeSlotId());
        throw new BizException("未找到对应的时间段信息");
      }

      LiveRoomPlanDetailDraft draft = new LiveRoomPlanDetailDraft();
      BeanUtils.copyProperties(detail, draft);

      // 设置时间相关字段
      setTimeFields(draft, timeSlot);
      draft.setOperatorName(SecurityUtils.getUser().getName());
      // 判断时间是否在当前时间之前
      if (ssProperty.getJwCheckPlanDateEnable() && draft.getClassStartDateTime().isBefore(now)) {
        throw new BizException("选择的日期不能早于当前时间，操作失败！");
      }
      saveList.add(draft);
    }
    return saveList;
  }

  /**
   * 设置时间相关字段
   *
   * <AUTHOR>
   * @date 2024/12/5 10:02
   * @param draft
   * @param timeSlot
   * @return void
   */
  private void setTimeFields(LiveRoomPlanDetailDraft draft, ClassTime timeSlot) {
    LocalTime startTime = timeSlot.getStartTime();
    LocalTime endTime = timeSlot.getEndTime();
    draft.setClassStartTime(startTime);
    draft.setClassEndTime(endTime);
    draft.setClassStartDateTime(LocalDateTime.of(draft.getClassDate(), startTime));
    draft.setClassEndDateTime(LocalDateTime.of(draft.getClassDate(), endTime));
  }

  /**
   * 批量保存并检查结果
   *
   * <AUTHOR>
   * @date 2024/12/5 10:02
   * @param saveList
   * @return void
   */
  private void saveBatchWithCheck(List<LiveRoomPlanDetailDraft> saveList) {
    log.info("批量保存直播间计划明细, size:{}", saveList.size());
    boolean success = saveBatch(saveList);
    if (!success) {
      log.error("保存直播间计划明细失败");
      throw new BizException("保存失败");
    }
  }

  /**
   * 更新课程序号
   *
   * <AUTHOR>
   * @date 2024/12/5 10:01
   * @param planId
   * @return void
   */
  private void updateLessonOrders(Long planId) {
    log.info("开始更新课程序号");
    List<LiveRoomPlanDetailDraft> sortedList =
        baseMapper.selectList(
            Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                .eq(LiveRoomPlanDetailDraft::getPlanId, planId)
                .orderByAsc(LiveRoomPlanDetailDraft::getClassStartDateTime));

    for (int i = 0; i < sortedList.size(); i++) {
      LiveRoomPlanDetailDraft draft = sortedList.get(i);
      boolean updateSuccess =
          update(
              Wrappers.lambdaUpdate(LiveRoomPlanDetailDraft.class)
                  .eq(LiveRoomPlanDetailDraft::getId, draft.getId())
                  .set(LiveRoomPlanDetailDraft::getLessonOrder, i + 1));

      if (!updateSuccess) {
        log.error("更新课程序号失败, id: {}, newOrder: {}", draft.getId(), i + 1);
        throw new BizException("更新课程序号失败");
      }
    }
    log.info("更新课程序号成功");
  }

  /**
   * 编辑直播间计划明细
   *
   * <AUTHOR>
   * @date 2024/12/5 9:41
   * @param planDetailDraftQuery 计划明细查询对象
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void editPlanDetail(LiveRoomPlanDetailDraftQuery planDetailDraftQuery) {
    log.info("开始修改直播间计划明细, 入参:{}", JSON.toJSONString(planDetailDraftQuery));
    try {
      // 1. 校验课程是否已结束并获取明细数据
      List<LiveRoomPlanDetailDraft> detailDrafts =
          checkAndGetFinishedDetails(Collections.singletonList(planDetailDraftQuery.getId()));
      LiveRoomPlanDetailDraft detailDraft = detailDrafts.get(0);
      log.info("查询到原计划明细信息: {}", JSON.toJSONString(detailDraft));

      // 新增限制：检查直播排期是否已经开始
      LocalDateTime now = LocalDateTime.now();
      LocalDateTime classStartDateTime = detailDraft.getClassStartDateTime();
      if (classStartDateTime != null && classStartDateTime.isBefore(now)) {
            log.warn("直播排期已经开始，不允许修改, 排期ID: {}, 开始时间: {}",
                detailDraft.getId(), classStartDateTime);
            throw new BizException("直播排期已经开始，不允许修改");
      }

      // 2. 批量获取时间段信息
      List<LiveRoomPlanDetailDraftQuery> detailList =
          Collections.singletonList(planDetailDraftQuery);
      Map<Long, ClassTime> timeSlotMap = getTimeSlotMap(detailList);
      if (timeSlotMap.isEmpty()) {
        log.error("未找到对应的时间段信息");
        throw new BizException("未找到对应的时间段信息");
      }

      // 3. 组装数据并计算新的上课时间
      ClassTime timeSlot = timeSlotMap.get(planDetailDraftQuery.getTimeSlotId());
      LocalDateTime newClassStartDateTime =
          LocalDateTime.of(planDetailDraftQuery.getClassDate(), timeSlot.getStartTime());

      // 检查是否存在关联的教学计划并验证修改区间
      List<TeachingPlanDraft> teachingPlanDrafts =
          teachingPlanDraftMapper.selectList(
              Wrappers.lambdaQuery(TeachingPlanDraft.class)
                  .eq(TeachingPlanDraft::getLiveRoomPlanId, detailDraft.getPlanId()));

      if (CollectionUtils.isNotEmpty(teachingPlanDrafts)) {
        // 存在关联的教学计划，检查时间区间
        checkAssociatedTeachingPlanTimeRange(
            detailDraft.getPlanId(), detailDraft.getId(), newClassStartDateTime);
      }

      // 4. 组装并更新数据
      List<LiveRoomPlanDetailDraft> saveList = buildSaveList(detailList, timeSlotMap);
      boolean updateSuccess = updateBatchById(saveList);
      if (!updateSuccess) {
        log.error("更新计划明细失败");
        throw new BizException("更新失败");
      }
      log.info("更新计划明细成功");

      // 5. 更新课程序号
      updateLessonOrders(detailDraft.getPlanId());
      // 对应的直播间计划更新成未发布需要二次发布
      updatePlanStatus(detailDraft.getPlanId());
      log.info("修改直播间计划明细完成");
    } catch (BizException e) {
      log.warn("修改直播间计划明细业务异常", e);
      throw e;
    } catch (Exception e) {
      log.error("修改直播间计划明细系统异常", e);
      throw new BizException("修改失败:" + e.getMessage());
    }
  }

  /**
   * 对应的直播间计划更新成未发布需要二次发布
   *
   * <AUTHOR>
   * @date 2024/12/6 9:24
   * @param planId
   * @return void
   */
  private void updatePlanStatus(Long planId) {
    roomPlanDraftMapper.update(
        Wrappers.lambdaUpdate(LiveRoomPlanDraft.class)
            .eq(LiveRoomPlanDraft::getId, planId)
            .set(LiveRoomPlanDraft::getPublisherName, StringUtils.EMPTY)
            .set(LiveRoomPlanDraft::getPlanStatus, PlanStatusEnum.STATUS_ENUM_0.code));
  }

  /**
   * 修改校验关联教学计划的直播间排期时间范围
   *
   * <AUTHOR>
   * @date 2024/12/8 15:30
   * @param planId 计划ID
   * @param detailId 明细ID
   * @param newClassStartDateTime 新的上课开始时间
   * @return void
   */
  private void checkAssociatedTeachingPlanTimeRange(
      Long planId, Long detailId, LocalDateTime newClassStartDateTime) {
    // 查询是否存在关联的教学计划
    List<TeachingPlanDraft> teachingPlanDrafts =
        teachingPlanDraftMapper.selectList(
            Wrappers.lambdaQuery(TeachingPlanDraft.class)
                .eq(TeachingPlanDraft::getLiveRoomPlanId, planId));

    // 如果存在关联的教学计划，则需要验证修改区间
    if (CollectionUtils.isNotEmpty(teachingPlanDrafts)) {
      log.info("存在关联的教学计划，校验修改时间区间");

      // 获取所有排期，按课程顺序排序
      List<LiveRoomPlanDetailDraft> allDetails =
          baseMapper.selectList(
              Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                  .eq(LiveRoomPlanDetailDraft::getPlanId, planId)
                  .orderByAsc(LiveRoomPlanDetailDraft::getLessonOrder));

      if (allDetails.size() <= 1) {
        log.info("计划只有一节课，无需校验区间");
        return;
      }

      // 找到当前要修改的课程在列表中的索引位置
      int currentIndex = -1;
      for (int i = 0; i < allDetails.size(); i++) {
        if (allDetails.get(i).getId().equals(detailId)) {
          currentIndex = i;
          break;
        }
      }

      if (currentIndex == -1) {
        log.error("未找到要修改的课程明细, detailId={}", detailId);
        throw new BizException("未找到要修改的课程明细");
      }

      // 检查上一节课的时间（如果不是第一节）
      if (currentIndex > 0) {
        LocalDateTime prevClassEndDateTime = allDetails.get(currentIndex - 1).getClassEndDateTime();
        if (newClassStartDateTime.isBefore(prevClassEndDateTime)) {
          log.error(
              "新的上课时间早于上一节课的结束时间, prevClassEndDateTime={}, newClassStartDateTime={}",
              prevClassEndDateTime,
              newClassStartDateTime);
          throw new BizException("新的上课时间不能早于上一节课的结束时间");
        }
      }

      // 检查下一节课的时间（如果不是最后一节）
      if (currentIndex < allDetails.size() - 1) {
        LocalDateTime nextClassStartDateTime =
            allDetails.get(currentIndex + 1).getClassStartDateTime();
        // 计算当前课程的结束时间（假设课程时长不变）
          LiveRoomPlanDetailDraft liveRoomPlanDetailDraft = allDetails.get(currentIndex);
          long duration =
            java.time.temporal.ChronoUnit.MINUTES.between(
                liveRoomPlanDetailDraft.getClassStartDateTime(), liveRoomPlanDetailDraft.getClassEndDateTime());
        LocalDateTime newClassEndDateTime = newClassStartDateTime.plusMinutes(duration);

        if (newClassEndDateTime.isAfter(nextClassStartDateTime)) {
          log.error(
              "新的下课时间晚于下一节课的开始时间, newClassEndDateTime={}, nextClassStartDateTime={}",
              newClassEndDateTime,
              nextClassStartDateTime);
          throw new BizException("新的下课时间不能晚于下一节课的开始时间");
        }
      }

      log.info("修改时间区间校验通过");
    }
  }

    /**
   * 删除直播间计划明细
   *
   * <AUTHOR>
   * @date 2024/12/5 11:17
   * @param ids 待删除的ID列表
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void removeDetail(ArrayList<Long> ids) {
    log.info("开始删除直播间计划明细, ids:{}", JSON.toJSONString(ids));

    // 参数校验
    if (CollectionUtils.isEmpty(ids)) {
      log.warn("删除直播间计划明细参数为空");
      return;
    }

    try {
      // 1. 校验课程是否已结束并获取明细数据
      List<LiveRoomPlanDetailDraft> detailDrafts = checkAndGetFinishedDetails(ids);
      Long planId = detailDrafts.get(0).getPlanId();
      log.info("获取到planId: {}", planId);

      liveRoomPlanDraftManager.checkAssociatedTeachingPlan(planId);

      // 2. 批量删除记录
      boolean removeSuccess = removeBatchByIds(ids);
      if (!removeSuccess) {
        log.error("删除计划明细失败");
        throw new BizException("删除失败");
      }
      log.info("删除计划明细成功, 删除数量: {}", ids.size());

      // 3. 更新课程序号
      updateLessonOrders(planId);

      // 删除更改对应的直播间计划为未发布状态
      updatePlanStatus(planId);

      // TODO-LY: liuyi 2024/12/5 Administrator 删除直播间排课同步处理已关联的教学计划明细

      log.info("删除直播间计划明细完成");
    } catch (BizException e) {
      log.warn("删除直播间计划明细业务异常", e);
      throw e;
    } catch (Exception e) {
      log.error("删除直播间计划明细系统异常", e);
      throw new BizException("系统异常:" + e.getMessage());
    }
  }

  /**
   * 校验课程是否已结束
   *
   * <AUTHOR>
   * @date 2024/12/7 10:30
   * @param ids 待校验的计划明细ID列表
   * @return List<LiveRoomPlanDetailDraft> 查询到的计划明细列表
   */
  private List<LiveRoomPlanDetailDraft> checkAndGetFinishedDetails(Collection<Long> ids) {
    log.info("开始校验课程是否已结束, ids:{}", JSON.toJSONString(ids));

    // 1. 批量查询计划明细
    List<LiveRoomPlanDetailDraft> detailDrafts =
        baseMapper.selectList(
            Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                .in(LiveRoomPlanDetailDraft::getId, ids));

    log.info("查询到计划明细数量: {}", detailDrafts.size());

    // 2. 校验数据是否存在
    if (detailDrafts.size() != ids.size()) {
      log.error("部分计划明细记录不存在");
      throw new BizException("部分计划明细记录不存在");
    }

    // 3. 检查是否有已结束的课程
    LocalDateTime now = LocalDateTime.now();
    List<LiveRoomPlanDetailDraft> finishedDetails =
        detailDrafts.stream().filter(detail -> detail.getClassEndDateTime().isBefore(now)).toList();

    if (!finishedDetails.isEmpty()) {
      log.error("存在已结束的课程,不允许操作, finishedDetails: {}", JSON.toJSONString(finishedDetails));
      throw new BizException("存在已结束的课程,不允许操作");
    }
    log.info("课程校验通过，所有课程均未结束");
    return detailDrafts;
  }
}

