package com.yuedu.ydsf.eduConnect.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.eduConnect.entity.SsDeviceAudioConfig;
import com.yuedu.ydsf.eduConnect.mapper.SsDeviceAudioConfigMapper;
import com.yuedu.ydsf.eduConnect.service.SsDeviceAudioConfigService;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.eduConnect.api.query.SsDeviceAudioConfigQuery;
import com.yuedu.ydsf.eduConnect.api.dto.SsDeviceAudioConfigDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceAudioConfigVO;

import java.util.List;


/**
* 设备音频相关配置服务层
*
* <AUTHOR>
* @date  2024/09/28
*/
@Service
public class SsDeviceAudioConfigServiceImpl extends ServiceImpl<SsDeviceAudioConfigMapper, SsDeviceAudioConfig>
    implements SsDeviceAudioConfigService {


    /**
    * 设备音频相关配置分页查询
    * @param page 分页对象
    * @param ssDeviceAudioConfigQuery 设备音频相关配置
    * @return IPage 分页结果
    */
    public IPage page(Page page,SsDeviceAudioConfigQuery ssDeviceAudioConfigQuery) {
        return page(page, Wrappers.<SsDeviceAudioConfig>lambdaQuery());
    }

    /**
    * 新增设备音频相关配置
    * @param ssDeviceAudioConfigDTO 设备音频相关配置
    * @return boolean 执行结果
    */
    public boolean add(SsDeviceAudioConfigDTO ssDeviceAudioConfigDTO) {
        SsDeviceAudioConfig ssDeviceAudioConfig = new SsDeviceAudioConfig();
        BeanUtils.copyProperties(ssDeviceAudioConfigDTO, ssDeviceAudioConfig);
        return save(ssDeviceAudioConfig);
    }


    /**
    * 修改设备音频相关配置
    * @param ssDeviceAudioConfigDTO 设备音频相关配置
    * @return boolean 执行结果
    */
    public boolean edit(SsDeviceAudioConfigDTO ssDeviceAudioConfigDTO) {
        SsDeviceAudioConfig ssDeviceAudioConfig = new SsDeviceAudioConfig();
        BeanUtils.copyProperties(ssDeviceAudioConfigDTO, ssDeviceAudioConfig);
        return updateById(ssDeviceAudioConfig);
    }



    /**
    * 导出excel 设备音频相关配置表格
    * @param ssDeviceAudioConfigQuery 查询条件
    * @param ids 导出指定ID
    * @return List<SsDeviceAudioConfigVO> 结果集合
    */
    public List<SsDeviceAudioConfigVO> export(SsDeviceAudioConfigQuery ssDeviceAudioConfigQuery, Long[] ids) {
        return list(Wrappers.<SsDeviceAudioConfig>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), SsDeviceAudioConfig::getId, ids))
            .stream()
            .map(entity -> {
                SsDeviceAudioConfigVO ssDeviceAudioConfigVO = new SsDeviceAudioConfigVO();
                BeanUtils.copyProperties(entity, ssDeviceAudioConfigVO);
                return ssDeviceAudioConfigVO;
            }).toList();
    }

}
