package com.yuedu.ydsf.eduConnect.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.eduConnect.mapper.SsLecturerMapper;
import com.yuedu.ydsf.eduConnect.service.SsLecturerService;
import com.yuedu.ydsf.eduConnect.api.query.SsLecturerQuery;
import com.yuedu.ydsf.eduConnect.api.dto.SsLecturerDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsLecturerVO;
import com.yuedu.ydsf.eduConnect.entity.SsLecturer;

import java.io.Serializable;
import java.util.Optional;
import java.util.List;


/**
* 主讲老师表服务层
*
* <AUTHOR>
* @date  2025/01/14
*/
@Service
public class SsLecturerServiceImpl extends ServiceImpl<SsLecturerMapper,SsLecturer>
    implements SsLecturerService{


    /**
     * 主讲老师表分页查询
     *
     * @param page 分页对象
     * @param ssLecturerQuery 主讲老师表
     * @return IPage 分页结果
     */
    @Override
    public IPage<SsLecturerVO> page(Page page,SsLecturerQuery ssLecturerQuery) {
        return page(page, Wrappers.<SsLecturer>lambdaQuery())
                .convert(entity -> {
                    SsLecturerVO ssLecturerVO = new SsLecturerVO();
                    BeanUtils.copyProperties(entity, ssLecturerVO);
                    return ssLecturerVO;
                });
    }


    /**
     * 根据ID获得主讲老师表信息
     *
     * @param id id
     * @return SsLecturerVO 详细信息
     */
    @Override
    public SsLecturerVO getInfoById(Serializable id) {
        return Optional.of(getById(id))
                .map(entity -> {
                    SsLecturerVO ssLecturerVO = new SsLecturerVO();
                    BeanUtils.copyProperties(entity, ssLecturerVO);
                    return ssLecturerVO;
                })
                .orElseThrow(()-> new CheckedException("查询结果为空"));
    }


    /**
     * 新增主讲老师表
     *
     * @param ssLecturerDTO 主讲老师表
     * @return boolean 执行结果
     */
    @Override
    public boolean add(SsLecturerDTO ssLecturerDTO) {
        SsLecturer ssLecturer = new SsLecturer();
        BeanUtils.copyProperties(ssLecturerDTO, ssLecturer);
        return save(ssLecturer);
    }


    /**
     * 修改主讲老师表
     *
     * @param ssLecturerDTO 主讲老师表
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(SsLecturerDTO ssLecturerDTO) {
        SsLecturer ssLecturer = new SsLecturer();
        BeanUtils.copyProperties(ssLecturerDTO, ssLecturer);
        return updateById(ssLecturer);
    }


    /**
     * 导出excel 主讲老师表表格
     *
     * @param ssLecturerQuery 查询条件
     * @param ids 导出指定ID
     * @return List<SsLecturerVO> 结果集合
     */
    @Override
    public List<SsLecturerVO> export(SsLecturerQuery ssLecturerQuery, Long[] ids) {
        return list(Wrappers.<SsLecturer>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), SsLecturer::getId, ids))
            .stream()
            .map(entity -> {
                SsLecturerVO ssLecturerVO = new SsLecturerVO();
                BeanUtils.copyProperties(entity, ssLecturerVO);
                return ssLecturerVO;
            }).toList();
    }

}
