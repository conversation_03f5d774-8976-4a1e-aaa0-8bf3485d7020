package com.yuedu.ydsf.eduConnect.service.impl;


import static com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum.REQUEST_ERROR;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.dto.LessonOrderDTO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.AuditStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DownloadStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.RecordingStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.StorageTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.TaskStatusEnum;
import com.yuedu.ydsf.eduConnect.api.dto.SsRecordingDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsRecordingQuery;
import com.yuedu.ydsf.eduConnect.api.vo.PlayAuthVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsRecordingVO;
import com.yuedu.ydsf.eduConnect.api.vo.UploadAuthVO;
import com.yuedu.ydsf.eduConnect.entity.RecordVideoTask;
import com.yuedu.ydsf.eduConnect.entity.Recording;
import com.yuedu.ydsf.eduConnect.entity.SsRecording;
import com.yuedu.ydsf.eduConnect.manager.SsRecordingManager;
import com.yuedu.ydsf.eduConnect.mapper.RecordingMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsRecordingMapper;
import com.yuedu.ydsf.eduConnect.service.RecordVideoTaskService;
import com.yuedu.ydsf.eduConnect.service.SsRecordingService;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RecordFileUpload;
import jakarta.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 点播库服务层
 *
 * <AUTHOR>
 * @date 2024/09/26
 */
@Slf4j
@Service
public class SsRecordingServiceImpl extends ServiceImpl<SsRecordingMapper, SsRecording> implements
    SsRecordingService {

    @Resource
    private SsRecordingManager ssRecordingManager;

    @Resource
    private RecordingMapper recordingMapper;

    @Resource
    private RecordVideoTaskService recordVideoTaskService;

    @Resource
    private RemoteLessonService remoteLessonService;

    /**
     * 点播库分页查询
     *
     * @param page             分页对象
     * @param ssRecordingQuery 点播库
     * @return IPage 分页结果
     */
    @Override
    public IPage page(Page page, SsRecordingQuery ssRecordingQuery) {
        IPage pages = page(page, Wrappers.<SsRecording>lambdaQuery()
            .like(StrUtil.isNotBlank(ssRecordingQuery.getBooksName()), SsRecording::getBooksName,
                ssRecordingQuery.getBooksName())
            .eq(Objects.nonNull(ssRecordingQuery.getGrade()), SsRecording::getGrade,
                ssRecordingQuery.getGrade())
            .eq(Objects.nonNull(ssRecordingQuery.getLecturerId()), SsRecording::getLecturerId,
                ssRecordingQuery.getLecturerId())
            .eq(Objects.nonNull(ssRecordingQuery.getAuditStatus()), SsRecording::getAuditStatus,
                ssRecordingQuery.getAuditStatus()).between(
                Objects.nonNull(ssRecordingQuery.getStartRecordingDate()) && Objects.nonNull(
                    ssRecordingQuery.getEndRecordingDate()), SsRecording::getRecordingTime,
                ssRecordingQuery.getStartRecordingDate(), ssRecordingQuery.getEndRecordingDate())
            .between(
                Objects.nonNull(ssRecordingQuery.getOriginalCourseStartDate()) && Objects.nonNull(
                    ssRecordingQuery.getOriginalCourseEndDate()),
                SsRecording::getOriginalCourseStartDate,
                ssRecordingQuery.getOriginalCourseStartDate(),
                ssRecordingQuery.getOriginalCourseEndDate())
            //.in(SsRecording::getRecordingStatus, Lists.newArrayList(RecordingStatusEnum.RECORDING_STATUS_2.code, RecordingStatusEnum.RECORDING_STATUS_7.code))
            .eq(SsRecording::getAuditStatus, AuditStatusEnum.AUDIT_STATUS_2.code)
            .eq(SsRecording::getRecordingStatus, RecordingStatusEnum.RECORDING_STATUS_2.code)
            .orderByDesc(SsRecording::getRecordingTime));
        return pages.setRecords(ssRecordingManager.convert(pages.getRecords()));
    }

    /**
     * 新增点播库
     *
     * @param ssRecordingDTO 点播库
     * @return boolean 执行结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(SsRecordingDTO ssRecordingDTO) {
        SsRecording ssRecording = new SsRecording();
        BeanUtils.copyProperties(ssRecordingDTO, ssRecording);
        if (StorageTypeEnum.STORAGE_TYPE_1.code.equals(ssRecordingDTO.getStorageType())) {
            if (StringUtils.isBlank(ssRecording.getVodVideoId())) {
                throw new BizException(REQUEST_ERROR, "点播库类型为VOD时，VodVideoId不能为空");
            }

            if (CollectionUtils.isNotEmpty(list(Wrappers.<SsRecording>lambdaQuery()
                .eq(SsRecording::getVodVideoId, ssRecordingDTO.getVodVideoId())))) {
                throw new BizException(REQUEST_ERROR, "点播库类型为VOD时，VodVideoId不能重复");
            }
            ssRecordingManager.fillResource(ssRecording);
        }
        save(ssRecording);
        if (StorageTypeEnum.STORAGE_TYPE_1.code.equals(ssRecordingDTO.getStorageType())) {
            ssRecordingManager.submitTranscodeTask(ssRecording);
            updateById(ssRecording);
        }

        return true;

    }


    /**
     * 修改点播库
     *
     * @param ssRecordingDTO 点播库
     * @return boolean 执行结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean edit(SsRecordingDTO ssRecordingDTO) {
        SsRecording oldSsRecording = getById(ssRecordingDTO.getId());
        if (Objects.isNull(oldSsRecording)) {
            throw new BizException(REQUEST_ERROR, "当前数据不存在");
        }
        SsRecording ssRecording = new SsRecording();
        BeanUtils.copyProperties(ssRecordingDTO, ssRecording);
        if (StorageTypeEnum.STORAGE_TYPE_1.code.equals(ssRecordingDTO.getStorageType())) {
            if (StringUtils.isNotBlank(ssRecording.getVodVideoId())
                && !oldSsRecording.getVodVideoId().equals(ssRecording.getVodVideoId())) {
                if (CollectionUtils.isNotEmpty(list(Wrappers.<SsRecording>lambdaQuery()
                    .eq(SsRecording::getVodVideoId, ssRecordingDTO.getVodVideoId())))) {
                    throw new BizException(REQUEST_ERROR, "点播库类型为VOD时，VodVideoId不能重复");
                }
                ssRecordingManager.fillResource(ssRecording);
            }

        }
        updateById(ssRecording);

        if (StorageTypeEnum.STORAGE_TYPE_1.code.equals(ssRecordingDTO.getStorageType())) {
            if (StringUtils.isNotBlank(ssRecording.getVodVideoId())
                && !oldSsRecording.getVodVideoId().equals(ssRecording.getVodVideoId())) {
                ssRecordingManager.submitTranscodeTask(ssRecording);
                updateById(ssRecording);
            }

        }

        return true;
    }

    /**
     * 通过id删除点播库
     *
     * @param id id
     * @return SsRecordingVO 执行结果
     */
    @Override
    public SsRecordingVO getRecordingById(Serializable id) {
        return ssRecordingManager.convert(List.of(getById(id))).stream().findFirst().orElse(null);
    }


    /**
     * 导出excel 点播库表格
     *
     * @param ssRecordingQuery 查询条件
     * @param ids              导出指定ID
     * @return List<SsRecordingVO> 结果集合
     */
    @Override
    public List<SsRecordingVO> export(SsRecordingQuery ssRecordingQuery, Long[] ids) {
        return ssRecordingManager.convert(list(
            Wrappers.<SsRecording>lambdaQuery().eq(SsRecording::getId, ssRecordingQuery.getId())));
    }

    /**
     * 获得视频上传凭证
     *
     * @param fileName
     * @param title
     * <AUTHOR>
     * @date 2024年09月27日 14时53分
     */
    @Override
    public UploadAuthVO getUploadAuth(String fileName, String title) {
        return ssRecordingManager.getUploadAuth(fileName, title);
    }

    /**
     * 刷新视频上传凭证
     *
     * @param videoId
     * <AUTHOR>
     * @date 2024年09月27日 14时53分
     */
    @Override
    public UploadAuthVO refreshUploadAuth(String videoId) {
        return ssRecordingManager.refreshUploadAuth(videoId);
    }


    @Override
    public SsRecordingVO download(Long id) {
        return Optional.ofNullable(getById(id))
            .filter(s -> DownloadStatusEnum.DOWNLOAD_STATUS_1.code.equals(s.getDownloadStatus()))
            .map(s -> {
                SsRecordingVO ssRecordingVO = new SsRecordingVO();
                ssRecordingVO.setRecordingResources(s.getDownloadUrl());
                return ssRecordingVO;
            }).orElseThrow(() -> {
                throw new BizException(REQUEST_ERROR, "当前下载链接不可用！");
            });

    }

    @Override
    public PlayAuthVO getPlayAuth(String videoId, Long expiredTime) {
        if (Objects.isNull(expiredTime)) {
            expiredTime = 24 * 3600L;
        }
        return ssRecordingManager.getPlayAuth(videoId, expiredTime);
    }

    @Override
    public boolean deleteByIds(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids) && ssRecordingManager.checkDeleteRecording(ids)) {
            return removeBatchByIds(ids);
        } else {
            throw new BizException(REQUEST_ERROR, "当前记录中存在排课，不允许删除操作！");
        }
    }


    @Override
    public boolean recordUpload(RecordFileUpload recordFileUpload) {
        log.info("接收声网视频存储信息：{}", recordFileUpload);

        // 查询旧版录制表是否已完成
        SsRecording oldSsRecording = getOne(Wrappers.<SsRecording>lambdaQuery()
            .eq(SsRecording::getAgoraCloudRecordId, recordFileUpload.getSid()));

        // 查询新版录制表是否已完成
        Recording newRecording = recordingMapper.selectOne(Wrappers.<Recording>lambdaQuery()
            .eq(Recording::getAgoraCloudRecordId, recordFileUpload.getSid()));

        if (Objects.isNull(oldSsRecording) && Objects.isNull(newRecording)) {
            log.info("接收声网视频存储信息：{}，未查询到录制表信息，忽略处理！", recordFileUpload);
            return true;
        }

        if ((Objects.nonNull(oldSsRecording) && RecordingStatusEnum.RECORDING_STATUS_2.code.equals(
            oldSsRecording.getRecordingStatus())) || (Objects.nonNull(newRecording)
            && RecordingStatusEnum.RECORDING_STATUS_2.code.equals(
            newRecording.getRecordingStatus()))) {

            log.info("接收声网视频存储信息：{}，录制已完成，忽略处理！", recordFileUpload);
            return true;
        }

        if (Objects.isNull(recordFileUpload.getDetails()) || !recordFileUpload.getDetails()
            .isSuccess()) {
            log.error("接收声网视频存储信息失败信息：{}", recordFileUpload);
            return false;
        }

        boolean bool = false;

        if (Objects.nonNull(oldSsRecording)) {

            log.info("开始处理旧版录制表信息: {}！", oldSsRecording);

            // 旧版录制表处理
            bool = oldSsRecordingDis(recordFileUpload, oldSsRecording);

        } else if (Objects.nonNull(newRecording)) {

            log.info("开始处理新版录制表信息: {}！", newRecording);

            // 新版录制表处理
            bool = newSsRecordingDis(recordFileUpload, newRecording);

        }
        return bool;
    }

    private boolean oldSsRecordingDis(RecordFileUpload recordFileUpload,
        SsRecording oldSsRecording) {

        try {
            List<SsRecording> ssRecordings = ssRecordingManager.registerVideo(
                recordFileUpload.getDetails().getFileList().stream().map(f -> {
                    SsRecording ssRecording = new SsRecording();
                    ssRecording.setBooksName(oldSsRecording.getBooksName());
                    ssRecording.setRecordingResources(f.getFileName());
                    return ssRecording;
                }).toList());

            if (CollectionUtils.isEmpty(ssRecordings)) {
                return false;
            }

            SsRecording newSsRecording = new SsRecording();

            ssRecordings.stream().filter(s -> s.getRecordingResources().endsWith(".m3u8"))
                .findFirst().ifPresent(s -> {
                    newSsRecording.setRecordingStatus(RecordingStatusEnum.RECORDING_STATUS_2.code);
                    newSsRecording.setAuditStatus(AuditStatusEnum.AUDIT_STATUS_1.code);
                    newSsRecording.setVodVideoId(s.getVodVideoId());
                    newSsRecording.setRecordingResources(s.getRecordingResources());
                });

            ssRecordings.stream().filter(s -> s.getRecordingResources().endsWith(".mp4"))
                .findFirst().ifPresent(s -> {
                    newSsRecording.setDownloadStatus(DownloadStatusEnum.DOWNLOAD_STATUS_1.code);
                    newSsRecording.setDownloadVodId(s.getVodVideoId());
                    newSsRecording.setDownloadUrl(s.getRecordingResources());
                });

            // 更新旧版录制表
            update(newSsRecording, Wrappers.<SsRecording>lambdaUpdate()
                .eq(SsRecording::getAgoraCloudRecordId, recordFileUpload.getSid()));

            return true;

        } catch (Exception e) {
            log.error("声网回调阿里云注册错误：{}", e.getMessage());
        }

        return false;

    }

    private boolean newSsRecordingDis(RecordFileUpload recordFileUpload,
        Recording newRecordingInfo) {

        try {

            // 查询课节名称
            String lessonName = getLessonName(newRecordingInfo);

            List<SsRecording> ssRecordings = ssRecordingManager.registerVideo(
                recordFileUpload.getDetails().getFileList().stream().map(f -> {
                    SsRecording ssRecording = new SsRecording();
                    ssRecording.setBooksName(lessonName);
                    ssRecording.setRecordingResources(f.getFileName());
                    return ssRecording;
                }).toList());

            if (CollectionUtils.isEmpty(ssRecordings)) {
                return false;
            }

            Recording newRecording = new Recording();

            ssRecordings.stream().filter(s -> s.getRecordingResources().endsWith(".m3u8"))
                .findFirst().ifPresent(s -> {
                    newRecording.setRecordingStatus(RecordingStatusEnum.RECORDING_STATUS_2.code);
                    newRecording.setAuditStatus(AuditStatusEnum.AUDIT_STATUS_1.code);
                    newRecording.setVodVideoId(s.getVodVideoId());
                    newRecording.setRecordingResources(s.getRecordingResources());
                });

            ssRecordings.stream().filter(s -> s.getRecordingResources().endsWith(".mp4"))
                .findFirst().ifPresent(s -> {
                    newRecording.setDownloadVodId(s.getVodVideoId());
                    newRecording.setDownloadUrl(s.getRecordingResources());
                });

            // 更新新版录制表
            recordingMapper.update(newRecording, Wrappers.<Recording>lambdaUpdate()
                .eq(Recording::getAgoraCloudRecordId, recordFileUpload.getSid()));

            // 录课完成后, 修改录课任务状态为已完成
            recordVideoTaskService.update(Wrappers.<RecordVideoTask>lambdaUpdate()
                .eq(newRecordingInfo.getRecordVideoTaskId() != null, RecordVideoTask::getId,
                    newRecordingInfo.getRecordVideoTaskId())
                .set(RecordVideoTask::getTaskStatus, TaskStatusEnum.TASK_STATUS_ENUM_1.CODE)
            );

            return true;

        } catch (Exception e) {
            log.error("声网回调阿里云注册错误：{}", e.getMessage());
        }

        return false;

    }

    /**
     * 查询课节名称
     *
     * @param newRecordingInfo
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/3/17 15:03
     */
    private String getLessonName(Recording newRecordingInfo) {

        RecordVideoTask recordVideoTask = recordVideoTaskService.getById(
            newRecordingInfo.getRecordVideoTaskId());
        List<LessonOrderDTO> lessonOrderDTOList = new ArrayList<>();
        LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
        lessonOrderDTO.setCourseId(recordVideoTask.getCourseId());
        lessonOrderDTO.setLessonOrderList(
            Collections.singletonList(recordVideoTask.getLessonOrder()));
        lessonOrderDTOList.add(lessonOrderDTO);
        log.info("课节信息请求参数: [{}]", JSONObject.toJSONString(lessonOrderDTOList));
        R<List<LessonVO>> lessonList = remoteLessonService.getLessonListByOrder(lessonOrderDTOList);
        log.info("课节信息返回参数: [{}]", JSONObject.toJSONString(lessonList));
        String lessonName = "";
        if (lessonList.isOk() && CollectionUtils.isNotEmpty(lessonList.getData())) {
            lessonName = lessonList.getData().get(0).getLessonName();
        }

        return lessonName;
    }

    @Override
    public boolean handlerTranscode(String videoId, boolean isTranscode) {
        Optional.ofNullable(getOne(Wrappers.<SsRecording>lambdaQuery()
                .eq(SsRecording::getVodVideoId, videoId)
            ))
            .ifPresent(ssRecording -> {
                ssRecordingManager.getPlayInfo(
                    ssRecording.getVodVideoId()).forEach(playInfo -> {
                    if (playInfo.endsWith(".m3u8")) {
                        ssRecording.setRecordingResources(playInfo);
                    } else if (playInfo.endsWith(".mp4")) {
                        ssRecording.setDownloadUrl(playInfo);
                    }
                });
                updateById(ssRecording);
            });
        return true;
    }


    /**
     * 老双师点播库查询
     * <AUTHOR>
     * @date 2025/4/22 14:18
     * @param ssRecordingQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsRecordingVO>
     */
    @Override
    public List<SsRecordingVO> recordTaskMatchList(SsRecordingQuery ssRecordingQuery) {
        // 构建查询条件
        LambdaQueryWrapper<SsRecording> wrapper = Wrappers.<SsRecording>lambdaQuery()
            .like(StringUtils.isNotBlank(ssRecordingQuery.getBooksName()),
                SsRecording::getBooksName, ssRecordingQuery.getBooksName())
            .eq(SsRecording::getAuditStatus, AuditStatusEnum.AUDIT_STATUS_2.code)
            .orderByDesc(SsRecording::getCtime);

        // 执行查询
        List<SsRecording> recordList = this.list(wrapper);

        // 转换为VO对象
        return recordList.stream()
            .map(record -> {
                SsRecordingVO vo = new SsRecordingVO();
                BeanUtils.copyProperties(record, vo);
                return vo;
            })
            .collect(Collectors.toList());
    }
}
