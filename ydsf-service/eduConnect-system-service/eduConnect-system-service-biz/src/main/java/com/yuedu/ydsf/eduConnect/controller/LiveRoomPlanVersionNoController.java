package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersionNo;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanVersionNoService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 直播间计划版本生成记录表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-29 14:38:15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/LiveRoomPlanVersionNo" )
@Tag(description = "ea_live_room_plan_version_no" , name = "直播间计划版本生成记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LiveRoomPlanVersionNoController {

    private final  LiveRoomPlanVersionNoService liveRoomPlanVersionNoService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param liveRoomPlanVersionNo 直播间计划版本生成记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("admin_LiveRoomPlanVersionNo_view")
    public R getLiveRoomPlanVersionNoPage(@ParameterObject Page page, @ParameterObject LiveRoomPlanVersionNo liveRoomPlanVersionNo) {
        LambdaQueryWrapper<LiveRoomPlanVersionNo> wrapper = Wrappers.lambdaQuery();
        return R.ok(liveRoomPlanVersionNoService.page(page, wrapper));
    }


    /**
     * 通过条件查询直播间计划版本生成记录表
     * @param liveRoomPlanVersionNo 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("admin_LiveRoomPlanVersionNo_view")
    public R getDetails(@ParameterObject LiveRoomPlanVersionNo liveRoomPlanVersionNo) {
        return R.ok(liveRoomPlanVersionNoService.list(Wrappers.query(liveRoomPlanVersionNo)));
    }

    /**
     * 新增直播间计划版本生成记录表
     * @param liveRoomPlanVersionNo 直播间计划版本生成记录表
     * @return R
     */
    @Operation(summary = "新增直播间计划版本生成记录表" , description = "新增直播间计划版本生成记录表" )
    @PostMapping("/add")
    @HasPermission("admin_LiveRoomPlanVersionNo_add")
    public R save(@RequestBody LiveRoomPlanVersionNo liveRoomPlanVersionNo) {
        return R.ok(liveRoomPlanVersionNoService.save(liveRoomPlanVersionNo));
    }

    /**
     * 修改直播间计划版本生成记录表
     * @param liveRoomPlanVersionNo 直播间计划版本生成记录表
     * @return R
     */
    @Operation(summary = "修改直播间计划版本生成记录表" , description = "修改直播间计划版本生成记录表" )
    @PutMapping("/edit")
    @HasPermission("admin_LiveRoomPlanVersionNo_edit")
    public R updateById(@RequestBody LiveRoomPlanVersionNo liveRoomPlanVersionNo) {
        return R.ok(liveRoomPlanVersionNoService.updateById(liveRoomPlanVersionNo));
    }

    /**
     * 通过id删除直播间计划版本生成记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除直播间计划版本生成记录表" , description = "通过id删除直播间计划版本生成记录表" )
    @DeleteMapping("/delete")
    @HasPermission("admin_LiveRoomPlanVersionNo_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(liveRoomPlanVersionNoService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param liveRoomPlanVersionNo 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("admin_LiveRoomPlanVersionNo_export")
    public List<LiveRoomPlanVersionNo> exportExcel(LiveRoomPlanVersionNo liveRoomPlanVersionNo,Long[] ids) {
        return liveRoomPlanVersionNoService.list(Wrappers.lambdaQuery(liveRoomPlanVersionNo).in(ArrayUtil.isNotEmpty(ids), LiveRoomPlanVersionNo::getId, ids));
    }

    /**
     * 导入excel 表
     * @param liveRoomPlanVersionNoList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("admin_LiveRoomPlanVersionNo_export")
    public R importExcel(@RequestExcel List<LiveRoomPlanVersionNo> liveRoomPlanVersionNoList, BindingResult bindingResult) {
        return R.ok(liveRoomPlanVersionNoService.saveBatch(liveRoomPlanVersionNoList));
    }
}
