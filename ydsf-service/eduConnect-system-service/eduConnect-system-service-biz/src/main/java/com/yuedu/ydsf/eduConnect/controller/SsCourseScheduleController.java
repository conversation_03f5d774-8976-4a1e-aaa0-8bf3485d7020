package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.api.constant.AttendClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.vo.CreateReadingScheduleVO;
import com.yuedu.ydsf.eduConnect.api.vo.CreateVodScheduleVO;
import com.yuedu.ydsf.eduConnect.entity.SsCourseSchedule;
import com.yuedu.ydsf.eduConnect.service.SsCourseScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 排课表 控制类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:10:16
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/ssCourseSchedule" )
@Tag(description = "ss_course_schedule" , name = "排课表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SsCourseScheduleController {

    private final  SsCourseScheduleService ssCourseScheduleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ssCourseSchedule 排课表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("edusystem_ssCourseSchedule_view")
    public R getSsCourseSchedulePage(@ParameterObject Page page, @ParameterObject SsCourseSchedule ssCourseSchedule) {
        LambdaQueryWrapper<SsCourseSchedule> wrapper = Wrappers.lambdaQuery();
        return R.ok(ssCourseScheduleService.page(page, wrapper));
    }


    /**
     * 通过条件查询排课表
     * @param ssCourseSchedule 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("edusystem_ssCourseSchedule_view")
    public R getDetails(@ParameterObject SsCourseSchedule ssCourseSchedule) {
        return R.ok(ssCourseScheduleService.list(Wrappers.query(ssCourseSchedule)));
    }

    /**
     * 新增读书会排课表
     * @param createReadingScheduleVo 排课表
     * @return R
     */
    @Operation(summary = "新增读书会排课表" , description = "新增读书会排课表" )
    @PostMapping("/reading")
    @HasPermission("course_schedule_reading_add")
    public R saveReading(@Valid @RequestBody CreateReadingScheduleVO createReadingScheduleVo) {
        log.info("新增读书会排课表:{}", JSONUtil.toJsonStr(createReadingScheduleVo));
        createReadingScheduleVo.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_0.CODE);
        ssCourseScheduleService.saveCourseSchedule(createReadingScheduleVo);
        return R.ok();
    }

    /**
     * 新增点播课排课表
     * @param createVodScheduleVo 排课表
     * @return R
     */
    @Operation(summary = "新增点播课排课表" , description = "新增点播课排课表" )
    @PostMapping("/vod")
    @HasPermission("edusystem_ssCourseSchedule_vod_add")
    public R saveVod(@Valid @RequestBody CreateVodScheduleVO createVodScheduleVo) {
        log.info("新增点播课排课表:{}", JSONUtil.toJsonStr(createVodScheduleVo));
        createVodScheduleVo.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_1.CODE);
        ssCourseScheduleService.saveCourseSchedule(createVodScheduleVo);
        return R.ok();
    }

    /**
     * 通过id删除排课表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除排课表" , description = "通过id删除排课表" )
    @DeleteMapping
    @HasPermission("edusystem_ssCourseSchedule_del")
    public R removeById(@RequestBody Long[] ids) {
        log.info("通过id删除排课表:{}", JSONUtil.toJsonStr(ids));
        return R.ok(ssCourseScheduleService.removeBatchByIds(CollUtil.toList(ids)));
    }
}
