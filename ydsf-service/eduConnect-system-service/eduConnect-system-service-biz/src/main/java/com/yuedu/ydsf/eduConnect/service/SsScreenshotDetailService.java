package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.query.SsScreenshotDetailQuery;
import com.yuedu.ydsf.eduConnect.api.dto.SsScreenshotDetailDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsScreenshotDetailVO;
import com.yuedu.ydsf.eduConnect.entity.SsScreenshotDetail;

import java.util.List;

/**
 * 双师截图明细表服务接口
 *
 * <AUTHOR>
 * @date 2024/10/11
 */
public interface SsScreenshotDetailService extends IService<SsScreenshotDetail> {

    /**
     * 双师截图明细表分页查询
     *
     * @param page                    分页对象
     * @param ssScreenshotDetailQuery 双师截图明细表
     * @return IPage 分页结果
     */
    IPage page(Page page,
               SsScreenshotDetailQuery ssScreenshotDetailQuery);

    /**
     * 新增双师截图明细表
     *
     * @param ssScreenshotDetailDTO 双师截图明细表
     * @return boolean 执行结果
     */
    boolean add(SsScreenshotDetailDTO ssScreenshotDetailDTO);

    /**
     * 修改双师截图明细表
     *
     * @param ssScreenshotDetailDTO 双师截图明细表
     * @return boolean 执行结果
     */
    boolean edit(SsScreenshotDetailDTO ssScreenshotDetailDTO);

    /**
     * 导出excel 双师截图明细表表格
     *
     * @param ssScreenshotDetailQuery 查询条件
     * @param ids                     导出指定ID
     * @return List<SsScreenshotDetailVO> 结果集合
     */
    List<SsScreenshotDetailVO> export(SsScreenshotDetailQuery ssScreenshotDetailQuery,
                                      Long[] ids);

    /**
     * 保存截图
     *
     * @param ssScreenshotDetailDTO
     * @return void
     * <AUTHOR>
     * @date 2024/10/11 10:37
     */
    void saveScreenshot(SsScreenshotDetailDTO ssScreenshotDetailDTO);


    /**
     *  图片分析处理
     *
     * <AUTHOR>
     * @date 2025年02月17日 17时07分
     */
    void pictureRecognitionHandle();
}
