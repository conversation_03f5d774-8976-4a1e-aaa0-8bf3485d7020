package com.yuedu.ydsf.eduConnect.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.yuedu.ydsf.eduConnect.api.vo.LessonOrderVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 已发布的教学计划明细表 持久层
 *
 * <AUTHOR>
 * @date 2024-11-29 15:14:17
 */
@Mapper
public interface TeachingPlanDetailPubMapper extends MPJBaseMapper<TeachingPlanDetailPub> {


    /**
     * 通过教学计划详情id获取课节顺序
     *
     * @param planDetailIdList 教学计划详情id列表
     * @return 结果
     */
    List<LessonOrderVO> selectLessonOrder(List<Long> planDetailIdList);


    /**
     * 通过教学任务Id查询已发布的教学任务详情
     *
     * @param planIdList 录课任务
     * @return 结果
     */
    List<TeachingPlanDetailPub> getPlanDetailPubByIdList(List<Long> planIdList);


    /**
     * 通过教学任务Id查询教学任务详情
     *
     * @param planId 教学任务Id
     * @return 结果
     */
    List<TeachingPlanDetailPubVO> getAllByPlanId(Long planId);

    /**
     * 通过教学任务Id和教师Id查询教学任务详情
     *
     * @param planId 教学任务Id
     * @return 结果
     */
    List<TeachingPlanDetailPub> getTeachingPlanDetail(Long planId);
}
