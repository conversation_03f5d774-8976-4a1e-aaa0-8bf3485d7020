package com.yuedu.ydsf.eduConnect.service;

import com.github.yulichang.base.MPJBaseService;
import com.yuedu.ydsf.eduConnect.api.dto.DeleteVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.EditVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.GenerateVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.LessonCoursewareEditDTO;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO;
import com.yuedu.ydsf.eduConnect.api.query.RecordVideoTaskQuery;
import com.yuedu.ydsf.eduConnect.api.vo.RecordVideoTaskVO;
import com.yuedu.ydsf.eduConnect.entity.RecordVideoTask;
import java.util.List;

/**
 * @ClassName RecordVideoTaskService
 * @Description 录课任务服务类
 * <AUTHOR>
 * @Date 2024/11/29 16:48:01
 * @Version v0.0.1
 */
public interface RecordVideoTaskService extends MPJBaseService<RecordVideoTask> {

    /**
     * 生成录课任务
     *
     * @param videoTaskDto 教学计划Id
     * @return 结果
     */
    int generateRecordVideoTask(GenerateVideoTaskDto videoTaskDto);


    /**
     * 编辑录课任务 修改课程包,主讲老师, 除已完成的录课任务外,全部删除后重新生成
     *
     * @param videoTaskDto 教学计划Id
     * @return 结果
     */
    int editRecordVideoTask(EditVideoTaskDto videoTaskDto);


    /**
     * 编辑教学计划中某个讲师的排期
     *
     * @param operateMqDTO 课节修改列表
     * @return 结果
     */
    int editTaskScheduling(TeachingPlanOperateMqDTO operateMqDTO);


    /**
     * 编辑课件
     *
     * @param coursewareId 课件Id
     * @return 结果
     */
    int editCourseware(Long coursewareId);


    /**
     * 课节中课件版本编辑
     *
     * @param coursewareIdList 课件Id
     * @return 结果
     */
    int editLessonCourseware(List<LessonCoursewareEditDTO> coursewareIdList);


    /**
     * 删除录课任务 1.已录制(已完成)的任务不可删除 2.未完成的任务逻辑删除 3.其他计划中任然存在的任务需比对上课最早开始时间后进行任务的调整
     *
     * @param videoTaskDto 教学计划Id
     * @return 结果
     */
    int deleteRecordVideoTask(DeleteVideoTaskDto videoTaskDto);


    /**
     * 根据条件查询讲师录课任务列表
     *
     * @param videoTaskQuery 查询条件
     * @return 结果
     */
    List<RecordVideoTaskVO> getRecordTaskList(RecordVideoTaskQuery videoTaskQuery);
}
