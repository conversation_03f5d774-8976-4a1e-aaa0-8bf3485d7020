package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.query.TimetableQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableExportVO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.entity.Timetable;
import com.yuedu.ydsf.eduConnect.live.api.dto.TimetablePictureDTO;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店课表 服务类
 *
 * <AUTHOR>
 * @date 2025-01-10 14:27:17
 */
public interface TimetableService extends IService<Timetable> {


    /**
     *  处理课中事件报警
     *
     * <AUTHOR>
     * @date 2025年03月11日 09时43分
     */
    void handlerEventAlarm(TimetablePictureDTO eventDTO);



    /**
     *  分页查询
     *
     * <AUTHOR>
     * @date 2025年03月11日 14时58分
     */
    IPage page(Page page, TimetableQuery timetableQuery);


    /**
     *  上课明细信息
     *
     * <AUTHOR>
     * @date 2025年03月12日 10时32分
     */
    TimetableVO getInfoById(Long id);


    /**
     *  根据门店id获取课程列表
     *
     * <AUTHOR>
     * @date 2025年06月05日 16时29分
     */
    List<Long> getCourseListByStoreId(Long storeId);


    /**
     *  课消导出
     *
     * <AUTHOR>
     * @date 2025年07月08日 09时14分
     */
    List<TimetableExportVO> export(TimetableQuery timetableQuery);
}
