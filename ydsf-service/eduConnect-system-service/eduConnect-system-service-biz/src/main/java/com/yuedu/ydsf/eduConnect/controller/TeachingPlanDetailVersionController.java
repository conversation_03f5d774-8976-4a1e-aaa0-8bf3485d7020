package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailVersion;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDetailVersionService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教学计划明细版本发布记录表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-29 14:44:35
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/TeachingPlanDetailVersion" )
@Tag(description = "ea_teaching_plan_detail_version" , name = "教学计划明细版本发布记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TeachingPlanDetailVersionController {

    private final  TeachingPlanDetailVersionService teachingPlanDetailVersionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param teachingPlanDetailVersion 教学计划明细版本发布记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("admin_TeachingPlanDetailVersion_view")
    public R getTeachingPlanDetailVersionPage(@ParameterObject Page page, @ParameterObject TeachingPlanDetailVersion teachingPlanDetailVersion) {
        LambdaQueryWrapper<TeachingPlanDetailVersion> wrapper = Wrappers.lambdaQuery();
        return R.ok(teachingPlanDetailVersionService.page(page, wrapper));
    }


    /**
     * 通过条件查询教学计划明细版本发布记录表
     * @param teachingPlanDetailVersion 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("admin_TeachingPlanDetailVersion_view")
    public R getDetails(@ParameterObject TeachingPlanDetailVersion teachingPlanDetailVersion) {
        return R.ok(teachingPlanDetailVersionService.list(Wrappers.query(teachingPlanDetailVersion)));
    }

    /**
     * 新增教学计划明细版本发布记录表
     * @param teachingPlanDetailVersion 教学计划明细版本发布记录表
     * @return R
     */
    @Operation(summary = "新增教学计划明细版本发布记录表" , description = "新增教学计划明细版本发布记录表" )
    @PostMapping("/add")
    @HasPermission("admin_TeachingPlanDetailVersion_add")
    public R save(@RequestBody TeachingPlanDetailVersion teachingPlanDetailVersion) {
        return R.ok(teachingPlanDetailVersionService.save(teachingPlanDetailVersion));
    }

    /**
     * 修改教学计划明细版本发布记录表
     * @param teachingPlanDetailVersion 教学计划明细版本发布记录表
     * @return R
     */
    @Operation(summary = "修改教学计划明细版本发布记录表" , description = "修改教学计划明细版本发布记录表" )
    @PutMapping("/edit")
    @HasPermission("admin_TeachingPlanDetailVersion_edit")
    public R updateById(@RequestBody TeachingPlanDetailVersion teachingPlanDetailVersion) {
        return R.ok(teachingPlanDetailVersionService.updateById(teachingPlanDetailVersion));
    }

    /**
     * 通过id删除教学计划明细版本发布记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除教学计划明细版本发布记录表" , description = "通过id删除教学计划明细版本发布记录表" )
    @DeleteMapping("/delete")
    @HasPermission("admin_TeachingPlanDetailVersion_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(teachingPlanDetailVersionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param teachingPlanDetailVersion 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("admin_TeachingPlanDetailVersion_export")
    public List<TeachingPlanDetailVersion> exportExcel(TeachingPlanDetailVersion teachingPlanDetailVersion,Long[] ids) {
        return teachingPlanDetailVersionService.list(Wrappers.lambdaQuery(teachingPlanDetailVersion).in(ArrayUtil.isNotEmpty(ids), TeachingPlanDetailVersion::getId, ids));
    }

    /**
     * 导入excel 表
     * @param teachingPlanDetailVersionList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("admin_TeachingPlanDetailVersion_export")
    public R importExcel(@RequestExcel List<TeachingPlanDetailVersion> teachingPlanDetailVersionList, BindingResult bindingResult) {
        return R.ok(teachingPlanDetailVersionService.saveBatch(teachingPlanDetailVersionList));
    }
}
