package com.yuedu.ydsf.eduConnect.constant;


/**
 * @ClassName RecordVideoTaskConstant
 * @Description 录课任务常量
 * <AUTHOR>
 * @Date 2024/12/7 14:24
 * @Version v0.0.1
 */
public class RecordVideoTaskConstant {
    //***** 录课任务增删改使用 *****

    /**
     * 年月日格式
     */
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 周一
     */
    public static final String MONDAY = "周一";

    /**
     * 周二
     */
    public static final String TUESDAY = "周二";

    /**
     * 周三
     */
    public static final String WEDNESDAY = "周三";

    /**
     * 周四
     */
    public static final String THURSDAY = "周四";

    /**
     * 周五
     */
    public static final String FRIDAY = "周五";

    /**
     * 周六
     */
    public static final String SATURDAY = "周六";

    /**
     * 周日
     */
    public static final String SUNDAY = "周日";

    /**
     * 1
     */
    public static final int ONE = 1;

    /**
     * 2
     */
    public static final int TWO = 2;

    /**
     * 3
     */
    public static final int THREE = 3;

    /**
     * 4
     */
    public static final int FOUR = 4;

    /**
     * 5
     */
    public static final int FIVE = 5;

    /**
     * 6
     */
    public static final int SIX = 6;

    /**
     * 7
     */
    public static final int SEVEN = 7;

    /**
     * 上午
     */
    public static final String MORNING = "上午";

    /**
     * 下午
     */
    public static final String AFTERNOON = "下午";


    /**
     * 录课任务状态:录制中
     */
    public static final int RECORDING = 4;

    /**
     * 录课任务状态:已完成
     */
    public static final int FINISHED_TASK = 1;

    /**
     * 录课任务状态:未完成
     */
    public static final int NOT_FINISHED_TASK = 0;

    /**
     * 分页大小
     */
    public static final int PAGE_SIZE = 25;

    /**
     * 教学计划状态:开启中
     */
    public static final int TEACHING_PLAN_OPEN_STATUS = 0;

    /**
     * 教学计划状态:已关闭
     */
    public static final int TEACHING_PLAN_CLOSED_STATUS = 1;

    //***** 录课任务消息队列使用 *****
    /**
     * 队列载体
     */
    public static final String PAYLOAD = "payload";

    public static final String MESSAGE = "message";

    /**
     * 操作类型
     */
    public static final String OPERATE_TYPE = "operateType";

    /**
     * 新建教学计划
     */
    public static final int INSERT_TEACHING_PLAN = 0;

    /**
     * 编辑教学计划
     */
    public static final int EDIT_TEACHING_PLAN = 1;

    /**
     * 删除教学计划
     */
    public static final int DELETE_TEACHING_PLAN = 2;


    /**
     * 编辑课节
     */
    public static final int EDIT_LESSON = 3;

    /**
     * 编辑课件
     */
    public static final int EDIT_COURSEWARE = 4;


    /**
     * 课件Id
     */
    public static final String COURSEWARE_ID = "coursewareId";

    /**
     * 匹配特定前缀
     */
    public static final String MATCH_PREFIX = "MATCH_";
}
