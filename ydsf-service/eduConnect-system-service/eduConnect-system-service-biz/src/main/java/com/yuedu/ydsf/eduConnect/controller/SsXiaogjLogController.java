package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjLog;
import com.yuedu.ydsf.eduConnect.service.SsXiaogjLogService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 校管家日志表 控制类
 *
 * <AUTHOR>
 * @date 2024-10-28 15:57:51
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/ssXiaogjLog" )
@Tag(description = "ss_xiaogj_log" , name = "校管家日志表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SsXiaogjLogController {

    private final  SsXiaogjLogService ssXiaogjLogService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ssXiaogjLog 校管家日志表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("edusystem_ssXiaogjLog_view")
    public R getSsXiaogjLogPage(@ParameterObject Page page, @ParameterObject SsXiaogjLog ssXiaogjLog) {
        LambdaQueryWrapper<SsXiaogjLog> wrapper = Wrappers.lambdaQuery();
        return R.ok(ssXiaogjLogService.page(page, wrapper));
    }


    /**
     * 通过条件查询校管家日志表
     * @param ssXiaogjLog 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("edusystem_ssXiaogjLog_view")
    public R getDetails(@ParameterObject SsXiaogjLog ssXiaogjLog) {
        return R.ok(ssXiaogjLogService.list(Wrappers.query(ssXiaogjLog)));
    }

    /**
     * 新增校管家日志表
     * @param ssXiaogjLog 校管家日志表
     * @return R
     */
    @Operation(summary = "新增校管家日志表" , description = "新增校管家日志表" )
    @PostMapping("/add")
    @HasPermission("edusystem_ssXiaogjLog_add")
    public R save(@RequestBody SsXiaogjLog ssXiaogjLog) {
        return R.ok(ssXiaogjLogService.save(ssXiaogjLog));
    }

    /**
     * 修改校管家日志表
     * @param ssXiaogjLog 校管家日志表
     * @return R
     */
    @Operation(summary = "修改校管家日志表" , description = "修改校管家日志表" )
    @PutMapping("/edit")
    @HasPermission("edusystem_ssXiaogjLog_edit")
    public R updateById(@RequestBody SsXiaogjLog ssXiaogjLog) {
        return R.ok(ssXiaogjLogService.updateById(ssXiaogjLog));
    }

    /**
     * 通过id删除校管家日志表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除校管家日志表" , description = "通过id删除校管家日志表" )
    @DeleteMapping("/delete")
    @HasPermission("edusystem_ssXiaogjLog_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(ssXiaogjLogService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param ssXiaogjLog 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_ssXiaogjLog_export")
    public List<SsXiaogjLog> exportExcel(SsXiaogjLog ssXiaogjLog,Long[] ids) {
        return ssXiaogjLogService.list(Wrappers.lambdaQuery(ssXiaogjLog).in(ArrayUtil.isNotEmpty(ids), SsXiaogjLog::getId, ids));
    }

    /**
     * 导入excel 表
     * @param ssXiaogjLogList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_ssXiaogjLog_export")
    public R importExcel(@RequestExcel List<SsXiaogjLog> ssXiaogjLogList, BindingResult bindingResult) {
        return R.ok(ssXiaogjLogService.saveBatch(ssXiaogjLogList));
    }
}
