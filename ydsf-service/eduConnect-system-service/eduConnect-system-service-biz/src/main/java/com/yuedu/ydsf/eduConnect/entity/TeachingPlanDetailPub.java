package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 已发布的教学计划明细表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-29 15:14:17
 */
@Data
@TableName("ea_teaching_plan_detail_pub")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "已发布的教学计划明细表实体类")
public class TeachingPlanDetailPub extends Model<TeachingPlanDetailPub> {


	/**
	* 主键id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键id")
    private Long id;

	/**
	* 教学计划id
	*/
    @Schema(description="教学计划id")
    private Long planId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 课程id
	*/
    @Schema(description="课程id")
    private Long courseId;

	/**
	* 课程名字
	*/
    @Schema(description="课程名字")
    private String courseName;

	/**
	* 课节id
	*/
    @Schema(description="课节id")
    private Long lessonId;

	/**
	* 课节名字
	*/
    @Schema(description="课节名字")
    private String lessonName;

	/**
	* 书籍ID
	*/
    @Schema(description="书籍ID")
    private Long bookId;

	/**
	* 书籍名称
	*/
    @Schema(description="书籍名称")
    private String bookName;

	/**
	* 上课开始日期
	*/
    @Schema(description="上课开始日期")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    private LocalTime classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    private LocalTime classEndTime;

	/**
	* 上课开始日期时间
	*/
    @Schema(description="上课开始日期时间")
    private LocalDateTime classStartDateTime;

	/**
	* 上课结束日期时间
	*/
    @Schema(description="上课结束日期时间")
    private LocalDateTime classEndDateTime;

	/**
	* 主讲id
	*/
    @Schema(description="主讲id")
    private Long lectureId;

    /**
     * 教学计划表中的主讲老师id
     */
    @Schema(description = "教学计划表中的主讲老师id")
    @TableField(exist = false)
    private Long lectureIdMain;

	/**
	* 主讲名字
	*/
    @Schema(description="主讲名字")
    private String lectureName;

	/**
	* 直播间ID
	*/
    @Schema(description="直播间ID")
    private Long liveRoomId;


    /**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;

    /**
     * 上课时段ID
     */
    @Schema(description="上课时段ID")
    private Long timeSlotId;

    /**
     * 课程版本(直播端进入课程时存储)
     */
    @Schema(description="课程版本(直播端进入课程时存储)")
    private Integer courseVersion;

    /**
     * 课件版本(直播端进入课程时存储)
     */
    @Schema(description="课件版本(直播端进入课程时存储)")
    private Integer coursewareVersion;

}
