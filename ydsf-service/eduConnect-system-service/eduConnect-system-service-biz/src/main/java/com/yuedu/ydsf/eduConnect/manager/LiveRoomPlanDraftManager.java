package com.yuedu.ydsf.eduConnect.manager;

import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDraftVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 直播间计划 公共服务类
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface LiveRoomPlanDraftManager {
  /**
   * 通过条件查询直播计划
   *
   * @return List<LiveRoomPlanDraftVO>
   */
  List<LiveRoomPlanDraftVO> listByIds(List<Long> ids);

  /**
   * 检查直播间计划id是否已经结束
   *
   * <AUTHOR>
   * @date 2024/12/6 10:23
   * @param id
   * @return void
   */
  void checkPlanEndStatus(Long id);

  /**
   * 校验直播间计划有没有关联教学计划
   *
   * @param id
   * @return void
   */
  void checkAssociatedTeachingPlan(Long id);

  /**
   * 校验直播间计划是否存在过期的明细
   *
   * @param planId
   * @return void
   */
  void checkHasExpiredDetail(Long planId);
}
