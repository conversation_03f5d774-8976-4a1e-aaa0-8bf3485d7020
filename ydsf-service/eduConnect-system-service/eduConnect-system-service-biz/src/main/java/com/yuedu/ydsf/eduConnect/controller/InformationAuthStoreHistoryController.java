package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.eduConnect.service.InformationAuthStoreHistoryService;
import com.yuedu.ydsf.eduConnect.api.query.InformationAuthStoreHistoryQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationAuthStoreHistoryDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationAuthStoreHistoryVO;

import java.io.Serializable;
import java.util.List;

/**
* 资料授权历史控制层
*
* <AUTHOR>
* @date  2025/07/22
*/

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/informationAuthStoreHistory")
@Tag(description = "ss_information_auth_store_history" , name = "资料授权历史" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class InformationAuthStoreHistoryController  {


    private final InformationAuthStoreHistoryService nformationAuthStoreHistoryService;


    /**
    * 资料授权历史分页查询
    * @param page 分页对象
    * @param informationAuthStoreHistoryQuery 资料授权历史
    * @return R
    */
    @GetMapping("/page" )
    @HasPermission("teaching_informationAuthStoreHistory_view")
    @Operation(summary = "分页查询" , description = "资料授权历史分页查询" )
    public R<IPage<InformationAuthStoreHistoryVO>> page(@ParameterObject Page page, @ParameterObject InformationAuthStoreHistoryQuery informationAuthStoreHistoryQuery) {
        return R.ok(nformationAuthStoreHistoryService.page(page, informationAuthStoreHistoryQuery));
    }


    /**
    * 通过id查询资料授权历史
    * @param id id
    * @return R
    */
    @Operation(summary = "通过id查询" , description = "通过id查询资料授权历史" )
    @GetMapping("/{id}" )
    @HasPermission("teaching_informationAuthStoreHistory_view")
    public R<InformationAuthStoreHistoryVO> getById(@PathVariable Serializable id) {
        return R.ok(nformationAuthStoreHistoryService.getInfoById(id));
    }



    /**
    * 新增资料授权历史
    * @param informationAuthStoreHistoryDTO 资料授权历史
    * @return R
    */
    @PostMapping
    @SysLog("新增资料授权历史" )
    @HasPermission("teaching_informationAuthStoreHistory_view" )
    @Operation(summary = "新增资料授权历史" , description = "新增资料授权历史" )
    public R add(@Validated(V_A.class) @RequestBody InformationAuthStoreHistoryDTO informationAuthStoreHistoryDTO) {
         return R.ok(nformationAuthStoreHistoryService.add(informationAuthStoreHistoryDTO));
    }


    /**
    * 修改资料授权历史
    * @param informationAuthStoreHistoryDTO 资料授权历史
    * @return R
    */
    @PutMapping
    @SysLog("修改资料授权历史" )
    @HasPermission("teaching_informationAuthStoreHistory_edit" )
    @Operation(summary = "修改资料授权历史" , description = "修改资料授权历史" )
    public R edit(@Validated(V_E.class) @RequestBody InformationAuthStoreHistoryDTO informationAuthStoreHistoryDTO) {
         return R.ok(nformationAuthStoreHistoryService.edit(informationAuthStoreHistoryDTO));
    }



    /**
    * 通过id删除资料授权历史
    * @param ids id列表
    * @return R
    */
    @DeleteMapping
    @SysLog("通过id删除资料授权历史" )
    @HasPermission("teaching_informationAuthStoreHistory_del" )
    @Operation(summary = "删除资料授权历史" , description = "删除资料授权历史" )
    public R delete(@RequestBody  Long[] ids){
         return R.ok(nformationAuthStoreHistoryService.removeBatchByIds(CollUtil.toList(ids)));
    }



   /**
   * 导出excel 资料授权历史表格
   * @param  informationAuthStoreHistoryQuery 查询条件
   * @param ids 导出指定ID
   * @return excel 文件流
   */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("teaching_informationAuthStoreHistory_export" )
    @Operation(summary = "导出资料授权历史表格" , description = "导出资料授权历史表格" )
    public List<InformationAuthStoreHistoryVO> export(InformationAuthStoreHistoryQuery informationAuthStoreHistoryQuery, Long[] ids) {
        return nformationAuthStoreHistoryService.export(informationAuthStoreHistoryQuery, ids);
    }


}
