package com.yuedu.ydsf.eduConnect.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.IsOnLineEnum;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanDetailPubDTO;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteLivingRoomPlanDetailVersionService;
import com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO;
import com.yuedu.ydsf.eduConnect.api.vo.LessonOrderVO;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanVO;
import com.yuedu.ydsf.eduConnect.api.vo.PlanNameVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.entity.LiveChannel;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanPub;
import com.yuedu.ydsf.eduConnect.mapper.ClassTimeMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveChannelMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailVersionMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanVersionMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDetailDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDetailPubMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanPubMapper;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDetailPubService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 已发布的教学计划明细表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-29 15:14:17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeachingPlanDetailPubServiceImpl extends
    ServiceImpl<TeachingPlanDetailPubMapper, TeachingPlanDetailPub> implements
    TeachingPlanDetailPubService {

    @Resource
    private final ClassTimeMapper classTimeMapper;
    private final LiveChannelMapper liveChannelMapper;
    private final TeachingPlanPubMapper teachingPlanPubMapper;
    private final LiveRoomPlanDetailVersionMapper liveRoomPlanDetailVersionMapper;
    private final TeachingPlanDetailDraftMapper teachingPlanDetailDraftMapper;
    private final TeachingPlanDraftMapper teachingPlanDraftMapper;
    @Resource
    private LiveRoomPlanVersionMapper liveRoomPlanVersionMapper;
    @Resource
    private TeachingPlanDetailPubMapper teachingPlanDetailPubMapper;
    @Resource
    private RemoteLivingRoomPlanDetailVersionService remoteLivingRoomPlanDetailVersionService;


    /**
     * 查询老师是否有未结束的排课
     *
     * @param lectureId 老师id
     * @return true:有未结束的排课 false:无未结束的排课
     */
    @Override
    public Boolean hasUnfinishedClass(Long lectureId) {
        //两个有一个true，返回true
        return checkPubTeachingPlan(lectureId) || checkNoPubTeachingPlan(lectureId);
    }

    private boolean checkPubTeachingPlan(Long lectureId) {
        //查询classEndDataTime，有大于当前时间的说明有未结束的排课，没有则证明无未结束的排课
        return exists(Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
            .eq(TeachingPlanDetailPub::getLectureId, lectureId)
            .gt(TeachingPlanDetailPub::getClassEndDateTime, LocalDateTime.now()));
    }

    private boolean checkNoPubTeachingPlan(Long lectureId) {
        List<TeachingPlanDetailDraft> teachingPlanDetailDrafts = teachingPlanDetailDraftMapper.selectList(
            Wrappers.lambdaQuery(TeachingPlanDetailDraft.class)
                .eq(TeachingPlanDetailDraft::getLectureId, lectureId));
        if (CollectionUtils.isEmpty(teachingPlanDetailDrafts)) {
            return false;
        }
        //将planId拿出转成List
        List<Long> planIds = teachingPlanDetailDrafts.stream()
            .map(TeachingPlanDetailDraft::getPlanId)
            .distinct().toList();
        //把lessonOrder拿出来转成List
        List<Integer> lessonOrders = teachingPlanDetailDrafts.stream()
            .map(TeachingPlanDetailDraft::getLessonOrder)
            .distinct().toList();
        //查teaching plan draft表，找id对应的live room plan id
        List<TeachingPlanDraft> teachingPlanDrafts = teachingPlanDraftMapper.selectList(
            Wrappers.lambdaQuery(TeachingPlanDraft.class)
                .in(TeachingPlanDraft::getId, planIds));
        if (CollectionUtils.isEmpty(teachingPlanDrafts)) {
            //教学计划为空，没有未结束的排课
            return false;
        }
        List<Long> liveRoomPlanIds = teachingPlanDrafts.stream()
            .map(TeachingPlanDraft::getLiveRoomPlanId)
            .distinct().toList();

        //查每个planId下，版本号最大的一条记录
        List<LiveRoomPlanVersion> liveRoomPlanVersions = liveRoomPlanVersionMapper.selectList(
            Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                .in(LiveRoomPlanVersion::getPlanId, liveRoomPlanIds));

        //查出所有之后，找出每个planId最高的版本号，加入一个list
        ArrayList<LiveRoomPlanVersion> liveRoomPlanVersionIds = new ArrayList<>(
            liveRoomPlanVersions.stream()
                .collect(Collectors.toMap(
                    LiveRoomPlanVersion::getPlanId,
                    version -> version,
                    (existing, replacement) -> existing.getVersion() > replacement.getVersion()
                        ? existing : replacement
                )).values());

        //判断版本号相同的是否classEndDataTime，有大于当前时间的说明有未结束的排课，没有则证明无未结束的排课
        return liveRoomPlanDetailVersionMapper.exists(Wrappers.lambdaQuery(
                LiveRoomPlanDetailVersion.class)
            .in(LiveRoomPlanDetailVersion::getVersion, liveRoomPlanVersionIds.stream()
                .map(LiveRoomPlanVersion::getVersion).toList())
            .in(LiveRoomPlanDetailVersion::getLessonOrder, lessonOrders)
            .gt(LiveRoomPlanDetailVersion::getClassEndDateTime, LocalDateTime.now()));
    }


    /**
     * 通过计划Id列表查询直播间计划名称
     *
     * @param planIdList 计划Id列表
     * @return 结果
     */
    @Override
    public LiveRoomPlanVO getPlanVo(List<Long> planIdList) {
        LiveRoomPlanVO liveRoomPlanVO = new LiveRoomPlanVO();

        //设置课节顺序
        List<LessonOrderVO> lessonOrderVOList = teachingPlanDetailPubMapper.selectLessonOrder(
            planIdList);
        liveRoomPlanVO.setLessonOrderVO(lessonOrderVOList);

        // 设置教学计划名称
        List<PlanNameVO> planNameVOList = liveRoomPlanVersionMapper.selectPlanName(
            planIdList);
        liveRoomPlanVO.setPlanNameVo(planNameVOList);

        return liveRoomPlanVO;
    }

    /**
     * 通过教学任务Id查询教学任务详情
     *
     * @param planId 教学任务Id
     * @return 结果
     */
    @Override
    public List<LiveRoomPlanDetailVersionVO> getAllByPlanId(Long planId) {
        List<LiveRoomPlanDetailVersionVO> roomPlanDetailVersionVoList = new ArrayList<>();

        //重新编写逻辑
        List<TeachingPlanDetailPub> planDetailPubList = teachingPlanDetailPubMapper.getTeachingPlanDetail(
            planId);

        Long courseId = planDetailPubList.get(0).getCourseId();
        //教学计划表中的讲师Id
        Long lectureIdMain = planDetailPubList.get(0).getLectureIdMain();
        log.info("教学计划详情:{}", JSON.toJSONString(planDetailPubList));
        if (!planDetailPubList.isEmpty()) {
            Long liveRoomPlanId = planDetailPubList.get(0).getLiveRoomId();
            R<List<LiveRoomPlanDetailVersionVO>> listR = remoteLivingRoomPlanDetailVersionService.selectLiveRoomPlanDetailVersionList(
                liveRoomPlanId);
            log.info("直播间信息:{}", JSON.toJSONString(listR.getCode()));
            if (listR.getCode() == 0) {
                roomPlanDetailVersionVoList = listR.getData();
                for (int i = 0; i < roomPlanDetailVersionVoList.size(); i++) {
                    roomPlanDetailVersionVoList.get(i).setCourseId(courseId);
                    for (int j = planDetailPubList.size() - 1; j >= i; j--) {
                        roomPlanDetailVersionVoList.get(i)
                            .setLectureId(planDetailPubList.get(j).getLectureId());
                        roomPlanDetailVersionVoList.get(i)
                            .setLectureIdMain(planDetailPubList.get(j).getLectureIdMain());
                    }
                }
            }
        }
        log.info("结果:{}", JSON.toJSONString(roomPlanDetailVersionVoList));
        return roomPlanDetailVersionVoList;
    }

    /**
     * 通过课程, 主讲老师 查询对应时间最早的直播间计划/教学计划
     *
     * @param teachingPlanDetailPubDTO
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO>
     * <AUTHOR>
     * @date 2024/12/11 14:54
     */
    @Override
    public List<AtTheEarliestAttendClassDetailVO> getAtTheEarliestCoursePlanList(
        TeachingPlanDetailPubDTO teachingPlanDetailPubDTO) {

        List<AtTheEarliestAttendClassDetailVO> atTheEarliestAttendClassDetailVOS = new ArrayList<>();

        List<TeachingPlanDetailPub> teachingPlanDetailPubList = this.list(
            Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                .eq(TeachingPlanDetailPub::getCourseId, teachingPlanDetailPubDTO.getCourseId())
                .eq(TeachingPlanDetailPub::getLectureId, teachingPlanDetailPubDTO.getLectureId())
        );

        if (CollectionUtils.isEmpty(teachingPlanDetailPubList)) {
            return atTheEarliestAttendClassDetailVOS;
        }

        // 过滤上课时间最早的教学计划明细
        TeachingPlanDetailPub minStartDateDetail = teachingPlanDetailPubList.stream()
            .min(Comparator.comparing(TeachingPlanDetailPub::getClassStartDateTime))
            .orElse(new TeachingPlanDetailPub());

        // 查询上课最早教学计划
        TeachingPlanPub teachingPlanPub = teachingPlanPubMapper.selectOne(
            Wrappers.lambdaQuery(TeachingPlanPub.class)
                .eq(TeachingPlanPub::getTeachingPlanId, minStartDateDetail.getPlanId())
        );

        // 查询上课最早教学计划对应的未关闭直播间计划
        LiveRoomPlanVersion liveRoomPlanVersion = liveRoomPlanVersionMapper.selectOne(
            Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                .eq(LiveRoomPlanVersion::getPlanId, teachingPlanPub.getLiveRoomPlanId())
                .eq(LiveRoomPlanVersion::getOnlineVersion, IsOnLineEnum.ISONLINE_1.code)
        );

        // 查询上课最早教学计划对应的直播间计划明细
        List<LiveRoomPlanDetailVersion> liveRoomPlanDetailVersionList = liveRoomPlanDetailVersionMapper.selectList(
            Wrappers.lambdaQuery(LiveRoomPlanDetailVersion.class)
                .eq(LiveRoomPlanDetailVersion::getPlanId, liveRoomPlanVersion.getPlanId())
                .eq(LiveRoomPlanDetailVersion::getVersion, liveRoomPlanVersion.getVersion())
                .eq(Objects.nonNull(teachingPlanDetailPubDTO.getLessonOrder()),
                    LiveRoomPlanDetailVersion::getLessonOrder,
                    teachingPlanDetailPubDTO.getLessonOrder())
                .orderByAsc(LiveRoomPlanDetailVersion::getClassStartDateTime)
        );

        atTheEarliestAttendClassDetailVOS = liveRoomPlanDetailVersionList.stream()
            .map(entity -> {

                AtTheEarliestAttendClassDetailVO atTheEarliestAttendClassDetailVO = new AtTheEarliestAttendClassDetailVO();
                atTheEarliestAttendClassDetailVO.setCourseId(
                    teachingPlanDetailPubDTO.getCourseId());
                atTheEarliestAttendClassDetailVO.setLessonOrder(entity.getLessonOrder());
                atTheEarliestAttendClassDetailVO.setClassDate(entity.getClassDate());
                atTheEarliestAttendClassDetailVO.setClassStartTime(entity.getClassStartTime());
                atTheEarliestAttendClassDetailVO.setClassEndTime(entity.getClassEndTime());
                atTheEarliestAttendClassDetailVO.setClassStartDateTime(
                    entity.getClassStartDateTime());
                atTheEarliestAttendClassDetailVO.setClassEndDateTime(entity.getClassEndDateTime());
                atTheEarliestAttendClassDetailVO.setTeachingPlanPubId(
                    teachingPlanPub.getTeachingPlanId());

                return atTheEarliestAttendClassDetailVO;
            }).toList();

        return atTheEarliestAttendClassDetailVOS;
    }

  /**
   * 专门为点播课场景：通过课程查询该课程下所有教学计划中每个课节的最早上课时间（不限制老师）
   *
   * 场景说明：
   * A课程有两个教学计划：
   * A1计划(贾老师)和A2计划(易老师)
   * A1第一节：2020年1月1日12:00，A2第一节：2022年1月1日12:00
   * 当为A2教学计划排期第一节课时，校验会基于该课程第一节课的最早时间（2020年1月1日12:00）进行校验
   * 用户排期A2计划第一节课为2020年1月1日12:00是允许的，因为它不早于该课程第一节课的最早时间
   *
   * <AUTHOR>
   * @date 2025/6/12 10:15
   * @param courseId
   * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO>
   */
  @Override
  public List<AtTheEarliestAttendClassDetailVO> getAtTheEarliestCoursePlanListForVod(
      Long courseId) {

    List<AtTheEarliestAttendClassDetailVO> atTheEarliestAttendClassDetailVOS = new ArrayList<>();

    // 查询该课程的所有教学计划明细（不限制主讲老师，以获取课程下所有教学计划的最早时间）
    List<TeachingPlanDetailPub> teachingPlanDetailPubList =
        this.list(
            Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                .eq(TeachingPlanDetailPub::getCourseId, courseId));

    if (CollectionUtils.isEmpty(teachingPlanDetailPubList)) {
      return atTheEarliestAttendClassDetailVOS;
    }

    // 获取所有教学计划ID
    List<Long> planIds =
        teachingPlanDetailPubList.stream()
            .map(TeachingPlanDetailPub::getPlanId)
            .distinct()
            .collect(Collectors.toList());

    // 查询所有相关的教学计划
    List<TeachingPlanPub> teachingPlanPubList =
        teachingPlanPubMapper.selectList(
            Wrappers.lambdaQuery(TeachingPlanPub.class)
                .in(TeachingPlanPub::getTeachingPlanId, planIds));

    // 获取所有直播间计划ID
    List<Long> liveRoomPlanIds =
        teachingPlanPubList.stream()
            .map(TeachingPlanPub::getLiveRoomPlanId)
            .distinct()
            .collect(Collectors.toList());

    // 查询所有相关的未关闭直播间计划版本
    List<LiveRoomPlanVersion> liveRoomPlanVersionList =
        liveRoomPlanVersionMapper.selectList(
            Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                .in(LiveRoomPlanVersion::getPlanId, liveRoomPlanIds)
                .eq(LiveRoomPlanVersion::getOnlineVersion, IsOnLineEnum.ISONLINE_1.code));

    // 查询所有相关的直播间计划明细
    List<LiveRoomPlanDetailVersion> allLiveRoomPlanDetailVersionList = new ArrayList<>();
    for (LiveRoomPlanVersion liveRoomPlanVersion : liveRoomPlanVersionList) {
      List<LiveRoomPlanDetailVersion> liveRoomPlanDetailVersionList =
          liveRoomPlanDetailVersionMapper.selectList(
              Wrappers.lambdaQuery(LiveRoomPlanDetailVersion.class)
                  .eq(LiveRoomPlanDetailVersion::getPlanId, liveRoomPlanVersion.getPlanId())
                  .eq(LiveRoomPlanDetailVersion::getVersion, liveRoomPlanVersion.getVersion()));
      allLiveRoomPlanDetailVersionList.addAll(liveRoomPlanDetailVersionList);
    }

    // 按课节号分组，每个课节取最早的上课时间
    Map<Integer, LiveRoomPlanDetailVersion> earliestByLessonOrder =
        allLiveRoomPlanDetailVersionList.stream()
            .collect(
                Collectors.toMap(
                    LiveRoomPlanDetailVersion::getLessonOrder,
                    entity -> entity,
                    (existing, replacement) ->
                        existing
                                .getClassStartDateTime()
                                .isBefore(replacement.getClassStartDateTime())
                            ? existing
                            : replacement));

    // 转换为返回对象
    atTheEarliestAttendClassDetailVOS =
        earliestByLessonOrder.values().stream()
            .map(
                entity -> {
                  AtTheEarliestAttendClassDetailVO atTheEarliestAttendClassDetailVO =
                      new AtTheEarliestAttendClassDetailVO();
                  atTheEarliestAttendClassDetailVO.setCourseId(courseId);
                  atTheEarliestAttendClassDetailVO.setLessonOrder(entity.getLessonOrder());
                  atTheEarliestAttendClassDetailVO.setClassDate(entity.getClassDate());
                  atTheEarliestAttendClassDetailVO.setClassStartTime(entity.getClassStartTime());
                  atTheEarliestAttendClassDetailVO.setClassEndTime(entity.getClassEndTime());
                  atTheEarliestAttendClassDetailVO.setClassStartDateTime(
                      entity.getClassStartDateTime());
                  atTheEarliestAttendClassDetailVO.setClassEndDateTime(
                      entity.getClassEndDateTime());

                  // 找到对应的教学计划ID（这里取任意一个，关心的是课节的最早时间）
                  Long teachingPlanId =
                      teachingPlanPubList.stream()
                          .findFirst()
                          .map(TeachingPlanPub::getTeachingPlanId)
                          .orElse(null);
                  atTheEarliestAttendClassDetailVO.setTeachingPlanPubId(teachingPlanId);

                  return atTheEarliestAttendClassDetailVO;
                })
            .sorted(Comparator.comparing(AtTheEarliestAttendClassDetailVO::getLessonOrder))
            .collect(Collectors.toList());

    return atTheEarliestAttendClassDetailVOS;
  }

    /**
     * 通过教学计划ID查询教学计划明细,声网频道
     *
     * @param teachingPlanIdList
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO>
     * <AUTHOR>
     * @date 2024/12/17 19:24
     */
    @Override
    public List<TeachingPlanDetailPubVO> getTeachingPlanDetailPubLiveChannel(
        List<Long> teachingPlanIdList) {

        List<TeachingPlanDetailPubVO> teachingPlanDetailPubVOList = new ArrayList<>();

        // 获取教学计划明细
        List<TeachingPlanDetailPub> teachingPlanDetailPubList = this.list(
            Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                .in(TeachingPlanDetailPub::getPlanId, teachingPlanIdList)
        );

        if (CollectionUtils.isEmpty(teachingPlanDetailPubList)) {
            return teachingPlanDetailPubVOList;
        }

        List<TeachingPlanPub> teachingPlanPubList = teachingPlanPubMapper.selectList(
            Wrappers.lambdaQuery(TeachingPlanPub.class)
                .in(TeachingPlanPub::getTeachingPlanId, teachingPlanIdList)
        );

        // 通过教学计划明细获取对应声网直播频道
        List<Long> teachingPlanDetailPubIdList = teachingPlanDetailPubList.stream()
            .map(TeachingPlanDetailPub::getId)
            .distinct()
            .collect(Collectors.toList());

        List<LiveChannel> liveChannelList = liveChannelMapper.selectList(
            Wrappers.lambdaQuery(LiveChannel.class)
                .in(LiveChannel::getTeachingPlanDetailId, teachingPlanDetailPubIdList)
        );

        teachingPlanDetailPubVOList = teachingPlanDetailPubList.stream()
            .map(entity -> {

                TeachingPlanDetailPubVO teachingPlanDetailPubVO = new TeachingPlanDetailPubVO();
                BeanUtils.copyProperties(entity, teachingPlanDetailPubVO);

                // 声网频道
                LiveChannel liveChannel = liveChannelList.stream()
                    .filter(
                        e -> e.getTeachingPlanDetailId().equals(teachingPlanDetailPubVO.getId()))
                    .findFirst()
                    .orElse(new LiveChannel());

                teachingPlanDetailPubVO.setChannelId(liveChannel.getChannelId());

                // 教学计划对应的主讲id
                TeachingPlanPub teachingPlanPub = teachingPlanPubList.stream()
                    .filter(e -> e.getTeachingPlanId().equals(teachingPlanDetailPubVO.getPlanId()))
                    .findFirst()
                    .orElse(new TeachingPlanPub());

                teachingPlanDetailPubVO.setTeachingPlanPubLectureId(teachingPlanPub.getLectureId());

                return teachingPlanDetailPubVO;

            }).toList();

        return teachingPlanDetailPubVOList;
    }

    /**
     * 通过教学计划ID查询教学计划明细列表
     *
     * @param teachingPlanId 教学计划Id列表
     * @return 结果
     */
    @Override
    public List<TeachingPlanDetailPubVO> getTeachingPlanDetailPub(Long teachingPlanId) {
        List<TeachingPlanDetailPubVO> teachingPlanDetailPubVOList = new ArrayList<>();
        List<TeachingPlanDetailPub> teachingPlanDetailPubList = teachingPlanDetailPubMapper.selectList(
            Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                .eq(TeachingPlanDetailPub::getPlanId, teachingPlanId)
                .orderByAsc(TeachingPlanDetailPub::getLessonOrder));
        if (!teachingPlanDetailPubList.isEmpty()) {
            teachingPlanDetailPubList.forEach(teachingPlanDetailPub -> {
                TeachingPlanDetailPubVO vo = new TeachingPlanDetailPubVO();
                BeanUtils.copyProperties(teachingPlanDetailPub, vo);
                teachingPlanDetailPubVOList.add(vo);
            });
        }
        return teachingPlanDetailPubVOList;
    }

    @Override
    public List<TeachingPlanDetailPub> getTeachingPlanDetailPubDTOList(
        List<Long> teachingPlanIdList) {
        return this.list(Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
            .in(TeachingPlanDetailPub::getPlanId, teachingPlanIdList));
    }
}
