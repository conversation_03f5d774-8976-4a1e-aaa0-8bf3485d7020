package com.yuedu.ydsf.eduConnect.util;

import com.yuedu.ydsf.eduConnect.constant.Constants;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * @author: z<PERSON>chuan<PERSON>
 * @date: 2024/10/14
 **/
public class DateUtil {

    private static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern(
        Constants.YYYY_MM_DD_HH_MM_SS);
    private static final DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern(
        Constants.YYYY_MM_DD);
    private static final DateTimeFormatter HH_MM_SS = DateTimeFormatter.ofPattern(
        Constants.HH_MM_SS);

    public static String format(LocalDateTime localDateTime) {
        return localDateTime.format(YYYY_MM_DD_HH_MM_SS);
    }

    public static String format(LocalDate localDate) {
        return localDate.format(YYYY_MM_DD);
    }

    public static String format(LocalTime localTime) {
        return localTime.format(HH_MM_SS);
    }

    public static LocalDateTime parse(String localDateTime) {
        return LocalDateTime.parse(localDateTime, YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 校验时间为上午, 下午, 晚上
     * @param timeStringToCheck
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/18 9:55
     */
    public static String isTime(String timeStringToCheck){

        String timeType = "";

        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");

        String time1 = "00:00:00";
        String time2 = "12:00:00";
        String time3 = "12:00:01";
        String time4 = "18:00:00";
        String time5 = "18:00:01";
        String time6 = "23:59:59";

        // 将字符串转换为LocalTime对象
        LocalTime timeToCheck = LocalTime.parse(timeStringToCheck, formatter);

        // 检查时间是否在范围内
        if (timeToCheck.isAfter(LocalTime.parse(time1)) && timeToCheck.isBefore(LocalTime.parse(time2))) {
            timeType = "morning";
        }

        if (timeToCheck.isAfter(LocalTime.parse(time3)) && timeToCheck.isBefore(LocalTime.parse(time4))) {
            timeType = "afternoon";
        }

        if (timeToCheck.isAfter(LocalTime.parse(time5)) && timeToCheck.isBefore(LocalTime.parse(time6))) {
            timeType = "evening";
        }

        return timeType;
    }

}
