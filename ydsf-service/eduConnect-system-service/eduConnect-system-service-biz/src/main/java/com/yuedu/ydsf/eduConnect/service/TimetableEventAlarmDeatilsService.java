package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.query.TimetableEventAlarmDeatilsQuery;
import com.yuedu.ydsf.eduConnect.api.dto.TimetableEventAlarmDeatilsDTO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableEventAlarmDeatilsVO;
import com.yuedu.ydsf.eduConnect.entity.TimetableEventAlarmDeatils;

import java.io.Serializable;
import java.util.List;

/**
* 课次报警明细表服务接口
*
* <AUTHOR>
* @date  2025/03/10
*/
public interface TimetableEventAlarmDeatilsService extends IService<TimetableEventAlarmDeatils> {



    /**
     * 课次报警明细表分页查询
     *
     * @param page 分页对象
     * @param timetableEventAlarmDeatilsQuery 课次报警明细表
     * @return IPage 分页结果
     */
    IPage<TimetableEventAlarmDeatilsVO> page(Page page, TimetableEventAlarmDeatilsQuery timetableEventAlarmDeatilsQuery);


    /**
     * 根据ID获得课次报警明细表信息
     *
     * @param id id
     * @return TimetableEventAlarmDeatilsVO 详细信息
     */
    TimetableEventAlarmDeatilsVO getInfoById(Serializable id);


    /**
     * 新增课次报警明细表
     *
     * @param timetableEventAlarmDeatilsDTO 课次报警明细表
     * @return boolean 执行结果
     */
    boolean add(TimetableEventAlarmDeatilsDTO timetableEventAlarmDeatilsDTO);


    /**
     * 修改课次报警明细表
     *
     * @param timetableEventAlarmDeatilsDTO 课次报警明细表
     * @return boolean 执行结果
     */
    boolean edit(TimetableEventAlarmDeatilsDTO timetableEventAlarmDeatilsDTO);


    /**
     * 导出excel 课次报警明细表表格
     *
     * @param timetableEventAlarmDeatilsQuery 查询条件
     * @param ids 导出指定ID
     * @return List<TimetableEventAlarmDeatilsVO> 结果集合
     */
    List<TimetableEventAlarmDeatilsVO> export(TimetableEventAlarmDeatilsQuery timetableEventAlarmDeatilsQuery, Long[] ids);
}
