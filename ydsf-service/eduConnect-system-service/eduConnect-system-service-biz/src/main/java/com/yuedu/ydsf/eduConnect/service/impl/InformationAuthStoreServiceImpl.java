package com.yuedu.ydsf.eduConnect.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.eduConnect.mapper.InformationAuthStoreMapper;
import com.yuedu.ydsf.eduConnect.service.InformationAuthStoreService;
import com.yuedu.ydsf.eduConnect.api.query.InformationAuthStoreQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationAuthStoreDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationAuthStoreVO;
import com.yuedu.ydsf.eduConnect.entity.InformationAuthStore;

import java.io.Serializable;
import java.util.Optional;
import java.util.List;


/**
* 资料授权服务层
*
* <AUTHOR>
* @date  2025/07/22
*/
@Service
public class InformationAuthStoreServiceImpl extends ServiceImpl<InformationAuthStoreMapper,InformationAuthStore>
    implements InformationAuthStoreService{


    /**
     * 资料授权分页查询
     *
     * @param page 分页对象
     * @param informationAuthStoreQuery 资料授权
     * @return IPage 分页结果
     */
    @Override
    public IPage<InformationAuthStoreVO> page(Page page,InformationAuthStoreQuery informationAuthStoreQuery) {
        return page(page, Wrappers.<InformationAuthStore>lambdaQuery())
                .convert(entity -> {
                    InformationAuthStoreVO informationAuthStoreVO = new InformationAuthStoreVO();
                    BeanUtils.copyProperties(entity, informationAuthStoreVO);
                    return informationAuthStoreVO;
                });
    }


    /**
     * 根据ID获得资料授权信息
     *
     * @param id id
     * @return InformationAuthStoreVO 详细信息
     */
    @Override
    public InformationAuthStoreVO getInfoById(Serializable id) {
        return Optional.of(getById(id))
                .map(entity -> {
                    InformationAuthStoreVO informationAuthStoreVO = new InformationAuthStoreVO();
                    BeanUtils.copyProperties(entity, informationAuthStoreVO);
                    return informationAuthStoreVO;
                })
                .orElseThrow(()-> new CheckedException("查询结果为空"));
    }


    /**
     * 新增资料授权
     *
     * @param informationAuthStoreDTO 资料授权
     * @return boolean 执行结果
     */
    @Override
    public boolean add(InformationAuthStoreDTO informationAuthStoreDTO) {
        InformationAuthStore informationAuthStore = new InformationAuthStore();
        BeanUtils.copyProperties(informationAuthStoreDTO, informationAuthStore);
        return save(informationAuthStore);
    }


    /**
     * 修改资料授权
     *
     * @param informationAuthStoreDTO 资料授权
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(InformationAuthStoreDTO informationAuthStoreDTO) {
        InformationAuthStore informationAuthStore = new InformationAuthStore();
        BeanUtils.copyProperties(informationAuthStoreDTO, informationAuthStore);
        return updateById(informationAuthStore);
    }


    /**
     * 导出excel 资料授权表格
     *
     * @param informationAuthStoreQuery 查询条件
     * @param ids 导出指定ID
     * @return List<InformationAuthStoreVO> 结果集合
     */
    @Override
    public List<InformationAuthStoreVO> export(InformationAuthStoreQuery informationAuthStoreQuery, Long[] ids) {
        return list(Wrappers.<InformationAuthStore>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), InformationAuthStore::getId, ids))
            .stream()
            .map(entity -> {
                InformationAuthStoreVO informationAuthStoreVO = new InformationAuthStoreVO();
                BeanUtils.copyProperties(entity, informationAuthStoreVO);
                return informationAuthStoreVO;
            }).toList();
    }

}
