package com.yuedu.ydsf.eduConnect.manager;

import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO.EditLessonDTO;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanDraftQuery;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import java.util.List;

/**
 * TeachingPlanDraftManager
 *
 * @date 2024/12/6 11:11
 * @project @Title: TeachingPlanDraftManager.java
 */
public interface TeachingPlanDraftManager {

  /**
   * 匹配选择的直播间计划与选择的课程的id到 教学计划草稿表
   *
   * <AUTHOR>
   * @date 2024/12/6 11:17
   * @param teachingPlanDraftQuery
   * @return void
   */
  void handleTeachingDetailDraft(TeachingPlanDraftQuery teachingPlanDraftQuery, boolean isEdit);

  /**
   * 远程调用获取课节信息
   *
   * <AUTHOR>
   * @date 2024/12/6 19:05
   * @param coursePublishQuery
   * @return java.util.List<com.yuedu.teaching.vo.LessonVO>
   */
  List<LessonVO> getPublishLessonList(CoursePublishQuery coursePublishQuery);

  /**
   * 构建并发送教学计划操作MQ消息
   *
   * <AUTHOR>
   * @date 2024/12/9 13:57
   * @param teachingPlanDraft
   * @param operateType
   * @param courseIdOld
   * @param lectureIdOld
   * @param lessonIdOldList
   * @return void
   */
  void buildAndSendOperateMqMessage(
      TeachingPlanDraft teachingPlanDraft,
      Integer operateType,
      Long courseIdOld,
      Long lectureIdOld,
      List<Long> lessonIdOldList,
      List<EditLessonDTO> editLessonDTOList);

  /**
   * 根据课程id获取最新的课程相关信息
   * <AUTHOR>
   * @date 2024/12/17 9:08
   * @param courseId
   * @return com.yuedu.teaching.vo.CourseVO
   */
    CourseVO getCourseLatest(Long courseId);
}
