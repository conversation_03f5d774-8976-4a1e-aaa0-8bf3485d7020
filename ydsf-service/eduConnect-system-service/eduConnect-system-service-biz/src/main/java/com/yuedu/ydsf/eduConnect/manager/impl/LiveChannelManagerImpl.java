package com.yuedu.ydsf.eduConnect.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.constant.PlanStatusEnum;
import com.yuedu.ydsf.eduConnect.entity.LiveChannel;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.manager.LiveChannelManager;
import com.yuedu.ydsf.eduConnect.mapper.LiveChannelMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDetailPubMapper;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateAgoraClassRoomDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RefreshScope
@AllArgsConstructor
public class LiveChannelManagerImpl implements LiveChannelManager {

    private final AgoraService agoraService;
    private final TeachingPlanDetailPubMapper teachingPlanDetailPubMapper;
    private final LiveChannelMapper liveChannelMapper;
    private final SsClassTimeMapper ssClassTimeMapper;

    @Value("${compatible.run:false}")
    private boolean startCompatible;

    @Async
    @Override
    public void createRoomByTeachingPlan(List<TeachingPlanDraft> teachingPlans) {
        log.debug("开始创建声网房间, teachingPlans size: {}", teachingPlans.size());

        try {
            for (TeachingPlanDraft plan : teachingPlans) {
                log.debug("处理教学计划 planId: {}", plan.getId());
                if (plan.getPlanStatus() != PlanStatusEnum.STATUS_ENUM_1.code) {
                    log.debug("教学计划状态不是已发布, planId: {}", plan.getId());
                    continue;
                }

                // 1. 查询教学计划明细
                List<TeachingPlanDetailPub> detailPubs =
                    teachingPlanDetailPubMapper.selectList(
                        Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                            .eq(TeachingPlanDetailPub::getPlanId, plan.getId())
                            .orderByAsc(TeachingPlanDetailPub::getLessonOrder));

                if (CollectionUtils.isEmpty(detailPubs)) {
                    log.warn("未找到教学计划明细数据, planId: {}", plan.getId());
                    continue;
                }

                // 2. 循环处理每个明细
                for (TeachingPlanDetailPub detail : detailPubs) {
                    LiveChannel exist = liveChannelMapper.selectOne(
                        Wrappers.lambdaQuery(LiveChannel.class)
                            .eq(LiveChannel::getTeachingPlanDetailId, detail.getId())
                            .last("limit 1"));
                    if (Objects.nonNull(exist)) {
                        log.debug("直播频道已存在, planId: {}, detailId: {}", plan.getId(),
                            detail.getId());
                        continue;
                    }
                    try {
                        // 3. 构建声网房间参数
                        CreateAgoraClassRoomDTO roomDTO = new CreateAgoraClassRoomDTO();
                        roomDTO.setAttendClassDate(detail.getClassDate());
                        roomDTO.setAttendClassStartTime(detail.getClassStartTime());
                        roomDTO.setAttendClassEndTime(detail.getClassEndTime());
                        roomDTO.setBooksName(detail.getCourseName());
                        // 4. 调用声网服务创建房间
                        String roomUuid = agoraService.syncAgoraClassRoom(roomDTO);
                        if (StringUtils.isBlank(roomUuid)) {
                            if (detail.getClassDate().isEqual(LocalDate.now())){
                                log.error("创建声网房间失败, planId: {}, detailId: {},上课时间:{}", plan.getId(),
                                    detail.getId(), detail.getClassStartTime().toString());
                            }
                            continue;
                        }

                        // 5. 保存直播频道信息
                        LiveChannel liveChannel = new LiveChannel();
                        liveChannel.setChannelId(roomUuid);
                        liveChannel.setTeachingPlanDetailId(detail.getId());
                        boolean success = liveChannelMapper.insert(liveChannel) > 0;
                        if (success) {
                            log.debug(
                                "保存直播频道成功, planId: {}, detailId: {}, channelId: {}",
                                plan.getId(),
                                detail.getId(),
                                roomUuid);

                            if(startCompatible){
                                SsClassTime ssClassTime = new SsClassTime();
                                ssClassTime.setRoomUuid(roomUuid);
                                ssClassTimeMapper.update(ssClassTime,Wrappers.<SsClassTime>lambdaUpdate()
                                    .eq(SsClassTime::getTeachingPlanDetailsId, liveChannel.getTeachingPlanDetailId()));
                            }


                        } else {
                            log.error("保存直播频道失败, planId: {}, detailId: {}", plan.getId(),
                                detail.getId());
                        }
                    } catch (Exception e) {
                        e.getStackTrace();
                        log.error("处理教学计划明细异常, planId: {}, detailId: {}", plan.getId(),
                            detail.getId(), e);
                    }
                }
            }
            log.debug("创建声网房间完成");

        } catch (Exception e) {
            e.getStackTrace();
            log.error("创建声网房间异常", e);
            throw new BizException("创建声网房间失败: " + e.getMessage());
        }
    }

}
