package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 直播间计划草稿表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-29 08:28:00
 */
@Data
@TableName("ea_live_room_plan_draft")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "直播间计划草稿表实体类")
public class LiveRoomPlanDraft extends Model<LiveRoomPlanDraft> {


    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键id")
    private Long id;

    /**
     * 计划名称
     */
    @Schema(description = "计划名称")
    private String planName;

    /**
     * 阶段
     */
    @Schema(description = "阶段")
    private Integer stage;

    /**
     * 草稿直播间
     */
    @Schema(description = "草稿直播间")
    private Long liveRoomId;

    /**
     * 直播间计划状态:0-未发布;1-已发布;
     */
    @Schema(description = "直播间计划状态:0-未发布;1-已发布;")
    private Integer planStatus;

    /**
     * 发布人姓名
     */
    @Schema(description = "发布人姓名")
    private String publisherName;

    /**
     * 草稿创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "草稿创建人")
    private String createBy;

    /**
     * 草稿创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "草稿创建时间")
    private LocalDateTime createTime;

    /**
     * 草稿修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "草稿修改人")
    private String updateBy;

    /**
     * 草稿修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "草稿修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除:0-否;1-是
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除:0-否;1-是")
    private Integer delFlag;
}
