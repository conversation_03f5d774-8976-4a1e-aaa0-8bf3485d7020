package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 直播间计划明细 实体类
 *
 * <AUTHOR>
 * @date 2024-12-17 14:23:13
 */
@Data
@TableName("ea_live_room_plan_detail_version")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "直播间计划明细实体类")
public class LiveRoomPlanDetailVersion extends Model<LiveRoomPlanDetailVersion> {


	/**
	* 主键id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键id")
    private Long id;

	/**
	* 直播间计划id
	*/
    @Schema(description="直播间计划id")
    private Long planId;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 上课开始日期（yyyy-MM-dd）
	*/
    @Schema(description="上课开始日期（yyyy-MM-dd）")
    private LocalDate classDate;


    /**
     * 上课开始日期（yyyy-MM-dd）
     */
	@TableField(exist = false)
    @Schema(description = "最早上课开始日期（yyyy-MM-dd）")
    private LocalDate startClassDate;

    /**
     * 上课开始日期（yyyy-MM-dd）
     */
	@TableField(exist = false)
    @Schema(description = "最晚上课开始日期（yyyy-MM-dd）")
    private LocalDate endClassDate;


	/**
	* 上课开始时间（HH:mm:ss）
	*/
    @Schema(description="上课开始时间（HH:mm:ss）")
    private LocalTime classStartTime;

	/**
	* 上课结束时间（HH:mm:ss）
	*/
    @Schema(description="上课结束时间（HH:mm:ss）")
    private LocalTime classEndTime;

	/**
	* 上课开始日期时间（yyyy-MM-dd HH:mm:ss)
	*/
    @Schema(description="上课开始日期时间（yyyy-MM-dd HH:mm:ss)")
    private LocalDateTime classStartDateTime;

	/**
	* 上课结束日期时间（yyyy-MM-dd HH:mm:ss)
	*/
    @Schema(description="上课结束日期时间（yyyy-MM-dd HH:mm:ss)")
    private LocalDateTime classEndDateTime;

	/**
	* 明细类型:0-单节课;1-常规课;2-精品课
	*/
    @Schema(description="明细类型:0-单节课;1-常规课;2-精品课")
    private Integer detailType;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 版本号
	*/
    @Schema(description="版本号")
    private Integer version;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
