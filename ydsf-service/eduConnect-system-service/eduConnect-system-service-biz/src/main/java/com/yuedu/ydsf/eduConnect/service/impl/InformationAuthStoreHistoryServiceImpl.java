package com.yuedu.ydsf.eduConnect.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.eduConnect.mapper.InformationAuthStoreHistoryMapper;
import com.yuedu.ydsf.eduConnect.service.InformationAuthStoreHistoryService;
import com.yuedu.ydsf.eduConnect.api.query.InformationAuthStoreHistoryQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationAuthStoreHistoryDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationAuthStoreHistoryVO;
import com.yuedu.ydsf.eduConnect.entity.InformationAuthStoreHistory;

import java.io.Serializable;
import java.util.Optional;
import java.util.List;


/**
* 资料授权历史服务层
*
* <AUTHOR>
* @date  2025/07/22
*/
@Service
public class InformationAuthStoreHistoryServiceImpl extends ServiceImpl<InformationAuthStoreHistoryMapper,InformationAuthStoreHistory>
    implements InformationAuthStoreHistoryService{


    /**
     * 资料授权历史分页查询
     *
     * @param page 分页对象
     * @param informationAuthStoreHistoryQuery 资料授权历史
     * @return IPage 分页结果
     */
    @Override
    public IPage<InformationAuthStoreHistoryVO> page(Page page,InformationAuthStoreHistoryQuery informationAuthStoreHistoryQuery) {
        return page(page, Wrappers.<InformationAuthStoreHistory>lambdaQuery())
                .convert(entity -> {
                    InformationAuthStoreHistoryVO informationAuthStoreHistoryVO = new InformationAuthStoreHistoryVO();
                    BeanUtils.copyProperties(entity, informationAuthStoreHistoryVO);
                    return informationAuthStoreHistoryVO;
                });
    }


    /**
     * 根据ID获得资料授权历史信息
     *
     * @param id id
     * @return InformationAuthStoreHistoryVO 详细信息
     */
    @Override
    public InformationAuthStoreHistoryVO getInfoById(Serializable id) {
        return Optional.of(getById(id))
                .map(entity -> {
                    InformationAuthStoreHistoryVO informationAuthStoreHistoryVO = new InformationAuthStoreHistoryVO();
                    BeanUtils.copyProperties(entity, informationAuthStoreHistoryVO);
                    return informationAuthStoreHistoryVO;
                })
                .orElseThrow(()-> new CheckedException("查询结果为空"));
    }


    /**
     * 新增资料授权历史
     *
     * @param informationAuthStoreHistoryDTO 资料授权历史
     * @return boolean 执行结果
     */
    @Override
    public boolean add(InformationAuthStoreHistoryDTO informationAuthStoreHistoryDTO) {
        InformationAuthStoreHistory informationAuthStoreHistory = new InformationAuthStoreHistory();
        BeanUtils.copyProperties(informationAuthStoreHistoryDTO, informationAuthStoreHistory);
        return save(informationAuthStoreHistory);
    }


    /**
     * 修改资料授权历史
     *
     * @param informationAuthStoreHistoryDTO 资料授权历史
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(InformationAuthStoreHistoryDTO informationAuthStoreHistoryDTO) {
        InformationAuthStoreHistory informationAuthStoreHistory = new InformationAuthStoreHistory();
        BeanUtils.copyProperties(informationAuthStoreHistoryDTO, informationAuthStoreHistory);
        return updateById(informationAuthStoreHistory);
    }


    /**
     * 导出excel 资料授权历史表格
     *
     * @param informationAuthStoreHistoryQuery 查询条件
     * @param ids 导出指定ID
     * @return List<InformationAuthStoreHistoryVO> 结果集合
     */
    @Override
    public List<InformationAuthStoreHistoryVO> export(InformationAuthStoreHistoryQuery informationAuthStoreHistoryQuery, Long[] ids) {
        return list(Wrappers.<InformationAuthStoreHistory>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), InformationAuthStoreHistory::getId, ids))
            .stream()
            .map(entity -> {
                InformationAuthStoreHistoryVO informationAuthStoreHistoryVO = new InformationAuthStoreHistoryVO();
                BeanUtils.copyProperties(entity, informationAuthStoreHistoryVO);
                return informationAuthStoreHistoryVO;
            }).toList();
    }

}
