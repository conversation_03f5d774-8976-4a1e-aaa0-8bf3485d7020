package com.yuedu.ydsf.eduConnect.convert;

import com.yuedu.ydsf.eduConnect.api.vo.ClassCourseScheduleVO;
import com.yuedu.ydsf.eduConnect.api.vo.CreateCourseScheduleVO;
import com.yuedu.ydsf.eduConnect.api.vo.RecordVideoTaskVO;
import com.yuedu.ydsf.eduConnect.entity.RecordVideoTask;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.SsCourseSchedule;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateAgoraClassRoomDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CreateClassSessionConvert {
    CreateClassSessionConvert INSTANCE = Mappers.getMapper(CreateClassSessionConvert.class);

    SsCourseSchedule toEntity(CreateCourseScheduleVO createClassSessionVo);

    CreateAgoraClassRoomDTO toAgoraEntity(SsClassTime ssClassTime);

    SsClassTime toEntity(ClassCourseScheduleVO classCourseScheduleVo);

    RecordVideoTaskVO toEntity(RecordVideoTask recordVideoTask);
}