package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 课次信息表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:12:52
 */
@Data
@TableName("ss_class_time")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "课次信息表实体类")
public class SsClassTime extends Model<SsClassTime> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 声网UUID
	*/
    @Schema(description="声网UUID")
    private String roomUuid;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 排课ID
	*/
    @Schema(description="排课ID")
    private Long courseScheduleId;

	/**
	* 排课书籍ID
	*/
    @Schema(description="排课书籍ID")
    private Long courseScheduleBooksId;

	/**
	* 排课规则ID
	*/
    @Schema(description="排课规则ID")
    private Long courseScheduleRuleId;

	/**
	* 上课日期（yyyy-MM-dd）
	*/
    @Schema(description="上课日期（yyyy-MM-dd）")
    private LocalDate attendClassDate;

	/**
	* 上课开始时间（HH:mm）
	*/
    @Schema(description="上课开始时间（HH:mm）")
    private LocalTime attendClassStartTime;

	/**
	* 上课结束时间（HH:mm）
	*/
    @Schema(description="上课结束时间（HH:mm）")
    private LocalTime attendClassEndTime;

	/**
	* 是否已同步声网创建课堂: 0-否; 1-是;
	*/
    @Schema(description="是否已同步声网创建课堂: 0-否; 1-是;")
    private Integer isSyncAgora;

	/**
	* 上课类型: 0-直播课; 1-点播课;
	*/
    @Schema(description="上课类型: 0-直播课; 1-点播课;")
    private Integer attendClassType;

	/**
	* 监课链接url路径
	*/
    @Schema(description="监课链接url路径")
    private String supervisionClassUrl;

	/**
	* 监课开始时间(yyyy-MM-dd HH:mm:ss）
	*/
    @Schema(description="监课开始时间(yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime supervisionClassStartTime;

	/**
	* 监课结束时间(yyyy-MM-dd HH:mm:ss）
	*/
    @Schema(description="监课结束时间(yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime supervisionClassEndTime;

	/**
	* 主讲老师ID(ss_lecturer主键ID)
	*/
    @Schema(description="主讲老师ID(ss_lecturer主键ID)")
    private Long lecturerId;

	/**
	* 主讲设备ID
	*/
    @Schema(description="主讲设备ID")
    private Long deviceId;

	/**
	* 主讲教室ID
	*/
    @Schema(description="主讲教室ID")
    private Long classRoomId;

	/**
	* 书籍ID
	*/
    @Schema(description="书籍ID")
    private String booksId;

	/**
	* 书籍名称
	*/
    @Schema(description="书籍名称")
    private String booksName;

	/**
	* 课程库ID(录播课资源ID)
	*/
    @Schema(description="课程库ID(录播课资源ID)")
    private Long recordingId;

	/**
	* 主讲端上课码(上课端标识1 + 5位随机数  例:115329)
	*/
    @Schema(description="主讲端上课码(上课端标识1 + 5位随机数  例:115329)")
    private String lecturerRoomCode;

	/**
	* 教室端上课码(教室端标识2 + 5位随机数  例:235329)
	*/
    @Schema(description="教室端上课码(教室端标识2 + 5位随机数  例:235329)")
    private String classRoomCode;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑者")
    private String modifer;

	/**
	* 删除标记
	*/
    @Schema(description="删除标记")
    private Integer delFlag;

    /**
     * 课次状态: 0-进行中； 1-未开始； 2-已结束
     */
    @TableField(exist = false)
    private Integer attendClassState;

    /**
     * 课次开始时间
     */
    private LocalDateTime attendTimeStartTime;

    /**
     * 课次结束时间
     */
    private LocalDateTime attendTimeEndTime;

    /**
     * 教学计划明细ID
     */
    private Long teachingPlanDetailsId;
}
