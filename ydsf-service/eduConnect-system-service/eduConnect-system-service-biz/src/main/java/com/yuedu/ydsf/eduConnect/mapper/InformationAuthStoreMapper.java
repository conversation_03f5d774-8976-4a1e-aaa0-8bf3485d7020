package com.yuedu.ydsf.eduConnect.mapper;

import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.vo.InformationAuthStoreVO;
import org.apache.ibatis.annotations.Mapper;
import com.yuedu.ydsf.eduConnect.entity.InformationAuthStore;
import java.util.List;
import java.util.Set;

/**
* 资料授权持久层接口
*
* <AUTHOR>
* @date  2025/07/22
*/
@Mapper
public interface InformationAuthStoreMapper extends YdsfBaseMapper<InformationAuthStore> {


    /**
     *  获得
     *
     * <AUTHOR>
     * @date 2025年07月22日 09时17分
     */
    List<InformationAuthStoreVO> getAuthStoreTotalByInformationIds(List<Long> informationIds);


    /**
     *  更加id删除
     *
     * <AUTHOR>
     * @date 2025年07月22日 13时57分
     */
    int batchDeleteByInformationIds(Long id ,List<Long> ids);


    /**
     *  批量保存
     *
     * <AUTHOR>
     * @date 2025年07月22日 13时57分
     */
    int batchSave(List<InformationAuthStore> informationAuthStores);

    /**
     *  删除授权
     *
     * <AUTHOR>
     * @date 2025年07月23日 15时06分
     */
    int deleteByInformationId(Long id);
}




