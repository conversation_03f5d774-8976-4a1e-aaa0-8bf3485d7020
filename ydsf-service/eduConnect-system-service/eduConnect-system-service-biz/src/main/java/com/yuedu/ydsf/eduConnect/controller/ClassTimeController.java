package com.yuedu.ydsf.eduConnect.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.houbb.heaven.util.lang.BeanUtil;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.eduConnect.api.vo.ClassTimeVO;
import com.yuedu.ydsf.eduConnect.entity.ClassTime;
import com.yuedu.ydsf.eduConnect.service.ClassTimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 上课时段 控制类
 *
 * <AUTHOR>
 * @date 2024-11-28 16:44:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/ClassTime")
@Tag(description = "ea_class_time", name = "上课时段管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ClassTimeController {

    private final ClassTimeService classTimeService;

    /**
     * 分页查询
     *
     * @return R<List < ClassTimeVO>>
     */
    @Operation(summary = "上课时段列表查询", description = "上课时段列表查询")
    @GetMapping("/list")
    @HasPermission("edusystem_ClassTime_view")
    public R<List<ClassTimeVO>> getClassTimeList() {
        return R.ok(classTimeService.getClassTimeList());
    }

    /**
     * 内部调用：获取所有上课时段
     *
     * @return List<ClassTime>
     */
    @Operation(summary = "小程序:获取所有上课时段", description = "小程序:获取所有上课时段")
    @GetMapping("/info")
    @StorePermission
    public R<List<ClassTime>> classTimeInfo() {
        return R.ok(classTimeService.list());
    }

    /**
     * 查询上课时段
     *
     * @param
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List <
        * com.yuedu.ydsf.eduConnect.entity.ClassTime>>
     * <AUTHOR>
     * @date 2024/12/12 16:33
     */
    @GetMapping("/getClassTimeVoList")
    @Inner
    public R<List<ClassTime>> getClassTimeVoList() {
        return R.ok(classTimeService.list());
    }

    @GetMapping("/getClassTimeById/{classTimeId}")
    @Inner
    public R<ClassTimeVO> getClassTimeById(@PathVariable Long classTimeId) {
        ClassTimeVO classTimeVO = new ClassTimeVO();
        ClassTime classTime = classTimeService.getOne(
            Wrappers.lambdaQuery(ClassTime.class).eq(ClassTime::getId, classTimeId));
        BeanUtil.copyProperties(classTime, classTimeVO);
        return R.ok(classTimeVO);
    }
}
