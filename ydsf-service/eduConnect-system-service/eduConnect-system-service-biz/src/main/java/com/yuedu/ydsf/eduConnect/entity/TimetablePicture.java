package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上课拍照记录
 * 
 * <AUTHOR>
 * @date 2025/02/13
 */
@TableName("b_timetable_picture")
@Data
@EqualsAndHashCode(callSuper = true)
public class TimetablePicture extends Model<TimetablePicture> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 上课课次ID
     */
    private Long timeableId;

    /**
     * 拍照设备ID
     */
    private Long deviceId;

    /**
     * 拍照照片url
     */
    private String photoUrl;

    /**
     * 识别总数量
     */
    private Integer recognitionTotalNum;

    /**
     * 识别幼儿数量
     */
    private Integer recognitionChildrenNum;

    /**
     * 识别青少年数量
     */
    private Integer recognitionTeenagersNum;

    /**
     * 识别青年数量
     */
    private Integer recognitionYouthNum;

    /**
     * 识别中年数量
     */
    private Integer recognitionMiddleNum;

    /**
     * 识别老年数量
     */
    private Integer recognitionElderlyNum;

    /**
     * 识别状态: 0-未处理; 1-处理成功; 2-处理失败
     */
    private Integer recognitionStatus;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 人体数量
     */
    private Integer recognitionHumanNum;


    /**
     * 计数第几张
     */
    private Integer sort;

    /**
     * 距离上课时长,单位：秒
     */
    private Integer duration;

    /**
     * 上课课号
     */
    private Long lessionNo;


    /**
     * 备注
     */
    private String remark;
}