package com.yuedu.ydsf.eduConnect.util;

/**
 * <AUTHOR>
 * @date 2024/10/18
 */
public class StringUtils {
  /**
   * 将逗号分隔字符串去除指定字符
   *
   * <AUTHOR>
   * @date 2024年03月27日 17时39分
   * @param originalString 原始逗号分割的字符串
   * @param elementToRemove 要去除的元素
   * @return String
   */
  public static String removeStr(String originalString, String elementToRemove) {

    // 使用逗号分割字符串
    String[] elements = originalString.split(",");

    // 创建一个StringBuilder来构建新的字符串
    StringBuilder newString = new StringBuilder();
    boolean first = true; // 用于处理第一个元素前的逗号

    // 遍历分割后的元素数组
    for (String element : elements) {
      // 去除元素前后的空格（如果需要）
      element = element.trim();

      // 检查元素是否等于要去除的元素
      if (!element.equals(elementToRemove)) {
        // 如果不是第一个元素，则添加逗号
        if (!first) {
          newString.append(",");
        }
        // 添加元素到新的字符串中
        newString.append(element);
        first = false; // 设置标志为false，表示已经不是第一个元素了
      }
    }

    // 输出新的字符串
    String modifiedString = newString.toString();

    return modifiedString;
  }
}
