package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店已约点播课 实体类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:24:34
 */
@Data
@TableName("b_course_vod")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店已约点播课实体类")
public class BCourseVod extends Model<BCourseVod> {

  /** 主键ID */
  @TableId(type = IdType.AUTO)
  @Schema(description = "主键ID")
  private Long id;

  /** 门店ID */
  @Schema(description = "门店ID")
  private Long storeId;

  /** 教学计划ID */
  @Schema(description = "教学计划ID")
  private Long teachingPlanId;

  /** 排课名称 */
  @Schema(description = "排课名称")
  private String name;

  /** 课程ID */
  @Schema(description = "课程ID")
  private Long courseId;

  /** 阶段 */
  @Schema(description = "阶段")
  private Integer stage;

  /** 主讲老师ID */
  @Schema(description = "主讲老师ID")
  private Long lectureId;

  /** 班级ID */
  @Schema(description = "班级ID")
  private Long classId;

  /** 教室ID */
  @Schema(description = "教室ID")
  private Long classroomId;

  /** 指导老师ID */
  @Schema(description = "指导老师ID")
  private Long teacherId;

  /** 是否已排课: 0-未排课; 1-已排课; */
  @Schema(description = "是否已排课: 0-未排课; 1-已排课;")
  private Integer scheduled;

  /** 创建人 */
  @TableField(fill = FieldFill.INSERT)
  @Schema(description = "创建人")
  private String createBy;

  /** 创建时间 */
  @TableField(fill = FieldFill.INSERT)
  @Schema(description = "创建时间")
  private LocalDateTime createTime;

  /** 修改人 */
  @TableField(fill = FieldFill.INSERT_UPDATE)
  @Schema(description = "修改人")
  private String updateBy;

  /** 修改时间 */
  @TableField(fill = FieldFill.INSERT_UPDATE)
  @Schema(description = "修改时间")
  private LocalDateTime updateTime;

  /** 是否删除: 0-否; 1-是; */
  @TableLogic
  @TableField(fill = FieldFill.INSERT)
  @Schema(description = "是否删除: 0-否; 1-是;")
  private Integer delFlag;
}
