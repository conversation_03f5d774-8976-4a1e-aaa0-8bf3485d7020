package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsInRoomVo;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 课次信息表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:12:52
 */
public interface SsClassTimeService extends IService<SsClassTime> {

    /**
     * 根据直播间教室ID和时间范围查找SsClassTimeEntity列表。
     *
     * @param classRoomId 直播间教室ID
     * @param attendClassDate 上课日期
     * @param attendClassStartTime 上课开始时间
     * @param attendClassEndTime 上课结束时间
     * @return 是否有符合给定条件的SsClassTimeEntity
     */
    Boolean countByClassIdAndTimeRange(Long classRoomId, LocalDate attendClassDate, LocalTime attendClassStartTime, LocalTime attendClassEndTime);

    /**
     * 监课通过房间id进入监课端
     *
     * @param classTimeQuery
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsInRoomVo
     * <AUTHOR>
     * @date 2024/10/14 10:09
     */
    SsInRoomVo inRoomByRoomUuid(SsClassTimeQuery classTimeQuery);
    /**
     * 查询班级课程安排
     * @param ssClassTimeQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2024/10/11 10:51
     */
    IPage<SsClassTimeVO> getPageClassTimeByClassId(Page page, SsClassTimeQuery ssClassTimeQuery);


    /**
     * 获取主讲端/教室端上课码
     *
     * @param ssClassTimeDto
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/15 9:04
     */
    String genRoomTimeCode(SsClassTimeDTO ssClassTimeDto);
    /**
     *  分页查询
     *
     * <AUTHOR>
     * @date 2024年10月15日 09时19分
     * @param ssClassTimeQuery
     */
    IPage<SsClassTimeVO> page(Page page, SsClassTimeQuery ssClassTimeQuery);


    /**
     *  查询授权校区
     *
     * <AUTHOR>
     * @date 2024年10月16日 10时50分
     * @param id
     */
    List<SsDeviceVO> selectAuthClassRoom(Long id);

    /**
     * 新增课次
     *
     * @param ssClassTimeDto
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 11:21
     */
    void addClassTime(SsClassTimeDTO ssClassTimeDto);

    /**
     * 编辑课次
     *
     * @param ssClassTimeDto
     * @return void
     * <AUTHOR>
     * @date 2024/10/17 10:49
     */
    void updateClassTime(SsClassTimeDTO ssClassTimeDto);

    /**
     * 删除课次
     * <AUTHOR>
     * @date 2024/10/18 13:53
     * @param ids
     * @return void
     */
    void deleteClassTime(Long[] ids);


    /**
     *  授权校区
     *
     * <AUTHOR>
     * @date 2024年10月16日 16时02分
     * @param ssClassTimeDTO
     */
    Boolean updateAuthClassTimeRoom(SsClassTimeDTO ssClassTimeDTO);

    /**
     * 查看讲师空闲时间
     * @param page
     * @param ssClassTimeQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2024/10/18 14:04
     */
    IPage selectLecturerFreeTime(Page page, SsClassTimeQuery ssClassTimeQuery);



    /**
     * 获取监课链接
     * <AUTHOR>
     * @date 2024/10/21 13:42
     * @return java.lang.String
     */
    SsClassTimeVO getSupervisionUrl(SsClassTimeQuery classTimeQuery);

    /**
     * 通过ID 获取课次详情
     * <AUTHOR>
     * @date 2024/10/21 14:05
     * @param id
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO
     */
    SsClassTimeVO getClassTimeInfo(Long id);


    /**
     * 获取课次对应的上课码
     *
     * @param id
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO
     * <AUTHOR>
     * @date 2024/10/23 16:24
     */
    SsClassTimeVO getRoomTimeCode(Long id);

  /**
   * 获取教室日历
   *
   * <AUTHOR>
   * @date 2024/10/24 14:32
   * @param classTimeQuery
   * @return com.baomidou.mybatisplus.core.metadata.IPage
   */
  IPage classRoomFreeTimeCalendar(SsClassTimeQuery classTimeQuery);

    /**
     * 根据排课ID获取课次信息
     * @param courseScheduleId 排课ID
     * @return 课次信息
     */
  List<SsClassTime> listByCourseScheduleId(Long courseScheduleId);

  /**
   * 自动创建SP1声网房间
   *
   * <AUTHOR>
   * @date 2024/10/25 11:04
   * @return void
   */
  void autoCreateSP1Channel();
}
