package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.dto.XiaogjBusinessIdDTO;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeStudent;

/**
 * 校区上课学生表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:32:06
 */
public interface SsClassTimeStudentService extends IService<SsClassTimeStudent> {

    /**
     * 校管家排课学生信息同步双师系统
     * @param xiaogjBusinessIdDTO
     * @return void
     * <AUTHOR>
     * @date 2025/2/12 14:02
     */
    void syncXiaogjStudent(XiaogjBusinessIdDTO xiaogjBusinessIdDTO);

    /**
     * 教室id下是否有学生
     * @param classRoomId
     * @return
     */
    Boolean existStudentByClassRoomId(Long classRoomId);
}
