package com.yuedu.ydsf.eduConnect.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.common.file.domain.OssStsResult;
import com.yuedu.ydsf.eduConnect.api.dto.InformationDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationVO;
import com.yuedu.ydsf.eduConnect.entity.Information;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/07/22
 **/
public interface InformationManager {

    /**
     *  填充数据
     *
     * <AUTHOR>
     * @date 2025年07月22日 09时12分
     */
    IPage<InformationVO> fillData(IPage<InformationVO> page);

    /**
     *  填充授权数据
     *
     * <AUTHOR>
     * @date 2025年07月22日 10时43分
     */
    void fillAuthData(InformationVO informationVO);

    /**
     *  填充资源数据
     *
     * <AUTHOR>
     * @date 2025年07月22日 10时43分
     */
    void fillResourceData(InformationVO informationVO);

    /**
     *  检查是否有授权
     *
     * <AUTHOR>
     * @date 2025年07月22日 10时43分
     */
    boolean checkAuht(Information oldInformation);

    /**
     *  检查是否有资源
     *
     * <AUTHOR>
     * @date 2025年07月22日 10时43分
     */
    boolean checkResource(Information oldInformation);


    /**
     *  获得资料临时token
     *
     * <AUTHOR>
     * @date 2025年07月22日 11时36分
     */
    OssStsResult getStsToken(Information oldInformation);


    /**
     *  授权校区
     *
     * <AUTHOR>
     * @date 2025年07月22日 13时48分
     */
    void authStore(InformationDTO informationDTO);

    /**
     *  删除授权
     *
     * <AUTHOR>
     * @date 2025年07月23日 15时05分
     */
    void deleteAuth(Information oldInformation);
}
