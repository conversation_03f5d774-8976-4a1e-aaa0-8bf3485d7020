package com.yuedu.ydsf.eduConnect.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dtflys.forest.utils.StringUtils;
import com.yuedu.ydsf.eduConnect.api.query.SsXiaogjLogQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsXiaogjLogVO;
import com.yuedu.ydsf.eduConnect.config.MessageUtils;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjLog;
import com.yuedu.ydsf.eduConnect.mapper.SsXiaogjLogMapper;
import com.yuedu.ydsf.eduConnect.service.SsXiaogjLogService;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.MqTypeEnums;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.SsPushXiaogjType;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.MqReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.SsPushXiaogjEventReq;
import com.yuedu.ydsf.eduConnect.util.StringUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 校管家日志表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-28 15:57:51
 */
@Slf4j
@Service
@AllArgsConstructor
public class SsXiaogjLogServiceImpl extends ServiceImpl<SsXiaogjLogMapper, SsXiaogjLog> implements SsXiaogjLogService {

    private final SsXiaogjLogMapper ssXiaogjLogMapper;

    /**
     * 同步校管家班级排课异常数据再次推送
     * @return void
     * <AUTHOR>
     * @date 2025/2/24 14:42
     */
    @Override
    public void syncFailXiaogjClassCourseSchedule(String param) {

        // string字符串转换map (例: requestId=1&responseCode=400)
        Map<String, String> stringStringMap = StringUtil.stringParseMap(param);

        List<SsXiaogjLogVO> xiaogjLogList = new ArrayList<>();

        // 分页查询推送失败消息, param参数为空时, 默认推送responseCode=400或者responseCode=null数据
        SsXiaogjLogQuery ssXiaogjLogQuery = new SsXiaogjLogQuery();
        ssXiaogjLogQuery.setRequestId(stringStringMap.get("requestId"));
        ssXiaogjLogQuery.setEventKey(SsPushXiaogjType.CLASS_COURSE.eventKey);
        ssXiaogjLogQuery.setResponseCode(StringUtils.isNotBlank(stringStringMap.get("responseCode")) ? Integer.parseInt(stringStringMap.get("responseCode")) : null);

        // 定义每页大小
        int pageSize = 1000;
        int pageNum = 1;

        while (true) {
            // 设置分页参数
            Page page = new Page(pageNum, pageSize);

            // 查询当前页的数据
            log.info("同步校管家班级排课异常数据查询请求参数为:{}", JSONObject.toJSONString(ssXiaogjLogQuery));
            IPage<SsXiaogjLogVO> currentPageList = ssXiaogjLogMapper.xiaogjListPage(page, ssXiaogjLogQuery);

            // 如果当前页没有数据，退出循环
            if (currentPageList.getRecords().isEmpty()) {
                break;
            }

            // 将当前页的数据添加到结果列表中
            xiaogjLogList.addAll(currentPageList.getRecords());

            // 增加页码
            pageNum++;
        }

        // 推送异常消息
        for (SsXiaogjLogVO ssXiaogjLogVO : xiaogjLogList) {

            SsPushXiaogjEventReq ssPushXiaogjEventReq = JSONObject.parseObject(ssXiaogjLogVO.getRequestParam(), SsPushXiaogjEventReq.class);

            MqReq<SsPushXiaogjEventReq<ClassCourseReq>> mqReq = new MqReq<>(
                MqTypeEnums.SS_XIAOGJ_PUSH_MSG.TYPE,
                MqTypeEnums.SS_XIAOGJ_PUSH_MSG.TOPTIC, ssPushXiaogjEventReq);

            MessageUtils.sendMessage(mqReq.getType(), mqReq.getTopic(), mqReq.getData());

        }

    }


}
