package com.yuedu.ydsf.eduConnect.constant;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/04/28
 **/
@AllArgsConstructor
public enum TimetableCourseTypeEnum {
    /**
     * 直播课
     */
    TIMETABLE_COURSE_TYPE_1(1, "直播课"),
    /**
     * 点播课
     */
    TIMETABLE_COURSE_TYPE_2(2, "点播课"),

    /**
     * 补课
     */
    TIMETABLE_COURSE_TYPE_3(3, "补课"),
    /**
     * 线上补课
     */
    TIMETABLE_COURSE_TYPE_4(4, "线上补课");

    public final Integer code;

    public final String desc;
}
