package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 已发布的教学计划表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-29 09:26:30
 */
@Data
@TableName("ea_teaching_plan_pub")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "已发布的教学计划表实体类")
public class TeachingPlanPub extends Model<TeachingPlanPub> {


	/**
	* 主键id，teaching_plan_draft.id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键id，teaching_plan_draft.id")
    private Long id;

    /**
     * 教学计划id
     */
    @Schema(description="教学计划id")
    private Long teachingPlanId;

	/**
	* 直播间计划id
	*/
    @Schema(description="直播间计划id")
    private Long liveRoomPlanId;

    /**
     * 阶段
     */
    @Schema(description="阶段")
    private Integer stage;

	/**
	* 课程包id
	*/
    @Schema(description="课程包id")
    private Long courseId;

	/**
	* 课程包名字
	*/
    @Schema(description="课程包名字")
    private String courseName;

	/**
	* 主讲id
	*/
    @Schema(description="主讲id")
    private Long lectureId;

	/**
	* 主讲名字
	*/
    @Schema(description="主讲名字")
    private String lectureName;

	/**
	* 是否关闭
	*/
    @Schema(description="是否关闭")
    private Integer closed;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
