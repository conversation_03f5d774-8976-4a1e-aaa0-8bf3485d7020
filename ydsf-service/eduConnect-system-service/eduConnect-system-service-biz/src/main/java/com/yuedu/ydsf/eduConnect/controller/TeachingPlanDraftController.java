package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanDraftQuery;
import com.yuedu.ydsf.eduConnect.api.valid.TeachingPlanDraftValidGroup.EditPlan;
import com.yuedu.ydsf.eduConnect.api.valid.TeachingPlanDraftValidGroup.SavePlan;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDraftService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import jakarta.validation.constraints.NotEmpty;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教学计划草稿表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-29 09:24:03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/TeachingPlanDraft" )
@Tag(description = "ea_teaching_plan_draft" , name = "教学计划草稿表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TeachingPlanDraftController {

    private final  TeachingPlanDraftService teachingPlanDraftService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param lectureId 主讲老师
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("admin_TeachingPlanDraft_view")
    public R getTeachingPlanDraftPage(@ParameterObject Page page, @ParameterObject TeachingPlanDraftQuery teachingPlanDraftQuery) {
   // public R getTeachingPlanDraftPage(@ParameterObject Page page, @RequestParam(name = "lectureId", required = false) Long lectureId) {
        return R.ok(teachingPlanDraftService.getPage(page, teachingPlanDraftQuery));
    }

    /**
     * 通过条件查询教学计划草稿表
     * @param id 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("admin_TeachingPlanDraft_detail")
    public R getDetails(@RequestParam(name = "id") Integer id){
        return R.ok(teachingPlanDraftService.getDetails(id));
    }

  /**
   * 新增教学计划草稿表
   *
   * @param teachingPlanDraftQuery 教学计划草稿表
   * @return R
   */
  @Operation(summary = "新增教学计划草稿表", description = "新增教学计划草稿表")
  @PostMapping("/add")
  @HasPermission("edusystem_TeachingPlanDraft_add")
  @Idempotent(expireTime = 5, info = "点击太快，休息一下吧！")
  public R save(
      @Validated(SavePlan.class) @RequestBody TeachingPlanDraftQuery teachingPlanDraftQuery) {
    teachingPlanDraftService.savePlan(teachingPlanDraftQuery);
    return R.ok();
  }

  /**
   * 修改教学计划草稿表
   *
   * @param teachingPlanDraftQuery 教学计划草稿表
   * @return R
   */
  @Operation(summary = "修改教学计划草稿表", description = "修改教学计划草稿表")
  @PutMapping("/edit")
  @HasPermission("edusystem_TeachingPlanDraft_edit")
  @Idempotent(expireTime = 5, info = "点击太快，休息一下吧！")
  public R updateById(
      @Validated(EditPlan.class) @RequestBody TeachingPlanDraftQuery teachingPlanDraftQuery) {
    teachingPlanDraftService.editPlan(teachingPlanDraftQuery);
    return R.ok();
  }

  /**
   * 通过id删除教学计划草稿表
   *
   * @param ids id列表
   * @return R
   */
  @Operation(summary = "通过id删除教学计划草稿表", description = "通过id删除教学计划草稿表")
  @DeleteMapping("/delete")
  @HasPermission("edusystem_TeachingPlanDraft_del")
  @Idempotent(expireTime = 5, info = "点击太快，休息一下吧！")
  public R removeById(@NotEmpty(message = "请选择操作数据") @RequestBody Long[] ids) {
    teachingPlanDraftService.deltePlan(CollUtil.toList(ids));
    return R.ok();
  }

  /**
   * 发布教学计划
   *
   * <AUTHOR>
   * @date 2024/12/3 9:19
   * @param ids
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @Operation(summary = "发布教学计划", description = "发布教学计划")
  @PutMapping("/publish")
  @HasPermission("edusystem_TeachingPlanDraft_publish")
  @Idempotent(expireTime = 5, info = "点击太快，休息一下吧！")
  public R publish(
      @NotEmpty(message = "请选择操作数据") @RequestBody Long[] ids,
      @RequestParam(defaultValue = "false") Boolean forcePublish) {
    teachingPlanDraftService.publish(ids, forcePublish);
    return R.ok();
  }

    /**
     * 导出excel 表格
     * @param teachingPlanDraft 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("admin_TeachingPlanDraft_export")
    public List<TeachingPlanDraft> exportExcel(TeachingPlanDraft teachingPlanDraft,Long[] ids) {
        return teachingPlanDraftService.list(Wrappers.lambdaQuery(teachingPlanDraft).in(ArrayUtil.isNotEmpty(ids), TeachingPlanDraft::getId, ids));
    }

    /**
     * 导入excel 表
     * @param teachingPlanDraftList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("admin_TeachingPlanDraft_export")
    public R importExcel(@RequestExcel List<TeachingPlanDraft> teachingPlanDraftList, BindingResult bindingResult) {
        return R.ok(teachingPlanDraftService.saveBatch(teachingPlanDraftList));
    }
}
