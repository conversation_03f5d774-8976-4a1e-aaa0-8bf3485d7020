package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.query.SsDeviceConfigQuery;
import com.yuedu.ydsf.eduConnect.api.dto.SsDeviceConfigDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceConfigVO;
import com.yuedu.ydsf.eduConnect.entity.SsDeviceConfig;

import java.util.List;

/**
* 设备配置参数表服务接口
*
* <AUTHOR>
* @date  2024/09/28
*/
public interface SsDeviceConfigService extends IService<SsDeviceConfig> {



    /**
    * 设备配置参数表分页查询
    * @param page 分页对象
    * @param ssDeviceConfigQuery 设备配置参数表
    * @return IPage 分页结果
    */
    IPage page(Page page, SsDeviceConfigQuery ssDeviceConfigQuery);


    /**
    * 新增设备配置参数表
    * @param ssDeviceConfigDTO 设备配置参数表
    * @return boolean 执行结果
    */
    boolean add(SsDeviceConfigDTO ssDeviceConfigDTO);


    /**
    * 修改设备配置参数表
    * @param ssDeviceConfigDTO 设备配置参数表
    * @return boolean 执行结果
    */
    boolean edit(SsDeviceConfigDTO ssDeviceConfigDTO);


    /**
    * 导出excel 设备配置参数表表格
    * @param ssDeviceConfigQuery 查询条件
    * @param ids 导出指定ID
    * @return List<SsDeviceConfigVO> 结果集合
    */
    List<SsDeviceConfigVO> export(SsDeviceConfigQuery ssDeviceConfigQuery, Long[] ids);





}
