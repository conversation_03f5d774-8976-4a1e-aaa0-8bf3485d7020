package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.query.InformationAuthStoreHistoryQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationAuthStoreHistoryDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationAuthStoreHistoryVO;
import com.yuedu.ydsf.eduConnect.entity.InformationAuthStoreHistory;

import java.io.Serializable;
import java.util.List;

/**
* 资料授权历史服务接口
*
* <AUTHOR>
* @date  2025/07/22
*/
public interface InformationAuthStoreHistoryService extends IService<InformationAuthStoreHistory> {



    /**
     * 资料授权历史分页查询
     *
     * @param page 分页对象
     * @param informationAuthStoreHistoryQuery 资料授权历史
     * @return IPage 分页结果
     */
    IPage<InformationAuthStoreHistoryVO> page(Page page, InformationAuthStoreHistoryQuery informationAuthStoreHistoryQuery);


    /**
     * 根据ID获得资料授权历史信息
     *
     * @param id id
     * @return InformationAuthStoreHistoryVO 详细信息
     */
    InformationAuthStoreHistoryVO getInfoById(Serializable id);


    /**
     * 新增资料授权历史
     *
     * @param informationAuthStoreHistoryDTO 资料授权历史
     * @return boolean 执行结果
     */
    boolean add(InformationAuthStoreHistoryDTO informationAuthStoreHistoryDTO);


    /**
     * 修改资料授权历史
     *
     * @param informationAuthStoreHistoryDTO 资料授权历史
     * @return boolean 执行结果
     */
    boolean edit(InformationAuthStoreHistoryDTO informationAuthStoreHistoryDTO);


    /**
     * 导出excel 资料授权历史表格
     *
     * @param informationAuthStoreHistoryQuery 查询条件
     * @param ids 导出指定ID
     * @return List<InformationAuthStoreHistoryVO> 结果集合
     */
    List<InformationAuthStoreHistoryVO> export(InformationAuthStoreHistoryQuery informationAuthStoreHistoryQuery, Long[] ids);
}
