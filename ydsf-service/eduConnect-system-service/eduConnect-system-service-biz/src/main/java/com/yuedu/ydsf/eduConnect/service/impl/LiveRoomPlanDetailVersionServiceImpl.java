package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailVersionMapper;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDetailVersionService;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 直播间计划明细 服务类
 *
 * <AUTHOR>
 * @date 2024-12-03 09:49:55
 */
@Slf4j
@Service
public class LiveRoomPlanDetailVersionServiceImpl extends ServiceImpl<LiveRoomPlanDetailVersionMapper, LiveRoomPlanDetailVersion> implements LiveRoomPlanDetailVersionService {

    @Resource
    private LiveRoomPlanDetailVersionMapper liveRoomPlanDetailVersionMapper;


    /**
     * 通过直播间计划Id获取直播间计划信息
     *
     * @param planId 计划Id
     * @return 结果
     */
    @Override
    public List<LiveRoomPlanDetailVersionVO> selectLiveRoomPlanDetailVersionList(Long planId) {
        return liveRoomPlanDetailVersionMapper.selectLiveRoomPlanDetailVersionList(planId);
    }
}
