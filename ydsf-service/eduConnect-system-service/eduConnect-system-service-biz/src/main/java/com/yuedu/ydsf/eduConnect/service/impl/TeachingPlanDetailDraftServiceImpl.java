package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.PlanStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.TeachingPlanDraftConstant;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanDetailDraftQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailDraftVO;
import com.yuedu.ydsf.eduConnect.entity.ClassTime;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailVersionMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanVersionMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDetailDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDetailPubMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.service.ClassTimeService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDetailDraftService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
/**
 * 教学计划明细草稿表 服务类
 *
 * <AUTHOR>
 * @date 2024-12-03 09:52:18
 */
@Slf4j
@Service
@AllArgsConstructor
public class TeachingPlanDetailDraftServiceImpl extends ServiceImpl<TeachingPlanDetailDraftMapper, TeachingPlanDetailDraft> implements TeachingPlanDetailDraftService {

    private final RemoteLessonService remoteLessonService;
    private final ClassTimeService classTimeService;
    private final LiveRoomPlanDetailDraftMapper liveRoomPlanDetailDraftMapper;
    private final TeachingPlanDetailDraftMapper teachingPlanDetailDraftMapper;
    private final TeachingPlanDraftMapper teachingPlanDraftMapper;
    private final TeachingPlanDetailPubMapper teachingPlanDetailPubMapper;
    private final LiveRoomPlanDetailVersionMapper liveRoomPlanDetailVersionMapper;
    private final LiveRoomPlanVersionMapper liveRoomPlanVersionMapper;
    /**
     * 根据ids集合返回总数
     *
     * @param ids 教学计划ID集合
     * @return 列表
     */
    @Override
    public Map<Long, Integer> countTeachs(List<Long> ids) {
        // 查询符合条件的数据
        List<TeachingPlanDetailDraft> drafts = baseMapper.selectList(Wrappers.lambdaQuery(TeachingPlanDetailDraft.class)
            .in(ObjectUtil.isNotEmpty(ids),TeachingPlanDetailDraft::getPlanId, ids));
        return drafts.stream()
            .collect(Collectors.groupingBy(TeachingPlanDetailDraft::getPlanId,
                Collectors.collectingAndThen(Collectors.counting(), Long::intValue)));
    }

    /**
     * 查询全部教学计划明细
     *
     * @return List<TeachingPlanDetailDraftVO>
     */
    @Override
    public List<TeachingPlanDetailDraftVO> listPlans(Integer planId, Long courseId,Long id) {
        // 主讲老师
        List<TeachingPlanDetailDraftVO> drafts = baseMapper.selectList(Wrappers.lambdaQuery(TeachingPlanDetailDraft.class)
            .eq(TeachingPlanDetailDraft::getPlanId, planId)
            .orderByAsc(TeachingPlanDetailDraft::getLessonOrder)
        ).stream().map(teachingPlanDetailDraft -> {
            TeachingPlanDetailDraftVO teachingPlanDetailDraftVo = new TeachingPlanDetailDraftVO();
            BeanUtils.copyProperties(teachingPlanDetailDraft, teachingPlanDetailDraftVo);
            return teachingPlanDetailDraftVo;
        }).collect(Collectors.toCollection(ArrayList::new));
        if (drafts.isEmpty()) {
            return Collections.emptyList();
        }
        // 课节名称、关联书籍
        CoursePublishQuery coursePublishQuery = new CoursePublishQuery();
        coursePublishQuery.setCourseId(courseId);

        R<List<LessonVO>> lessonListResult = remoteLessonService.getPublishLessonList(coursePublishQuery);
        List<LessonVO> lessonList = lessonListResult.getData();
        if (lessonList == null) {
            lessonList = Collections.emptyList();
        }
        // 获取直播计划明细上课时段
        LiveRoomPlanVersion PlanVersion = liveRoomPlanVersionMapper.selectOne(Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
            .eq(LiveRoomPlanVersion::getPlanId, id)
            .eq(LiveRoomPlanVersion::getOnlineVersion, YesNoEnum.YES.getCode())
            .select(LiveRoomPlanVersion::getVersion));
        List<LiveRoomPlanDetailVersion> planList = liveRoomPlanDetailVersionMapper.selectList(Wrappers.lambdaQuery(LiveRoomPlanDetailVersion.class)
            .eq(LiveRoomPlanDetailVersion::getPlanId, id)
            .eq(LiveRoomPlanDetailVersion::getVersion, PlanVersion.getVersion())
            .orderByAsc(LiveRoomPlanDetailVersion::getLessonOrder));
        if (planList == null) {
            planList = Collections.emptyList();
        }
        List<ClassTime> timeSlots = classTimeService.list();
        Map<Long, String> timeSlotMap = timeSlots.stream().collect(Collectors.toMap(ClassTime::getId, ClassTime::getName));
        Integer planSize = planList.size();
        Integer lessonSize = lessonList.size();
        Integer size = Math.max(lessonSize, planSize);
        for (int i = 0; i < size; i++) {
            TeachingPlanDetailDraftVO draft;
            if (i < drafts.size()) {
                draft = drafts.get(i);
            } else {
                draft = new TeachingPlanDetailDraftVO();
                drafts.add(draft);
            }
            if (i < planSize && ObjectUtil.isNotEmpty(planList.get(i))) {
                LiveRoomPlanDetailVersion planDetail = planList.get(i);
                String classWeek = String.valueOf(planDetail.getClassDate().getDayOfWeek().getValue());
                draft.setClassDate(planDetail.getClassDate());
                draft.setSlotId(planDetail.getTimeSlotId());
                draft.setClassWeek(classWeek);
            }
            if ( i < lessonSize && ObjectUtil.isNotEmpty(lessonList.get(i))) {
                LessonVO lesson = lessonList.get(i);
                draft.setLessonName(lesson.getLessonName());
                draft.setBookName(lesson.getBookName());
            }
            draft.setDateName(timeSlotMap.get(draft.getTimeSlotId()));
            draft.setLessonCount(lessonSize);
            draft.setPlanCount(planSize);
        }

        return drafts;
    }

    /**
     * 修改主讲老师
     *
     * @return Boolean
     */
    @Override
    public Boolean updateById(TeachingPlanDetailDraftQuery teachingPlanDetailDraftQuery) {
        try {
            TeachingPlanDetailDraft planDetail = teachingPlanDetailDraftMapper.selectOne(Wrappers.lambdaQuery(TeachingPlanDetailDraft.class)
                .eq(TeachingPlanDetailDraft::getId, teachingPlanDetailDraftQuery.getId()));
            if (planDetail == null) {
                throw new BizException("未找到对应的教学计划明细");
            }
            Long planId = planDetail.getPlanId();
            if (planId == null) {
                throw new BizException("教学计划id为空");
            }
            TeachingPlanDraft planDraft = teachingPlanDraftMapper.selectOne(Wrappers.lambdaQuery(TeachingPlanDraft.class)
                .eq(TeachingPlanDraft::getId, planId));
            if (planDraft == null) {
                throw new BizException("未找到对应的直播计划");
            }

            if (teachingPlanDetailDraftQuery.getUseAll() == 1) {
                Long liveRoomPlanId = planDraft.getLiveRoomPlanId();
                List<LiveRoomPlanDetailDraft> liveRoomPlanDetails = liveRoomPlanDetailDraftMapper.selectList(Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                    .eq(LiveRoomPlanDetailDraft::getPlanId, liveRoomPlanId)
                    .gt(LiveRoomPlanDetailDraft::getClassStartDateTime, LocalDateTime.now())
                    .select(LiveRoomPlanDetailDraft::getLessonOrder));
                List<Integer> lessonOrders = liveRoomPlanDetails.stream()
                    .map(LiveRoomPlanDetailDraft::getLessonOrder)
                    .toList();

                if (lessonOrders.isEmpty()) {
                    throw new BizException("未找到还未开始的课节");
                }
                //查发布表信息，如果有则存修改信息
                List<TeachingPlanDetailPub> planPubList = teachingPlanDetailPubMapper.selectList(Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                    .eq(TeachingPlanDetailPub::getPlanId, planId));
                if (ObjectUtil.isNotEmpty(planPubList)) {
                    Map<Integer, Map<String, Object>> lessonOrderMap = new HashMap<>();
                    for (TeachingPlanDetailPub pub : planPubList) {
                        Map<String, Object> courseInfo = new HashMap<>();
                        courseInfo.put("courseIdOld", pub.getCourseId());
                        courseInfo.put("lessonId", pub.getLessonId());
                        courseInfo.put("lectureIdOld", pub.getLectureId());
                        lessonOrderMap.put(pub.getLessonOrder(), courseInfo);
                    }
                    List<TeachingPlanDetailDraft> lessonIdOlds = baseMapper.selectList(Wrappers.lambdaQuery(TeachingPlanDetailDraft.class)
                        .eq(TeachingPlanDetailDraft::getPlanId, planId)
                        .in(TeachingPlanDetailDraft::getLessonOrder,lessonOrders)
                        .select(TeachingPlanDetailDraft::getId, TeachingPlanDetailDraft::getLessonOrder,TeachingPlanDetailDraft::getLectureId));
                    if (ObjectUtil.isNotEmpty(lessonIdOlds)) {
                        for (TeachingPlanDetailDraft draft : lessonIdOlds) {
                            Integer lessonOrder = draft.getLessonOrder();
                            if (lessonOrderMap.containsKey(lessonOrder)) {
                                Map<String, Object> courseInfo = lessonOrderMap.get(lessonOrder);
                                String jsonField = String.format(
                                    "{\"planId\": \"%d\", \"lectureId\": \"%d\", \"lectureName\": \"%s\", \"lessonId\": \"%d\", \"courseIdOld\": \"%d\", \"lectureIdOld\": \"%d\"}",
                                    planId,
                                    teachingPlanDetailDraftQuery.getLectureId(),
                                    teachingPlanDetailDraftQuery.getLectureName(),
                                    courseInfo.get("lessonId"),
                                    courseInfo.get("courseIdOld"),
                                    courseInfo.get("lectureIdOld")
                                );
                                draft.setEditRemark(jsonField);
                                // 更新数据
                                baseMapper.updateById(draft);
                            }
                        }
                    }
                }
                this.update(Wrappers.<TeachingPlanDetailDraft>update()
                    .eq(TeachingPlanDraftConstant.TEACH_PLAN_DETAIL_DRAFT_PLAN_ID, planId)
                    .in(TeachingPlanDraftConstant.TEACH_PLAN_DETAIL_DRAFT_ORDER, lessonOrders)
                    .set(TeachingPlanDraftConstant.TEACH_PLAN_DETAIL_DRAFT_LECTURE_ID, teachingPlanDetailDraftQuery.getLectureId())
                    .set(TeachingPlanDraftConstant.TEACH_PLAN_DETAIL_DRAFT_LECTURE_NAME, teachingPlanDetailDraftQuery.getLectureName())
                );

                teachingPlanDraftMapper.update(null, Wrappers.<TeachingPlanDraft>update()
                    .eq(TeachingPlanDraftConstant.TEACH_PLAN_DETAIL_DRAFT_ID, planId)
                    .set(TeachingPlanDraftConstant.TEACH_PLAN_DETAIL_DRAFT_LECTURE_ID, teachingPlanDetailDraftQuery.getLectureId())
                    .set(TeachingPlanDraftConstant.TEACH_PLAN_DETAIL_DRAFT_LECTURE_NAME, teachingPlanDetailDraftQuery.getLectureName())
                    .set(TeachingPlanDraftConstant.TEACH_PLAN_DETAIL_DRAFT_STATUS, PlanStatusEnum.STATUS_ENUM_0.code));

            } else {
                TeachingPlanDetailDraft draft = BeanUtil.copyProperties(teachingPlanDetailDraftQuery, TeachingPlanDetailDraft.class);
                TeachingPlanDetailPub planPubInfo = teachingPlanDetailPubMapper.selectOne(Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                    .eq(TeachingPlanDetailPub::getPlanId, planId)
                    .eq(TeachingPlanDetailPub::getLessonOrder, planDetail.getLessonOrder()));
                if(ObjectUtil.isNotEmpty(planPubInfo)){
                    JSONObject editRemark = new JSONObject();
                    editRemark.set("planId", planId);
                    editRemark.set("lectureId", teachingPlanDetailDraftQuery.getLectureId());
                    editRemark.set("lectureName", teachingPlanDetailDraftQuery.getLectureName());
                    editRemark.set("lessonId", planPubInfo.getLessonId());
                    editRemark.set("courseIdOld", planPubInfo.getCourseId());
                    editRemark.set("lectureIdOld", planPubInfo.getLectureId());
                    draft.setEditRemark(editRemark.toString());
                }

                this.updateById(draft);
                teachingPlanDraftMapper.update(null, Wrappers.<TeachingPlanDraft>update()
                    .eq(TeachingPlanDraftConstant.TEACH_PLAN_DETAIL_DRAFT_ID, planId)
                    .set(TeachingPlanDraftConstant.TEACH_PLAN_DETAIL_DRAFT_STATUS, PlanStatusEnum.STATUS_ENUM_0.code));
            }

            return Boolean.TRUE;
        } catch (BizException e) {
            log.error("业务异常: {}", e.getMessage());
            throw e;
        }

    }
}
