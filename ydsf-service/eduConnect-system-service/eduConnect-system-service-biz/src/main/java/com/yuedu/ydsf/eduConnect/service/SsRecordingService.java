package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.vo.PlayAuthVO;
import com.yuedu.ydsf.eduConnect.api.vo.UploadAuthVO;
import com.yuedu.ydsf.eduConnect.api.query.SsRecordingQuery;
import com.yuedu.ydsf.eduConnect.api.dto.SsRecordingDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsRecordingVO;
import com.yuedu.ydsf.eduConnect.entity.SsRecording;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RecordFileUpload;

import java.io.Serializable;
import java.util.List;

/**
 * 点播库服务接口
 *
 * <AUTHOR>
 * @date 2024/09/26
 */
public interface SsRecordingService extends IService<SsRecording> {


    /**
     * 点播库分页查询
     *
     * @param page             分页对象
     * @param ssRecordingQuery 点播库
     * @return IPage 分页结果
     */
    IPage page(Page page, SsRecordingQuery ssRecordingQuery);


    /**
     * 新增点播库
     *
     * @param ssRecordingDTO 点播库
     * @return boolean 执行结果
     */
    boolean add(SsRecordingDTO ssRecordingDTO);


    /**
     * 修改点播库
     *
     * @param ssRecordingDTO 点播库
     * @return boolean 执行结果
     */
    boolean edit(SsRecordingDTO ssRecordingDTO);

    /**
     * 通过id删除点播库
     *
     * @param id id
     * @return SsRecordingVO 执行结果
     */
    SsRecordingVO getRecordingById(Serializable id);


    /**
     * 导出excel 点播库表格
     *
     * @param ssRecordingQuery 查询条件
     * @param ids              导出指定ID
     * @return List<SsRecordingVO> 结果集合
     */
    List<SsRecordingVO> export(SsRecordingQuery ssRecordingQuery, Long[] ids);


    /**
     * 删除
     * @param ids
     * <AUTHOR>
     * @date 2024年10月11日 10时25分
     */
    boolean deleteByIds(List<Long> ids);


    /**
     * 获得视频上传凭证
     *
     * @param fileName
     * @param title
     * <AUTHOR>
     * @date 2024年09月27日 14时53分
     */
    UploadAuthVO getUploadAuth(String fileName, String title);

    /**
     * 刷新视频上传凭证
     *
     * @param videoId
     * <AUTHOR>
     * @date 2024年09月27日 14时53分
     */
    UploadAuthVO refreshUploadAuth(String videoId);


    /**
     *  获取下载地址
     *
     * <AUTHOR>
     * @date 2024年10月10日 15时12分
     * @param id 记录ID

     */
    SsRecordingVO download(Long id);

    /**
     *  获取播放凭证
     *
     * <AUTHOR>
     * @date 2024年10月11日 18时18分
     * @param videoId
     */
    PlayAuthVO getPlayAuth(String videoId,Long expiredTime);



    /**
     *  录制上传
     *
     * <AUTHOR>
     * @date 2024年11月05日 13时54分
     */
    boolean recordUpload(RecordFileUpload recordFileUpload);


    /**
     * 转码处理
     * @param videoId 视频ID
     * @param  isTranscode 是否转码成功完成
     * <AUTHOR>
     * @date 2024年10月11日 10时25分
     */
    boolean handlerTranscode(String videoId, boolean isTranscode);

    /**
     * 老双师点播库查询
     * <AUTHOR>
     * @date 2025/4/22 14:17
     * @param ssRecordingQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsRecordingVO>
     */
    List<SsRecordingVO> recordTaskMatchList(SsRecordingQuery ssRecordingQuery);
}
