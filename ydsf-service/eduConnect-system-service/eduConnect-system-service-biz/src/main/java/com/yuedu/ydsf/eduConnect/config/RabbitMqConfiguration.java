package com.yuedu.ydsf.eduConnect.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.retry.MessageRecoverer;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;

@Slf4j
public class RabbitMqConfiguration {


    @Bean
    public RabbitListenerContainerFactory rabbitListenerContainerFactory(
        SimpleRabbitListenerContainerFactoryConfigurer configuration,
        ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory simpleRabbitListenerContainerFactory = new SimpleRabbitListenerContainerFactory();
        simpleRabbitListenerContainerFactory.setConnectionFactory(connectionFactory);
        simpleRabbitListenerContainerFactory.setMessageConverter(new Jackson2JsonMessageConverter());
        configuration.configure(simpleRabbitListenerContainerFactory, connectionFactory);
        return simpleRabbitListenerContainerFactory;
    }


    /**
     * 消息发送的一个流程： producer -> rabbitmq broker cluster -> exchange -> queue -> consumer
     * message 从 producer 到 rabbitmq broker cluster 则会返回一个 confirmCallback 。
     * message 从 exchange -> queue 投递失败则会返回一个 returnCallback 。
     * 如果消息没有到 exchange,则 confirm 回调,ack = false
     * 如果消息到达 exchange,则 confirm 回调,ack = true
     * exchange 到 queue 成功,则不回调 return
     * exchange 到 queue 失败,则回调 return (需设置 mandatory = true,否则不回回调,消息就丢了)
     * <p>
     * setPublisherConfirmType
     * NONE值是禁用发布确认模式，是默认值
     * CORRELATED值是发布消息成功到交换器后会触发回调方法
     * SIMPLE值经测试有两种效果，其一效果和CORRELATED值一样会触发回调方法，
     * 其二在发布消息成功后使用rabbitTemplate调用waitForConfirms或waitForConfirmsOrDie方法等待broker节点返回发送结果，
     * 根据返回结果来判定下一步的逻辑，要注意的点是waitForConfirmsOrDie方法如果返回false则会关闭channel，则接下来无法发送消息到broker;
     * <p>
     * mandatory：交换器无法根据自身类型和路由键找到一个符合条件的队列时的处理方式 跟setPublisherConfirmType合用
     * true：RabbitMQ会调用Basic.Return命令将消息返回给生产者
     * false：RabbitMQ会把消息直接丢弃
     *
     * @Author: KL
     * @Date: 2022-09-23 14:15
     */
    @Bean
    public RabbitTemplate rabbitTemplate(CachingConnectionFactory cachingConnectionFactory) {
        cachingConnectionFactory.setPublisherReturns(true); // 确认消息已发送到队列(Queue)
        // cachingConnectionFactory.setPublisherConfirms(true); 老版本开启 确认消息已发送到交换机
        cachingConnectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED); //新版本 确认消息已发送到交换机
        RabbitTemplate rabbitTemplate = new RabbitTemplate(cachingConnectionFactory);
        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        // 设置消息发送至 RabbitMQ 的回调
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            log.info("消息发送成功:{}-{}-{}", correlationData, ack, cause);
        });

        // 设置消息发送至队列失败的回调
        rabbitTemplate.setReturnsCallback((message) -> log.info("消息发送失败:{}", message));

        return rabbitTemplate;
    }



    /**
     * 失败消息处理
     */
    @Bean
    public MessageRecoverer messageRecoverer(RabbitTemplate rabbitTemplate) {
        return (message, throwable) -> {
            log.info("消息处理失败:{}-{}",message.toString(),throwable.getMessage());
        };
    }
}
