package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.api.dto.SsScreenshotDetailDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsScreenshotDetailQuery;
import com.yuedu.ydsf.eduConnect.api.valid.SsScreenshotDetailValidGroup;
import com.yuedu.ydsf.eduConnect.api.vo.SsScreenshotDetailVO;
import com.yuedu.ydsf.eduConnect.service.SsScreenshotDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.Serializable;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 双师截图明细表控制层
 *
 * <AUTHOR>
 * @date 2024/10/11
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/ssScreenshotDetail")
@Tag(description = "ss_screenshot_detail", name = "双师截图明细表")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SsScreenshotDetailController {

    private final SsScreenshotDetailService sScreenshotDetailService;

    /**
     * 双师截图明细表分页查询
     *
     * @param page                    分页对象
     * @param ssScreenshotDetailQuery 双师截图明细表
     * @return R
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询", description = "双师截图明细表分页查询")
    public R page(@ParameterObject Page page,
                  @ParameterObject SsScreenshotDetailQuery ssScreenshotDetailQuery)
    {
        return R.ok(sScreenshotDetailService.page(page, ssScreenshotDetailQuery));
    }

    /**
     * 通过id查询双师截图明细表
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询双师截图明细表")
    @GetMapping("/{id}")
    @HasPermission("edusystem_ssScreenshotDetail_view")
    public R getById(@PathVariable Serializable id) {
        return R.ok(sScreenshotDetailService.getById(id));
    }

    /**
     * 新增双师截图明细表
     *
     * @param ssScreenshotDetailDTO 双师截图明细表
     * @return R
     */
    @PostMapping
    @HasPermission("edusystem_ssScreenshotDetail_view")
    @Operation(summary = "新增双师截图明细表", description = "新增双师截图明细表")
    public R add(@Validated(V_A.class) @RequestBody SsScreenshotDetailDTO ssScreenshotDetailDTO) {
        return R.ok(sScreenshotDetailService.add(ssScreenshotDetailDTO));
    }

    /**
     * 修改双师截图明细表
     *
     * @param ssScreenshotDetailDTO 双师截图明细表
     * @return R
     */
    @PutMapping
    @HasPermission("edusystem_ssScreenshotDetail_edit")
    @Operation(summary = "修改双师截图明细表", description = "修改双师截图明细表")
    public R edit(@Validated(V_E.class) @RequestBody SsScreenshotDetailDTO ssScreenshotDetailDTO) {
        return R.ok(sScreenshotDetailService.edit(ssScreenshotDetailDTO));
    }

    /**
     * 通过id删除双师截图明细表
     *
     * @param ids id列表
     * @return R
     */
    @DeleteMapping
    @HasPermission("edusystem_ssScreenshotDetail_del")
    @Operation(summary = "删除双师截图明细表", description = "删除双师截图明细表")
    public R delete(@RequestBody Long[] ids) {
        return R.ok(sScreenshotDetailService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 导出excel 双师截图明细表表格
     *
     * @param ssScreenshotDetailQuery 查询条件
     * @param ids                     导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_ssScreenshotDetail_export")
    @Operation(summary = "导出双师截图明细表表格", description = "导出双师截图明细表表格")
    public List<SsScreenshotDetailVO> export(SsScreenshotDetailQuery ssScreenshotDetailQuery,
                                             Long[] ids)
    {
        return sScreenshotDetailService.export(ssScreenshotDetailQuery, ids);
    }

    /**
     * 保存截图
     *
     * @return AjaxResult
     * <AUTHOR>
     * @date 2024/10/11 10:20
     */
    @PostMapping(value = "/saveScreenshot")
    @Operation(summary = "保存截图", description = "保存截图")
    public R saveScreenshot(@Validated(SsScreenshotDetailValidGroup.SaveScreenDetail.class) @RequestBody SsScreenshotDetailDTO ssScreenshotDetailDTO)
    {
        sScreenshotDetailService.saveScreenshot(ssScreenshotDetailDTO);
        return R.ok();
    }

}
