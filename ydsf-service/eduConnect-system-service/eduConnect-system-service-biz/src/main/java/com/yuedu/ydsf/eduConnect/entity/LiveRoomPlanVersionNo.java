package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 直播间计划版本生成记录表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-29 14:38:15
 */
@Data
@TableName("ea_live_room_plan_version_no")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "直播间计划版本生成记录表实体类")
public class LiveRoomPlanVersionNo extends Model<LiveRoomPlanVersionNo> {


	/**
	* 版本号ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="版本号ID")
    private Long id;

	/**
	* 直播间计划ID
	*/
    @Schema(description="直播间计划ID")
    private Long planId;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
