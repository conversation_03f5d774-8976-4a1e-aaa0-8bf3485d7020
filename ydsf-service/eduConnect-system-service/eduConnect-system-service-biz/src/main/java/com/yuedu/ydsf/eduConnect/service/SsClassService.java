package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassDTO;
import com.yuedu.ydsf.eduConnect.api.excel.ClassExcel;
import com.yuedu.ydsf.eduConnect.api.query.SsClassAuthRoomQuery;
import com.yuedu.ydsf.eduConnect.api.query.SsClassQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassAuthRoomVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassVO;
import com.yuedu.ydsf.eduConnect.entity.SsClass;
import org.springframework.validation.BindingResult;
import java.io.Serializable;
import java.util.List;

/**
 * 班级信息表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 10:44:48
 */
public interface SsClassService extends IService<SsClass> {

    /**
     * 班级管理分页查询
     * @param page
     * @param ssClassQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2024/10/9 10:52
     */
    IPage page(Page page, SsClassQuery ssClassQuery);

    /**
     * 班级管理查询全部
     * @param ssClassQuery
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsClassVO
     * <AUTHOR>
     * @date 2024/10/15 16:30
     */
    List<SsClassVO> getSsClassList(SsClassQuery ssClassQuery);

    /**
     * 通过id查询班级信息
     * @param id
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsClassVO
     * <AUTHOR>
     * @date 2024/10/14 10:29
     */
    SsClassVO selectById(Serializable id);

    /**
     * 新增班级信息
     * @param ssClassDTO
     * <AUTHOR>
     * @date 2024/10/9 14:07
     */
    void add(SsClassDTO ssClassDTO);

    /**
     * 修改班级信息
     * @param ssClassDTO
     * <AUTHOR>
     * @date 2024/10/9 14:07
     */
    void edit(SsClassDTO ssClassDTO);

    /**
     * 班级删除
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/10/10 16:12
     */
    void delete(Long[] ids);

    /**
     * 班级导入
     * @param classExcelList
     * @param bindingResult
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/10 16:49
     */
    R importClass(List<ClassExcel> classExcelList, BindingResult bindingResult);

    /**
     * 班级结业
     * @param ssClassDTO
     * @return void
     * <AUTHOR>
     * @date 2024/10/10 15:41
     */
    void completeClass(SsClassDTO ssClassDTO);

    /**
     * 查询班级已授权设备信息
     * @param ssClassAuthRoomQuery
     * @return java.util.List<com.yuedu.ydsf.eduConncet.api.vo.SsClassAuthRoomVO>
     * <AUTHOR>
     * @date 2024/10/9 17:19
     */
    List<SsClassAuthRoomVO> getAuthDeviceListByClassId(SsClassAuthRoomQuery ssClassAuthRoomQuery);


    /**
     * 获取可用状态的班级
     * @param classId 班级ID
     * @return 班级实体
     * @throws BizException 未查询到班级，抛出异常
     */
    SsClass getAvailableClass(Long classId) throws BizException;

}
