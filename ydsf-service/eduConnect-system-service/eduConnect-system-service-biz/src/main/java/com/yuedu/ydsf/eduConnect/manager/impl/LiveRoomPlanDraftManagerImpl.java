package com.yuedu.ydsf.eduConnect.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDraftVO;
import com.yuedu.ydsf.eduConnect.config.SsProperty;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.manager.LiveRoomPlanDraftManager;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDraftMapper;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDraftService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 班级信息表 公共服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-09 10:44:48
 */
@Slf4j
@Service
@AllArgsConstructor
public class LiveRoomPlanDraftManagerImpl implements LiveRoomPlanDraftManager {

    private final LiveRoomPlanDraftMapper liveRoomPlanDraftMapper;

    private final LiveRoomPlanDetailDraftMapper liveRoomPlanDetailDraftMapper;

    private final TeachingPlanDraftMapper teachingPlanDraftMapper;

    private final SsProperty ssProperty;

    /**
     * 通过条件查询直播计划
     *
     * @return List<LiveRoomPlanDraftVO>
     */
    public List<LiveRoomPlanDraftVO> listByIds(List<Long> ids) {

        return liveRoomPlanDraftMapper
            .selectList(
                Wrappers.lambdaQuery(LiveRoomPlanDraft.class).in(LiveRoomPlanDraft::getId, ids))
            .stream()
            .map(
                liveRoomPlanDraft -> {
                    LiveRoomPlanDraftVO vo = new LiveRoomPlanDraftVO();
                    BeanUtils.copyProperties(liveRoomPlanDraft, vo);
                    return vo;
                })
            .toList();
    }

    /**
     * 检查计划是否已结束
     *
     * @param liveRoomPlanId
     * @return void
     * <AUTHOR>
     * @date 2024/12/4 10:28
     */
    @Override
    public void checkPlanEndStatus(Long liveRoomPlanId) {
        if (!ssProperty.getJwCheckPlanDateEnable()){
            return;
        }
        List<LiveRoomPlanDetailDraft> planDetails =
            liveRoomPlanDetailDraftMapper.selectList(
                Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                    .eq(LiveRoomPlanDetailDraft::getPlanId, liveRoomPlanId)
                    .orderByDesc(LiveRoomPlanDetailDraft::getClassEndDateTime));

        if (CollectionUtils.isNotEmpty(planDetails)
            && planDetails.get(0).getClassEndDateTime().isBefore(LocalDateTime.now())) {
            log.error("直播间计划已结束, planId={}", liveRoomPlanId);
            throw new BizException("该直播间计划已结束，不可编辑!");
        }
    }

    /**
     * 检查关联的教学计划
     *
     * @param planId
     * @return void
     * <AUTHOR>
     * @date 2024/12/4 10:27
     */
    @Override
    public void checkAssociatedTeachingPlan(Long planId) {

        List<TeachingPlanDraft> teachingPlanDrafts =
            teachingPlanDraftMapper.selectList(
                Wrappers.lambdaQuery(TeachingPlanDraft.class)
                    .eq(TeachingPlanDraft::getLiveRoomPlanId, planId));

        if (CollectionUtils.isNotEmpty(teachingPlanDrafts)) {
            log.warn("直播间计划已关联教学计划, planId={}", planId);
            throw new BizException("已经关联教学计划的直播间计划不可操作!");
        }
    }

    /**
     * 校验直播间计划是否存在过期的明细
     *
     * @param planId
     * @return void
     * <AUTHOR>
     * @date 2025/1/20 10:25
     */
    @Override
    public void checkHasExpiredDetail(Long planId) {
        if (!ssProperty.getJwCheckPlanDateEnable()) {
            return;
        }
        // 查询计划下的有效排期明细
        List<LiveRoomPlanDetailDraft> details =
            liveRoomPlanDetailDraftMapper.selectList(
                Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                    .eq(LiveRoomPlanDetailDraft::getPlanId, planId));

        if (CollectionUtils.isEmpty(details)) {
            log.warn("直播间计划下不存在有效的排期明细, planId={}", planId);
            throw new BizException("计划下不存在有效的排期明细!");
        }
        // 检查是否存在过期的排期
        LocalDateTime now = LocalDateTime.now();
        List<LiveRoomPlanDetailDraft> expiredDetails =
            details.stream().filter(detail -> detail.getClassStartDateTime().isBefore(now))
                .toList();

        if (!expiredDetails.isEmpty()) {
            log.warn("存在过期的排期明细,expiredDetails={}", expiredDetails);
            throw new BizException("直播间计划已开始，不允许操作!");
        }
    }
}
