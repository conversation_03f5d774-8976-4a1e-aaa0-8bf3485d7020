package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.eduConnect.api.vo.ClassTimeVO;
import com.yuedu.ydsf.eduConnect.entity.ClassTime;
import com.yuedu.ydsf.eduConnect.mapper.ClassTimeMapper;
import com.yuedu.ydsf.eduConnect.service.ClassTimeService;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 上课时段 服务类
 *
 * <AUTHOR>
 * @date 2024-11-28 16:44:02
 */
@Slf4j
@Service
public class ClassTimeServiceImpl extends ServiceImpl<ClassTimeMapper, ClassTime> implements ClassTimeService {
    /**
     * 获取全部上课时段
     *
     * @return List<ClassTimeVO>
     */
    @Override
    public List<ClassTimeVO> getClassTimeList() {
        List<ClassTimeVO> classTimeVOList = BeanUtil.copyToList(this.list(Wrappers.lambdaQuery(ClassTime.class)
            .orderByAsc(ClassTime::getStartTime)), ClassTimeVO.class);

        // 定义时间格式化对象
        DateTimeFormatter timeFormat = DateTimeFormatter.ofPattern("HH:mm");

        // 给上课时段匹配对应时间
        classTimeVOList.forEach(classTimeVO -> {
            String correspondingTime = timeFormat.format(classTimeVO.getStartTime())+"-"+timeFormat.format(classTimeVO.getEndTime());
            classTimeVO.setCorrespondingTime(correspondingTime);
        });
        return classTimeVOList;
    }
}
