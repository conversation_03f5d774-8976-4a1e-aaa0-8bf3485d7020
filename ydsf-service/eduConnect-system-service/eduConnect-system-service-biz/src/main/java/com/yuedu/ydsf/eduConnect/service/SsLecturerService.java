package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.query.SsLecturerQuery;
import com.yuedu.ydsf.eduConnect.api.dto.SsLecturerDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsLecturerVO;
import com.yuedu.ydsf.eduConnect.entity.SsLecturer;

import java.io.Serializable;
import java.util.List;

/**
* 主讲老师表服务接口
*
* <AUTHOR>
* @date  2025/01/14
*/
public interface SsLecturerService extends IService<SsLecturer> {



    /**
     * 主讲老师表分页查询
     *
     * @param page 分页对象
     * @param ssLecturerQuery 主讲老师表
     * @return IPage 分页结果
     */
    IPage<SsLecturerVO> page(Page page, SsLecturerQuery ssLecturerQuery);


    /**
     * 根据ID获得主讲老师表信息
     *
     * @param id id
     * @return SsLecturerVO 详细信息
     */
    SsLecturerVO getInfoById(Serializable id);


    /**
     * 新增主讲老师表
     *
     * @param ssLecturerDTO 主讲老师表
     * @return boolean 执行结果
     */
    boolean add(SsLecturerDTO ssLecturerDTO);


    /**
     * 修改主讲老师表
     *
     * @param ssLecturerDTO 主讲老师表
     * @return boolean 执行结果
     */
    boolean edit(SsLecturerDTO ssLecturerDTO);


    /**
     * 导出excel 主讲老师表表格
     *
     * @param ssLecturerQuery 查询条件
     * @param ids 导出指定ID
     * @return List<SsLecturerVO> 结果集合
     */
    List<SsLecturerVO> export(SsLecturerQuery ssLecturerQuery, Long[] ids);
}
