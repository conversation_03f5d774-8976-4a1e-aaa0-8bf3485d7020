package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineExportVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.eduConnect.service.CourseMakeUpOnlineService;
import com.yuedu.ydsf.eduConnect.api.query.CourseMakeUpOnlineQuery;
import com.yuedu.ydsf.eduConnect.api.dto.CourseMakeUpOnlineDTO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineVO;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* 门店线上补课表控制层
*
* <AUTHOR>
* @date  2025/04/28
*/

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/courseMakeUpOnline")
@Tag(description = "b_course_make_up_online" , name = "门店线上补课" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseMakeUpOnlineController  {


    private final CourseMakeUpOnlineService ourseMakeUpOnlineService;


    /**
    * 门店线上补课表分页查询
    * @param page 分页对象
    * @param courseMakeUpOnlineQuery 门店线上补课表分页查询
    * @return R
    */
    @GetMapping("/page" )
    @HasPermission("courseMakeUpOnline_page_view")
    @Operation(summary = "分页查询" , description = "门店线上补课表分页查询" )
    public R<IPage<CourseMakeUpOnlineVO>> page(@ParameterObject Page page, @ParameterObject CourseMakeUpOnlineQuery courseMakeUpOnlineQuery) {
        return R.ok(ourseMakeUpOnlineService.page(page, courseMakeUpOnlineQuery));
    }


    /**
    * 通过id查询门店线上补课表分页查询
    * @param id id
    * @return R
    */
    @Operation(summary = "通过id门店线上补课表分页查询" , description = "通过id查询门店线上补课表分页查询" )
    @GetMapping("/{id}" )
    @HasPermission("courseMakeUpOnline_info_view")
    public R<CourseMakeUpOnlineVO> getById(@PathVariable Serializable id) {
        return R.ok(ourseMakeUpOnlineService.getInfoById(id));
    }



    /**
    * 新增门店线上补课表
    * @param courseMakeUpOnlineDTO 门店线上补课表
    * @return R
    */
    @PostMapping
    @SysLog("新增门店线上补课表" )
    @HasPermission("classConnect_courseMakeUpOnline_view" )
    @Operation(summary = "新增门店线上补课表" , description = "新增门店线上补课表" )
    public R add(@Validated(V_A.class) @RequestBody CourseMakeUpOnlineDTO courseMakeUpOnlineDTO) {
         return R.ok(ourseMakeUpOnlineService.add(courseMakeUpOnlineDTO));
    }


    /**
    * 修改门店线上补课表
    * @param courseMakeUpOnlineDTO 门店线上补课表
    * @return R
    */
    @PutMapping
    @SysLog("修改门店线上补课表" )
    @HasPermission("classConnect_courseMakeUpOnline_edit" )
    @Operation(summary = "修改门店线上补课表" , description = "修改门店线上补课表" )
    public R edit(@Validated(V_E.class) @RequestBody CourseMakeUpOnlineDTO courseMakeUpOnlineDTO) {
         return R.ok(ourseMakeUpOnlineService.edit(courseMakeUpOnlineDTO));
    }



    /**
    * 通过id删除门店线上补课表
    * @param ids id列表
    * @return R
    */
    @DeleteMapping
    @SysLog("通过id删除门店线上补课表" )
    @HasPermission("classConnect_courseMakeUpOnline_del" )
    @Operation(summary = "删除门店线上补课表" , description = "删除门店线上补课表" )
    public R delete(@RequestBody  Long[] ids){
         return R.ok(ourseMakeUpOnlineService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     *  导出
     *
     * <AUTHOR>
     * @date 2025年07月08日 13时47分
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("classConnect_courseMakeUpOnline_export" )
    @Idempotent(info = "导出执行中,请勿频繁操作！", expireTime = 5)
    public List<CourseMakeUpOnlineExportVO> export(CourseMakeUpOnlineQuery courseMakeUpOnlineQuery) {
        if(Objects.isNull(courseMakeUpOnlineQuery.getClassStartDateTime()) || Objects.isNull(courseMakeUpOnlineQuery.getClassEndDateTime())){
            return new ArrayList<>();
        }
//        if(Objects.isNull(courseMakeUpOnlineQuery.getClassStartDateTime()) || Objects.isNull(courseMakeUpOnlineQuery.getClassEndDateTime())){
//            LocalDate today = LocalDate.now();
//            courseMakeUpOnlineQuery.setClassStartDateTime(
//                LocalDateTime.of(today.with(TemporalAdjusters.firstDayOfMonth()), LocalTime.of(0,0,0)));
//            courseMakeUpOnlineQuery.setClassEndDateTime(LocalDateTime.of(today.with(TemporalAdjusters.lastDayOfMonth()),LocalTime.of(23,59,59)));
//        }else {
//            if( ChronoUnit.MONTHS.between(courseMakeUpOnlineQuery.getClassStartDateTime(), courseMakeUpOnlineQuery.getClassEndDateTime())> 3){
//                throw new BizException("导出的课消时间间隔不能超过3个月");
//            }
//        }

        return ourseMakeUpOnlineService.export(courseMakeUpOnlineQuery);
    }


}
