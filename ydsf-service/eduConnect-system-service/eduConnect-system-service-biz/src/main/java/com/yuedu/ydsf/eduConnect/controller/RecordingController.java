package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.eduConnect.entity.Recording;
import com.yuedu.ydsf.eduConnect.service.RecordingService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 主讲录课表 控制类
 *
 * <AUTHOR>
 * @date 2024-12-03 15:01:44
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/recording" )
@Tag(description = "ea_recording" , name = "主讲录课表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class RecordingController {

    private final  RecordingService recordingService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param recording 主讲录课表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("edusystem_recording_view")
    public R getRecordingPage(@ParameterObject Page page, @ParameterObject Recording recording) {
        LambdaQueryWrapper<Recording> wrapper = Wrappers.lambdaQuery();
        return R.ok(recordingService.page(page, wrapper));
    }


    /**
     * 通过条件查询主讲录课表
     * @param recording 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("edusystem_recording_view")
    public R getDetails(@ParameterObject Recording recording) {
        return R.ok(recordingService.list(Wrappers.query(recording)));
    }

    /**
     * 新增主讲录课表
     * @param recording 主讲录课表
     * @return R
     */
    @Operation(summary = "新增主讲录课表" , description = "新增主讲录课表" )
    @PostMapping("/add")
    @HasPermission("edusystem_recording_add")
    public R save(@RequestBody Recording recording) {
        return R.ok(recordingService.save(recording));
    }

    /**
     * 修改主讲录课表
     * @param recording 主讲录课表
     * @return R
     */
    @Operation(summary = "修改主讲录课表" , description = "修改主讲录课表" )
    @PutMapping("/edit")
    @HasPermission("edusystem_recording_edit")
    public R updateById(@RequestBody Recording recording) {
        return R.ok(recordingService.updateById(recording));
    }

    /**
     * 通过id删除主讲录课表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除主讲录课表" , description = "通过id删除主讲录课表" )
    @DeleteMapping("/delete")
    @HasPermission("edusystem_recording_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(recordingService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param recording 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_recording_export")
    public List<Recording> exportExcel(Recording recording,Long[] ids) {
        return recordingService.list(Wrappers.lambdaQuery(recording).in(ArrayUtil.isNotEmpty(ids), Recording::getId, ids));
    }

    /**
     * 导入excel 表
     * @param recordingList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_recording_export")
    public R importExcel(@RequestExcel List<Recording> recordingList, BindingResult bindingResult) {
        return R.ok(recordingService.saveBatch(recordingList));
    }
}
