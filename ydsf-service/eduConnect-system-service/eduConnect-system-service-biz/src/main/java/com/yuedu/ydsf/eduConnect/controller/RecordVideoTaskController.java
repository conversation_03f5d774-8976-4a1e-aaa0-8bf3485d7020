package com.yuedu.ydsf.eduConnect.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.dto.DeleteVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.EditVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.GenerateVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.LessonCoursewareEditDTO;
import com.yuedu.ydsf.eduConnect.api.dto.RecordVideoTaskDTO;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO;
import com.yuedu.ydsf.eduConnect.api.query.RecordVideoTaskQuery;
import com.yuedu.ydsf.eduConnect.api.query.TimetableQuery;
import com.yuedu.ydsf.eduConnect.api.valid.RecordVideoTaskValidGroup;
import com.yuedu.ydsf.eduConnect.api.valid.TeachingPlanDraftValidGroup.EditPlan;
import com.yuedu.ydsf.eduConnect.api.vo.RecordVideoTaskVO;
import com.yuedu.ydsf.eduConnect.service.RecordVideoTaskNewService;
import com.yuedu.ydsf.eduConnect.service.RecordVideoTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName RecordVideoTaskController
 * @Description 录课任务控制类
 * <AUTHOR>
 * @Date 2024/11/29 16:48:01
 * @Version v0.0.1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/recordVideoTask")
@Tag(description = "ea_record_video_task", name = "录课任务表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class RecordVideoTaskController {

    @Resource
    private RecordVideoTaskService recordVideoTaskService;

    @Resource
    private RecordVideoTaskNewService recordVideoTaskNewService;


    /**
     * 生成录课任务(新版)
     *
     * @return 结果
     */
    @Operation(summary = "生成录课任务(新版)", description = "生成录课任务(新版)")
    @PostMapping("/saveRecordVideoTask")
    public R saveRecordVideoTask() {
        return R.ok(recordVideoTaskNewService.saveRecordVideoTask());
    }


    /**
     * 生成讲师录课任务
     *
     * @param videoTaskDto 生成录课任务Dto
     * @return 结果
     */
    @Operation(summary = "生成录课任务", description = "生成录课任务")
    @GetMapping("/generateRecordVideoTask")
    public R generateRecordVideoTask(@ParameterObject GenerateVideoTaskDto videoTaskDto) {
        return R.ok(recordVideoTaskService.generateRecordVideoTask(videoTaskDto));
    }


    /**
     * 编辑教学计划后修改录课任务
     *
     * @param videoTaskDto 编辑录课任务dto
     * @return 结果
     */
    @Operation(summary = "编辑教学计划后修改录课任务", description = "编辑教学计划后修改录课任务")
    @PostMapping("/editRecordVideoTask")
    public R editRecordVideoTask(@ParameterObject EditVideoTaskDto videoTaskDto) {
        return R.ok(recordVideoTaskService.editRecordVideoTask(videoTaskDto));
    }


    /**
     * 删除教学计划后修改录课任务
     *
     * @param videoTaskDto 删除录课任务dto
     * @return 结果
     */
    @Operation(summary = "删除教学计划后修改录课任务", description = "删除教学计划后修改录课任务")
    @DeleteMapping("/deleteRecordVideoTask")
    public R deleteRecordVideoTask(@ParameterObject DeleteVideoTaskDto videoTaskDto) {
        return R.ok(recordVideoTaskService.deleteRecordVideoTask(videoTaskDto));
    }


    /**
     * 编辑教学计划中某个讲师的排期
     *
     * @param operateMqDTO 编辑录课任务dto
     * @return 结果
     */
    @Operation(summary = "编辑教学计划中某个讲师的排期", description = "编辑教学计划中某个讲师的排期")
    @PostMapping("/editTaskScheduling")
    public R editTaskScheduling(@RequestBody TeachingPlanOperateMqDTO operateMqDTO) {
        return R.ok(recordVideoTaskService.editTaskScheduling(operateMqDTO));
    }


    /**
     * 编辑课程中某课件信息
     *
     * @param coursewareId 课件Id
     * @return 结果
     */
    @Operation(summary = "编辑课程中某课件信息", description = "编辑课程中某课件信息")
    @PostMapping("/editCourseware")
    public R editCourseware(@ParameterObject Long coursewareId) {
        return R.ok(recordVideoTaskService.editCourseware(coursewareId));
    }


    /**
     * 编辑课程中某课件信息
     *
     * @param coursewareEdit 课件列表
     * @return 结果
     */
    @Operation(summary = "编辑课程中某课件信息", description = "编辑课程中某课件信息")
    @PostMapping("/editLessonCourseware")
    public R editLessonCourseware(@RequestBody LessonCoursewareEditDTO coursewareEdit) {
        List<LessonCoursewareEditDTO> coursewareEditList = new ArrayList<>();
        coursewareEditList.add(coursewareEdit);
        return R.ok(recordVideoTaskService.editLessonCourseware(coursewareEditList));
    }


    /**
     * 根据条件查询讲师录课任务列表
     *
     * @param videoTaskDto 录课任务dto
     * @return 结果
     */
    @Operation(summary = "根据条件查询讲师录课任务列表", description = "根据条件查询讲师录课任务列表")
    @PostMapping("/getTaskList")
    @Inner
    public R getTaskList(@RequestBody RecordVideoTaskQuery videoTaskDto) {
        return R.ok(recordVideoTaskService.getRecordTaskList(videoTaskDto));
    }


    /**
     * 根据条件查询讲师录课任务列表
     *
     * @param videoTaskDto 录课任务dto
     * @return 结果
     */
    @Operation(summary = "根据条件查询讲师录课任务列表", description = "根据条件查询讲师录课任务列表")
    @PostMapping("/getAllTaskList")
    public R getAllTaskList(@RequestBody RecordVideoTaskQuery videoTaskDto) {
        return R.ok(recordVideoTaskService.getRecordTaskList(videoTaskDto));
    }

  /**
   * 录课任务管理-获取录课任务列表
   *
   * <AUTHOR>
   * @date 2025/4/22 9:53
   * @param videoTaskDto
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @Operation(summary = "录课任务管理-获取录课任务列表", description = "录课任务管理-获取录课任务列表")
  @GetMapping("/getRecordVideoTaskList")
  public R<IPage<RecordVideoTaskVO>> getRecordVideoTaskList(
      @ParameterObject Page page, @ParameterObject RecordVideoTaskQuery RecordVideoTaskQuery) {
      IPage<RecordVideoTaskVO> iPage = recordVideoTaskNewService.getRecordVideoTaskListPage(page, RecordVideoTaskQuery);
    return R.ok(iPage);
  }

  /**
   * 录课任务管理-匹配已有录课资源
   * <AUTHOR>
   * @date 2025/4/22 14:57
   * @param recordVideoTaskDTO
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @Operation(summary = "录课任务管理-匹配已有录课资源", description = "录课任务管理-匹配已有录课资源")
  @PutMapping("/matchResources")
  public R matchResources(@Validated(RecordVideoTaskValidGroup.MatchResource.class) @RequestBody RecordVideoTaskDTO recordVideoTaskDTO) {
    recordVideoTaskNewService.matchResources(recordVideoTaskDTO);
    return R.ok();
  }
}