package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备音频相关配置
 *
 * <AUTHOR>
 * @date 2024/09/28
 */
@TableName("ss_device_audio_config")
@Data
@EqualsAndHashCode(callSuper = true)
public class SsDeviceAudioConfig extends Model<SsDeviceAudioConfig> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 参数配置类:
     * che.audio.start_debug_recording-本地生成音频文件
     * che.audio.codec.name-打开OPUS和百家云相似,关闭OPUS是声网独特音质
     * che.audio.mute.input.channel-忽略右声道杂音
     * che.audio.current.recording.boostMode-关闭系统音量调整
     * che.audio.enable.agc-关闭增益调整
     * che.audio.input.volume-输入音量
     *
     * @type ss_device_audio_config_parameters
     */
    private String parameters;

    /**
     * 加入频道之后的参数
     */
    private String inClassParameters;

    /**
     * 音量增益
     */
    private Integer adjustRecordingSignalVolume;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 编辑时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifer;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除:0-未删除;1-已删除
     *
     * @type ss_device_audio_config_del_flag
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
}
