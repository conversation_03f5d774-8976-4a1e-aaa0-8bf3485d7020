package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.eduConnect.service.InformationAuthStoreService;
import com.yuedu.ydsf.eduConnect.api.query.InformationAuthStoreQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationAuthStoreDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationAuthStoreVO;

import java.io.Serializable;
import java.util.List;

/**
* 资料授权控制层
*
* <AUTHOR>
* @date  2025/07/22
*/

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/informationAuthStore")
@Tag(description = "ss_information_auth_store" , name = "资料授权" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class InformationAuthStoreController  {


    private final InformationAuthStoreService nformationAuthStoreService;


    /**
    * 资料授权分页查询
    * @param page 分页对象
    * @param informationAuthStoreQuery 资料授权
    * @return R
    */
    @GetMapping("/page" )
    @HasPermission("teaching_informationAuthStore_view")
    @Operation(summary = "分页查询" , description = "资料授权分页查询" )
    public R<IPage<InformationAuthStoreVO>> page(@ParameterObject Page page, @ParameterObject InformationAuthStoreQuery informationAuthStoreQuery) {
        return R.ok(nformationAuthStoreService.page(page, informationAuthStoreQuery));
    }


    /**
    * 通过id查询资料授权
    * @param id id
    * @return R
    */
    @Operation(summary = "通过id查询" , description = "通过id查询资料授权" )
    @GetMapping("/{id}" )
    @HasPermission("teaching_informationAuthStore_view")
    public R<InformationAuthStoreVO> getById(@PathVariable Serializable id) {
        return R.ok(nformationAuthStoreService.getInfoById(id));
    }



    /**
    * 新增资料授权
    * @param informationAuthStoreDTO 资料授权
    * @return R
    */
    @PostMapping
    @SysLog("新增资料授权" )
    @HasPermission("teaching_informationAuthStore_view" )
    @Operation(summary = "新增资料授权" , description = "新增资料授权" )
    public R add(@Validated(V_A.class) @RequestBody InformationAuthStoreDTO informationAuthStoreDTO) {
         return R.ok(nformationAuthStoreService.add(informationAuthStoreDTO));
    }


    /**
    * 修改资料授权
    * @param informationAuthStoreDTO 资料授权
    * @return R
    */
    @PutMapping
    @SysLog("修改资料授权" )
    @HasPermission("teaching_informationAuthStore_edit" )
    @Operation(summary = "修改资料授权" , description = "修改资料授权" )
    public R edit(@Validated(V_E.class) @RequestBody InformationAuthStoreDTO informationAuthStoreDTO) {
         return R.ok(nformationAuthStoreService.edit(informationAuthStoreDTO));
    }



    /**
    * 通过id删除资料授权
    * @param ids id列表
    * @return R
    */
    @DeleteMapping
    @SysLog("通过id删除资料授权" )
    @HasPermission("teaching_informationAuthStore_del" )
    @Operation(summary = "删除资料授权" , description = "删除资料授权" )
    public R delete(@RequestBody  Long[] ids){
         return R.ok(nformationAuthStoreService.removeBatchByIds(CollUtil.toList(ids)));
    }



   /**
   * 导出excel 资料授权表格
   * @param  informationAuthStoreQuery 查询条件
   * @param ids 导出指定ID
   * @return excel 文件流
   */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("teaching_informationAuthStore_export" )
    @Operation(summary = "导出资料授权表格" , description = "导出资料授权表格" )
    public List<InformationAuthStoreVO> export(InformationAuthStoreQuery informationAuthStoreQuery, Long[] ids) {
        return nformationAuthStoreService.export(informationAuthStoreQuery, ids);
    }


}
