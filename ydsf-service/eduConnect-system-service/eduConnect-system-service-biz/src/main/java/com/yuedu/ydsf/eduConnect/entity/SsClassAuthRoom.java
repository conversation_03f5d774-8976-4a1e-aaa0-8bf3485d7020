package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 班级授权教室表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-08 11:23:29
 */
@Data
@TableName("ss_class_auth_room")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "班级授权教室表实体类")
public class SsClassAuthRoom extends Model<SsClassAuthRoom> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 所属门店
	*/
    @Schema(description="所属门店")
    private Long source;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 校管家班级ID
	*/
    @Schema(description="校管家班级ID")
    private String xgjClassId;

	/**
	* 校区ID
	*/
    @Schema(description="校区ID")
    private Long campusId;

	/**
	* 教室ID
	*/
    @Schema(description="教室ID")
    private Long classRoomId;

	/**
	* 教室端设备ID
	*/
    @Schema(description="教室端设备ID")
    private Long deviceId;

	/**
	* 预约时间/授权时间
	*/
    @Schema(description="预约时间/授权时间")
    private LocalDateTime appointmentTime;

	/**
	* 校管家校区ID
	*/
    @Schema(description="校管家校区ID")
    private String xgjCampusId;

	/**
	* 校管家教室ID
	*/
    @Schema(description="校管家教室ID")
    private String xgjClassRoomId;

	/**
	* 课次IDS(代表临时授权课次,多个以英文逗号分隔)
	*/
    @Schema(description="课次IDS(代表临时授权课次,多个以英文逗号分隔)")
    private String classTimeIds;

	/**
	* 预约状态(字典类型: class_appointment_status)
	*/
    @Schema(description="预约状态(字典类型: class_appointment_status)")
    private Integer appointmentStatus;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑者")
    private String modifer;

}
