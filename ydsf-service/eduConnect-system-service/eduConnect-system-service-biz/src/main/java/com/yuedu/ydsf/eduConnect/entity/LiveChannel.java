package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 直播频道 实体类
 *
 * <AUTHOR>
 * @date 2024-11-28 16:56:34
 */
@Data
@TableName("ea_live_channel")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "直播频道实体类")
public class LiveChannel extends Model<LiveChannel> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 教学计划排期ID
	*/
    @Schema(description="教学计划排期ID")
    private Long teachingPlanDetailId;

	/**
	* 频道ID
	*/
    @Schema(description="频道ID")
    private String channelId;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-未删除;1-已删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除: 0-未删除;1-已删除")
    private Integer delFlag;
}
