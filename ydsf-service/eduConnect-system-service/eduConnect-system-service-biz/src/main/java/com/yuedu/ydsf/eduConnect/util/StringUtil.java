package com.yuedu.ydsf.eduConnect.util;

import cn.hutool.core.text.StrFormatter;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import java.text.DecimalFormat;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
public class StringUtil extends org.apache.commons.lang3.StringUtils {

    /**
     * 空字符串
     */
    private static final String NULLSTR = "";

    /**
     * 下划线
     */
    private static final char SEPARATOR = '_';

    /**
     * 星号
     */
    private static final String START = "*";

    /**
     * 获取参数不为空值
     *
     * @param value defaultValue 要判断的value
     * @return value 返回值
     */
    public static <T> T nvl(T value, T defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * * 判断一个Collection是否为空， 包含List，Set，Queue
     *
     * @param coll 要判断的Collection
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(Collection<?> coll) {
        return isNull(coll) || coll.isEmpty();
    }

    /**
     * * 判断一个Collection是否非空，包含List，Set，Queue
     *
     * @param coll 要判断的Collection
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Collection<?> coll) {
        return !isEmpty(coll);
    }

    /**
     * * 判断一个对象数组是否为空
     *
     * @param objects 要判断的对象数组
     *                * @return true：为空 false：非空
     */
    public static boolean isEmpty(Object[] objects) {
        return isNull(objects) || (objects.length == 0);
    }

    /**
     * * 判断一个对象数组是否非空
     *
     * @param objects 要判断的对象数组
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Object[] objects) {
        return !isEmpty(objects);
    }

    /**
     * * 判断一个Map是否为空
     *
     * @param map 要判断的Map
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return isNull(map) || map.isEmpty();
    }

    /**
     * * 判断一个Map是否为空
     *
     * @param map 要判断的Map
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }

    /**
     * * 判断一个字符串是否为空串
     *
     * @param str String
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(String str) {
        return isNull(str) || NULLSTR.equals(str.trim());
    }

    /**
     * * 判断一个字符串是否为非空串
     *
     * @param str String
     * @return true：非空串 false：空串
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * * 判断一个对象是否为空
     *
     * @param object Object
     * @return true：为空 false：非空
     */
    public static boolean isNull(Object object) {
        return object == null;
    }

    /**
     * * 判断一个对象是否非空
     *
     * @param object Object
     * @return true：非空 false：空
     */
    public static boolean isNotNull(Object object) {
        return !isNull(object);
    }

    /**
     * * 判断一个对象是否是数组类型（Java基本型别的数组）
     *
     * @param object 对象
     * @return true：是数组 false：不是数组
     */
    public static boolean isArray(Object object) {
        return isNotNull(object) && object.getClass().isArray();
    }

    /**
     * 去空格
     */
    public static String trim(String str) {
        return (str == null ? "" : str.trim());
    }

    /**
     * 截取字符串
     *
     * @param str   字符串
     * @param start 开始
     * @return 结果
     */
    public static String substring(final String str, int start) {
        if (str == null) {
            return NULLSTR;
        }

        if (start < 0) {
            start = str.length() + start;
        }

        if (start < 0) {
            start = 0;
        }
        if (start > str.length()) {
            return NULLSTR;
        }

        return str.substring(start);
    }

    /**
     * 截取字符串
     *
     * @param str   字符串
     * @param start 开始
     * @param end   结束
     * @return 结果
     */
    public static String substring(final String str, int start, int end) {
        if (str == null) {
            return NULLSTR;
        }

        if (end < 0) {
            end = str.length() + end;
        }
        if (start < 0) {
            start = str.length() + start;
        }

        if (end > str.length()) {
            end = str.length();
        }

        if (start > end) {
            return NULLSTR;
        }

        if (start < 0) {
            start = 0;
        }
        if (end < 0) {
            end = 0;
        }

        return str.substring(start, end);
    }

    /**
     * 格式化文本, {} 表示占位符<br> 此方法只是简单将占位符 {} 按照顺序替换为参数<br> 如果想输出 {} 使用 \\转义 { 即可，如果想输出 {} 之前的 \ 使用双转义符
     * \\\\ 即可<br> 例：<br> 通常使用：format("this is {} for {}", "a", "b") -> this is a for b<br> 转义{}：
     * format("this is \\{} for {}", "a", "b") -> this is \{} for a<br> 转义\： format("this is \\\\{}
     * for {}", "a", "b") -> this is \a for b<br>
     *
     * @param template 文本模板，被替换的部分用 {} 表示
     * @param params   参数值
     * @return 格式化后的文本
     */
    public static String format(String template, Object... params) {
        if (isEmpty(params) || isEmpty(template)) {
            return template;
        }
        return StrFormatter.format(template, params);
    }

    /**
     * 下划线转驼峰命名
     */
    public static String toUnderScoreCase(String str) {
        if (str == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        // 前置字符是否大写
        boolean preCharIsUpperCase = true;
        // 当前字符是否大写
        boolean curreCharIsUpperCase = true;
        // 下一字符是否大写
        boolean nexteCharIsUpperCase = true;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (i > 0) {
                preCharIsUpperCase = Character.isUpperCase(str.charAt(i - 1));
            } else {
                preCharIsUpperCase = false;
            }

            curreCharIsUpperCase = Character.isUpperCase(c);

            if (i < (str.length() - 1)) {
                nexteCharIsUpperCase = Character.isUpperCase(str.charAt(i + 1));
            }

            if (preCharIsUpperCase && curreCharIsUpperCase && !nexteCharIsUpperCase) {
                sb.append(SEPARATOR);
            } else if ((i != 0 && !preCharIsUpperCase) && curreCharIsUpperCase) {
                sb.append(SEPARATOR);
            }
            sb.append(Character.toLowerCase(c));
        }

        return sb.toString();
    }

    /**
     * 是否包含字符串
     *
     * @param str  验证字符串
     * @param strs 字符串组
     * @return 包含返回true
     */
    public static boolean inStringIgnoreCase(String str, String... strs) {
        if (str != null && strs != null) {
            for (String s : strs) {
                if (str.equalsIgnoreCase(trim(s))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 将下划线大写方式命名的字符串转换为驼峰式。如果转换前的下划线大写方式命名的字符串为空，则返回空字符串。 例如：HELLO_WORLD->HelloWorld
     *
     * @param name 转换前的下划线大写方式命名的字符串
     * @return 转换后的驼峰式命名的字符串
     */
    public static String convertToCamelCase(String name) {
        StringBuilder result = new StringBuilder();
        // 快速检查
        if (name == null || name.isEmpty()) {
            // 没必要转换
            return "";
        } else if (!name.contains("_")) {
            // 不含下划线，仅将首字母大写
            return name.substring(0, 1).toUpperCase() + name.substring(1);
        }
        // 用下划线将原始字符串分割
        String[] camels = name.split("_");
        for (String camel : camels) {
            // 跳过原始字符串中开头、结尾的下换线或双重下划线
            if (camel.isEmpty()) {
                continue;
            }
            // 首字母大写
            result.append(camel.substring(0, 1).toUpperCase());
            result.append(camel.substring(1).toLowerCase());
        }
        return result.toString();
    }

    /**
     * 驼峰式命名法 例如：user_name->userName
     */
    public static String toCamelCase(String s) {
        if (s == null) {
            return null;
        }
        s = s.toLowerCase();
        StringBuilder sb = new StringBuilder(s.length());
        boolean upperCase = false;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);

            if (c == SEPARATOR) {
                upperCase = true;
            } else if (upperCase) {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 查找指定字符串是否匹配指定字符串列表中的任意一个字符串
     *
     * @param str  指定字符串
     * @param strs 需要检查的字符串数组
     * @return 是否匹配
     */
    public static boolean matches(String str, List<String> strs) {
        if (isEmpty(str) || isEmpty(strs)) {
            return false;
        }
        for (String testStr : strs) {
            if (matches(str, testStr)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查找指定字符串是否匹配指定字符串数组中的任意一个字符串
     *
     * @param str  指定字符串
     * @param strs 需要检查的字符串数组
     * @return 是否匹配
     */
    public static boolean matches(String str, String... strs) {
        if (isEmpty(str) || isEmpty(strs)) {
            return false;
        }
        for (String testStr : strs) {
            if (matches(str, testStr)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查找指定字符串是否匹配
     *
     * @param str     指定字符串
     * @param pattern 需要检查的字符串
     * @return 是否匹配
     */
    public static boolean matches(String str, String pattern) {
        if (isEmpty(pattern) || isEmpty(str)) {
            return false;
        }

        pattern = pattern.replaceAll("\\s*", ""); // 替换空格
        int beginOffset = 0; // pattern截取开始位置
        int formerStarOffset = -1; // 前星号的偏移位置
        int latterStarOffset = -1; // 后星号的偏移位置

        String remainingURI = str;
        String prefixPattern = "";
        String suffixPattern = "";

        boolean result = false;
        do {
            formerStarOffset = indexOf(pattern, START, beginOffset);
            prefixPattern = substring(pattern, beginOffset,
                formerStarOffset > -1 ? formerStarOffset : pattern.length());

            // 匹配前缀Pattern
            result = remainingURI.contains(prefixPattern);
            // 已经没有星号，直接返回
            if (formerStarOffset == -1) {
                return result;
            }

            // 匹配失败，直接返回
            if (!result) {
                return false;
            }

            if (!isEmpty(prefixPattern)) {
                remainingURI = substringAfter(str, prefixPattern);
            }

            // 匹配后缀Pattern
            latterStarOffset = indexOf(pattern, START, formerStarOffset + 1);
            suffixPattern = substring(pattern, formerStarOffset + 1,
                latterStarOffset > -1 ? latterStarOffset : pattern.length());

            result = remainingURI.contains(suffixPattern);
            // 匹配失败，直接返回
            if (!result) {
                return false;
            }

            if (!isEmpty(suffixPattern)) {
                remainingURI = substringAfter(str, suffixPattern);
            }

            // 移动指针
            beginOffset = latterStarOffset + 1;

        }
        while (!isEmpty(suffixPattern) && !isEmpty(remainingURI));

        return true;
    }

    @SuppressWarnings("unchecked")
    public static <T> T cast(Object obj) {
        return (T) obj;
    }

    /**
     * @param mobile 需要校验的手机号
     * @return java.lang.String
     * @Description 手机号格式校验
     * <AUTHOR>
     * @Date 2021/10/11 14:48
     **/
    public static boolean matchMobile(String mobile) {
        if (mobile == null) {
            return false;
        } else {
            String regex = "^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-8])|(18[0-9])|166|198|199|195|(147))\\d{8}$";
            return Pattern.matches(regex, mobile);
        }
    }

    /**
     * 将请求路径后的拼接的参数转换成Json对象
     *
     * @param paramStr
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/5/19 8:43
     */
    public static JSONObject getJsonStrByQueryUrl(String paramStr) {
        if (StringUtil.isBlank(paramStr)) {
            return new JSONObject();
        }
        if (isjson(paramStr)) {
            JSONObject jsonStr = JSONObject.parseObject(paramStr);
            return jsonStr;
        }
        //String paramStr = "a=a1&b=b1&c=c1";
        String[] params = paramStr.split("&");
        JSONObject obj = new JSONObject();
        for (int i = 0; i < params.length; i++) {
            String[] param = params[i].split("=");
            if (param.length >= 2) {
                String key = param[0];
                String value = param[1];
                for (int j = 2; j < param.length; j++) {
                    value += "=" + param[j];
                }
                try {
                    obj.put(key, value);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
        return obj;
    }

    /**
     * 判断字符串是不是Json类型
     *
     * @param str
     * @return boolean
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/5/23 11:07
     */
    private static boolean isjson(String str) {
        try {
            JSONObject.parse(str);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 字符串金额转换为整型（乘以100）
     *
     * @param money
     * @return Integer
     * @author: Bobbie.Qi
     * @time:2017年2月4日
     */
    public static Integer moneyStrToInt(String money) {
        if (StringUtil.isNoneBlank(money)) {
            if (money.contains(".")) {
                String num = new DecimalFormat("#.00").format(Double.parseDouble(money));
                return Integer.valueOf(num.replace(".", ""));
            } else {
                return Integer.valueOf(money + "00");
            }
        } else {
            return 0;
        }
    }


    /**
     * 根据findStr在data查找第count次出现的下标
     *
     * @param data    字符串标本
     * @param findStr 要查找的字符串
     * @param count   次数
     * @return
     */
    public static int getCharIndex(String data, String findStr, int count) {
        int indexCount = 0;
        int resultIndex = -1;
        int length = findStr.length();
        char[] findCharArray = findStr.toCharArray();
        char[] dataArray = data.toCharArray();
        for (int i = 0; i < dataArray.length; i++) {
            char dataChar = dataArray[i];
            char findChar = findCharArray[0];
            if (findChar == dataChar) {
                String match = "";
                for (int i1 = i; i1 < i + length; i1++) {
                    match += dataArray[i1];
                }
                if (findStr.equals(match)) {
                    indexCount++;
                    if (count == indexCount) {
                        resultIndex = i;
                        break;
                    }
                }
            }
        }
        return resultIndex;
    }

    /**
     * String逗号拼接的转换成IN查询格式的
     *
     * @param str
     * @return java.lang.String
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2023/2/17 10:38
     */
    public static String convertStringToIn(String str) {
        String[] strs = str.split(",");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < strs.length; i++) {
            sb.append("'")
                .append(strs[i])
                .append("'");
            if (i < strs.length - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    /**
     * 将逗号分隔字符串去除指定字符
     *
     * @param originalString  原始逗号分割的字符串
     * @param elementToRemove 要去除的元素
     * @return String
     * <AUTHOR>
     * @date 2024年03月27日 17时39分
     */
    public static String removeStr(String originalString, String elementToRemove) {

        // 使用逗号分割字符串
        String[] elements = originalString.split(",");

        // 创建一个StringBuilder来构建新的字符串
        StringBuilder newString = new StringBuilder();
        boolean first = true; // 用于处理第一个元素前的逗号

        // 遍历分割后的元素数组
        for (String element : elements) {
            // 去除元素前后的空格（如果需要）
            element = element.trim();

            // 检查元素是否等于要去除的元素
            if (!element.equals(elementToRemove)) {
                // 如果不是第一个元素，则添加逗号
                if (!first) {
                    newString.append(",");
                }
                // 添加元素到新的字符串中
                newString.append(element);
                first = false; // 设置标志为false，表示已经不是第一个元素了
            }
        }

        // 输出新的字符串
        String modifiedString = newString.toString();

        return modifiedString;
    }

    /**
     * 获取链接除域名之后的path 地址 <pre/> 例：
     * https://example.com/606a2533a81443b38ceaccc131d6d7e0/202404/6fc9efda731b4a6eb8b14e3832566cea/cc2ebf3eab4d3d9215db96ba8dc0d922_6fc9efda-731b-4a6e-b8b1-4e3832566cea_0.mp4
     * 返回：606a2533a81443b38ceaccc131d6d7e0/202404/6fc9efda731b4a6eb8b14e3832566cea/cc2ebf3eab4d3d9215db96ba8dc0d922_6fc9efda-731b-4a6e-b8b1-4e3832566cea_0.mp4
     *
     * @param str
     * @return java.lang.String
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2024/4/30 10:21
     */
    public static String getUrlPath(String url) {
         // 定义通用的正则表达式
         String regex = "^https?://(?:www\\\\.)?[^/]+/(.*)$";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        // 创建 Matcher 对象
        Matcher matcher = pattern.matcher(url);
        // 查找匹配的字符串
        if (matcher.find()) {
            // 获取匹配到的内容（不包括正则表达式中的分组内容）
            return matcher.group(2);
        }
        return "";
    }

    /**
     * string字符串转换map (例: id=1&name=张张)
     * @param queryString
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/3/3 10:37
     */
    public static Map<String, String> stringParseMap(String queryString) {
        Map<String, String> result = new HashMap<>();
        if (queryString == null || queryString.isEmpty()) {
            return result;
        }

        String[] pairs = queryString.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            if (idx > 0) {
                String key = pair.substring(0, idx);
                String value = pair.substring(idx + 1);
                result.put(key, value);
            } else {
                // 如果没有等号，可以根据需求决定如何处理，这里简单地将整个部分作为键，值为null
                result.put(pair, null);
            }
        }

        return result;
    }


}
