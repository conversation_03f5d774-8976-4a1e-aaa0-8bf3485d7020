package com.yuedu.ydsf.eduConnect.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.eduConnect.api.dto.ClassAuthRoomDTO;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.entity.SsClass;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeStudent;
import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.AgoraProperties;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 * 课次处理层
 *
 * @author: KL
 * @date: 2024/10/15
 **/
public interface SsClassTimeManager {


    /**
     *  转换数据
     *
     * <AUTHOR>
     * @date 2024年10月15日
     * @param page
     */
    IPage<SsClassTimeVO> fillData(IPage<SsClassTimeVO> page);

    /**
     * 获取RTMtoken
     *
     * @param roomUuid
     * @param s
     * @param aShort
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/16 10:34
     */
    String getAgoraRtmToken(String roomUuid, String s, Short aShort);

    /**
     *  获取授权教室列表
     *
     * <AUTHOR>
     * @date 2024年10月16日 13时44分
     * @param ssClassTime
     */
    List<SsDeviceVO> selectAuthClassRoom(SsClassTime ssClassTime);

    /**
     * 返回项目所需要用到的线程池
     *
     * @return java.util.concurrent.Executor
     * <AUTHOR>
     * @date 2024/10/16 10:36
     */
    Executor getAsyncExecutor();

    /**
     * 通过班级ID获取班级信息
     *
     * @param classId
     * @return com.yuedu.ydsf.eduConnect.entity.SsClass
     * <AUTHOR>
     * @date 2024/10/16 11:30
     */
    SsClass getClassById(Long classId);

    /**
     * 获取课程状态
     * @param ssClassTimeVO
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:40
     */
    void setClassTimeAttendStateAndData(SsClassTimeVO ssClassTimeVO);


    /**
     * 同步声网创建房间
     *
     * @param ssClassTimeDto
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:06
     */
    void syncAgoraClassRoom(SsClassTimeDTO ssClassTimeDto);

    /**
     * 创建课次
     *
     * @param newClassTime
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:08
     */
    void saveNewClassTime(SsClassTime newClassTime);

    /**
     * 获取老师信息
     *
     * @param ssClassTime
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:11
     */
    LecturerVO getLecturerInfoById(Long lecturerId);

    /**
     * 查询班级下指定授权设备,指定状态的课次信息
     * <AUTHOR>
     * @date 2024/10/16 14:20
     * @param classTimeDto
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
     */
    List<SsClassTimeVO> selectDeviceClassTimeByClassId(SsClassTimeDTO classTimeDto);


    /**
     * 查询班级已添加学生信息
     *
     * @param id
     * @param campusIdList
     * @return java.util.List<com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent>
     * <AUTHOR>
     * @date 2024/10/16 14:27
     */
    List<SsClassAuthRoomStudent> ssClassAuthRoomStudentSelectList(Long id, List<Long> campusIdList);


    /**
     * 单次授权更新对应的字段
     *
     * @param ssClassAuthRoom
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:31
     */
    void ssClassAuthRoomupdateById(SsClassAuthRoom ssClassAuthRoom);

    /**
     * 课次授权教室保存
     *
     * @param ssClassTimeAuthRoom
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:37
     */
    void ssClassTimeAuthRoomSave(SsClassTimeAuthRoom ssClassTimeAuthRoom);

    /**
     * 保存课次学生
     *
     * @param saveSsClassTimeStudentList
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:46
     */
    void ssClassTimeStudentSaveBatch(List<SsClassTimeStudent> saveSsClassTimeStudentList);

    /**
     * 通过班级ID获取班级信息
     *
     * @param classId
     * @return com.yuedu.ydsf.eduConnect.entity.SsClass
     * <AUTHOR>
     * @date 2024/10/16 15:13
     */
    SsClass ClassGetById(Long classId);

  /**
   * 根据课次id删除课次授权教室
   *
   * <AUTHOR>
   * @date 2024/10/18 14:37
   * @param ids
   * @return void
   */
  void deleteClassTimeAuthRoomByIds(Long[] ids);

  /**
   * 根据课次id删除课次授权教室 下得学生
   *
   * <AUTHOR>
   * @date 2024/10/18 14:41
   * @param ids
   * @return void
   */
  void deleteClassTimeStudent(Long[] ids);

  /**
   * 根据id更新班级授权教室信息
   *
   * <AUTHOR>
   * @date 2024/10/18 14:49
   * @param ssClassAuthRoom
   * @return void
   */
  void classAuthRoomUpdateById(SsClassAuthRoom ssClassAuthRoom);

  /**
   * 根据id获取班级信息
   *
   * <AUTHOR>
   * @date 2024/10/18 14:53
   * @param ids
   * @return java.util.Map<java.lang.Long,com.yuedu.ydsf.eduConnect.entity.SsClass>
   */
  Map<Long, SsClass> getClassMapByIds(Set<Long> collect);

  /**
   * 根据id获取课次信息
   *
   * <AUTHOR>
   * @date 2024/10/18 15:08
   * @param ids
   * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
   */
  List<SsClassTimeVO> selectDeviceClassTimeByIds(List<Long> ids);

  /**
   *  批量更新班级授权教室信息
   *
   * <AUTHOR>
   * @date 2024/10/18 15:13
   * @param updateAuthRoomList
   * @return void
   */
  void updateBatchClassAuthRoom(List<SsClassAuthRoom> updateAuthRoomList);

    /**
     *  修改授权课次
     *
     * <AUTHOR>
     * @date 2024年10月17日 09时39分
     * @param ssClassTime
     */
    boolean updateAuthClassTimeRoom(SsClassTime ssClassTime, List<ClassAuthRoomDTO> newAuthClassRoomList);

    /**
     * 获取监课链接
     * <AUTHOR>
     * @date 2024/10/21 13:45
     * @param
     * @return java.lang.String
     */
    SsClassTimeVO getSupervisionUrl(SsClassTimeQuery classTimeQuery);

    /**
     * 通过ID 获取课次详情
     * <AUTHOR>
     * @date 2024/10/21 14:06
     * @param id
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO
     */
    SsClassTimeVO getClassTimeInfo(Long id);

  /**
   * 获取教室日历
   *
   * <AUTHOR>
   * @date 2024/10/24 14:34
   * @param classTimeQuery
   * @return com.baomidou.mybatisplus.core.metadata.IPage
   */
  IPage classRoomFreeTimeCalendar(SsClassTimeQuery classTimeQuery);


    /**
     *  根据校区id获得课次集合
     *
     * <AUTHOR>
     * @date 2024年10月28日 13时46分
     */
    List<Long> getClassTimeIdsByCampulsId(String campusId);


    AgoraProperties getAgoraProperties();

  /**
   * 根据选择的直播间教室查询对应的设备
   *
   * <AUTHOR>
   * @date 2024/10/30 9:26
   * @param ssClassTimeDto
   * @return void
   */
  void updateLiveDevice(SsClassTimeDTO ssClassTimeDto);

  int getUpdateXiaogjPushTask(SsClassTime classTime, List<ClassAuthRoomDTO> newAuthClassRoomList,
        SsClass ssClass);
}
