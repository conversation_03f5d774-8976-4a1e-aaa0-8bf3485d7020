package com.yuedu.ydsf.eduConnect.manager;

import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.DeleteCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.SsPushXiaogjEventReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.UpdateCourseReq;

import java.util.List;

/**
 * @author: zhangchuanfu
 * @date: 2024/10/14
 **/
public interface XiaoGuanJiaService {

    /**
     * 双师排课推送校管家消息队列公共方法
     * <AUTHOR>
     * @date 2024年04月28日 14时19分
     * @param classCourseReq 班级排课信息
     */
    SsPushXiaogjEventReq<ClassCourseReq> ssPushXiaogjMessage(ClassCourseReq classCourseReq);

    /**
     * 双师排课推送校管家消息队列公共方法
     * <AUTHOR>
     * @date 2024年04月28日 14时19分
     * @param classCourseReqJson 班级排课信息
     */
    boolean ssPushXiaogjMessage(String msgId, String classCourseReqJson);

    /**
     * 同步校管家班级参数封装
     * @param classType 班级操作类型
     * @param xgjClassId 校管家班级ID
     * @param xgjCampusId 校管家校区ID
     * @param className 班级名称
     * @param createCourseReqList 班级下创建排课信息
     * @param updateCourseReqList 班级下更新排课信息
     * @param deleteCourseReqList 班级下删除课次信息
     * @return com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq
     * <AUTHOR>
     * @date 2024/10/17 15:51
     */
    ClassCourseReq syncXiaogjClassParam(Integer classType,
                                        String xgjClassId,
                                        String xgjCampusId,
                                        String className,
                                        List<CreateCourseReq> createCourseReqList,
                                        List<UpdateCourseReq> updateCourseReqList,
                                        List<DeleteCourseReq> deleteCourseReqList
    );

    /**
     * 同步校管家创建课次参数封装
     * @param xgjClassId 校管家班级ID
     * @param cStartTime 课次开始时间
     * @param cEndTime 课次结束时间
     * @param courseDataList 课次详情
     * @return java.util.List<com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq>
     * <AUTHOR>
     * @date 2024/10/17 16:22
     */
    List<CreateCourseReq> syncXiaogjCreateClassTimeParam(String xgjClassId,
                                                         String cStartTime,
                                                         String cEndTime,
                                                         List<CreateCourseReq.CourseData> courseDataList);

    /**
     * 同步校管家创建课次详情参数封装
     * @param cWeekday 周几
     * @param cWeekStartTime 上课开始时间点 HH:mm:ss
     * @param cWeekEndTime 上课结束时间点 HH:mm:ss
     * @param cDate 上课日期 yyyy-MM-dd
     * @param xgjClassRoomId 校管家教室ID
     * @param cStartTime 上课开始时间 yyyy-MM-dd HH:mm:ss
     * @param cEndTime 上课结束时间 yyyy-MM-dd HH:mm:ss
     * @param xgjClassTimeId 校管家课次ID
     * @param xgjLecturerId 校管家主讲ID
     * @return java.util.List<com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq.CourseData>
     * <AUTHOR>
     * @date 2024/10/17 16:35
     */
    List<CreateCourseReq.CourseData> syncXiaogjCreateClassTimeCourseDateParam(int cWeekday,
                                                                              String cWeekStartTime,
                                                                              String cWeekEndTime,
                                                                              String cDate,
                                                                              String xgjClassRoomId,
                                                                              String cStartTime,
                                                                              String cEndTime,
                                                                              String xgjClassTimeId,
                                                                              String xgjLecturerId);

    /**
     * 同步校管家删除课次参数封装
     * @param xgjClassId 校管家班级ID
     * @param xgjClassTimeId 校管家课次ID
     * @return java.util.List<com.yuedu.ydsf.eduConnect.system.proxy.dto.DeleteCourseReq>
     * <AUTHOR>
     * @date 2024/10/17 16:08
     */
    List<DeleteCourseReq> syncXiaogjDeleteClassTimeParam(String xgjClassId,
                                                         String xgjClassTimeId);

}
