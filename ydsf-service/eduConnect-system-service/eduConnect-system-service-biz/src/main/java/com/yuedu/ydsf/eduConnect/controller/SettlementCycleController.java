package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.query.SettlementCycleQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SettlementCycleVO;
import com.yuedu.ydsf.eduConnect.entity.SettlementCycle;
import com.yuedu.ydsf.eduConnect.service.SettlementCycleService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 结算周期 控制类
 *
 * <AUTHOR>
 * @date 2025-04-21 15:28:49
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/SettlementCycle")
@Tag(description = "ea_settlement_cycle", name = "结算周期管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SettlementCycleController {

    private final SettlementCycleService settlementCycleService;

    /**
     * 分页查询
     *
     * @param page            分页对象
     * @param settlementCycle 结算周期
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("edusystem_SettlementCycle_view")
    public R getSettlementCyclePage(@ParameterObject Page page, @ParameterObject SettlementCycle settlementCycle) {
        LambdaQueryWrapper<SettlementCycle> wrapper = Wrappers.lambdaQuery();
        return R.ok(settlementCycleService.page(page, wrapper));
    }


    /**
     * 通过条件查询结算周期
     *
     * @param settlementCycleQuery 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询", description = "通过条件查询对象")
    @GetMapping("/list")
    @HasPermission("edusystem_SettlementCycle_view")
    public R getList(@ParameterObject SettlementCycleQuery settlementCycleQuery) {
        log.info("查询结算周期:{}", settlementCycleQuery);
        return R.ok(settlementCycleService.listAll());
    }

    /**
     * 新增结算周期
     *
     * @param settlementCycle 结算周期
     * @return R
     */
    @Operation(summary = "新增结算周期", description = "新增结算周期")
    @PostMapping("/add")
    @HasPermission("edusystem_SettlementCycle_add")
    public R save(@RequestBody SettlementCycle settlementCycle) {
        return R.ok(settlementCycleService.save(settlementCycle));
    }

    /**
     * 修改结算周期
     *
     * @param settlementCycleVO 结算周期
     * @return R
     */
    @Operation(summary = "修改结算周期", description = "修改结算周期")
    @PutMapping("/edit")
    @HasPermission("edusystem_SettlementCycle_edit")
    @Idempotent(key = "'sc_edit_'.concat(#settlementCycleVO.id)", expireTime = 3, info = "请勿重复操作")
    public R updateById(@RequestBody SettlementCycleVO settlementCycleVO) {
        log.info("修改结算周期:{}", settlementCycleVO);
        settlementCycleService.updateById(settlementCycleVO);
        return R.ok();
    }

    /**
     * 锁定或解锁考勤
     */
    @Operation(summary = "锁定或解锁考勤", description = "锁定或解锁考勤")
    @PutMapping("/lockCheckin")
    @HasPermission("edusystem_SettlementCycle_edit")
    @Idempotent(key = "'sc_edit_'.concat(#settlementCycle.id)", expireTime = 3, info = "请勿重复操作")
    public R lockCheckin(@RequestBody SettlementCycle settlementCycle) {
        log.info("锁定或解锁考勤:{}", settlementCycle);
        if (Objects.isNull(settlementCycle.getId())) {
            return R.failed("id不能为空");
        }
        if (Objects.isNull(settlementCycle.getCheckinLocked())) {
            return R.failed("锁定状态不能为空");
        }
        settlementCycleService.lockOrUnlockCheckin(settlementCycle);
        return R.ok();
    }

    /**
     * 通过id删除结算周期
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除结算周期", description = "通过id删除结算周期")
    @DeleteMapping("/delete")
    @HasPermission("edusystem_SettlementCycle_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(settlementCycleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @param settlementCycle 查询条件
     * @param ids             导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_SettlementCycle_export")
    public List<SettlementCycle> exportExcel(SettlementCycle settlementCycle, Long[] ids) {
        return settlementCycleService.list(Wrappers.lambdaQuery(settlementCycle).in(ArrayUtil.isNotEmpty(ids), SettlementCycle::getId, ids));
    }

    /**
     * 导入excel 表
     *
     * @param settlementCycleList 对象实体列表
     * @param bindingResult       错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_SettlementCycle_export")
    public R importExcel(@RequestExcel List<SettlementCycle> settlementCycleList, BindingResult bindingResult) {
        return R.ok(settlementCycleService.saveBatch(settlementCycleList));
    }

    /**
     * 通过条件查询结算周期
     *
     * @param settlementCycleQuery 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "查询取消考勤周期", description = "查询取消考勤周期")
    @PostMapping("/listAll")
    @Inner
    public R<List<SettlementCycleVO>> getAllList(@RequestBody SettlementCycleQuery settlementCycleQuery) {
        log.info("查询取消考勤周期:{}", settlementCycleQuery);
        return R.ok(settlementCycleService.listAll());
    }
}
