package com.yuedu.ydsf.eduConnect.controller;

import com.yuedu.ydsf.eduConnect.service.CourseVodVideoService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 点播课程视频 控制类
 *
 * <AUTHOR>
 * @date 2024-12-02 11:08:51
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/CourseVodVideo" )
@Tag(description = "ea_course_vod_video" , name = "点播课程视频管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseVodVideoController {

    private final  CourseVodVideoService courseVodVideoService;

}
