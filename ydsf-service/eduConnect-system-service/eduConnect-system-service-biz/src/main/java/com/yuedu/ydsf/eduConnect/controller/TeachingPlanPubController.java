package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanPubDTO;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanToLiveRoomDTO;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanPubQuery;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanToLiveRoomVO;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanPub;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanPubService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 已发布的教学计划表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-29 09:26:30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/teachingPlanPub")
@Tag(description = "ea_teaching_plan_pub" , name = "已发布的教学计划表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TeachingPlanPubController {
    @Resource
    private final  TeachingPlanPubService teachingPlanPubService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param teachingPlanPub 已发布的教学计划表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @HasPermission("admin_TeachingPlanPub_view")
    public R getTeachingPlanPubPage(@ParameterObject Page page, @ParameterObject TeachingPlanPub teachingPlanPub) {
        LambdaQueryWrapper<TeachingPlanPub> wrapper = Wrappers.lambdaQuery();
        return R.ok(teachingPlanPubService.page(page, wrapper));
    }


    /**
     * 通过条件查询已发布的教学计划表
     * @param teachingPlanPub 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("admin_TeachingPlanPub_view")
    public R getDetails(@ParameterObject TeachingPlanPub teachingPlanPub) {
        return R.ok(teachingPlanPubService.list(Wrappers.query(teachingPlanPub)));
    }

    /**
     * 新增已发布的教学计划表
     * @param teachingPlanPub 已发布的教学计划表
     * @return R
     */
    @Operation(summary = "新增已发布的教学计划表" , description = "新增已发布的教学计划表" )
    @PostMapping("/add")
    @HasPermission("admin_TeachingPlanPub_add")
    public R save(@RequestBody TeachingPlanPub teachingPlanPub) {
        return R.ok(teachingPlanPubService.save(teachingPlanPub));
    }

    /**
     * 修改已发布的教学计划表
     * @param teachingPlanPub 已发布的教学计划表
     * @return R
     */
    @Operation(summary = "修改已发布的教学计划表" , description = "修改已发布的教学计划表" )
    @PutMapping("/edit")
    @HasPermission("admin_TeachingPlanPub_edit")
    public R updateById(@RequestBody TeachingPlanPub teachingPlanPub) {
        return R.ok(teachingPlanPubService.updateById(teachingPlanPub));
    }

    /**
     * 通过id删除已发布的教学计划表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除已发布的教学计划表" , description = "通过id删除已发布的教学计划表" )
    @DeleteMapping("/delete")
    @HasPermission("admin_TeachingPlanPub_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(teachingPlanPubService.removeBatchByIds(CollUtil.toList(ids)));
    }

  /**
   * 根据教学计划发布表的id集合批量获取对应的信息
   *
   * <AUTHOR>
   * @date 2024/12/19 10:19
   * @param teachingPlanIds
   * @return
   *     com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>>
   */
  @Inner
  @PostMapping("/getTeachingPlanPubByTeachingPlanIds")
  @SysLog("根据教学计划发布表的id集合批量获取对应的信息")
  public R<List<TeachingPlanPubVO>> getTeachingPlanPubByTeachingPlanIds(
      @RequestBody List<Long> teachingPlanIds) {
    return R.ok(teachingPlanPubService.getTeachingPlanPubByTeachingPlanIds(teachingPlanIds));
  }
    /**
     * 导出excel 表格
     * @param teachingPlanPub 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("admin_TeachingPlanPub_export")
    public List<TeachingPlanPub> exportExcel(TeachingPlanPub teachingPlanPub,Long[] ids) {
        return teachingPlanPubService.list(Wrappers.lambdaQuery(teachingPlanPub).in(ArrayUtil.isNotEmpty(ids), TeachingPlanPub::getId, ids));
    }

    /**
     * 导入excel 表
     * @param teachingPlanPubList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("admin_TeachingPlanPub_export")
    public R importExcel(@RequestExcel List<TeachingPlanPub> teachingPlanPubList, BindingResult bindingResult) {
        return R.ok(teachingPlanPubService.saveBatch(teachingPlanPubList));
    }

    /**
     * 查询教学计划详情
     * @param teachingPlanPubDTO
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/12/11 19:00
     */
    @PostMapping("/getTeachingPlanPubDetail")
    @Inner
    public R<TeachingPlanPubVO> getTeachingPlanPubDetail(@RequestBody TeachingPlanPubDTO teachingPlanPubDTO) {

        TeachingPlanPub teachingPlanPub = teachingPlanPubService.getOne(Wrappers.lambdaQuery(TeachingPlanPub.class)
            .eq(TeachingPlanPub::getTeachingPlanId, teachingPlanPubDTO.getTeachingPlanId())
        );

        TeachingPlanPubVO teachingPlanPubVO = new TeachingPlanPubVO();
        BeanUtils.copyProperties(teachingPlanPub, teachingPlanPubVO);
        return R.ok(teachingPlanPubVO);
    }

    /**
     * 根据教学计划id查询直播间计划名称
     *
     * @param teachingPlanToLiveRoomDTO 教学计划id集合
     * @return 直播间计划名称集合
     */
    @Inner
    @PostMapping("/teachingPlanToLiveRoom")
    public R<List<TeachingPlanToLiveRoomVO>> getTeachingPlanToLiveRoom(@RequestBody TeachingPlanToLiveRoomDTO teachingPlanToLiveRoomDTO) {
        return R.ok(teachingPlanPubService.getTeachingPlanToLiveRoom(teachingPlanToLiveRoomDTO.getTeachingPlanIdList()));
    }


    /**
     * 通过指定条件查询教学计划列表
     *
     * @param teachingPlanQuery 条件
     * @return R
     */
    @Operation(summary = "通过指定条件查询教学计划列表", description = "通过指定条件查询教学计划列表")
    @PostMapping("/getTeachingPlanList")
    @Inner(value = false)
    public R getTeachingPlanList(@RequestBody TeachingPlanQuery teachingPlanQuery) {
        return R.ok(teachingPlanPubService.getTeachingPlanList(teachingPlanQuery));
    }

    /**
     * 查询双师后台约点播课-选择课程列表
     * @param teachingPlanPubQuery
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>>
     * <AUTHOR>
     * @date 2025/1/20 9:26
     */
    @Operation(summary = "查询双师后台约点播课-选择课程列表", description = "查询双师后台约点播课-选择课程列表")
    @GetMapping("/getTeachingPlanCourseList")
    public R<List<TeachingPlanPubVO>> getTeachingPlanCourseList(@ParameterObject TeachingPlanPubQuery teachingPlanPubQuery) {
        List<TeachingPlanPubVO> teachingPlanCourseList = teachingPlanPubService.getTeachingPlanCourseList(teachingPlanPubQuery);
        return R.ok(teachingPlanCourseList);
    }

    /**
     * 根据课程类型和门店ID查询授权过的教学计划列表（含历史）
     */
    @Operation(summary = "根据课程类型和门店ID查询授权过的教学计划列表（含历史）", description = "根据课程类型和门店ID查询授权过的教学计划列表（含历史）")
    @GetMapping("/inner/getIdListByStoreIdAndCourseType")
    @Inner
    public R<List<Long>> getByStoreIdAndCourseType(
            @RequestParam("storeId") Long storeId, @RequestParam("courseType") Integer courseType) {
        List<Long> teachingPlanIdList = teachingPlanPubService.getByStoreIdAndCourseType(storeId, courseType);
        return R.ok(teachingPlanIdList);
    }

}
