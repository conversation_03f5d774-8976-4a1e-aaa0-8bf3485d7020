package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.file.core.FileProperties;
import com.yuedu.ydsf.common.file.core.FileTemplate;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.dto.SsScreenshotDetailDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsScreenshotDetailQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsScreenshotDetailVO;
import com.yuedu.ydsf.eduConnect.constant.RecognitionStatusEnum;
import com.yuedu.ydsf.eduConnect.entity.SsScreenshotDetail;
import com.yuedu.ydsf.eduConnect.mapper.SsScreenshotDetailMapper;
import com.yuedu.ydsf.eduConnect.service.SsScreenshotDetailService;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.BaiduAttributeConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAttrReq;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAttrResp;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAttrResp.PersonAge;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAttrResp.PersonHuman;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.BaiduApi;
import com.yuedu.ydsf.eduConnect.system.proxy.util.Base64Util;
import jakarta.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * 双师截图明细表服务层
 *
 * <AUTHOR>
 * @date 2024/10/11
 */
@Slf4j
@Service
@RefreshScope
public class SsScreenshotDetailServiceImpl extends ServiceImpl<SsScreenshotDetailMapper, SsScreenshotDetail> implements SsScreenshotDetailService {

    @Resource
    private FileProperties fileProperties;

    @Autowired
    private FileTemplate fileTemplate;

    @Autowired
    private BaiduApi baiduApi;

    @Value("${baidu.rate:0}")
    private BigDecimal rate;



    /**
     * 双师截图明细表分页查询
     *
     * @param page                    分页对象
     * @param ssScreenshotDetailQuery 双师截图明细表
     * @return IPage 分页结果
     */
    @Override
    public IPage<SsScreenshotDetailVO> page(Page page,
                                            SsScreenshotDetailQuery ssScreenshotDetailQuery)
    {
        Page<SsScreenshotDetail> selectPage = baseMapper.selectPage(page, Wrappers.<SsScreenshotDetail>lambdaQuery()
                                                                                  .eq(SsScreenshotDetail::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                                                                                  .eq(SsScreenshotDetail::getClassTimeId, ssScreenshotDetailQuery.getClassTimeId()));

        return selectPage.convert(this::convertToVO);
    }

  /**
   * 分页查询将就实体转成VO
   *
   * @param ssScreenshotDetail
   * @return com.yuedu.ydsf.eduConnect.api.vo.SsScreenshotDetailVO
   * <AUTHOR>
   * @date 2024/10/11 13:58
   */
  private SsScreenshotDetailVO convertToVO(SsScreenshotDetail ssScreenshotDetail) {
    SsScreenshotDetailVO ssScreenshotDetailVO = new SsScreenshotDetailVO();
    BeanUtils.copyProperties(ssScreenshotDetail, ssScreenshotDetailVO);
    // 处理全路径
    ssScreenshotDetailVO.setResourcesPath(
        String.format(
            "%s/%s/%s",
            fileProperties.getOss().getCustomDomain(),
            fileProperties.getOss().getPrefix(),
            ssScreenshotDetail.getResourcesPath()));
    return ssScreenshotDetailVO;
  }

    /**
     * 新增双师截图明细表
     *
     * @param ssScreenshotDetailDTO 双师截图明细表
     * @return boolean 执行结果
     */
    @Override
    public boolean add(SsScreenshotDetailDTO ssScreenshotDetailDTO) {
        SsScreenshotDetail ssScreenshotDetail = new SsScreenshotDetail();
        BeanUtils.copyProperties(ssScreenshotDetailDTO, ssScreenshotDetail);
        return save(ssScreenshotDetail);
    }

    /**
     * 修改双师截图明细表
     *
     * @param ssScreenshotDetailDTO 双师截图明细表
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(SsScreenshotDetailDTO ssScreenshotDetailDTO) {
        SsScreenshotDetail ssScreenshotDetail = new SsScreenshotDetail();
        BeanUtils.copyProperties(ssScreenshotDetailDTO, ssScreenshotDetail);
        return updateById(ssScreenshotDetail);
    }

    /**
     * 导出excel 双师截图明细表表格
     *
     * @param ssScreenshotDetailQuery 查询条件
     * @param ids                     导出指定ID
     * @return List<SsScreenshotDetailVO> 结果集合
     */
    @Override
    public List<SsScreenshotDetailVO> export(SsScreenshotDetailQuery ssScreenshotDetailQuery,
                                             Long[] ids)
    {
        return list(Wrappers.<SsScreenshotDetail>lambdaQuery()
                            .in(ArrayUtil.isNotEmpty(ids), SsScreenshotDetail::getId, ids)).stream()
                                                                                           .map(entity -> {
                                                                                               SsScreenshotDetailVO ssScreenshotDetailVO = new SsScreenshotDetailVO();
                                                                                               BeanUtils.copyProperties(entity, ssScreenshotDetailVO);
                                                                                               return ssScreenshotDetailVO;
                                                                                           })
                                                                                           .toList();
    }

    /**
     * 保存截图
     *
     * @param ssScreenshotDetailDTO
     * @return void
     * <AUTHOR>
     * @date 2024/10/11 10:37
     */
    @Override
    public void saveScreenshot(SsScreenshotDetailDTO ssScreenshotDetailDTO) {
        // 尝试更新记录
        if (!updateScreenshotDetail(ssScreenshotDetailDTO)) {
            add(ssScreenshotDetailDTO);
        }
    }

    /**
     * 插入之前的判断操作
     *
     * @param dto
     * @return boolean
     * <AUTHOR>
     * @date 2024/10/11 10:42
     */
    private boolean updateScreenshotDetail(SsScreenshotDetailDTO dto) {
        // 更新逻辑封装，保持主方法的清晰
        return update(Wrappers.lambdaUpdate(SsScreenshotDetail.class)
                              .eq(SsScreenshotDetail::getClassTimeId, dto.getClassTimeId())
                              .eq(SsScreenshotDetail::getDeviceId, dto.getDeviceId())
                              .set(SsScreenshotDetail::getScreenshotTime, LocalDateTime.now())
                              .set(SsScreenshotDetail::getResourcesName, dto.getResourcesName())
                              .set(SsScreenshotDetail::getResourcesPath, dto.getResourcesPath()));
    }


    @Override
    public void pictureRecognitionHandle() {
        list(Wrappers.<SsScreenshotDetail>lambdaQuery().ne(SsScreenshotDetail::getRecognitionStatus,
            RecognitionStatusEnum.RECOGNITION_STATUS_1.code)).forEach(s->{

            if(StringUtils.isBlank(s.getResourcesPath())){
                SsScreenshotDetail SsScreenshotDetail = new SsScreenshotDetail();
                SsScreenshotDetail.setId(s.getId());
                SsScreenshotDetail.setRecognitionStatus(RecognitionStatusEnum.RECOGNITION_STATUS_1.code);
                updateById(SsScreenshotDetail);
                return;
            }

            String fileName = String.format("%s/%s",fileProperties.getOss().getPrefix(),s.getResourcesPath());

            try(InputStream inputStream = fileTemplate.getObject(fileProperties.getBucketName(),fileName).getObjectContent()){
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                // 读取文件内容到字节数组。
                byte[] readBuffer = new byte[1024*1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(readBuffer)) != -1) {
                    byteArrayOutputStream.write(readBuffer, 0, bytesRead);
                }
                BodyAttrResp bodyAttrResp = baiduApi.bodyAttr(BodyAttrReq.builder()
                    .image(URLEncoder.encode(Base64Util.encode(byteArrayOutputStream.toByteArray()), StandardCharsets.UTF_8))
                    .type(BaiduAttributeConstant.TYPE_AGE+","+BaiduAttributeConstant.TYPE_IS_HUMAN)
                    .build());

                if(!bodyAttrResp.isSuccess()){
                    log.error("识别上课Id：{} 图片路径：{} 错误：{}",s.getId(),s.getResourcesPath(),bodyAttrResp.getErrorMsg());
                    return;
                }

                SsScreenshotDetail ssScreenshotDetail = new SsScreenshotDetail();
                ssScreenshotDetail.setId(s.getId());
                ssScreenshotDetail.setRecognitionStatus(RecognitionStatusEnum.RECOGNITION_STATUS_1.code);
                ssScreenshotDetail.setRecognitionTotalNum(bodyAttrResp.getPersonNum());
                ssScreenshotDetail.setRecognitionChildrenNum(0);
                ssScreenshotDetail.setRecognitionTeenagersNum(0);
                ssScreenshotDetail.setRecognitionYouthNum(0);
                ssScreenshotDetail.setRecognitionMiddleNum(0);
                ssScreenshotDetail.setRecognitionElderlyNum(0);
                ssScreenshotDetail.setRecognitionHumanNum(0);
                if(!Objects.isNull(bodyAttrResp.getPersonInfo())){
                    bodyAttrResp.getPersonInfo().forEach(personInfo -> {
                        if(!Objects.isNull(personInfo.getAttributes()) && !Objects.isNull(personInfo.getAttributes().getAge())){
                            PersonAge age = personInfo.getAttributes().getAge();
                            switch (age.getName()){
                                case BaiduAttributeConstant.TYPE_AGE_CHILDREN  -> ssScreenshotDetail.setRecognitionChildrenNum(ssScreenshotDetail.getRecognitionChildrenNum()+1);
                                case BaiduAttributeConstant.TYPE_AGE_TEENAGERS -> ssScreenshotDetail.setRecognitionTeenagersNum(ssScreenshotDetail.getRecognitionTeenagersNum()+1);
                                case BaiduAttributeConstant.TYPE_AGE_YOUTH  -> ssScreenshotDetail.setRecognitionYouthNum(ssScreenshotDetail.getRecognitionYouthNum()+1);
                                case BaiduAttributeConstant.TYPE_AGE_MIDDLE  -> ssScreenshotDetail.setRecognitionMiddleNum(ssScreenshotDetail.getRecognitionMiddleNum()+1);
                                case BaiduAttributeConstant.TYPE_AGE_ELDERLY  -> ssScreenshotDetail.setRecognitionElderlyNum(ssScreenshotDetail.getRecognitionElderlyNum()+1);
                                default -> {
                                }
                            }
                        }

                        if(!Objects.isNull(personInfo.getAttributes()) && !Objects.isNull(personInfo.getAttributes().getHuman())){
                            PersonHuman human = personInfo.getAttributes().getHuman();
                            if(BaiduAttributeConstant.TYPE_HUMAN_STANDARD.equals(human.getName()) && rate.compareTo(human.getScore()) < 0 ){
                                ssScreenshotDetail.setRecognitionHumanNum(ssScreenshotDetail.getRecognitionHumanNum()+1);
                            }
                        }
                    });
                }
                updateById(ssScreenshotDetail);

            }catch (Exception e){
                log.error("识别上课Id：{} 图片路径：{} 错误：{}",s.getId(),s.getResourcesPath(),e.getMessage());
            }
        });

    }
}
