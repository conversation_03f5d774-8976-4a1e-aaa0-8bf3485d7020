package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课次报警明细表
 * 
 * <AUTHOR>
 * @date 2025/03/10
 */
@TableName("b_timetable_event_alarm_deatils")
@Data
@EqualsAndHashCode(callSuper = true)
public class TimetableEventAlarmDeatils extends Model<TimetableEventAlarmDeatils> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 上课课次ID
     */
    private Long timeableId;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 事件描述
     */
    private String eventDescribe;

    /**
     * 事件时间
     */
    private LocalDateTime eventTime;

    /**
     * 事件状态标识
     */
    private Integer eventType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上课课号
     */
    private Long lessionNo;
}