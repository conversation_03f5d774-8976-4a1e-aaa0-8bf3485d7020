package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 双师截图明细表
 *
 * <AUTHOR>
 * @date 2024/10/11
 */
@TableName("ss_screenshot_detail")
@Data
@EqualsAndHashCode(callSuper = true)
public class SsScreenshotDetail extends Model<SsScreenshotDetail> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 课次ID
     */
    private Long classTimeId;

    /**
     * 教室端设备ID
     */
    private Long deviceId;

    /**
     * 资源名称
     */
    private String resourcesName;

    /**
     * 资源路径
     */
    private String resourcesPath;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 编辑时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime mtime;

    /**
     * 编辑人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifer;

    /**
     * 截图时间
     */
    private LocalDateTime screenshotTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 识别总数量
     */
    @Schema(description = "识别总数量", defaultValue = "0")
    private Integer recognitionTotalNum;

    /**
     * 识别幼儿数量
     */
    @Schema(description = "识别幼儿数量")
    private Integer recognitionChildrenNum;

    /**
     * 识别青少年数量
     */
    @Schema(description = "识别青少年数量")
    private Integer recognitionTeenagersNum;

    /**
     * 识别青年数量
     */
    @Schema(description = "识别青年数量")
    private Integer recognitionYouthNum;

    /**
     * 识别中年数量
     */
    @Schema(description = "识别中年数量")
    private Integer recognitionMiddleNum;

    /**
     * 识别老年数量
     */
    @Schema(description = "识别老年数量")
    private Integer recognitionElderlyNum;

    /**
     * 人体数量
     */
    @Schema(description = "人体数量")
    private Integer recognitionHumanNum;

    /**
     * 识别状态: 0-未处理; 1-处理成功; 2-处理失败
     */
    @Schema(description = "识别状态: 0-未处理; 1-处理成功; 2-处理失败 字典类型：recognition_status" ,type = "recognition_status", defaultValue = "0")
    private Integer recognitionStatus;

}