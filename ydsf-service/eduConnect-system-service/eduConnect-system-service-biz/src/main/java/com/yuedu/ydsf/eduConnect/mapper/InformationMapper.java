package com.yuedu.ydsf.eduConnect.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.query.InformationQuery;
import com.yuedu.ydsf.eduConnect.api.vo.InformationVO;
import org.apache.ibatis.annotations.Mapper;
import com.yuedu.ydsf.eduConnect.entity.Information;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
* 资料管理持久层接口
*
* <AUTHOR>
* @date  2025/07/22
*/
@Mapper
public interface InformationMapper extends YdsfBaseMapper<Information> {

    /**
     *  分页查询
     *
     * <AUTHOR>
     * @date 2025年07月22日 09时09分
     */
    IPage<InformationVO> page(Page page,@Param("query") InformationQuery informationQuery);


    /**
     *  获得子目录数量
     *
     * <AUTHOR>
     * @date 2025年07月23日 15时35分
     */
    List<InformationVO> getChildTotalByInformationIds(List<Long> ids);
}




