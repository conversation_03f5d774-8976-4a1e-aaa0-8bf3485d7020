package com.yuedu.ydsf.eduConnect.mapper;


import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsCourseScheduleVO;
import com.yuedu.ydsf.eduConnect.entity.SsCourseSchedule;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 排课表 持久层
 *
 * <AUTHOR>
 * @date 2024-10-09 15:10:16
 */
@Mapper
public interface SsCourseScheduleMapper extends YdsfBaseMapper<SsCourseSchedule> {

    /**
     * 获取主讲端的排课列表
     * <AUTHOR>
     * @date 2024/10/28 11:48
     * @param ssClassTimeDTO
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsCourseScheduleVO>
     */
    List<SsCourseScheduleVO> getCourseListByDeviceId(SsClassTimeDTO ssClassTimeDTO);
}
