package com.yuedu.ydsf.eduConnect.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.dto.SsXiaogjLogDTO;
import com.yuedu.ydsf.eduConnect.api.query.CourseVodQuery;
import com.yuedu.ydsf.eduConnect.api.query.SsXiaogjLogQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsXiaogjLogVO;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 校管家日志表 持久层
 *
 * <AUTHOR>
 * @date 2024-10-28 15:57:51
 */
@Mapper
public interface SsXiaogjLogMapper extends YdsfBaseMapper<SsXiaogjLog> {

    /**
     * 查询同步校管家班级排课异常数据
     * @param page
     * @param ssXiaogjLogQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yuedu.ydsf.eduConnect.api.vo.SsXiaogjLogVO>
     * <AUTHOR>
     * @date 2025/3/3 14:20
     */
    IPage<SsXiaogjLogVO> xiaogjListPage(Page page, @Param("query") SsXiaogjLogQuery ssXiaogjLogQuery);

}
