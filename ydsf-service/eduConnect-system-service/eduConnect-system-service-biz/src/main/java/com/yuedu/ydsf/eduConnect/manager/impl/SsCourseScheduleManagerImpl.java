package com.yuedu.ydsf.eduConnect.manager.impl;


import static com.yuedu.ydsf.eduConnect.constant.Constants.YYYY_MM_DD_HH_MM_SS;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.yuedu.store.api.feign.RemoteLecturerService;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.data.annotation.ForceMaster;
import com.yuedu.ydsf.eduConnect.api.constant.AppointmentEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncAgoraEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncXiaogjEnum;
import com.yuedu.ydsf.eduConnect.constant.MqConstants;
import com.yuedu.ydsf.eduConnect.convert.CreateClassSessionConvert;
import com.yuedu.ydsf.eduConnect.entity.SsClass;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeStudent;
import com.yuedu.ydsf.eduConnect.entity.SsCourseSchedule;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjPushTask;
import com.yuedu.ydsf.eduConnect.entity.bo.SsClassTimeBO;
import com.yuedu.ydsf.eduConnect.manager.SsCourseScheduleManager;
import com.yuedu.ydsf.eduConnect.manager.SsXiaogjLogManager;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomStudentMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeStudentMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsCourseScheduleMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsXiaogjPushTaskMapper;
import com.yuedu.ydsf.eduConnect.mq.dto.CourseScheduleMqDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.SsPushXiaogjType;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq.CreateClassType;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateAgoraClassRoomDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq.CourseData;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.SsPushXiaogjEventReq;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import com.yuedu.ydsf.eduConnect.manager.XiaoGuanJiaService;
import com.yuedu.ydsf.eduConnect.util.DateUtil;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * @author: zhangchuanfu
 * @date: 2024/09/29
 **/
@Slf4j
@Service
public class SsCourseScheduleManagerImpl implements SsCourseScheduleManager {

    @Value("${rocketmq.topics.create_course_schedule_topic}")
    private String createCourseScheduleTopic;
    @Value("${ss.config.xiaogj_push_task:true}")
    private Boolean xiaogjPushTask;

    private final SsClassAuthRoomMapper ssClassAuthRoomMapper;
    private final RocketMQClientTemplate rocketMQClientTemplate;
    private final SsClassTimeMapper ssClassTimeMapper;
    private final AgoraService agoraService;
    private final SsClassAuthRoomStudentMapper ssClassAuthRoomStudentMapper;
    private final SsClassTimeStudentMapper ssClassTimeStudentMapper;
    private final SsCourseScheduleMapper ssCourseScheduleMapper;
    private final SsClassMapper ssClassMapper;
    private final SsClassTimeAuthRoomMapper ssClassTimeAuthRoomMapper;
    private final RemoteLecturerService remoteLecturerService;
    private final XiaoGuanJiaService xiaoGuanJiaService;
    private final SsXiaogjLogManager xiaogjLogManager;
    private final SsXiaogjPushTaskMapper ssXiaogjPushTaskMapper;


    public SsCourseScheduleManagerImpl(RocketMQClientTemplate rocketMQClientTemplate,
        SsClassTimeMapper ssClassTimeMapper, AgoraService agoraService,
        SsClassAuthRoomStudentMapper ssClassAuthRoomStudentMapper,
        SsClassTimeStudentMapper ssClassTimeStudentMapper,
        SsCourseScheduleMapper ssCourseScheduleMapper, SsClassMapper ssClassMapper,
        SsClassTimeAuthRoomMapper ssClassTimeAuthRoomMapper,
        RemoteLecturerService remoteLecturerService, SsClassAuthRoomMapper ssClassAuthRoomMapper,
        XiaoGuanJiaService xiaoGuanJiaService, SsXiaogjLogManager xiaogjLogManager,
        SsXiaogjPushTaskMapper ssXiaogjPushTaskMapper) {
        this.rocketMQClientTemplate = rocketMQClientTemplate;
        this.ssClassTimeMapper = ssClassTimeMapper;
        this.agoraService = agoraService;
        this.ssClassAuthRoomStudentMapper = ssClassAuthRoomStudentMapper;
        this.ssClassTimeStudentMapper = ssClassTimeStudentMapper;
        this.ssCourseScheduleMapper = ssCourseScheduleMapper;
        this.ssClassMapper = ssClassMapper;
        this.ssClassTimeAuthRoomMapper = ssClassTimeAuthRoomMapper;
        this.remoteLecturerService = remoteLecturerService;
        this.ssClassAuthRoomMapper = ssClassAuthRoomMapper;
        this.xiaoGuanJiaService = xiaoGuanJiaService;
        this.xiaogjLogManager = xiaogjLogManager;
        this.ssXiaogjPushTaskMapper = ssXiaogjPushTaskMapper;
    }


    @Async
    @Override
    public void sendCreatedCourseScheduleMessage(Long courseScheduleId) {
        log.info("排课计划添加成功后，发送的消息 courseScheduleId:{}", courseScheduleId);
        CourseScheduleMqDTO courseScheduleMqDTO = new CourseScheduleMqDTO();
        courseScheduleMqDTO.setCourseScheduleId(courseScheduleId);
        Message<CourseScheduleMqDTO> message = MessageBuilder.withPayload(courseScheduleMqDTO)
            .build();
        try {
            rocketMQClientTemplate.convertAndSend(
                createCourseScheduleTopic + MqConstants.COLON_TAG, message);
            log.info("排课计划添加成功后，发送消息成功, courseScheduleMqDTO:{}",
                courseScheduleMqDTO);
        } catch (Exception e) {
            log.error("排课计划添加成功后，发送消息失败, error", e);
        }
    }

    @ForceMaster
    @Override
    public void syncAgoraRooms(Long courseScheduleId) {
        List<SsClassTime> classTimeList = ssClassTimeMapper.selectList(
            Wrappers.lambdaQuery(SsClassTime.class)
                .eq(SsClassTime::getCourseScheduleId, courseScheduleId)
                .eq(SsClassTime::getDelFlag, DelFlagEnum.DELFLAG_0.code));
        //未查询到课次信息，可能的原因是数据库主从延迟，MQ需要重试。
        if (CollectionUtils.isEmpty(classTimeList)) {
            throw new BizException("课次信息为空");
        }
        classTimeList.forEach(classTime -> {
            //已同步声网房间，不再重复同步
            if (StringUtils.isNotBlank(classTime.getRoomUuid())) {
                return;
            }
            //同步声网创建房间
            try {
                CreateAgoraClassRoomDTO createAgoraClassRoomDTO = CreateClassSessionConvert.INSTANCE.toAgoraEntity(
                    classTime);
                String roomUUID = agoraService.syncAgoraClassRoom(createAgoraClassRoomDTO);
                if (StringUtils.isNotBlank(roomUUID)) {

                    ssClassTimeMapper.update(Wrappers.lambdaUpdate(SsClassTime.class)
                        .eq(SsClassTime::getId, classTime.getId())
                        .set(SsClassTime::getRoomUuid, roomUUID)
                        .set(SsClassTime::getIsSyncAgora, IsSyncAgoraEnum.ISSYNCAGORA_1.CODE()));
                }
            } catch (Exception e) {
                log.error("classId： {} 同步声网创建房间失败: {}", classTime.getClassId(),
                    e.getMessage(), e);
                throw new BizException("同步声网创建房间失败");
            }
        });
    }

    /**
     * 同步校管家
     *
     * @param courseScheduleId 课程表ID
     */
    @Override
    public void syncXiaoGuanJia(Long courseScheduleId) {
        SsCourseSchedule courseSchedule = ssCourseScheduleMapper.selectById(courseScheduleId);
        if (courseSchedule == null) {
            throw new BizException("排课表不存在");
        }
        SsClass ssClass = ssClassMapper.selectById(courseSchedule.getClassId());
        if (ssClass == null) {
            throw new BizException("班级不存在");
        } else if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_0.CODE.equals(ssClass.getIsSyncXiaogj())) {
            return;
        }
        //查询班级下的授权教室，过滤单课次授权的校区
        ssClassAuthRoomMapper.selectList(
                Wrappers.lambdaQuery(SsClassAuthRoom.class)
                    .eq(SsClassAuthRoom::getClassId, ssClass.getId()))
            .stream()
            .filter(ssClassAuthRoom -> StringUtils.isBlank(ssClassAuthRoom.getClassTimeIds())
                && Objects.equals(ssClassAuthRoom.getAppointmentStatus(),
                AppointmentEnum.TYPE_1.CODE))
            .forEach(ssClassAuthRoom -> {//2次
                if (xiaogjPushTask) {
                    List<SsXiaogjPushTask> xiaogjPushTaskEntityList = saveClassTimeStudent(
                        courseScheduleId, ssClass.getId(),
                        ssClassAuthRoom.getClassRoomId(),
                        ssClassAuthRoom.getCampusId(),
                        courseSchedule.getLecturerId(),
                        ssClassAuthRoom.getXgjClassId(),
                        ssClassAuthRoom.getXgjClassRoomId(),
                        ssClassAuthRoom.getDeviceId()).stream().map(classCourseReq -> {
                        try {
                            String eventId = UUID.randomUUID().toString();
                            Long timestamp = System.currentTimeMillis();
                            SsPushXiaogjEventReq<ClassCourseReq> ssPushXiaogjEventReq = new SsPushXiaogjEventReq<>();
                            // 双师排课推送校管家请求参数
                            ssPushXiaogjEventReq.setData(classCourseReq);
                            ssPushXiaogjEventReq.setEventId(eventId);
                            ssPushXiaogjEventReq.setEventKey(
                                SsPushXiaogjType.CLASS_COURSE.eventKey);
                            ssPushXiaogjEventReq.setEventTimestamp(timestamp);

                            String jsonStr = JSONUtil.toJsonStr(ssPushXiaogjEventReq);
                            SsXiaogjPushTask ssXiaogjPushTask = new SsXiaogjPushTask();
                            ssXiaogjPushTask.setCourseScheduleId(courseScheduleId);
                            ssXiaogjPushTask.setClassTimeId(classCourseReq.getClassTimeId());
                            String cStartTime = classCourseReq.getCreateCourseList().get(0)
                                .getCStartTime();
                            ssXiaogjPushTask.setAttendClassStartTime(
                                DateUtil.parse(cStartTime));
                            ssXiaogjPushTask.setXgjClassRoomId(
                                ssClassAuthRoom.getXgjClassRoomId());
                            ssXiaogjPushTask.setXgjEventId(eventId);
                            ssXiaogjPushTask.setXgjEventKey(
                                SsPushXiaogjType.CLASS_COURSE.eventKey);
                            ssXiaogjPushTask.setMessageBody(jsonStr);
                            return ssXiaogjPushTask;
                        } catch (Exception e) {
                            log.error("同步校管家失败", e);
                            return null;
                        }
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(xiaogjPushTaskEntityList)) {
                        return;
                    }
                    ssXiaogjPushTaskMapper.insert(xiaogjPushTaskEntityList);
                } else {
                    saveClassTimeStudent(courseScheduleId, ssClass.getId(),
                        ssClassAuthRoom.getClassRoomId(),
                        ssClassAuthRoom.getCampusId(),
                        courseSchedule.getLecturerId(),
                        ssClassAuthRoom.getXgjClassId(),
                        ssClassAuthRoom.getXgjClassRoomId(),
                        ssClassAuthRoom.getDeviceId()).forEach(classCourseReq -> {
                        // 双师排课推送校管家消息队列公共方法
                        SsPushXiaogjEventReq<ClassCourseReq> ssPushXiaogjEventReq = xiaoGuanJiaService.ssPushXiaogjMessage(
                            classCourseReq);

                        String userName = courseSchedule.getCreator();
                        // 保存排课校管家日志
                        xiaogjLogManager.saveXiaogjLog(ssPushXiaogjEventReq, classCourseReq,
                            userName);
                    });
                }
            });
    }

    @Override
    public void saveClassTimeStendsAndSyncXgj(SsClass ssClass, List<SsClassTimeBO> classTimes) {

        List<ClassCourseReq> classCourseReqList = new ArrayList<>();
        classTimes.forEach(s -> {
            ssClassAuthRoomMapper.selectList(Wrappers.<SsClassAuthRoom>lambdaQuery()
                .eq(SsClassAuthRoom::getClassId, s.getClassId())
            ).forEach(u -> {

                SsClassTimeAuthRoom ssClassTimeAuthRoom = new SsClassTimeAuthRoom();
                ssClassTimeAuthRoom.setClassId(s.getClassId());
                ssClassTimeAuthRoom.setClassTimeId(s.getId());
                ssClassTimeAuthRoom.setCampusId(u.getCampusId());
                ssClassTimeAuthRoom.setClassRoomId(u.getClassRoomId());
                ssClassTimeAuthRoom.setDeviceId(u.getDeviceId());
                ssClassTimeAuthRoom.setXgjCampusId(u.getXgjCampusId());
                ssClassTimeAuthRoom.setXgjClassRoomId(u.getXgjClassRoomId());
                if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClass.getIsSyncXiaogj())) {
                    ssClassTimeAuthRoom.setXgjClassTimeId(UUID.randomUUID().toString());
                }
                ssClassTimeAuthRoomMapper.insert(ssClassTimeAuthRoom);

                ssClassAuthRoomStudentMapper.selectList(
                    Wrappers.<SsClassAuthRoomStudent>lambdaQuery()
                        .eq(SsClassAuthRoomStudent::getClassId, ssClassTimeAuthRoom.getClassId())
                        .eq(SsClassAuthRoomStudent::getCampusId, ssClassTimeAuthRoom.getCampusId())
                ).forEach(j -> {
                    SsClassTimeStudent ssClassTimeStudent = new SsClassTimeStudent();
                    ssClassTimeStudent.setClassId(ssClassTimeAuthRoom.getClassId());
                    ssClassTimeStudent.setClassTimeId(ssClassTimeAuthRoom.getClassTimeId());
                    ssClassTimeStudent.setClassTimeAuthRoomId(ssClassTimeAuthRoom.getClassTimeId());
                    ssClassTimeStudent.setCampusId(ssClassTimeAuthRoom.getCampusId());
                    ssClassTimeStudent.setClassRoomId(ssClassTimeAuthRoom.getClassRoomId());
                    ssClassTimeStudent.setDeviceId(ssClassTimeAuthRoom.getDeviceId());
                    ssClassTimeStudent.setStudentId(j.getStudentId());
                    ssClassTimeStudent.setStudentMobile(j.getStudentMobile());
                    ssClassTimeStudent.setStudentName(j.getStudentName());
                    ssClassTimeStudentMapper.insert(ssClassTimeStudent);
                });

                ClassCourseReq classCourseReq = xiaogjClassCourseParam(
                    u.getXgjClassId(), ssClassTimeAuthRoom.getXgjClassRoomId(),
                    ssClassTimeAuthRoom.getXgjClassTimeId(), s,
                    s.getXgjLecturerId());

                if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClass.getIsSyncXiaogj())) {
                    classCourseReqList.add(classCourseReq);
                }

            });
        });

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                if (CollectionUtils.isNotEmpty(classCourseReqList)) {
                    classCourseReqList.forEach(s -> {
                        SsPushXiaogjEventReq<ClassCourseReq> ssPushXiaogjEventReq = xiaoGuanJiaService.ssPushXiaogjMessage(
                            s);
                        xiaogjLogManager.saveXiaogjLog(ssPushXiaogjEventReq, s, null);
                    });

                }
            }
        });
    }


    /**
     * 保存学生信息
     *
     * @param courseScheduleId 课程表ID
     * @param classId          班级ID
     * @param classRoomId      教室ID
     * @param campusId         校区ID
     * @param lecturerId       主讲老师ID
     * @param xgjClassId       校管家班级ID
     * @param xgjClassRoomId   校管家教室ID
     * @return List<ClassCourseReq>
     */
    private List<ClassCourseReq> saveClassTimeStudent(Long courseScheduleId, Long classId,
        Long classRoomId,
        Long campusId,
        Long lecturerId,
        String xgjClassId,
        String xgjClassRoomId,
        Long deviceId) {
        List<ClassCourseReq> classCourseReqList = new ArrayList<>();
        R<LecturerVO> lecture = remoteLecturerService.getLecture(lecturerId);
        if (lecture == null || lecture.getData() == null) {
            throw new BizException("主讲老师信息不存在");
        }
        //获取课次信息
        ssClassTimeMapper.selectList(
                Wrappers.lambdaQuery(SsClassTime.class)
                    .eq(SsClassTime::getCourseScheduleId, courseScheduleId))
            .forEach(classTime -> {//1次
                //获取课次授权教室信息
                List<SsClassTimeAuthRoom> allClassTimeAuthRoomList = ssClassTimeAuthRoomMapper.selectList(
                    Wrappers.lambdaQuery(SsClassTimeAuthRoom.class)
                        .eq(SsClassTimeAuthRoom::getClassTimeId, classTime.getId()));
                //保存学生信息

                Optional<SsClassTimeAuthRoom> optionalSsClassTimeAuthRoom = allClassTimeAuthRoomList.stream()
                    .filter(ssClassTimeAuthRoom -> ssClassTimeAuthRoom.getClassId().equals(classId)
                        && ssClassTimeAuthRoom.getXgjClassRoomId()
                        .equals(xgjClassRoomId)).findFirst();

                if (optionalSsClassTimeAuthRoom.isEmpty()) {
                    throw new BizException("课次授权教室信息不存在");
                }
                String xgjClassTimeId = optionalSsClassTimeAuthRoom.get().getXgjClassTimeId();
                List<SsClassAuthRoomStudent> ssClassAuthRoomStudentList = ssClassAuthRoomStudentMapper.selectList(
                    Wrappers.lambdaQuery(SsClassAuthRoomStudent.class)
                        .eq(SsClassAuthRoomStudent::getClassId, classId)
                        .eq(SsClassAuthRoomStudent::getCampusId,
                            campusId));
                List<SsClassTimeStudent> ssClassTimeStudentList = getSsClassTimeStudents(
                    classId, classTime.getId(), campusId, classRoomId,
                    ssClassAuthRoomStudentList,
                    deviceId);
                ssClassTimeStudentMapper.insert(ssClassTimeStudentList);
                ClassCourseReq classCourseReq = xiaogjClassCourseParam(
                    xgjClassId, xgjClassRoomId, xgjClassTimeId, classTime,
                    lecture.getData().getXgjLecturerId());
                classCourseReqList.add(classCourseReq);
            });
        return classCourseReqList;
    }

    /**
     * 获取课次学生信息
     *
     * @param classId                    班级ID
     * @param classTimeId                课次ID
     * @param campusId                   校区ID
     * @param classRoomId                教室ID
     * @param ssClassAuthRoomStudentList 班级授权校区学生信息
     * @return List<SsClassTimeStudent>
     */
    private List<SsClassTimeStudent> getSsClassTimeStudents(Long classId, Long classTimeId,
        Long campusId, Long classRoomId,
        List<SsClassAuthRoomStudent> ssClassAuthRoomStudentList,
        Long deviceId) {
        SsClassTimeAuthRoom classTimeAuthRoom = ssClassTimeAuthRoomMapper.selectOne(
            Wrappers.lambdaQuery(SsClassTimeAuthRoom.class)
                .eq(SsClassTimeAuthRoom::getClassId, classId)
                .eq(SsClassTimeAuthRoom::getClassTimeId, classTimeId)
                .eq(SsClassTimeAuthRoom::getCampusId, campusId)
                .eq(SsClassTimeAuthRoom::getClassRoomId, classRoomId)
                .eq(SsClassTimeAuthRoom::getDeviceId, deviceId)
        );
        List<SsClassTimeStudent> classTimeStudentList = new ArrayList<>();
        for (SsClassAuthRoomStudent ssClassAuthRoomStudent : ssClassAuthRoomStudentList) {
            SsClassTimeStudent classTimeStudent = new SsClassTimeStudent();
            classTimeStudent.setClassId(classId);
            classTimeStudent.setClassTimeAuthRoomId(classTimeAuthRoom.getId());
            classTimeStudent.setClassTimeId(classTimeAuthRoom.getClassTimeId());
            classTimeStudent.setStudentId(ssClassAuthRoomStudent.getStudentId());
            classTimeStudent.setStudentName(
                ssClassAuthRoomStudent.getStudentName());
            classTimeStudent.setStudentMobile(
                ssClassAuthRoomStudent.getStudentMobile());
            classTimeStudent.setCampusId(classTimeAuthRoom.getCampusId());
            classTimeStudent.setClassRoomId(classTimeAuthRoom.getClassRoomId());
            classTimeStudent.setDeviceId(classTimeAuthRoom.getDeviceId());
            classTimeStudentList.add(classTimeStudent);
        }
        return classTimeStudentList;
    }

    /**
     * 封装同步校管家参数
     *
     * @param xgjClassId     校管家班级ID
     * @param xgjClassRoomId 校管家教室ID
     * @param xgjClassTimeId 校管家课次ID
     * @param classTime      课次信息
     * @param xgjLecturerId  校管家主讲老师ID
     * @return ClassCourseReq
     */
    private ClassCourseReq xiaogjClassCourseParam(String xgjClassId,
        String xgjClassRoomId,
        String xgjClassTimeId,
        SsClassTime classTime,
        String xgjLecturerId) {
        String startTime = LocalDateTime.of(classTime.getAttendClassDate(),
                classTime.getAttendClassStartTime())
            .format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
        String endTime = LocalDateTime.of(classTime.getAttendClassDate(),
                classTime.getAttendClassEndTime())
            .format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));

        ClassCourseReq classCourseReq = new ClassCourseReq();
        classCourseReq.setClassTimeId(classTime.getId());
        classCourseReq.setTripartiteId(xgjClassId);
        classCourseReq.setType(CreateClassType.CREATE_CLASS_TYPE.CODE);

        List<CreateCourseReq> createCourseReqList = Lists.newArrayList();
        CreateCourseReq createCourseReq = new CreateCourseReq();
        createCourseReq.setTripartiteId(xgjClassId);
        createCourseReq.setCCourseTimes(1);
        createCourseReq.setCClassroomID(xgjClassRoomId);
        createCourseReq.setCCourseMode(1);
        createCourseReq.setCStartTime(startTime);
        createCourseReq.setCEndTime(endTime);

        int dayOfWeek = classTime.getAttendClassDate().getDayOfWeek().getValue();
        List<CourseData> courseDataList = new ArrayList<>();
        CourseData courseData = CourseData.builder()
            .cWeekday(dayOfWeek)
            .cWeekStartTime(startTime)
            .cWeekEndTime(endTime)
            .cDate(DateUtil.format(classTime.getAttendClassDate()))
            .cClassroomID(xgjClassRoomId)
            .cStartTime(startTime)
            .cEndTime(endTime)
            .threeId(xgjClassTimeId)
            .speaker(xgjLecturerId)
            .build();
        courseDataList.add(courseData);
        createCourseReq.setCCourseData(courseDataList);
        createCourseReqList.add(createCourseReq);
        classCourseReq.setCreateCourseList(createCourseReqList);
        return classCourseReq;
    }
}
