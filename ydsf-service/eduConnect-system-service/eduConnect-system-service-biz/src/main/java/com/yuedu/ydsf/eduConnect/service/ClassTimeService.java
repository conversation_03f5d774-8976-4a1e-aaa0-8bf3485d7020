package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.vo.ClassTimeVO;
import com.yuedu.ydsf.eduConnect.entity.ClassTime;
import java.util.List;

/**
 * 上课时段 服务类
 *
 * <AUTHOR>
 * @date 2024-11-28 16:44:02
 */
public interface ClassTimeService extends IService<ClassTime> {

    /**
     * 获取全部上课时段
     *
     * @return List<ClassTimeVO>
     */
    List<ClassTimeVO> getClassTimeList();
}
