package com.yuedu.ydsf.eduConnect;

import com.yuedu.ydsf.common.feign.annotation.EnableYdsfFeignClients;
import com.yuedu.ydsf.common.job.annotation.EnableYdsfXxlJob;
import com.yuedu.ydsf.common.security.annotation.EnableYdsfResourceServer;
import com.yuedu.ydsf.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR> auto
 * <p>
 * 项目启动类
 */
@EnableAsync
@EnableOpenApi("edusystem")
@EnableYdsfFeignClients
@EnableDiscoveryClient
@EnableYdsfResourceServer
@SpringBootApplication
@EnableYdsfXxlJob
public class EduConnectSystemApp {
    public static void main(String[] args) {
        SpringApplication.run(EduConnectSystemApp.class, args);
    }
}
