package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.vo.CreateCourseScheduleVO;
import com.yuedu.ydsf.eduConnect.entity.SsCourseSchedule;

/**
 * 排课表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:10:16
 */
public interface SsCourseScheduleService extends IService<SsCourseSchedule> {

    /**
     * 保存排课信息
     *
     * @param createCourseScheduleVo 创建排课信息
     * @return 排课ID
     */
    void saveCourseSchedule(CreateCourseScheduleVO createCourseScheduleVo);
}
