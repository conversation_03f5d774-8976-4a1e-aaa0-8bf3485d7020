package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 点播库
 * 
 * <AUTHOR>
 * @date 2024/09/28
 */
@TableName("ss_recording")
@Data
@EqualsAndHashCode(callSuper = true)
public class SsRecording extends Model<SsRecording> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 录制类型:0-点播课;1-培训会议
     * @type ss_recording_recording_type
     */
    private Integer recordingType;

    /**
     * 录制设备ID
     */
    private Long deviceId;

    /**
     * 年级:1-lv1;2-lv2;3-lv3;4-lv4;5-lv5;6-lv6;7-lv7
     * @type ss_recording_grade
     */
    private Integer grade;

    /**
     * 书籍ID
     */
    private String booksId;

    /**
     * 书籍名称
     */
    private String booksName;

    /**
     * 主讲老师ID(ss_lecturer主键ID)
     */
    private Long lecturerId;

    /**
     * 主讲姓名
     */
    private String lecturerName;

    /**
     * 原考勤班级上课开始时间
     */
    private LocalDateTime originalCourseStartDate;

    /**
     * 原考勤班级上课结束时间
     */
    private LocalDateTime originalCourseEndDate;

    /**
     * 声网录制ID
     */
    private String agoraRecordId;

    /**
     * 声网房间UUID
     */
    private String roomUuid;

    /**
     * 上下架状态:0-未上架;1-已上架
     * @type ss_recording_shelf_status
     */
    private Integer shelfStatus;

    /**
     * 录制状态:0-待录制;1-录制中;2-正常录制完成;3-录制作废（重新录制）;4-视频处理中
     * @type ss_recording_recording_status
     */
    private Integer recordingStatus;

    /**
     * 资源存储类型:0-OSS;1-VOD
     * @type ss_recording_storage_type
     */
    private Integer storageType;

    /**
     * 视频点播Vod中videoId
     */
    private String vodVideoId;

    /**
     * 录制资源
     */
    private String recordingResources;

    /**
     * 声网云端录制id
     */
    private String agoraCloudRecordId;

    /**
     * 云端录制资源地址
     */
    private String cloudRecordingResources;

    /**
     * 云端录制ID
     */
    private String agoraCloudRecordIndividualId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 编辑时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime mtime;

    /**
     * 编辑人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifer;

    /**
     * 逻辑删除: 0-正常;1-删除
     * @type ss_recording_del_flag
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;


    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 下载状态: 0-不可用; 1-可用; 2-错误
     */
    private Integer downloadStatus;

    /**
     * 下载Vod的Id
     */
    private String downloadVodId;

    /**
     * 提交状态: 0-未提交; 1-待提交; 2-已提交; 3-回收站
     */
    private Integer auditStatus;


    /**
     * 录制时间
     */
    @Schema(description = "录制时间")
    private LocalDateTime recordingTime;

}