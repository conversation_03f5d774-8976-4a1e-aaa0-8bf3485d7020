package com.yuedu.ydsf.eduConnect.job;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjPushTask;
import com.yuedu.ydsf.eduConnect.manager.SsXiaogjLogManager;
import com.yuedu.ydsf.eduConnect.service.SsXiaogjPushTaskService;
import com.yuedu.ydsf.eduConnect.manager.XiaoGuanJiaService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

/**
 * 双师后台建班排课后推送校管家消息定时任务
 */
@Slf4j
@Component
public class SyncXiaogjMessageJobService {

    private static final Integer NON_EXEC_TASK_STATUS = 0;
    private static final Integer EXEC_TASK_STATUS = 1;
    private static final String USER_NAME = "xxljob";
    private static final int PAGE_SIZE = 100;

    private final SsXiaogjPushTaskService ssXiaogjPushTaskService;
    private final XiaoGuanJiaService xiaoGuanJiaService;
    private final SsXiaogjLogManager ssXiaogjLogManager;

    @XxlJob("syncXiaogjMessageJob")
    public void syncXiaogjMessageJob() {
        XxlJobHelper.log("校管家消息同步定时任务开始");
        String jobParam = XxlJobHelper.getJobParam();
        long day = 1;
        if (StringUtils.isNumeric(jobParam)) {
            day = Long.parseLong(jobParam);
        }
        Result result = getResult(day);
        if (result == null){
            XxlJobHelper.log("校管家消息同步定时任务结束，无待执行任务");
            return;
        }
        XxlJobHelper.log("校管家消息同步定时任务结束，成功执行{}条，失败{}条", result.succeedCount().get(), result.failCount()
            .get());
    }

    @Nullable
    public Result getResult(long day) {
        log.info("校管家消息同步定时任务开始");
        int pageNum = 1;
        //当天00点
        Page<SsXiaogjPushTask> page = new Page<>(pageNum, PAGE_SIZE);
        LocalDate currentDate = LocalDate.now();
        LocalDateTime start = currentDate.atStartOfDay().plusDays(day + 1);
        Page<SsXiaogjPushTask> resultPage = ssXiaogjPushTaskService.page(page,
            Wrappers.lambdaQuery(SsXiaogjPushTask.class)
                .eq(SsXiaogjPushTask::getTaskStatus, NON_EXEC_TASK_STATUS)
                .le(SsXiaogjPushTask::getAttendClassStartTime, start));
        if (resultPage.getRecords().isEmpty()) {
            log.info("校管家消息同步定时任务结束，无待执行任务");
            return null;
        }
        AtomicInteger succeedCount = new AtomicInteger();
        AtomicInteger failCount = new AtomicInteger();
        while (pageNum <= resultPage.getPages()) {
            resultPage.getRecords().forEach(task -> {
                try {
                    log.info("待推送的任务，task:{}", JSONUtil.toJsonStr(task));
                    // 双师排课推送校管家消息队列公共方法
                    boolean success = xiaoGuanJiaService.ssPushXiaogjMessage(task.getXgjEventId(),
                        task.getMessageBody());
                    if (success) {
                        succeedCount.getAndIncrement();
                        SsXiaogjPushTask update = new SsXiaogjPushTask();
                        update.setId(task.getId());
                        update.setTaskStatus(EXEC_TASK_STATUS);
                        ssXiaogjPushTaskService.updateById(update);
                        // 保存排课校管家日志
                        ssXiaogjLogManager.saveXiaogjLog(task, USER_NAME);
                    } else {
                        failCount.getAndIncrement();
                        log.error("校管家消息同步定时任务失败，taskId:{}", task.getId());
                    }
                } catch (Exception e) {
                    failCount.getAndIncrement();
                    log.error("校管家消息同步定时任务异常，taskId:{}", task.getId(), e);
                }
            });
            pageNum++;
            page = new Page<>(pageNum, PAGE_SIZE);
            resultPage = ssXiaogjPushTaskService.page(page,
                Wrappers.lambdaQuery(SsXiaogjPushTask.class)
                    .eq(SsXiaogjPushTask::getTaskStatus, NON_EXEC_TASK_STATUS)
                    .le(SsXiaogjPushTask::getAttendClassStartTime, start));
        }
        log.info("校管家消息同步定时任务结束，成功执行{}条，失败{}条", succeedCount.get(), failCount.get());
        return new Result(succeedCount, failCount);
    }

    public record Result(AtomicInteger succeedCount, AtomicInteger failCount) {

    }

    public SyncXiaogjMessageJobService(SsXiaogjPushTaskService ssXiaogjPushTaskService,
        XiaoGuanJiaService xiaoGuanJiaService, SsXiaogjLogManager ssXiaogjLogManager) {
        this.ssXiaogjPushTaskService = ssXiaogjPushTaskService;
        this.xiaoGuanJiaService = xiaoGuanJiaService;
        this.ssXiaogjLogManager = ssXiaogjLogManager;
    }
}
