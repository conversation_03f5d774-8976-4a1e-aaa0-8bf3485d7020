package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yuedu.teaching.api.feign.RemoteCourseVersionService;
import com.yuedu.teaching.api.feign.RemoteCoursewareService;
import com.yuedu.teaching.api.feign.RemoteCoursewareVersionService;
import com.yuedu.teaching.api.feign.RemoteLessonPubService;
import com.yuedu.teaching.query.LessonPubQuery;
import com.yuedu.teaching.vo.CourseVersionVO;
import com.yuedu.teaching.vo.CoursewareVO;
import com.yuedu.teaching.vo.CoursewareVersionVO;
import com.yuedu.teaching.vo.LessonPubVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.dto.DeleteVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.EditVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.GenerateVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.LessonCoursewareEditDTO;
import com.yuedu.ydsf.eduConnect.api.dto.RecordVideoTaskDTO;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO.EditLessonDTO;
import com.yuedu.ydsf.eduConnect.api.query.RecordVideoTaskQuery;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanVO;
import com.yuedu.ydsf.eduConnect.api.vo.PlanNameVO;
import com.yuedu.ydsf.eduConnect.api.vo.RecordVideoTaskVO;
import com.yuedu.ydsf.eduConnect.constant.Constants;
import com.yuedu.ydsf.eduConnect.constant.RecordVideoTaskConstant;
import com.yuedu.ydsf.eduConnect.convert.CreateClassSessionConvert;
import com.yuedu.ydsf.eduConnect.entity.RecordVideoTask;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub;
import com.yuedu.ydsf.eduConnect.mapper.RecordVideoTaskMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDetailPubMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanPubMapper;
import com.yuedu.ydsf.eduConnect.service.RecordVideoTaskService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDetailPubService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.BatchResult;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

/**
 * @ClassName RecordVideoTaskServiceImpl
 * @Description 录课任务控制类
 * <AUTHOR>
 * @Date 2024/11/29 16:48:01
 * @Version v0.0.1
 */
@Slf4j
@Service
public class RecordVideoTaskServiceImpl extends
    MPJBaseServiceImpl<RecordVideoTaskMapper, RecordVideoTask> implements RecordVideoTaskService {

    @Resource
    private TeachingPlanPubMapper planPubMapper;

    @Resource
    private TeachingPlanDetailPubMapper planDetailPubMapper;

    @Resource
    private RecordVideoTaskMapper recordVideoTaskMapper;

    @Resource
    private TeachingPlanDetailPubService teachingPlanDetailPubService;

    @Resource
    private RemoteLessonPubService lessonPubService;

    @Resource
    private RemoteCourseVersionService courseVersionService;

    @Resource
    private RemoteCoursewareVersionService remoteCoursewareVersionService;

    @Resource
    private TeachingPlanDetailPubMapper teachingPlanDetailPubMapper;

    @Resource
    private RemoteCoursewareService coursewareService;

    /**
     * 通过讲师Id,课程Id,课节Id,最早课程开始时间去掉重复的教学计划
     *
     * @param detailList 教学计划
     */
    private static void duplicateRemovalDetail(List<TeachingPlanDetailPub> detailList) {
        if (!detailList.isEmpty()) {
            for (int i = 0; i < detailList.size(); i++) {
                for (int j = detailList.size() - 1; j > i; j--) {
                    TeachingPlanDetailPub detail = detailList.get(i);
                    TeachingPlanDetailPub detailPub = detailList.get(j);
                    //同一个讲师的,同一个课程的,同一节课 只保留时间最早的教学计划
                    LocalDateTime detailClassStartDateTime = detail.getClassStartDateTime();
                    LocalDateTime detailPubClassStartDateTime = detailPub.getClassStartDateTime();
                    if ((detailClassStartDateTime.isBefore(detailPubClassStartDateTime)
                        || detailClassStartDateTime.isEqual(detailPubClassStartDateTime))
                        && detail.getLessonId().compareTo(detailPub.getLessonId()) == 0) {
                        detailList.remove(j);
                    }
                }
            }

        }
    }

    /**
     * 通过date+time设置为可用时间
     *
     * @param earliestStartDate 最早开始时间
     * @param recordVideoTaskVO 录课任务结果
     */
    private static void getEarlierStartDate(LocalDateTime earliestStartDate,
        RecordVideoTaskVO recordVideoTaskVO) {
        String date = DateUtil.format(earliestStartDate, RecordVideoTaskConstant.YYYY_MM_DD);
        LocalTime time = earliestStartDate.toLocalTime();
        String week = switch (earliestStartDate.getDayOfWeek().getValue()) {
            case RecordVideoTaskConstant.ONE -> RecordVideoTaskConstant.MONDAY;
            case RecordVideoTaskConstant.TWO -> RecordVideoTaskConstant.TUESDAY;
            case RecordVideoTaskConstant.THREE -> RecordVideoTaskConstant.WEDNESDAY;
            case RecordVideoTaskConstant.FOUR -> RecordVideoTaskConstant.THURSDAY;
            case RecordVideoTaskConstant.FIVE -> RecordVideoTaskConstant.FRIDAY;
            case RecordVideoTaskConstant.SIX -> RecordVideoTaskConstant.SATURDAY;
            case RecordVideoTaskConstant.SEVEN -> RecordVideoTaskConstant.SUNDAY;
            default -> "";
        };
        String amOrPm = null;
        if (DateUtil.isAM(
            Date.from(earliestStartDate.atZone(ZoneId.systemDefault()).toInstant()))) {
            amOrPm = RecordVideoTaskConstant.MORNING;
        } else if (DateUtil.isPM(
            Date.from(earliestStartDate.atZone(ZoneId.systemDefault()).toInstant()))) {
            amOrPm = RecordVideoTaskConstant.AFTERNOON;
        }
        recordVideoTaskVO.setEarliestTruthStartDate(date + week + amOrPm + time);
    }

    /**
     * 设置录课任务部分字段信息
     *
     * @param teachingPlanDetailPub 教学任务详情
     * @param recordVideoTask       录课任务
     */
    private static void setTaskPartInfo(TeachingPlanDetailPub teachingPlanDetailPub,
        RecordVideoTask recordVideoTask) {
        recordVideoTask.setTeachingPlanId(teachingPlanDetailPub.getPlanId());
        recordVideoTask.setTeachingPlanDetailId(teachingPlanDetailPub.getPlanId());
        recordVideoTask.setCourseId(teachingPlanDetailPub.getCourseId());
        recordVideoTask.setLessonOrder(teachingPlanDetailPub.getLessonOrder());
        recordVideoTask.setEarliestStartDate(teachingPlanDetailPub.getClassStartDateTime());
        recordVideoTask.setLectureId(teachingPlanDetailPub.getLectureId());
        recordVideoTask.setLectureName(teachingPlanDetailPub.getLectureName());
    }


    /**
     * 设置课程版本信息
     *
     * @param courseVersionList     课程版本列表
     * @param teachingPlanDetailPub 教学计划详情
     * @param recordVideoTask       录课任务
     */
    private static void setCourseVersionInfo(List<CourseVersionVO> courseVersionList,
        TeachingPlanDetailPub teachingPlanDetailPub, RecordVideoTask recordVideoTask) {
        if (!courseVersionList.isEmpty()) {
            for (CourseVersionVO courseVersionVO : courseVersionList) {
                if (Objects.equals(teachingPlanDetailPub.getCourseId(), courseVersionVO.getId())) {
                    recordVideoTask.setCourseVersion(courseVersionVO.getVersion());
                }
            }
        }
    }

    /**
     * 设置课件信息
     *
     * @param coursewareVersionList 课件版本列表
     * @param teachingPlanDetailPub 教学计划详情
     * @param recordVideoTask       录课任务
     */
    private static void setCoursewareInfo(List<CoursewareVersionVO> coursewareVersionList,
        TeachingPlanDetailPub teachingPlanDetailPub, RecordVideoTask recordVideoTask) {
        if (!coursewareVersionList.isEmpty()) {
            coursewareVersionList.stream().filter(
                versionVO -> Objects.equals(teachingPlanDetailPub.getLessonId(),
                    versionVO.getLessonId())).forEach(versionVO -> {
                recordVideoTask.setCoursewareId(versionVO.getCoursewareId());
                recordVideoTask.setCoursewareVersion(versionVO.getCoursewareVersion());
            });
        }
    }

    /**
     * 组装请求课件信息
     *
     * @param recordVideoTask           录课任务
     * @param notFinishCourseIdList     未完成的课程Id列表
     * @param notFinishLessonOrderList  未完成的课节顺序列表
     * @param finishCourseIdList        已完成的课程Id列表
     * @param finishLessonOrderList     已完成的课节顺序列表
     * @param finishedCourseVersionList 已完成的课节版本列表(课程版本与课件版本一致)
     */
    private static void setLessonPubQuery(RecordVideoTask recordVideoTask,
        List<Long> notFinishCourseIdList,
        List<Integer> notFinishLessonOrderList, List<Integer> notFinishTaskList,
        List<Long> finishCourseIdList,
        List<Integer> finishLessonOrderList, List<Integer> finishedCourseVersionList,
        List<Integer> finishTaskList) {
        //任务状态
        Integer taskStatus = recordVideoTask.getTaskStatus();

        //已录制的任务
        if (taskStatus == 1) {
            //课程Id列表
            finishCourseIdList.add(recordVideoTask.getCourseId());
            //课节顺序列表
            finishLessonOrderList.add(recordVideoTask.getLessonOrder());
            finishedCourseVersionList.add(recordVideoTask.getCourseVersion());
            //已完成
            finishTaskList.add(recordVideoTask.getTaskStatus());
        }else{
            //课程Id列表
            notFinishCourseIdList.add(recordVideoTask.getCourseId());
            //课节顺序列表
            notFinishLessonOrderList.add(recordVideoTask.getLessonOrder());
            //未完成
            notFinishTaskList.add(recordVideoTask.getTaskStatus());
        }
    }

    /**
     * 生成录课任务
     *
     * @param videoTaskDto 已发布的教学计划Id
     * @return 结果
     */
    @Override
    public int generateRecordVideoTask(GenerateVideoTaskDto videoTaskDto) {
        log.info("监听到新增教学计划,开始生成录课任务,videoTaskDto={}", videoTaskDto);
        //获取可能会出现重复录课任务的教学详情列表
        List<TeachingPlanDetailPub> detailList = getPlanDetailPubList(videoTaskDto);
        if (CollectionUtils.isEmpty(detailList)) {
            log.info("新增教学计划详情为空");
            return 0;
        }
        log.info("开始生成录课任务，教学计划排期共:{}条", detailList.size());
        //生成任务
        int count = generateTask(detailList, videoTaskDto.getOperateType());
        log.info("新增教学计划完成,共生成录课任务:{}条", count);
        return count;
    }

    /**
     * 生成任务
     *
     * @param detailList 教学计划详情
     */
    private int generateTask(List<TeachingPlanDetailPub> detailList, Integer operateType) {

        //通过讲师Id,课程Id,课节Id,最早课程开始时间去掉重复的教学计划详情
        duplicateRemovalDetail(detailList);
        log.info("需要生成录制任务的教学计划的数量:{}", JSON.toJSONString(detailList.size()));

        //开始获取课件版本信息
        List<CoursewareVersionVO> versionVOList = new ArrayList<>();
        //课件id列表
        List<Long> lessonIdList = detailList.stream().map(TeachingPlanDetailPub::getLessonId)
            .collect(Collectors.toList());

        if (!lessonIdList.isEmpty()) {
            //获取课件版本列表
            versionVOList = getCoursewareVersionVOList(lessonIdList);
        }

        //组装获取已发布的课节信息
        List<Long> courseIdList = detailList.stream().map(TeachingPlanDetailPub::getCourseId)
            .collect(Collectors.toList());
        List<Integer> lessonOrderList = detailList.stream()
            .map(TeachingPlanDetailPub::getLessonOrder)
            .collect(Collectors.toList());

        LessonPubQuery lessonPubQuery = new LessonPubQuery();
        lessonPubQuery.setCourseIdList(courseIdList);
        lessonPubQuery.setLessonOrderList(lessonOrderList);

        //获取已发布的课节信息
        List<LessonPubVO> lessonPubVOList = getLessonPubVo(lessonPubQuery);
        log.info("已发布的课节信息的数量:{}", JSON.toJSONString(lessonPubVOList.size()));

        //开始获取课程版本信息
        List<CourseVersionVO> courseVersionVoList = new ArrayList<>();
        //课程id列表
        if (!courseIdList.isEmpty()) {
            //获取课程版本信息
            courseVersionVoList = getCourseVersionVoList(courseIdList);
        }

        //全部信息获取完成后批量添加录课任务
        return batchInsertVideoTask(detailList, versionVOList, courseVersionVoList,
            lessonPubVOList);
    }

    /**
     * 获取教学计划详情列表
     *
     * @param videoTaskDto 生成课任务Dto
     * @return 结果
     */
    private List<TeachingPlanDetailPub> getPlanDetailPubList(GenerateVideoTaskDto videoTaskDto) {
        //通过课程Id,讲师Id获取可能会出现重复录课任务的教学计划Id列表
        List<Long> planIdList = planPubMapper.getPlanIdList(videoTaskDto);
        log.info("可能会出现重复录课任务的教学计划Id列表:{}", JSON.toJSONString(planIdList));

        List<TeachingPlanDetailPub> detailList = new ArrayList<>();
        if (!planIdList.isEmpty()) {
            //通过教学任务Id查询教学任务详情列表
            detailList = planDetailPubMapper.getPlanDetailPubByIdList(planIdList);
            log.info("查询已发布的教学任务详情列表:{}", JSON.toJSONString(detailList));
        }
        return detailList;
    }

    /**
     * 编辑录课任务 变动直播间计划,课程包,主讲老师
     *
     * @param videoTaskDto 教学计划Id
     * @return 结果
     */
    @Override
    public int editRecordVideoTask(EditVideoTaskDto videoTaskDto) {
        log.info("监听到编辑教学计划,开始编辑录课任务,videoTaskDto={}", videoTaskDto);
        //教学计划详情列表
        List<TeachingPlanDetailPub> detailList = getPlanDetailPubList(videoTaskDto);

        //通过通过课程,讲师,课节id列表删除未完成的录课任务
        deleteTask(videoTaskDto);

        if (CollectionUtils.isEmpty(detailList)) {
            log.info("编辑教学计划详情为空");
            return 0;
        }

        //编辑后的录课任务进行重新添加
        int count = generateTask(detailList, videoTaskDto.getOperateType());
        log.info("教学计划编辑完成,共生成录课任务:{}条", count);
        return count;
    }

    /**
     * 通过通过课程,讲师,课节id列表删除未完成的录课任务
     *
     * @param videoTaskDto 录课任务
     */
    private void deleteTask(EditVideoTaskDto videoTaskDto) {

        //删除全部计划中课程一样 讲师一样 课节一样 未完成的录课任务
        LambdaUpdateWrapper<RecordVideoTask> deleteWrapper = Wrappers.lambdaUpdate(
                RecordVideoTask.class)
            .eq(videoTaskDto.getCourseIdOld() != null, RecordVideoTask::getCourseId,
                videoTaskDto.getCourseIdOld())
            .eq(videoTaskDto.getLectureIdOld() != null, RecordVideoTask::getLectureId,
                videoTaskDto.getLectureIdOld())
            .eq(videoTaskDto.getPlanId() != null, RecordVideoTask::getTeachingPlanId,
                videoTaskDto.getPlanId())
            .notIn(RecordVideoTask::getTaskStatus, RecordVideoTaskConstant.FINISHED_TASK);
        recordVideoTaskMapper.delete(deleteWrapper);
    }

    /**
     * 编辑教学计划中某课节讲师
     *
     * @param operateMqDTO 教学计划Id
     * @return 结果
     */
    @Override
    public int editTaskScheduling(TeachingPlanOperateMqDTO operateMqDTO) {
        log.info("监听到编辑教学计划中某课节讲师,operateMqDTO={}", operateMqDTO);
        List<EditLessonDTO> editLessonDTOList = operateMqDTO.getEditLessonDTOList();
        if (!editLessonDTOList.isEmpty()) {
            //更新未完成的录课任务为新老师
            for (EditLessonDTO editLessonDTO : editLessonDTOList) {
                recordVideoTaskMapper.updateTaskByLectureId(editLessonDTO);
            }

            List<RecordVideoTask> resultList = new ArrayList<>();
            for (EditLessonDTO editLessonDTO : editLessonDTOList) {
                RecordVideoTask record = new RecordVideoTask();
                record.setCourseId(operateMqDTO.getCourseId());
                record.setTaskStatus(1);
                record.setTeachingPlanId(operateMqDTO.getPlanId());
                record.setLectureId(editLessonDTO.getLectureIdOld());
                List<RecordVideoTask> taskList = recordVideoTaskMapper.recordTaskOldList(record);
                log.info("已完成的任务:{}", JSON.toJSONString(taskList));
                if (!taskList.isEmpty()) {
                    RecordVideoTask finishTask = taskList.get(0);
                    log.info("当前老师已完成的录课任务:{}", JSON.toJSONString(finishTask));
                    finishTask.setId(null);
                    finishTask.setTaskStatus(0);
                    finishTask.setLectureId(editLessonDTO.getLectureId());
                    finishTask.setLectureName(editLessonDTO.getLectureName());
                    resultList.add(finishTask);
                    log.info("新老师需要重新创建的录课任务:{}", JSON.toJSONString(resultList));
                }
            }
            //重新添加新任务
            if (!resultList.isEmpty()) {
                recordVideoTaskMapper.insert(resultList);
            }
        }
        //已完成的录课任务给新老师生成录课任务
        log.info("编辑教学计划中某课节讲师完成");
        return 0;
    }

    /**
     * 编辑课件Id
     *
     * @param coursewareId 课件Id
     * @return 结果
     */
    @Override
    public int editCourseware(Long coursewareId) {
        log.info("监听到编辑课件信息");
        int result = 0;
        if (coursewareId != null) {
            //获取最新的课件版本
            Integer coursewareVersion = getCoursewareVersion(coursewareId);
            coursewareVersion = 1002;
            LessonCoursewareEditDTO lessonCoursewareEdit = new LessonCoursewareEditDTO();
            lessonCoursewareEdit.setCoursewareId(coursewareId);
            lessonCoursewareEdit.setCoursewareVersion(coursewareVersion);
            //直接更新全部计划中未完成的录课任务
            updateNotFinishedTask(lessonCoursewareEdit);

            //查询全部已完成的录课任务,重新生成新课件版本的录课任务
            saveFinishedTask(lessonCoursewareEdit, null);
        }
        log.info("编辑课件信息完成");
        return 0;
    }

    /**
     * 更新未完成的录课任务的课件版本
     *
     * @param lessonCoursewareEdit 课件编辑dto
     */
    private void updateNotFinishedTask(LessonCoursewareEditDTO lessonCoursewareEdit) {
        RecordVideoTaskDTO videoTaskDTO = new RecordVideoTaskDTO();
        if (lessonCoursewareEdit.getLessonId() != null) {
            videoTaskDTO.setLessonId(lessonCoursewareEdit.getLessonId());
        }
        if (lessonCoursewareEdit.getLessonVersion() != null) {
            videoTaskDTO.setLessonVersion(lessonCoursewareEdit.getLessonVersion());
        }
        if (lessonCoursewareEdit.getCoursewareId() != null) {
            videoTaskDTO.setCoursewareId(lessonCoursewareEdit.getCoursewareId());
        }
        if (lessonCoursewareEdit.getCoursewareVersion() != null) {
            videoTaskDTO.setCoursewareVersion(lessonCoursewareEdit.getCoursewareVersion());
        }
        recordVideoTaskMapper.updateCoursewareVersion(videoTaskDTO);
    }

    /**
     * 重新添加已完成的录课任务
     *
     * @param lessonCoursewareEdit 课件Id
     */
    private void saveFinishedTask(LessonCoursewareEditDTO lessonCoursewareEdit,
        Long coursewareIdOld) {

        LambdaQueryWrapper<RecordVideoTask> queryWrapper = Wrappers.lambdaQuery(
            RecordVideoTask.class);
        queryWrapper.eq(coursewareIdOld != null, RecordVideoTask::getCoursewareId, coursewareIdOld)
            .eq(coursewareIdOld == null,
                RecordVideoTask::getCoursewareId, lessonCoursewareEdit.getCoursewareId())
            .eq(RecordVideoTask::getTaskStatus, RecordVideoTaskConstant.FINISHED_TASK);
        List<RecordVideoTask> taskList = recordVideoTaskMapper.selectList(queryWrapper);

        log.info("跟当前课件Id相关的已完成的录课任务:{}", JSON.toJSONString(taskList));

        List<RecordVideoTask> insertList = new ArrayList<>();
        if (!taskList.isEmpty()) {
            for (RecordVideoTask recordVideoTask : taskList) {
                recordVideoTask.setId(null);
                if (lessonCoursewareEdit.getCoursewareVersion() != null) {
                    recordVideoTask.setCoursewareVersion(
                        lessonCoursewareEdit.getCoursewareVersion());
                }
                recordVideoTask.setTaskStatus(RecordVideoTaskConstant.NOT_FINISHED_TASK);
                if (lessonCoursewareEdit.getCoursewareId() != null) {
                    recordVideoTask.setCoursewareId(lessonCoursewareEdit.getCoursewareId());
                }
                //防止出现重复数据
                if (recordVideoTaskMapper.selectByRecordVideoTask(recordVideoTask) == null) {
                    insertList.add(recordVideoTask);
                }
            }
            log.info("最终要插入的数据:{}", JSON.toJSONString(insertList));
            recordVideoTaskMapper.insert(insertList);
        }
    }

    /**
     * 获取最新的课件版本
     *
     * @param coursewareId 课件Id
     * @return 课件版本
     */
    @Nullable
    private Integer getCoursewareVersion(Long coursewareId) {
        R<CoursewareVO> coursewareR = coursewareService.getById(coursewareId);
        Integer coursewareVersion = null;
        log.info("课件信息:{}", JSON.toJSONString(coursewareR));
        if (coursewareR != null && coursewareR.getCode() == 0 && null != coursewareR.getData()
            .getVersion()) {
            coursewareVersion = coursewareR.getData().getVersion();
        }
        return coursewareVersion;
    }

    /**
     * 课节中课件版本编辑
     *
     * @param coursewareIdList 课件Id
     * @return 结果
     */
    @Override
    public int editLessonCourseware(List<LessonCoursewareEditDTO> coursewareIdList) {
        log.info("监听到课节中课件版本编辑");
        coursewareIdList.forEach(lessonCoursewareEdit -> {
            //直接更新全部计划中未完成的录课任务
            updateNotFinishedTask(lessonCoursewareEdit);

            //查询全部已完成的录课任务,重新生成新课件版本的录课任务
            saveFinishedTask(lessonCoursewareEdit, lessonCoursewareEdit.getCoursewareIdOld());
        });
        return 0;
    }

    /**
     * 删除录课任务 1.已录制(已完成)的任务不可删除,未完成的任务逻辑删除 2.其他教学计划中任然存在计划需比对最早上课开始时间后进行新任务的添加
     *
     * @param videoTaskDto 教学计划Id
     * @return 结果
     */
    @Override
    public int deleteRecordVideoTask(DeleteVideoTaskDto videoTaskDto) {
        log.info("监听到删除教学计划,开始删除录课任务,videoTaskDto={}", videoTaskDto);
        //通过通过课程,讲师,课节id列表删除未完成的录课任务
        deleteTask(videoTaskDto);

        //要删除的计划Id
        videoTaskDto.setPlanId(videoTaskDto.getPlanId());

        //教学计划详情列表
        List<TeachingPlanDetailPub> detailList = getPlanDetailPubList(videoTaskDto);
        if (CollectionUtils.isEmpty(detailList)) {
            log.info("删除教学计划详情为空");
            return 0;
        }
        int count = generateTask(detailList, videoTaskDto.getOperateType());
        log.info("删除教学计划完成, 共生成录课任务:{}条", count);
        return count;
    }

    /**
     * 获取课程版本信息
     *
     * @param courseIdList 课程id列表
     * @return 结果
     */
    private List<CourseVersionVO> getCourseVersionVoList(List<Long> courseIdList) {
        //课程列表Id
        R<List<CourseVersionVO>> courseVersionListR = courseVersionService.listCoursewareByIds(
            courseIdList);
        log.info("课程信息列表:{}", JSON.toJSONString(courseVersionListR));
        List<CourseVersionVO> courseVersionVoList = new ArrayList<>();
        if (courseVersionListR != null && courseVersionListR.getCode() == 0) {
            courseVersionVoList = courseVersionListR.getData();
        }
        return courseVersionVoList;
    }

    /**
     * 通过课节Id列表获取课件版本列表
     *
     * @param lessonIdList 课件id列表
     * @return 结果
     */
    private List<CoursewareVersionVO> getCoursewareVersionVOList(List<Long> lessonIdList) {
        R<List<CoursewareVersionVO>> coursewareVersionR = remoteCoursewareVersionService.listCoursewareByIds(
            lessonIdList);
        log.info("课件版本信息:{}", JSON.toJSONString(coursewareVersionR));
        List<CoursewareVersionVO> versionVOList = new ArrayList<>();
        if (coursewareVersionR != null && coursewareVersionR.getCode() == 0) {
            versionVOList = coursewareVersionR.getData();
        }
        return versionVOList;
    }

    /**
     * 根据条件查询讲师录课任务列表
     *
     * @param videoTaskQuery 查询条件
     * @return 结果
     */
    @Override
    public List<RecordVideoTaskVO> getRecordTaskList(RecordVideoTaskQuery videoTaskQuery) {
        List<LessonPubVO> resultList = new ArrayList<>();
        //页面展示内容: 直播计划名称、课节名、上课时段、老师名
        List<RecordVideoTaskVO> recordVideoTaskVOList = new ArrayList<>();

        //根据讲师Id获取录课任务列表
        List<RecordVideoTask> taskList = recordVideoTaskMapper.getRecordVideoTaskList(
            videoTaskQuery);
        log.info("根据讲师Id获取录课任务列表:{}", JSON.toJSONString(taskList));
        if (!taskList.isEmpty()) {
            //未完成的课节顺序列表
            List<Integer> notFinishLessonOrderList = new ArrayList<>();

            //未完成的课程Id列表
            List<Long> notFinishCourseIdList = new ArrayList<>();

            //未完成的状态列表
            List<Integer> notFinishTaskList = new ArrayList<>();

            //未完成的课节顺序列表
            List<Integer> finishLessonOrderList = new ArrayList<>();

            //未完成的课程Id列表
            List<Long> finishCourseIdList = new ArrayList<>();

            //已完成的课节版本列表(课程版本与课件版本一致)
            List<Integer> finishedCourseVersionList = new ArrayList<>();

            //已完成的状态列表
            List<Integer> finishTaskList = new ArrayList<>();

            //教学计划id
            List<Long> planIdList = new ArrayList<>();

            //直播计划名称、课节名、上课时段、老师名
            taskList.forEach(recordVideoTask -> {
                RecordVideoTaskVO recordVideoTaskVO = CreateClassSessionConvert.INSTANCE.toEntity(
                    recordVideoTask);
                if (StringUtils.isNotEmpty(recordVideoTaskVO.getLectureName())
                    && recordVideoTaskVO.getLectureName().contains(Constants.LEFT_BRACKET)) {
                    //杜雨坤(杜老师),取括号里面的昵称
                    recordVideoTaskVO.setLectureNickName(
                        recordVideoTaskVO.getLectureName().substring(
                            recordVideoTaskVO.getLectureName().indexOf(Constants.LEFT_BRACKET) + 1,
                            recordVideoTaskVO.getLectureName().indexOf(Constants.RIGHT_BRACKET)));
                }

                //组装请求课件信息
                setLessonPubQuery(recordVideoTask, notFinishCourseIdList, notFinishLessonOrderList,
                    notFinishTaskList,
                    finishCourseIdList, finishLessonOrderList, finishedCourseVersionList,
                    finishTaskList);

                //设置教学计划id
                planIdList.add(recordVideoTask.getTeachingPlanId());

                //获取最早上课时间(无课节顺序)
                getEarlierStartDate(recordVideoTask.getEarliestStartDate(), recordVideoTaskVO);

                recordVideoTaskVOList.add(recordVideoTaskVO);
            });

            //获取课节信息(包括未完成与已完成)
            getLessonPub(notFinishCourseIdList, notFinishLessonOrderList, resultList,
                finishCourseIdList, finishLessonOrderList, finishedCourseVersionList);

            //获取课节名称
            setLessonName(resultList, recordVideoTaskVOList);

            //获取直播计划名称与课节顺序
            setPlanName(planIdList, recordVideoTaskVOList);
        }
        return recordVideoTaskVOList;
    }


    /**
     * 获取课节信息
     *
     * @param notFinishCourseIdList     未完成的课程Id列表
     * @param notFinishLessonOrderList  未完成的课节顺序列表
     * @param resultList                课节信息列表
     * @param finishCourseIdList        已完成的课程Id列表
     * @param finishLessonOrderList     已完成的课节顺序列表
     * @param finishedCourseVersionList 已完成的课节版本列表(课程版本与课件版本一致)
     */
    private void getLessonPub(List<Long> notFinishCourseIdList,
        List<Integer> notFinishLessonOrderList,
        List<LessonPubVO> resultList, List<Long> finishCourseIdList,
        List<Integer> finishLessonOrderList, List<Integer> finishedCourseVersionList) {
        //获取课节信息
        LessonPubQuery notFinishPubQuery = new LessonPubQuery();
        notFinishPubQuery.setCourseIdList(notFinishCourseIdList);
        notFinishPubQuery.setLessonOrderList(notFinishLessonOrderList);
        log.info("未完成的课节信息的入参:{},", JSON.toJSONString(notFinishPubQuery));
        if (!notFinishPubQuery.getLessonOrderList().isEmpty()
            && !notFinishPubQuery.getCourseIdList().isEmpty()) {
            List<LessonPubVO> notFinishLessonPubVoList = getLessonPubVo(notFinishPubQuery);
            log.info("未完成的课节信息的结果:{}", JSON.toJSONString(notFinishLessonPubVoList));
            if (!notFinishLessonPubVoList.isEmpty()) {
                resultList.addAll(notFinishLessonPubVoList);
            }
        }

        //获取课节信息
        LessonPubQuery finishPubQuery = new LessonPubQuery();
        finishPubQuery.setCourseIdList(finishCourseIdList);
        finishPubQuery.setLessonOrderList(finishLessonOrderList);
        finishPubQuery.setCourseVersionList(finishedCourseVersionList);
        log.info("已完成的课节信息的入参:{},", JSON.toJSONString(finishPubQuery));
        if (!finishPubQuery.getLessonOrderList().isEmpty()
            && !finishPubQuery.getCourseIdList().isEmpty()) {
            List<LessonPubVO> finishLessonPubVoList = getLessonPubVo(finishPubQuery);
            log.info("已完成的课节信息的结果:{}", JSON.toJSONString(finishLessonPubVoList));
            if (!finishLessonPubVoList.isEmpty()) {
                resultList.addAll(finishLessonPubVoList);
            }
        }

        log.info("全部的课节信息:{}", JSON.toJSONString(resultList));
    }

    /**
     * 设置课节信息
     *
     * @param lessonPubQuery 课节Id列表
     */
    private List<LessonPubVO> getLessonPubVo(LessonPubQuery lessonPubQuery) {
        //获取课节信息
        R<List<LessonPubVO>> lessonNameListR = lessonPubService.getLessonByIds(lessonPubQuery);
        log.info("课节信息:{}", JSON.toJSONString(lessonNameListR));
        List<LessonPubVO> lessonNameList = new ArrayList<>();
        if (lessonNameListR != null && lessonNameListR.getCode() == 0) {
            lessonNameList = lessonNameListR.getData();
        }
        return lessonNameList;
    }

    /**
     * 设置直播计划名称与课节顺序
     *
     * @param planIdList            直播计划名称列表
     * @param recordVideoTaskVOList 录课任务
     */
    private void setPlanName(List<Long> planIdList,
        List<RecordVideoTaskVO> recordVideoTaskVOList) {
        LiveRoomPlanVO liveRoomPlanVO = teachingPlanDetailPubService.getPlanVo(
            planIdList);
        log.info("直播间计划信息:{}", JSON.toJSONString(liveRoomPlanVO));

        if (liveRoomPlanVO != null) {
            //直播计划名称
            List<PlanNameVO> planNameList = liveRoomPlanVO.getPlanNameVo();
            if (!planNameList.isEmpty()) {
                for (RecordVideoTaskVO recordVideoTaskVO : recordVideoTaskVOList) {
                    recordVideoTaskVO.setLessonOrderStr(
                        NumberChineseFormatter.format(recordVideoTaskVO.getLessonOrder(), false));
                    planNameList.stream().filter(
                        planName -> Objects.equals(recordVideoTaskVO.getTeachingPlanDetailId(),
                            planName.getPlanId())).forEach(
                        planName -> recordVideoTaskVO.setTeachingPlanName(planName.getPlanName()));
                }
            }
        }
    }

    /**
     * 课节名称
     *
     * @param lessonPubVoList       课节发布列表
     * @param recordVideoTaskVOList 录课任务列表
     */
    private void setLessonName(List<LessonPubVO> lessonPubVoList,
        List<RecordVideoTaskVO> recordVideoTaskVOList) {
        if (!lessonPubVoList.isEmpty()) {
            for (RecordVideoTaskVO recordVideoTaskVO : recordVideoTaskVOList) {
                for (LessonPubVO lessonPubVO : lessonPubVoList) {
                    if (recordVideoTaskVO.getCourseId().compareTo(lessonPubVO.getCourseId())
                        == 0 &&
                        recordVideoTaskVO.getLessonOrder().equals(lessonPubVO.getLessonOrder())
                        &&
                        recordVideoTaskVO.getCourseVersion().equals(lessonPubVO.getVersion())) {
                        recordVideoTaskVO.setLessonName(lessonPubVO.getLessonName());
                    }
                }
            }
        }
    }

    /**
     * 批量插入录课任务
     *
     * @param detailList 教学计划详情
     */
    private int batchInsertVideoTask(
        List<TeachingPlanDetailPub> detailList,
        List<CoursewareVersionVO> coursewareVersionList,
        List<CourseVersionVO> courseVersionList,
        List<LessonPubVO> lessonPubList) {
        if (CollectionUtils.isEmpty(detailList)) {
            log.info("教学计划详情为空");
            return 0;
        }

        //任务列表
        List<RecordVideoTask> taskList = new ArrayList<>();

        detailList.forEach(teachingPlanDetailPub -> {
            RecordVideoTask recordVideoTask = new RecordVideoTask();

            // 设置录课任务部分字段信息
            setTaskPartInfo(teachingPlanDetailPub, recordVideoTask);

            //设置课件信息
            setCoursewareInfo(coursewareVersionList, teachingPlanDetailPub, recordVideoTask);

            //设置课程版本信息
            setCourseVersionInfo(courseVersionList, teachingPlanDetailPub, recordVideoTask);

            //查询已完成的录课任务
            recordVideoTask.setTaskStatus(RecordVideoTaskConstant.FINISHED_TASK);
            log.info("查询已完成的录课任务的入参:{}", JSON.toJSONString(recordVideoTask));
            List<RecordVideoTask> taskCompleted = recordVideoTaskMapper.recordTaskOldList(
                recordVideoTask);
            log.info("已完成的录课任务的结果:{}", JSON.toJSONString(taskCompleted));

            recordVideoTask.setTaskStatus(RecordVideoTaskConstant.NOT_FINISHED_TASK);
            List<RecordVideoTask> recordTaskOldList = recordVideoTaskMapper.recordTaskOldList(
                recordVideoTask);
            log.info("未完成的录课任务:{}", JSON.toJSONString(recordTaskOldList));

            //已完成和已存在的录课任务不再重新生成
            if (taskCompleted.isEmpty() && recordTaskOldList.isEmpty()) {
                taskList.add(recordVideoTask);
            }

        });
        log.info("最后的的任务数据:{}", JSON.toJSONString(taskList));
        //批量插入录课任务
        List<BatchResult> inserted = recordVideoTaskMapper.insert(taskList);
        return inserted.size();
    }
}
