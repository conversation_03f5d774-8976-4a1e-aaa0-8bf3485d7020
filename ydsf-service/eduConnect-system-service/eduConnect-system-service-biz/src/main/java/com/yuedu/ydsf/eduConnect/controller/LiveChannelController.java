package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.entity.LiveChannel;
import com.yuedu.ydsf.eduConnect.service.LiveChannelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 直播频道 控制类
 *
 * <AUTHOR>
 * @date 2024-11-28 16:56:34
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/LiveChannel" )
@Tag(description = "ea_live_channel" , name = "直播频道管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LiveChannelController {

    private final  LiveChannelService liveChannelService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param liveChannel 直播频道
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("edusystem_LiveChannel_view")
    public R getLiveChannelPage(@ParameterObject Page page, @ParameterObject LiveChannel liveChannel) {
        LambdaQueryWrapper<LiveChannel> wrapper = Wrappers.lambdaQuery();
        return R.ok(liveChannelService.page(page, wrapper));
    }


    /**
     * 通过条件查询直播频道
     * @param liveChannel 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("edusystem_LiveChannel_view")
    public R getDetails(@ParameterObject LiveChannel liveChannel) {
        return R.ok(liveChannelService.list(Wrappers.query(liveChannel)));
    }

    /**
     * 新增直播频道
     * @param liveChannel 直播频道
     * @return R
     */
    @Operation(summary = "新增直播频道" , description = "新增直播频道" )
    @PostMapping("/add")
    @HasPermission("edusystem_LiveChannel_add")
    public R save(@RequestBody LiveChannel liveChannel) {
        return R.ok(liveChannelService.save(liveChannel));
    }

    /**
     * 修改直播频道
     * @param liveChannel 直播频道
     * @return R
     */
    @Operation(summary = "修改直播频道" , description = "修改直播频道" )
    @PutMapping("/edit")
    @HasPermission("edusystem_LiveChannel_edit")
    public R updateById(@RequestBody LiveChannel liveChannel) {
        return R.ok(liveChannelService.updateById(liveChannel));
    }

    /**
     * 通过id删除直播频道
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除直播频道" , description = "通过id删除直播频道" )
    @DeleteMapping("/delete")
    @HasPermission("edusystem_LiveChannel_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(liveChannelService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param liveChannel 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_LiveChannel_export")
    public List<LiveChannel> exportExcel(LiveChannel liveChannel,Long[] ids) {
        return liveChannelService.list(Wrappers.lambdaQuery(liveChannel).in(ArrayUtil.isNotEmpty(ids), LiveChannel::getId, ids));
    }

    /**
     * 导入excel 表
     * @param liveChannelList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_LiveChannel_export")
    public R importExcel(@RequestExcel List<LiveChannel> liveChannelList, BindingResult bindingResult) {
        return R.ok(liveChannelService.saveBatch(liveChannelList));
    }
}
