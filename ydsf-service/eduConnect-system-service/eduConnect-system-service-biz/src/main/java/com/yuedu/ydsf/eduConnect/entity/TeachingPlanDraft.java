package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 教学计划草稿表 实体类
 *
 * <AUTHOR>
 * @date 2024-12-04 09:55:04
 */
@Data
@TableName("ea_teaching_plan_draft")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "教学计划草稿表实体类")
public class TeachingPlanDraft extends Model<TeachingPlanDraft> {


	/**
	* 主键id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键id")
    private Long id;

	/**
	* 直播间计划id
	*/
    @Schema(description="直播间计划id")
    private Long liveRoomPlanId;

	/**
	* 发布状态:0-未发布;1-已发布;2-已关闭
	*/
    @Schema(description="发布状态:0-未发布;1-已发布;2-已关闭")
    private Integer planStatus;

	/**
	* 课程id
	*/
    @Schema(description="课程id")
    private Long courseId;

	/**
	* 课程名字
	*/
    @Schema(description="课程名字")
    private String courseName;

	/**
	* 主讲老师id
	*/
    @Schema(description="主讲老师id")
    private Long lectureId;

	/**
	* 主讲老师名字
	*/
    @Schema(description="主讲老师名字")
    private String lectureName;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    private Integer stage;

	/**
	* 草稿创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="草稿创建人")
    private String createBy;

	/**
	* 草稿创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="草稿创建时间")
    private LocalDateTime createTime;

	/**
	* 草稿修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="草稿修改人")
    private String updateBy;

	/**
	* 草稿修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="草稿修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
