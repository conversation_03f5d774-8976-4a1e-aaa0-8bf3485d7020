package com.yuedu.ydsf.eduConnect.controller;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanPubCourseQuery;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanPubQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanPubService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序接口类
 * <AUTHOR>
 * @date 2024/12/11 9:23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/teachingPlan" )
@Tag(description = "ea_teaching_plan_pub" , name = "小程序接口类" )
public class AppController {

    private final TeachingPlanPubService teachingPlanPubService;

    /**
     * 查询小程序约点播课-选择课程列表
     * @param teachingPlanPubQuery
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>>
     * <AUTHOR>
     * @date 2024/12/9 11:24
     */
    @Operation(summary = "查询小程序约点播课-选择课程列表", description = "查询小程序约点播课-选择课程列表")
    @GetMapping("/getTeachingPlanCourseList")
    @StorePermission
    public R<List<TeachingPlanPubVO>> getTeachingPlanCourseList(@ParameterObject TeachingPlanPubQuery teachingPlanPubQuery) {
        List<TeachingPlanPubVO> teachingPlanCourseList = teachingPlanPubService.getTeachingPlanCourseList(teachingPlanPubQuery);
        return R.ok(teachingPlanCourseList);
    }

    @PostMapping("/inner/getTeachingPlanCourseList")
    @Inner
    public R<List<TeachingPlanPubVO>> innerGetTeachingPlanCourseList(@RequestBody TeachingPlanPubCourseQuery teachingPlanPubCourseQuery) {
        TeachingPlanPubQuery teachingPlanPubQuery = new TeachingPlanPubQuery();
        BeanUtils.copyProperties(teachingPlanPubCourseQuery,teachingPlanPubQuery);
        StoreContextHolder.setStoreUserAttr(teachingPlanPubCourseQuery.getSchoolId()
            ,teachingPlanPubCourseQuery.getStoreId());
        return getTeachingPlanCourseList(teachingPlanPubQuery);
    }


  /**
   * 查询小程序约直播播课-选择课程列表
   *
   * <AUTHOR>
   * @date 2024/12/17 10:15
   * @param teachingPlanPubQuery
   * @return
   *     com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>>
   */
  @Operation(summary = "查询小程序约直播播课-选择课程列表", description = "查询小程序约直播播课-选择课程列表")
  @GetMapping("/getTeachingPlanCourseLiveList")
  @StorePermission
  public R<List<TeachingPlanPubVO>> getTeachingPlanCourseLiveList(
      @ParameterObject TeachingPlanPubQuery teachingPlanPubQuery) {
    List<TeachingPlanPubVO> teachingPlanCourseList =
        teachingPlanPubService.getTeachingPlanCourseLiveList(teachingPlanPubQuery);
    return R.ok(teachingPlanCourseList);
  }
}
