package com.yuedu.ydsf.eduConnect.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.config.AsyncConfiguration;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.common.core.util.SpringContextHolder;
import com.yuedu.ydsf.common.operatelog.annotation.OperateLog;
import com.yuedu.ydsf.eduConnect.api.constant.DefaultRedPacketSettingEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.EduSystemConstant;
import com.yuedu.ydsf.eduConnect.api.constant.OperateCategoryEnum;
import com.yuedu.ydsf.eduConnect.api.constant.OperateLogStrTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.OperateTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.RulesSettingEnum;
import com.yuedu.ydsf.eduConnect.api.dto.SsInteractionRedPacketSettingDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsInteractionRedPacketSettingQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsInteractionRedPacketSettingInfoVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsInteractionRedPacketSettingVO;
import com.yuedu.ydsf.eduConnect.entity.SsInteractionRedPacketSetting;
import com.yuedu.ydsf.eduConnect.manager.SsOperateLogManager;
import com.yuedu.ydsf.eduConnect.mapper.SsInteractionRedPacketSettingMapper;
import com.yuedu.ydsf.eduConnect.service.SsInteractionRedPacketSettingService;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 门店红包规则设置表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-04 08:41:26
 */
@Slf4j
@Service
@AllArgsConstructor
public class SsInteractionRedPacketSettingServiceImpl extends ServiceImpl<SsInteractionRedPacketSettingMapper, SsInteractionRedPacketSetting> implements SsInteractionRedPacketSettingService {

    private final RedisTemplate<String, Object> redisTemplate;
    /**
     * 约读店管家小程序-门店红包规则设置
     * @param ssInteractionRedPacketSettingDTO
     * @return void
     * <AUTHOR>
     * @date 2024/11/4 9:42
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void interactionRedPacketSetting(SsInteractionRedPacketSettingDTO ssInteractionRedPacketSettingDTO) {

        log.info("约读店管家小程序门店自定义红包设置请求参数: {}", JSONObject.toJSONString(ssInteractionRedPacketSettingDTO));

        // 红包规则校验
        checkInteractionRedPacketSetting(ssInteractionRedPacketSettingDTO);

        // 校验飞天店长账号是否设置过自定义红包
        SsInteractionRedPacketSetting oldInteractionRedPacketSetting = this.getOne(
            Wrappers.lambdaQuery(SsInteractionRedPacketSetting.class)
                .eq(SsInteractionRedPacketSetting::getSource, ssInteractionRedPacketSettingDTO.getSource())
        );

        // 封装参数
        SsInteractionRedPacketSetting newInteractionRedPacketSetting = new SsInteractionRedPacketSetting();
        BeanUtils.copyProperties(ssInteractionRedPacketSettingDTO, newInteractionRedPacketSetting);

        // 通过飞天店长账号ID校验新增或编辑
        if (Objects.isNull(oldInteractionRedPacketSetting)) {

            // 选择默认时不保存门店设置信息
            if (RulesSettingEnum.RULES_SETTING_ENUM_1.CODE.equals(ssInteractionRedPacketSettingDTO.getRulesSetting())) {

                newInteractionRedPacketSetting.setCreateBy(ssInteractionRedPacketSettingDTO.getManagerMobile());
                newInteractionRedPacketSetting.setUpdateBy(ssInteractionRedPacketSettingDTO.getManagerMobile());
                this.save(newInteractionRedPacketSetting);

                // 切面操作日志保存(新增)
                SpringContextHolder.getBean(SsInteractionRedPacketSettingServiceImpl.class)
                    .addOperateLogAspect(oldInteractionRedPacketSetting,
                        ssInteractionRedPacketSettingDTO);

            }


        } else {

            // 已自定义设置又改回默认规则, 删除门店自定义设置信息
            if (RulesSettingEnum.RULES_SETTING_ENUM_0.CODE.equals(ssInteractionRedPacketSettingDTO.getRulesSetting())
                && DelFlagEnum.DELFLAG_0.code.equals(oldInteractionRedPacketSetting.getDelFlag())) {

                this.update(Wrappers.lambdaUpdate(SsInteractionRedPacketSetting.class)
                    .eq(SsInteractionRedPacketSetting::getSource, ssInteractionRedPacketSettingDTO.getSource())
                    .set(SsInteractionRedPacketSetting::getDelFlag, DelFlagEnum.DELFLAG_1.code)
                );

                // 切面操作日志保存(删除)
                SpringContextHolder.getBean(SsInteractionRedPacketSettingServiceImpl.class)
                    .deleteOperateLogAspect(oldInteractionRedPacketSetting,
                        ssInteractionRedPacketSettingDTO);

            } else if (RulesSettingEnum.RULES_SETTING_ENUM_1.CODE.equals(ssInteractionRedPacketSettingDTO.getRulesSetting())) {

                this.update(Wrappers.lambdaUpdate(SsInteractionRedPacketSetting.class)
                    .eq(SsInteractionRedPacketSetting::getSource, ssInteractionRedPacketSettingDTO.getSource())
                    .set(SsInteractionRedPacketSetting::getXgjCampusId, ssInteractionRedPacketSettingDTO.getXgjCampusId())
                    .set(SsInteractionRedPacketSetting::getRedPacketNumber, ssInteractionRedPacketSettingDTO.getRedPacketNumber())
                    .set(SsInteractionRedPacketSetting::getRedPacketUpperLimit, ssInteractionRedPacketSettingDTO.getRedPacketUpperLimit())
                    .set(SsInteractionRedPacketSetting::getRedPacketLowerLimit, ssInteractionRedPacketSettingDTO.getRedPacketLowerLimit())
                    .set(SsInteractionRedPacketSetting::getUpdateBy, ssInteractionRedPacketSettingDTO.getManagerMobile())
                    .set(DelFlagEnum.DELFLAG_1.code.equals(oldInteractionRedPacketSetting.getDelFlag()), SsInteractionRedPacketSetting::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                );

                // 切面操作日志保存(编辑)
                SpringContextHolder.getBean(SsInteractionRedPacketSettingServiceImpl.class)
                    .editOperateLogAspect(oldInteractionRedPacketSetting,
                        ssInteractionRedPacketSettingDTO);

            }

        }

        // 红包设置变更后, 删除门店红包设置缓存
        redisTemplate.delete(String.format(EduSystemConstant.SS_RED_PACKET_INTERACTION, ssInteractionRedPacketSettingDTO.getXgjCampusId().toLowerCase()));

        log.info("约读店管家小程序门店自定义红包设置删除缓存,redis-key为:{}", String.format(EduSystemConstant.SS_RED_PACKET_INTERACTION, ssInteractionRedPacketSettingDTO.getXgjCampusId().toLowerCase()));

        log.info("约读店管家小程序门店自定义红包设置结束");

    }


    /**
     * 约读店管家小程序-查询门店红包设置
     * @param ssInteractionRedPacketSettingQuery
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsInteractionRedPacketSettingInfoVO
     * <AUTHOR>
     * @date 2024/11/4 9:42
     */
    @Override
    public SsInteractionRedPacketSettingInfoVO getInteractionRedPacketSettingBySource(SsInteractionRedPacketSettingQuery ssInteractionRedPacketSettingQuery) {

        log.info("约读店管家小程序查询门店自定义红包设置开始:{}", JSONObject.toJSONString(ssInteractionRedPacketSettingQuery));

        SsInteractionRedPacketSettingInfoVO ssInteractionRedPacketSettingInfoVO = new SsInteractionRedPacketSettingInfoVO();

        // 默认红包分数规则封装
        SsInteractionRedPacketSettingVO defaultInteractionRedPacketSettingVO = new SsInteractionRedPacketSettingVO();
        defaultInteractionRedPacketSettingVO.setRedPacketNumber(ssInteractionRedPacketSettingQuery.getRedPacketNumber());
        defaultInteractionRedPacketSettingVO.setRedPacketNumber(DefaultRedPacketSettingEnum.RED_PACKET_NUMBER.code);
        defaultInteractionRedPacketSettingVO.setRedPacketUpperLimit(DefaultRedPacketSettingEnum.PACKET_NUMBER_UPPER_LIMIT.code);
        defaultInteractionRedPacketSettingVO.setRedPacketLowerLimit(DefaultRedPacketSettingEnum.PACKET_NUMBER_LOWER_LIMIT.code);
        ssInteractionRedPacketSettingInfoVO.setDefaultInteractionRedPacketSettingVO(defaultInteractionRedPacketSettingVO);

        // 通过飞天门店ID查询门店自定义红包规则
        List<SsInteractionRedPacketSetting> ssInteractionRedPacketSettingList = this.list(
            Wrappers.lambdaQuery(SsInteractionRedPacketSetting.class)
                .eq(SsInteractionRedPacketSetting::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                .eq(SsInteractionRedPacketSetting::getSource, ssInteractionRedPacketSettingQuery.getSource())
                .orderByDesc(SsInteractionRedPacketSetting::getCreateTime)
        );

        if (CollectionUtils.isNotEmpty(ssInteractionRedPacketSettingList)) {

            List<SsInteractionRedPacketSettingVO> customInteractionRedPacketSettingVOList = ssInteractionRedPacketSettingList.stream().map(entity -> {
                SsInteractionRedPacketSettingVO ssInteractionRedPacketSettingVO = new SsInteractionRedPacketSettingVO();
                BeanUtils.copyProperties(entity, ssInteractionRedPacketSettingVO);
                return ssInteractionRedPacketSettingVO;
            }).toList();

            ssInteractionRedPacketSettingInfoVO.setCustomInteractionRedPacketSettingVO(customInteractionRedPacketSettingVOList.get(0));
            ssInteractionRedPacketSettingInfoVO.setRulesSetting(RulesSettingEnum.RULES_SETTING_ENUM_1.CODE);

        }

        log.info("约读店管家小程序查询门店自定义红包设置结束:{}", JSONObject.toJSONString(ssInteractionRedPacketSettingInfoVO));

        return ssInteractionRedPacketSettingInfoVO;

    }

    /**
     * 红包规则校验
     * @param ssInteractionRedPacketSettingDTO
     * @return void
     * <AUTHOR>
     * @date 2024/11/11 11:49
     */
    private void checkInteractionRedPacketSetting(SsInteractionRedPacketSettingDTO ssInteractionRedPacketSettingDTO) {

        if (ssInteractionRedPacketSettingDTO.getRedPacketUpperLimit().compareTo(ssInteractionRedPacketSettingDTO.getRedPacketNumber()) > 0) {
            throw new CheckedException("最高分不能大于总分数，请重新输入。");
        }

        if (ssInteractionRedPacketSettingDTO.getRedPacketLowerLimit().compareTo(ssInteractionRedPacketSettingDTO.getRedPacketNumber()) >= 0) {
            throw new CheckedException("最低分不能大于等于总分数，请重新输入。");
        }

        if (ssInteractionRedPacketSettingDTO.getRedPacketLowerLimit().compareTo(ssInteractionRedPacketSettingDTO.getRedPacketUpperLimit()) >= 0) {
            throw new CheckedException("最高分不能小于等于最低分，请重新输入。");
        }

    }

    /**
     * 新增-操作记录切面
     * @param oldInteractionRedPacketSetting
     * @param ssInteractionRedPacketSettingDTO
     * @return void
     * <AUTHOR>
     * @date 2024/11/19 14:57
     */
    @OperateLog(name = "门店红包规则设置",
        operateCategory = OperateCategoryEnum.CATEGORY_ENUM_1,
        operateType = OperateTypeEnum.OPERATE_TYPE_ENUM_1,
        operateLogStrType = OperateLogStrTypeEnum.OPERATE_LOG_STR_TYPE_ENUM_2,
        spel = "@ssInteractionRedPacketSettingMapper.getInteractionRedPacketSettingBySource(#ssInteractionRedPacketSettingDTO.source)",
        objectId = "#ssInteractionRedPacketSettingDTO.source",
        operateId = "#ssInteractionRedPacketSettingDTO.source",
        operateName = "#ssInteractionRedPacketSettingDTO.managerName",
        operateUserName = "#ssInteractionRedPacketSettingDTO.managerMobile"
    )
    public void addOperateLogAspect(SsInteractionRedPacketSetting oldInteractionRedPacketSetting,
        SsInteractionRedPacketSettingDTO ssInteractionRedPacketSettingDTO) {}

    /**
     * 编辑-操作记录切面
     * @param oldInteractionRedPacketSetting
     * @param ssInteractionRedPacketSettingDTO
     * @return void
     * <AUTHOR>
     * @date 2024/11/19 14:57
     */
    @OperateLog(name = "门店红包规则设置",
        operateCategory = OperateCategoryEnum.CATEGORY_ENUM_1,
        operateType = OperateTypeEnum.OPERATE_TYPE_ENUM_2,
        operateLogStrType = OperateLogStrTypeEnum.OPERATE_LOG_STR_TYPE_ENUM_2,
        oldVal = "#oldInteractionRedPacketSetting",
        spel = "@ssInteractionRedPacketSettingMapper.getInteractionRedPacketSettingBySource(#ssInteractionRedPacketSettingDTO.source)",
        objectId = "#ssInteractionRedPacketSettingDTO.source",
        operateId = "#ssInteractionRedPacketSettingDTO.source",
        operateName = "#ssInteractionRedPacketSettingDTO.managerName",
        operateUserName = "#ssInteractionRedPacketSettingDTO.managerMobile"
    )
    public void editOperateLogAspect(SsInteractionRedPacketSetting oldInteractionRedPacketSetting,
        SsInteractionRedPacketSettingDTO ssInteractionRedPacketSettingDTO) {}

    /**
     * 删除-操作记录切面
     * @param oldInteractionRedPacketSetting
     * @param ssInteractionRedPacketSettingDTO
     * @return void
     * <AUTHOR>
     * @date 2024/11/19 14:57
     */
    @OperateLog(name = "门店红包规则设置",
        operateCategory = OperateCategoryEnum.CATEGORY_ENUM_1,
        operateType = OperateTypeEnum.OPERATE_TYPE_ENUM_3,
        operateLogStrType = OperateLogStrTypeEnum.OPERATE_LOG_STR_TYPE_ENUM_2,
        oldVal = "#oldInteractionRedPacketSetting",
        spel = "@ssInteractionRedPacketSettingMapper.getInteractionRedPacketSettingBySource(#ssInteractionRedPacketSettingDTO.source)",
        objectId = "#ssInteractionRedPacketSettingDTO.source",
        operateId = "#ssInteractionRedPacketSettingDTO.source",
        operateName = "#ssInteractionRedPacketSettingDTO.managerName",
        operateUserName = "#ssInteractionRedPacketSettingDTO.managerMobile"
    )
    public void deleteOperateLogAspect(SsInteractionRedPacketSetting oldInteractionRedPacketSetting,
        SsInteractionRedPacketSettingDTO ssInteractionRedPacketSettingDTO) {}


}
