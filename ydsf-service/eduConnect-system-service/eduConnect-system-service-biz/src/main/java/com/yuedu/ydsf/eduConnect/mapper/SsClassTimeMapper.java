package com.yuedu.ydsf.eduConnect.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.vo.AttendedClassCountVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 课次信息表 持久层
 *
 * <AUTHOR>
 * @date 2024-10-09 15:12:52
 */
@Mapper
public interface SsClassTimeMapper extends YdsfBaseMapper<SsClassTime> {

    /**
     * 通过班级ID查询各状态课次数
     * @param classTimeQuery
     * @return java.util.List<com.yuedu.ydsf.eduConncet.api.vo.AttendedClassCountVo>
     * <AUTHOR>
     * @date 2024/10/9 14:55
     */
    List<AttendedClassCountVO> selectAttendedClassCount(SsClassTimeQuery classTimeQuery);

    /**
     * 查询课程安排
     * @param ssClassTimeQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
     * <AUTHOR>
     * @date 2024/10/11 10:51
     */
    List<SsClassTimeVO> getClassTimeByClassId(SsClassTimeQuery ssClassTimeQuery);

    /**
     * 查询课程安排分页
     * @param ssClassTimeQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
     * <AUTHOR>
     * @date 2024/10/11 10:51
     */
    IPage<SsClassTimeVO> getClassTimeByClassIdPage(Page page, @Param("query") SsClassTimeQuery ssClassTimeQuery);

    /**
     *  分页查询
     *
     * <AUTHOR>
     * @date 2024年10月15日 09时45分
     * @param ssClassTimeQuery
     */
    IPage<SsClassTimeVO> page(Page page, @Param("query") SsClassTimeQuery ssClassTimeQuery);

    /**
     * 查询班级下指定授权设备进行中,未开始的课次信息
     * @param classTimeDto
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
     * <AUTHOR>
     * @date 2024/10/15 9:56
     */
    List<SsClassTimeVO> getDeviceClassTimeByClassId(SsClassTimeDTO classTimeDto);

    /**
     * 根据id获取课次信息
     * <AUTHOR>
     * @date 2024/10/18 15:09
     * @param ids
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
     */
    List<SsClassTimeVO> selectDeviceClassTimeByIds(List<Long> ids);
    /**
     *  查询课次对应设备信息
     *
     * <AUTHOR>
     * @date 2024年10月17日 16时21分
     * @param classTimeDto
     */
    List<SsClassTimeVO> selectDeviceInfosByClassId(SsClassTimeDTO classTimeDto);

  /**
   * 根据classRoomId以及查询日期获取所排课次
   *
   * <AUTHOR>
   * @date 2024/10/24 15:24
   * @param classRoomIds
   * @param
   * @param
   * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
   */
  List<SsClassTimeVO> getFreeTimeClassTimes(
      List<Long> classRoomIds, LocalDateTime selectAttendClassStartTime, LocalDateTime selectAttendClassEndTime);

  /**
   * 查询未同步至声网并且距离开课前24小时内课次数据
   *
   * <AUTHOR>
   * @date 2025/2/14 10:16
   * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
   */
  List<SsClassTimeVO> selectSyncAgora();
}
