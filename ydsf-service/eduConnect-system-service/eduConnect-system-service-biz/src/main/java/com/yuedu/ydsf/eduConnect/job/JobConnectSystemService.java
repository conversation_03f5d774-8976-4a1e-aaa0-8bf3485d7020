package com.yuedu.ydsf.eduConnect.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yuedu.ydsf.eduConnect.service.LiveChannelService;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanPubService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 双师后台定时任务
 *
 * @date 2024/12/24 15:31
 * @project @Title: JobConnectSystemService.java
 */
@Slf4j
@Service
public class JobConnectSystemService {

  @Resource private TeachingPlanPubService teachingPlanPubService;

  @Resource private LiveChannelService liveChannelService;

  @Resource private SsClassTimeService ssClassTimeService;

  /**
   * 更新教学计划为关闭状态
   *
   * <AUTHOR>
   * @date 2024/12/24 16:20
   * @return void
   */
  @XxlJob("updateTeachingPlanPubClose")
  public void demoJobHandler() {
    teachingPlanPubService.updateTeachingPlanPubClose();
    // 调用日志打印 （此日志会在 xxl-job-admin 控制台日志列表）
    XxlJobHelper.log("更新教学计划为关闭状态定时任务成功！");
    // v2.3.0 设置任务结果
    XxlJobHelper.handleSuccess();
  }

  /**
   * SP1自动创建声网房间
   *
   * <AUTHOR>
   * @date 2025/2/14 10:08
   * @return void
   */
  @XxlJob("autoCreateSP1Channel")
  public void autoCreateSP1Channel() {
    ssClassTimeService.autoCreateSP1Channel();
    // 调用日志打印 （此日志会在 xxl-job-admin 控制台日志列表）
    XxlJobHelper.log("SP1自动创建直播频道定时任务成功！");
    // v2.3.0 设置任务结果
    XxlJobHelper.handleSuccess();
  }

  /**
   * 自动创建直播频道
   *
   * <AUTHOR>
   * @date 2024/12/28 10:15
   * @return void
   */
  @XxlJob("autoCreateChannel")
  public void autoCreateChannel() {
    liveChannelService.autoCreateChannel();
    // 调用日志打印 （此日志会在 xxl-job-admin 控制台日志列表）
    XxlJobHelper.log("自动创建直播频道定时任务成功！");
    // v2.3.0 设置任务结果
    XxlJobHelper.handleSuccess();
  }
}
