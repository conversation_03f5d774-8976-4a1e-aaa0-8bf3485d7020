package com.yuedu.ydsf.eduConnect.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.stream.CollectorUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.permission.api.feign.RemoteLecturerInfoService;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.api.feign.RemoteClassRoomService;
import com.yuedu.store.api.feign.RemoteClassService;
import com.yuedu.store.api.feign.RemoteEmployeeService;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.dto.ClassRoomDTO;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.teaching.api.feign.RemoteStageService;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.admin.api.feign.RemoteTenantService;
import com.yuedu.ydsf.common.core.config.AsyncConfiguration;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.StageProductEnum;
import com.yuedu.ydsf.eduConnect.api.vo.CourseLiveVO;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanPub;
import com.yuedu.ydsf.eduConnect.manager.CourseLiveManager;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanPubMapper;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/02/25
 **/
@Component
public class CourseLiveManagerImpl implements CourseLiveManager {


    @Autowired
    private RemoteCampusService remoteCampusService;

    @Autowired
    private RemoteLecturerInfoService remoteLecturerInfoService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteClassService remoteClassService;

    @Autowired
    private RemoteClassRoomService remoteClassRoomService;

    @Autowired
    private  RemoteStageService   remoteStageService;

    @Autowired
    private TeachingPlanPubMapper teachingPlanPubMapper;

    @Autowired
    private  AsyncConfiguration asyncConfiguration;



    @Override
    public IPage<CourseLiveVO> fillDate(IPage<CourseLiveVO> page) {

        if(CollectionUtils.isEmpty(page.getRecords())){
            return page;
        }

        Map<Long, CampusVO> storeCache = new HashMap();
        Map<Long, ClassVO> classCache = new HashMap();
        Map<Long, ClassRoomVO> classRoomCache = new HashMap();
        Map<Long, EmployeeVO> employeeCache = new HashMap();
      //  Map<Integer, StageVO> stageCache = new HashMap();

        CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
            CampusDTO campusDTO = new CampusDTO();
            campusDTO.setSchoolIdList(
                page.getRecords().stream().map(CourseLiveVO::getStoreId).distinct().toList());
            R<List<CampusVO>> campusList = remoteCampusService.getCampusList(campusDTO);
            if (campusList.isOk() && CollectionUtils.isNotEmpty(campusList.getData())) {
                campusList.getData().forEach(s -> {
                    storeCache.put(s.getId(), s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
                R<List<ClassVO>> classByIdList = remoteClassService
                    .getClassByIdList(
                        page.getRecords().stream().map(CourseLiveVO::getClassId).distinct().toList());
                if (classByIdList.isOk() && CollectionUtils.isNotEmpty(classByIdList.getData())) {
                    classByIdList.getData().forEach(s -> {
                        classCache.put(Long.valueOf(s.getId()), s);
                    });
                }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task3 = CompletableFuture.runAsync(() -> {
              R<Map<Long, ClassRoomVO>> classRoomByIdList = remoteClassRoomService
                      .getClassRoomMapByIdList(page.getRecords().stream().map(CourseLiveVO::getClassroomId).distinct().toList());
              if (classRoomByIdList.isOk() && CollectionUtils.isNotEmpty(classRoomByIdList.getData())){
                  classRoomByIdList.getData().forEach((k,v)->{
                      classRoomCache.put(k,v);
                  });
               }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task4 = CompletableFuture.runAsync(() -> {
              R<List<EmployeeVO>> employeeMapByIdList = remoteEmployeeService.getEmployeeMapByIdList(page.getRecords().stream().map(CourseLiveVO::getTeacherId).distinct().toList());
                  if (employeeMapByIdList.isOk() && CollectionUtils.isNotEmpty(employeeMapByIdList.getData())){
                      employeeMapByIdList.getData().forEach(s->{
                          employeeCache.put(s.getUserId(),s);
                      });
                  }
        }, asyncConfiguration.getAsyncExecutor());

//        CompletableFuture<Void> task5 = CompletableFuture.runAsync(() -> {
//        R<List<StageVO>> stageList = remoteStageService.getStageList(StageProductEnum.STAGE_PRODUCT_ENUM_1.code);
//               if (stageList.isOk() && CollectionUtils.isNotEmpty(stageList.getData())){
//                   stageList.getData().forEach(s->{
//                       stageCache.put(s.getId(),s);
//                   });
//               }
//        }, asyncConfiguration.getAsyncExecutor());



        Map<Long, TeachingPlanPub> teachingPlanPubMap = teachingPlanPubMapper
            .selectList(Wrappers.<TeachingPlanPub>lambdaQuery().in(TeachingPlanPub::getTeachingPlanId,page.getRecords().stream().map(CourseLiveVO::getTeachingPlanId).distinct().toList()))
            .stream().collect(Collectors.toMap(s -> s.getTeachingPlanId(), s -> s, (k1, k2) -> k2));

        CompletableFuture.allOf(task1,task2,task3,task4).join();

        return page.convert(s->{
            s.setStoreName(storeCache.getOrDefault(s.getStoreId(),new CampusVO()).getCampusName());
            s.setClassName(classCache.getOrDefault(s.getClassId(),new ClassVO()).getCName());
            s.setClassroomName(classRoomCache.getOrDefault(s.getClassroomId(),new ClassRoomVO()).getClassRoomName());
            s.setTeacherName(employeeCache.getOrDefault(s.getTeacherId(),new EmployeeVO()).getName());
           // s.setStageName(stageCache.getOrDefault(s.getStage(),new StageVO()).getStageName());

            if(CollectionUtil.isNotEmpty(teachingPlanPubMap)){
                TeachingPlanPub planPub = teachingPlanPubMap.getOrDefault(s.getTeachingPlanId(),new TeachingPlanPub());
                s.setCourseName(planPub.getCourseName());
                s.setLectureName(planPub.getLectureName());
                s.setClose(planPub.getClosed());
            }
            return s;
        });
    }
}
