package com.yuedu.ydsf.eduConnect.manager.impl;

import com.alibaba.fastjson.JSONObject;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjLog;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjPushTask;
import com.yuedu.ydsf.eduConnect.manager.SsXiaogjLogManager;
import com.yuedu.ydsf.eduConnect.mapper.SsXiaogjLogMapper;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.SsPushXiaogjType;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.SsPushXiaogjEventReq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.time.temporal.ChronoField;

/**
 * 校管家日志表 公共服务类
 *
 * <AUTHOR>
 * @date 2024/10/28 16:09
 */
@Slf4j
@Component
@AllArgsConstructor
public class SsXiaogjLogManagerImpl implements SsXiaogjLogManager {

    private final SsXiaogjLogMapper ssXiaogjLogMapper;

    /**
     * 保存排课校管家日志
     *
     * @param ssPushXiaogjEventReq
     * @param classCourseReq
     * @return void
     * <AUTHOR>
     * @date 2024/10/28 16:24
     */
    @Override
    public void saveXiaogjLog(SsPushXiaogjEventReq ssPushXiaogjEventReq,
        ClassCourseReq classCourseReq, String userName) {

        // 保存请求校管家日志
        SsXiaogjLog ssXiaogjLog = new SsXiaogjLog();
        ssXiaogjLog.setRequestId(ssPushXiaogjEventReq.getEventId());
//        ssXiaogjLog.setRequestParam(JSONObject.toJSONString(classCourseReq));
        ssXiaogjLog.setTimestamp(ssPushXiaogjEventReq.getEventTimestamp());
        ssXiaogjLog.setEventKey(SsPushXiaogjType.CLASS_COURSE.eventKey);
        ssXiaogjLog.setRequestParam(JSONObject.toJSONString(ssPushXiaogjEventReq));
        ssXiaogjLog.setCreator(userName);
        ssXiaogjLog.setModifer(userName);

        ssXiaogjLogMapper.insert(ssXiaogjLog);

    }

    /**
     * 保存排课校管家日志
     */
    @Override
    public void saveXiaogjLog(SsXiaogjPushTask ssXiaogjPushTaskEntity, String userName) {
        // 保存请求校管家日志
        SsXiaogjLog ssXiaogjLog = new SsXiaogjLog();
        ssXiaogjLog.setRequestId(ssXiaogjPushTaskEntity.getXgjEventId());
        ssXiaogjLog.setTimestamp(ssXiaogjPushTaskEntity.getUpdateTime().getLong(ChronoField.MILLI_OF_SECOND));
        ssXiaogjLog.setEventKey(ssXiaogjPushTaskEntity.getXgjEventKey());
        ssXiaogjLog.setRequestParam(ssXiaogjPushTaskEntity.getMessageBody());
        ssXiaogjLog.setCreator(userName);
        ssXiaogjLog.setModifer(userName);
        ssXiaogjLogMapper.insert(ssXiaogjLog);
    }


}
