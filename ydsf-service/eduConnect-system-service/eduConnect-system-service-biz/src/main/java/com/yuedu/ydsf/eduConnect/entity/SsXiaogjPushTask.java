package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 推送排课数据到校管家定时任务表
 *
 * <AUTHOR>
 * @date 2025-02-25 10:07:36
 */
@Data
@TableName("ss_xiaogj_push_task")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "推送排课数据到校管家定时任务表")
public class SsXiaogjPushTask extends Model<SsXiaogjPushTask> {

 
	/**
	* id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="id")
    private Long id;

	/**
	* ss_course_schedule.id
	*/
    @Schema(description="ss_course_schedule.id")
    private Long courseScheduleId;

    /**
     * 课次ID
     */
    @Schema(description="课次ID")
    private Long classTimeId;

	/**
	* 上课时间
	*/
    @Schema(description="上课时间")
    private LocalDateTime attendClassStartTime;

	/**
	* 校管家教室ID
	*/
    @Schema(description="校管家教室ID")
    private String xgjClassRoomId;

	/**
	* 推送校管家消息事件ID，全局唯一
	*/
    @Schema(description="推送校管家消息事件ID，全局唯一")
    private String xgjEventId;

	/**
	* 推送校管家消息事件类型
	*/
    @Schema(description="推送校管家消息事件类型")
    private String xgjEventKey;

	/**
	* 推送消息体内容
	*/
    @Schema(description="推送消息体内容")
    private String messageBody;

	/**
	* 任务状态
	*/
    @Schema(description="任务状态")
    private Integer taskStatus;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除")
    private Integer delFlag;
}