package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 排课表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:10:16
 */
@Data
@TableName("ss_course_schedule")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "排课表实体类")
public class SsCourseSchedule extends Model<SsCourseSchedule> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 主讲老师ID(ss_lecturer主键ID)
	*/
    @Schema(description="主讲老师ID(ss_lecturer主键ID)")
    private Long lecturerId;

	/**
	* 排课方式: 0-按周排课; 1-按日历排课;
	*/
    @Schema(description="排课方式: 0-按周排课; 1-按日历排课;")
    private Integer classTimeMethod;

	/**
	* 上课开始日期（yyyy-MM-dd）
	*/
    @Schema(description="上课开始日期（yyyy-MM-dd）")
    private LocalDate attendClassStartDate;

	/**
	* 上课结束日期（yyyy-MM-dd）
	*/
    @Schema(description="上课结束日期（yyyy-MM-dd）")
    private LocalDate attendClassEndDate;

	/**
	* 排课上限几次
	*/
    @Schema(description="排课上限几次")
    private Integer scheduleCap;

	/**
	* 上课类型: 0-直播课; 1-点播课;
	*/
    @Schema(description="上课类型: 0-直播课; 1-点播课;")
    private Integer attendClassType;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑者")
    private String modifer;

	/**
	* 删除标记
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记")
    private Integer delFlag;
}
