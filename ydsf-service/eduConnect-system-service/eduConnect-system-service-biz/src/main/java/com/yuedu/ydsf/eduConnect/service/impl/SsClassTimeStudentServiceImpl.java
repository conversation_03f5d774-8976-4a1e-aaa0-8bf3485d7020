package com.yuedu.ydsf.eduConnect.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.api.feign.RemoteCourseStudentService;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.dto.XiaogjBusinessIdDTO;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.TclassCourseStudentVo;
import com.yuedu.store.vo.XiaogjStudentVo;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeStudent;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeStudentMapper;
import com.yuedu.ydsf.eduConnect.service.SsClassAuthRoomStudentService;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeStudentService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 校区上课学生表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:32:06
 */
@Slf4j
@Service
@AllArgsConstructor
public class SsClassTimeStudentServiceImpl extends ServiceImpl<SsClassTimeStudentMapper, SsClassTimeStudent> implements SsClassTimeStudentService {


    private final SsClassTimeAuthRoomMapper ssClassTimeAuthRoomMapper;

    private final SsClassAuthRoomMapper ssClassAuthRoomMapper;

    private final SsClassAuthRoomStudentService ssClassAuthRoomStudentService;

    private final RemoteCampusService remoteCampusService;

    private final RemoteCourseStudentService remoteCourseStudentService;


    /**
     * 校管家排课学生信息同步双师系统
     * @param xiaogjBusinessIdDTO
     * @return void
     * <AUTHOR>
     * @date 2025/2/12 14:02
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncXiaogjStudent(XiaogjBusinessIdDTO xiaogjBusinessIdDTO) {

        R<TclassCourseStudentVo> tclassCourseStudentVoR = remoteCourseStudentService.getXgjPushClassCourseStudent(xiaogjBusinessIdDTO);

        log.info("查询校管家指定班级/排课学生信息返回参数: {}", JSON.toJSONString(tclassCourseStudentVoR));

        if(!tclassCourseStudentVoR.isOk()){
            return;
        }

        if(StringUtils.isBlank(tclassCourseStudentVoR.getData().getCClassId())
            && StringUtils.isBlank(tclassCourseStudentVoR.getData().getCCourseId())){
            return;
        }

        // 查询班级授权信息
        List<SsClassAuthRoom> ssClassAuthRoomList = ssClassAuthRoomMapper.selectList(Wrappers.lambdaQuery(SsClassAuthRoom.class)
            .eq(SsClassAuthRoom::getXgjClassId, tclassCourseStudentVoR.getData().getClassThreeId())
        );

        if(CollectionUtils.isEmpty(ssClassAuthRoomList)){
            return;
        }

        TclassCourseStudentVo tclassCourseStudentVo = tclassCourseStudentVoR.getData();

        // 班级下学生信息
        List<XiaogjStudentVo> xiaogjClassStudentList = tclassCourseStudentVo.getXiaogjClassStudent();

        // 班级下课次学生信息
        List<XiaogjStudentVo> xiaogjCourseStudentList = tclassCourseStudentVo.getXiaogjCourseStudent();

        // 无学生排课信息
        List<String> xiaogjNoStudentCourseId = tclassCourseStudentVo.getXiaogjNoStudentCourseId();

        // 同步排课ID进行分组
        Map<String, List<XiaogjStudentVo>> xiaogjStudentMap = xiaogjCourseStudentList.stream().collect(
            Collectors.groupingBy(XiaogjStudentVo::getCourseTreeId));

        // 保存课次对应学生信息
        for(Map.Entry<String, List<XiaogjStudentVo>> map : xiaogjStudentMap.entrySet()){

            // 创建课次学生信息
            createClassTimeStudent(map.getKey(),map.getValue(),xiaogjClassStudentList);
        }

        // 删除无学生排课信息下的学生
        if(CollectionUtils.isNotEmpty(xiaogjNoStudentCourseId)){
            deleteNoClassTimeStudent(xiaogjNoStudentCourseId);
        }

        // 保存班级对应学生信息
        createClassAuthRoomStudent(tclassCourseStudentVo,xiaogjClassStudentList);

    }

    @Override
    public Boolean existStudentByClassRoomId(Long classRoomId) {
        return this.exists(Wrappers.lambdaQuery(SsClassTimeStudent.class).eq(SsClassTimeStudent::getClassRoomId, classRoomId));
    }

    /**
     * 删除无学生排课信息下的学生
     * <AUTHOR>
     * @date 2024年04月16日 14时00分
     * @param xiaogjNoStudentCourseId
     */
    private void deleteNoClassTimeStudent(List<String> xiaogjNoStudentCourseId) {

        // 通过校管家排课ID查询课次授权信息
        List<SsClassTimeAuthRoom> ssClassTimeAuthRoomList = ssClassTimeAuthRoomMapper.selectList(
            Wrappers.lambdaQuery(SsClassTimeAuthRoom.class)
                .in(SsClassTimeAuthRoom::getXgjClassTimeId, xiaogjNoStudentCourseId)
        );

        List<Long> classTimeAuthRoomIdList = ssClassTimeAuthRoomList.stream()
            .map(SsClassTimeAuthRoom::getId)
            .distinct()
            .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(classTimeAuthRoomIdList)){

            this.remove(Wrappers.lambdaQuery(SsClassTimeStudent.class)
                .in(SsClassTimeStudent::getClassTimeAuthRoomId, classTimeAuthRoomIdList)
            );

        }


    }

    /**
     * 创建班级授权校区学生
     * <AUTHOR>
     * @date 2024年04月15日 15时01分
     * @param tclassCourseStudentVo
     * @param xiaogjClassStudentList
     */
    private void createClassAuthRoomStudent(TclassCourseStudentVo tclassCourseStudentVo,List<XiaogjStudentVo> xiaogjClassStudentList) {


        // 通过班级授权ID查询班级信息
        List<SsClassAuthRoom> ssClassAuthRoomList = ssClassAuthRoomMapper.selectList(Wrappers.lambdaQuery(SsClassAuthRoom.class)
            .eq(SsClassAuthRoom::getXgjClassId, tclassCourseStudentVo.getClassThreeId())
        );

        if(CollectionUtils.isEmpty(ssClassAuthRoomList)){
            return;
        }

        SsClassAuthRoom ssClassAuthRoom = ssClassAuthRoomList.get(0);

        // 根据校区ID获取校区信息
        CampusDTO campusDTO = new CampusDTO();
        campusDTO.setSchoolId(ssClassAuthRoom.getCampusId());
        R<List<CampusVO>> campusList = remoteCampusService.getCampusAll(campusDTO);

        List<String> newStudentIdList = xiaogjClassStudentList.stream()
            .map(XiaogjStudentVo::getStudentId)
            .distinct()
            .collect(Collectors.toList());

        // 查询班级授权校区学生
        List<SsClassAuthRoomStudent> classAuthRoomStudentList = ssClassAuthRoomStudentService.list(Wrappers.lambdaQuery(SsClassAuthRoomStudent.class)
            .eq(SsClassAuthRoomStudent::getClassId, ssClassAuthRoom.getClassId())
            .eq(SsClassAuthRoomStudent::getCampusId, ssClassAuthRoom.getCampusId())
        );

        List<String> oldStudentIdList = classAuthRoomStudentList.stream()
            .map(SsClassAuthRoomStudent::getStudentId)
            .distinct()
            .collect(Collectors.toList());

        // 删除本次移除学生
        List<String> deleteStudentIdList = new ArrayList<>(oldStudentIdList);
        deleteStudentIdList.removeAll(newStudentIdList);
        if(CollectionUtils.isNotEmpty(deleteStudentIdList)){
            ssClassAuthRoomStudentService.remove(Wrappers.lambdaQuery(SsClassAuthRoomStudent.class)
                .eq(SsClassAuthRoomStudent::getClassId, ssClassAuthRoom.getClassId())
                .eq(SsClassAuthRoomStudent::getCampusId, ssClassAuthRoom.getCampusId())
                .in(SsClassAuthRoomStudent::getStudentId,deleteStudentIdList)
            );
        }

        for (XiaogjStudentVo xiaogjStudentVo : xiaogjClassStudentList) {

            boolean isContainStudent = oldStudentIdList.contains(xiaogjStudentVo.getStudentId());

            // 校验学生是否在本班级已存在,不存在则新增,存着则更新
            if(!isContainStudent){

                SsClassAuthRoomStudent ssClassAuthRoomStudent = new SsClassAuthRoomStudent();
                ssClassAuthRoomStudent.setClassId(ssClassAuthRoom.getClassId());
                ssClassAuthRoomStudent.setCampusId(ssClassAuthRoom.getCampusId());
                ssClassAuthRoomStudent.setStudentId(xiaogjStudentVo.getStudentId());
                ssClassAuthRoomStudent.setStudentMobile(xiaogjStudentVo.getStudentMobile());
                ssClassAuthRoomStudent.setStudentName(xiaogjStudentVo.getStudentName());
                ssClassAuthRoomStudent.setCtime(LocalDateTime.now());
                ssClassAuthRoomStudent.setMtime(LocalDateTime.now());
                if(campusList.isOk() && CollectionUtils.isNotEmpty(campusList.getData())){
                    ssClassAuthRoomStudent.setCreator(campusList.getData().get(0).getCampusName());
                    ssClassAuthRoomStudent.setModifer(campusList.getData().get(0).getCampusName());
                }

                ssClassAuthRoomStudentService.save(ssClassAuthRoomStudent);

            }else{
                SsClassAuthRoomStudent ssClassAuthRoomStudent = new SsClassAuthRoomStudent();
                ssClassAuthRoomStudent.setStudentMobile(xiaogjStudentVo.getStudentMobile());
                ssClassAuthRoomStudent.setStudentName(xiaogjStudentVo.getStudentName());
                ssClassAuthRoomStudentService.update(ssClassAuthRoomStudent, Wrappers.lambdaUpdate(SsClassAuthRoomStudent.class)
                    .eq(SsClassAuthRoomStudent::getClassId, ssClassAuthRoom.getClassId())
                    .eq(SsClassAuthRoomStudent::getCampusId, ssClassAuthRoom.getCampusId())
                    .eq(SsClassAuthRoomStudent::getStudentId, xiaogjStudentVo.getStudentId())
                );
            }

        }

    }

    /**
     * 创建课次学生信息
     * <AUTHOR>
     * @date 2024年04月15日 09时58分
     * @param courseTreeId 校管家与双师系统排课ID
     * @param xiaogjCourseStudentList 课次学生信息
     * @param xiaogjClassStudentList 班级学生信息
     */
    private void createClassTimeStudent(String courseTreeId,List<XiaogjStudentVo> xiaogjCourseStudentList,List<XiaogjStudentVo> xiaogjClassStudentList) {

        // 通过排课授权校区ID查询排课信息
        List<SsClassTimeAuthRoom> ssClassTimeAuthRoomList = ssClassTimeAuthRoomMapper.selectList(Wrappers.lambdaQuery(SsClassTimeAuthRoom.class)
            .eq(SsClassTimeAuthRoom::getXgjClassTimeId, courseTreeId)
        );

        if(CollectionUtils.isEmpty(ssClassTimeAuthRoomList)){
            return;
        }

        SsClassTimeAuthRoom ssClassTimeAuthRoom = ssClassTimeAuthRoomList.get(0);

        // 根据校区ID获取校区信息
        CampusDTO campusDTO = new CampusDTO();
        campusDTO.setSchoolId(ssClassTimeAuthRoom.getCampusId());
        R<List<CampusVO>> campusList = remoteCampusService.getCampusAll(campusDTO);

        if(CollectionUtils.isNotEmpty(xiaogjCourseStudentList)){

            List<String> newStudentIdList = xiaogjCourseStudentList.stream()
                .map(XiaogjStudentVo::getStudentId)
                .distinct()
                .collect(Collectors.toList());

            // 查询本校区本课次已存在学生信息
            List<SsClassTimeStudent> classTimeStudentList = this.list(Wrappers.lambdaQuery(SsClassTimeStudent.class)
                .eq(SsClassTimeStudent::getClassTimeAuthRoomId, ssClassTimeAuthRoom.getId())
                .eq(SsClassTimeStudent::getClassTimeId, ssClassTimeAuthRoom.getClassTimeId())
            );

            List<String> oldStudentIdList = classTimeStudentList.stream()
                .map(SsClassTimeStudent::getStudentId)
                .distinct()
                .collect(Collectors.toList());

            // 删除本次移除学生
            List<String> deleteStudentIdList = new ArrayList<>(oldStudentIdList);
            deleteStudentIdList.removeAll(newStudentIdList);
            if(CollectionUtils.isNotEmpty(deleteStudentIdList) ){
                this.remove(Wrappers.lambdaQuery(SsClassTimeStudent.class)
                    .eq(SsClassTimeStudent::getClassTimeAuthRoomId, ssClassTimeAuthRoom.getId())
                    .eq(SsClassTimeStudent::getClassTimeId, ssClassTimeAuthRoom.getClassTimeId())
                    .in(SsClassTimeStudent::getStudentId,deleteStudentIdList)
                );
            }

            for (XiaogjStudentVo xiaogjStudentVo : xiaogjCourseStudentList) {

                boolean isContainStudent = oldStudentIdList.contains(xiaogjStudentVo.getStudentId());

                // 校验学生是否在本课次已存在,不存在则新增,存着则更新
                if(!isContainStudent){

                    SsClassTimeStudent ssClassTimeStudent = new SsClassTimeStudent();
                    ssClassTimeStudent.setClassId(ssClassTimeAuthRoom.getClassId());
                    ssClassTimeStudent.setClassTimeAuthRoomId(ssClassTimeAuthRoom.getId());
                    ssClassTimeStudent.setClassTimeId(ssClassTimeAuthRoom.getClassTimeId());
                    ssClassTimeStudent.setCampusId(ssClassTimeAuthRoom.getCampusId());
                    ssClassTimeStudent.setClassRoomId(ssClassTimeAuthRoom.getClassRoomId());
                    ssClassTimeStudent.setDeviceId(ssClassTimeAuthRoom.getDeviceId());
                    ssClassTimeStudent.setStudentId(xiaogjStudentVo.getStudentId());
                    ssClassTimeStudent.setStudentName(xiaogjStudentVo.getStudentName());
                    ssClassTimeStudent.setStudentMobile(xiaogjStudentVo.getStudentMobile());
                    ssClassTimeStudent.setCtime(LocalDateTime.now());
                    ssClassTimeStudent.setMtime(LocalDateTime.now());
                    if(campusList.isOk() && CollectionUtils.isNotEmpty(campusList.getData())){
                        ssClassTimeStudent.setCreator(campusList.getData().get(0).getCampusName());
                        ssClassTimeStudent.setModifer(campusList.getData().get(0).getCampusName());
                    }
                    this.save(ssClassTimeStudent);

                }else{

                    SsClassTimeStudent ssClassTimeStudent = new SsClassTimeStudent();
                    ssClassTimeStudent.setStudentName(xiaogjStudentVo.getStudentName());
                    ssClassTimeStudent.setStudentMobile(xiaogjStudentVo.getStudentMobile());
                    this.update(ssClassTimeStudent, Wrappers.lambdaUpdate(SsClassTimeStudent.class)
                        .eq(SsClassTimeStudent::getClassTimeAuthRoomId, ssClassTimeAuthRoom.getId())
                        .eq(SsClassTimeStudent::getClassTimeId, ssClassTimeAuthRoom.getClassTimeId())
                        .eq(SsClassTimeStudent::getStudentId, xiaogjStudentVo.getStudentId())
                    );

                }

            }

        }

    }


}
