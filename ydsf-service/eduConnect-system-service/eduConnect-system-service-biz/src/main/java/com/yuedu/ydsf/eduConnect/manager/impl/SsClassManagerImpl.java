package com.yuedu.ydsf.eduConnect.manager.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.store.api.feign.RemoteLecturerService;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.common.core.config.AsyncConfiguration;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.api.constant.AppointmentEnum;
import com.yuedu.ydsf.eduConnect.api.constant.AttendClassStateEnum;
import com.yuedu.ydsf.eduConnect.api.constant.AuthTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncXiaogjEnum;
import com.yuedu.ydsf.eduConnect.api.constant.SsClassStateEnum;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassAuthInfoDTO;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassDTO;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassVO;
import com.yuedu.ydsf.eduConnect.entity.SsAppointmentClassLog;
import com.yuedu.ydsf.eduConnect.entity.SsAuthRoomLog;
import com.yuedu.ydsf.eduConnect.entity.SsClass;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeStudent;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjPushTask;
import com.yuedu.ydsf.eduConnect.manager.SsClassManager;
import com.yuedu.ydsf.eduConnect.manager.SsXiaogjLogManager;
import com.yuedu.ydsf.eduConnect.mapper.SsAppointmentClassLogMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsAuthRoomLogMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomStudentMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeStudentMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsXiaogjPushTaskMapper;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.SsPushXiaogjType;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq.CreateClassType;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq.CourseData;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.DeleteCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.SsPushXiaogjEventReq;
import com.yuedu.ydsf.eduConnect.manager.XiaoGuanJiaService;
import com.yuedu.ydsf.eduConnect.util.DateUtil;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 班级信息表 公共服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-09 10:44:48
 */
@Slf4j
@Component
@AllArgsConstructor
public class SsClassManagerImpl implements SsClassManager {

    private final SsClassMapper ssClassMapper;

    private final SsClassAuthRoomMapper ssClassAuthRoomMapper;

    private final SsClassTimeMapper ssClassTimeMapper;

    private final SsAppointmentClassLogMapper ssAppointmentClassLogMapper;

    private final SsClassTimeAuthRoomMapper ssClassTimeAuthRoomMapper;

    private final SsClassTimeStudentMapper ssClassTimeStudentMapper;

    private final SsAuthRoomLogMapper ssAuthRoomLogMapper;

    private final SsClassAuthRoomStudentMapper ssClassAuthRoomStudentMapper;

    private final XiaoGuanJiaService xiaoGuanJiaService;

    private final RemoteLecturerService remoteLecturerService;

    private final AsyncConfiguration asyncConfiguration;

    private final SsXiaogjLogManager xiaogjLogManager;
    private final SsXiaogjPushTaskMapper ssXiaogjPushTaskMapper;

    /**
     * 实体类转换VO类
     *
     * @param ssClassList
     * @return java.util.List<com.yuedu.ydsf.eduConncet.api.vo.SsClassVO>
     * <AUTHOR>
     * @date 2024/10/10 10:56
     */
    @Override
    public List<SsClassVO> entityConvertVo(List<SsClass> ssClassList) {
        return ssClassList.stream().map(entity -> {
            SsClassVO ssClassVO = new SsClassVO();
            BeanUtils.copyProperties(entity, ssClassVO);
            return ssClassVO;
        }).toList();
    }

    /**
     * 处理授权校区并同步校管家
     *
     * @param ssClassDTO
     * @return void
     * <AUTHOR>
     * @date 2024/10/15 9:25
     */
    @Override
    public void bindClassAuthRoom(SsClassDTO ssClassDTO) {

        // 验证班级是否可操作
        SsClass ssClass = checkClassState(ssClassDTO.getId());

        ssClassDTO.setIsSyncXiaogj(ssClass.getIsSyncXiaogj());

        // 查询班级已授权设备信息
        List<SsClassAuthRoom> oldClassAuthRoomList = ssClassAuthRoomMapper.selectList(
            Wrappers.lambdaQuery(SsClassAuthRoom.class)
                .eq(SsClassAuthRoom::getClassId, ssClassDTO.getId())
                .eq(SsClassAuthRoom::getAppointmentStatus, AppointmentEnum.TYPE_1.CODE)
                .and(wrapper -> wrapper.isNull(SsClassAuthRoom::getClassTimeIds)
                    .or()
                    .eq(SsClassAuthRoom::getClassTimeIds, "")
                )
        );

        List<Long> oldDeviceIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(oldClassAuthRoomList)) {
            oldDeviceIdList = oldClassAuthRoomList.stream()
                .map(SsClassAuthRoom::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        }

        // 本次班级授权设备信息
        List<SsClassAuthInfoDTO> newAuthClassRoomList = ssClassDTO.getSsClassAuthInfoDTOList();
        List<Long> newDeviceIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(newAuthClassRoomList)) {

            newDeviceIdList = newAuthClassRoomList.stream()
                .map(SsClassAuthInfoDTO::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(oldDeviceIdList) && CollectionUtils.isEmpty(newDeviceIdList)) {
            return;
        }

        // 设备ID排序
        oldDeviceIdList.sort(Comparator.comparing(Long::longValue));
        newDeviceIdList.sort(Comparator.comparing(Long::longValue));

        // 本次授权新增的设备信息
        List<Long> addDeviceIdList = new ArrayList<>(newDeviceIdList);
        addDeviceIdList.removeAll(oldDeviceIdList);
        List<SsClassAuthInfoDTO> saveAuthDeviceList = newAuthClassRoomList.stream()
            .filter(room -> addDeviceIdList.contains(room.getDeviceId()))
            .collect(Collectors.toList());

        // 本次授权删除的设备信息
        List<Long> deleteDeviceIdList = new ArrayList<>(oldDeviceIdList);
        deleteDeviceIdList.removeAll(newDeviceIdList);

        // 本次授权更新的设备信息
        List<Long> deleteOldDeviceIdList = new ArrayList<>(deleteDeviceIdList);
        List<Long> updateDeviceIdList = new ArrayList<>(oldDeviceIdList);
        updateDeviceIdList.removeAll(deleteOldDeviceIdList);
        List<SsClassAuthInfoDTO> updateAuthDeviceList = newAuthClassRoomList.stream()
            .filter(room -> updateDeviceIdList.contains(room.getDeviceId()))
            .collect(Collectors.toList());

        // 校管家班级同步参数封装
        List<ClassCourseReq> campusClassList = new ArrayList<>();

        // ======================本次授权删除设备相关处理======================
        if (CollectionUtils.isNotEmpty(deleteDeviceIdList)) {

            deleteAuthDeviceDispose(ssClassDTO, deleteDeviceIdList, campusClassList);
            log.debug("班级管理授权校区,删除授权设备信息,班级ID:{},班级名称:{},删除设备ID:{}",
                ssClassDTO.getId(),
                ssClassDTO.getClassName(),
                JSONUtil.toJsonStr(campusClassList));

        }

        // ======================本次授权新增设备相关处理======================
        if (CollectionUtils.isNotEmpty(saveAuthDeviceList)) {

            saveAuthDeviceDispose(ssClassDTO, saveAuthDeviceList, campusClassList);
            log.debug("班级管理授权校区,新增授权设备信息,班级ID:{},班级名称:{},新增设备ID:{}",
                ssClassDTO.getId(),
                ssClassDTO.getClassName(),
                JSONUtil.toJsonStr(campusClassList));

        }

        // ======================本次授权编辑设备相关处理======================
        if (CollectionUtils.isNotEmpty(updateAuthDeviceList)) {

            updateAuthDeviceDispose(ssClassDTO, updateDeviceIdList, campusClassList);
            log.debug("班级管理授权校区,编辑授权设备信息,班级ID:{},班级名称:{},编辑设备ID:{}",
                ssClassDTO.getId(),
                ssClassDTO.getClassName(),
                JSONUtil.toJsonStr(campusClassList));

        }

        // 记录更改班级授权设备修改日志
        saveSsAuthRoomLog(ssClassDTO, oldDeviceIdList, newDeviceIdList);

        // 异步同步校管家
        if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClassDTO.getIsSyncXiaogj())) {

            String userName = SecurityUtils.getUser().getUsername();

            TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        asyncConfiguration.getAsyncExecutor().execute(() -> {
                            for (ClassCourseReq classCourseReq : campusClassList) {

                                Long classTimeId = classCourseReq.getClassTimeId();

                                try {
                                    if (Objects.isNull(classTimeId)) {
                                        handleNonClassTimeSync(classCourseReq, userName);
                                    } else {
                                        handleClassTimeSync(classCourseReq, classTimeId, userName);
                                    }
                                } catch (Exception e) {
                                    log.error(
                                        "同步校管家处理异常,班级ID:{},班级名称:{},课次ID:{},错误信息:{}",
                                        ssClassDTO.getId(), ssClassDTO.getClassName(), classTimeId,
                                        e.getMessage(), e);
                                }
                            }
                        });
                    }
                });
        }
    }

    /**
     * 处理非课次同步
     *
     * @param classCourseReq 班级课程请求
     * @param userName       操作用户
     */
    private void handleNonClassTimeSync(ClassCourseReq classCourseReq, String userName) {
        log.info("班级管理授权校区,课次ID为空,非课次同步");
        // 双师排课推送校管家消息队列公共方法
        SsPushXiaogjEventReq ssPushXiaogjEventReq = xiaoGuanJiaService.ssPushXiaogjMessage(
            classCourseReq);

        // 保存排课校管家日志
        xiaogjLogManager.saveXiaogjLog(ssPushXiaogjEventReq, classCourseReq, userName);
    }

    /**
     * 处理课次同步
     *
     * @param classCourseReq 班级课程请求
     * @param classTimeId    课次ID
     * @param userName       操作用户
     */
    private void handleClassTimeSync(ClassCourseReq classCourseReq, Long classTimeId,
        String userName) {
        log.info("班级管理授权校区,课次ID不为空,课次同步,课次ID:{}", classTimeId);

        List<SsXiaogjPushTask> ssXiaogjPushTasks = ssXiaogjPushTaskMapper.selectList(
            Wrappers.lambdaQuery(SsXiaogjPushTask.class)
                .eq(SsXiaogjPushTask::getClassTimeId, classTimeId)
                .eq(SsXiaogjPushTask::getTaskStatus, 0));

        if (CollectionUtils.isEmpty(ssXiaogjPushTasks)) {
            handleSyncedClassTime(classCourseReq, classTimeId, userName);
        } else if (CollectionUtils.isNotEmpty(classCourseReq.getCreateCourseList())) {
            handleNewClassTime(classCourseReq, classTimeId);
        } else if (CollectionUtils.isNotEmpty(classCourseReq.getDeleteCourseList())) {
            //需要删除的课次，threeId=a964ca3f-e75d-4a40-9ed1-582eaeef6738
            Set<String> threeIdList = classCourseReq.getDeleteCourseList().stream()
                .map(DeleteCourseReq::getThreeId).filter(Objects::nonNull).collect(
                    Collectors.toSet());
            // 需要删除的校管家推送任务
            List<SsXiaogjPushTask> deleteXiaogjPushTasks = ssXiaogjPushTasks.stream()
                .filter(ssXiaogjPushTask -> threeIdList.stream()
                    .anyMatch(threeId -> ssXiaogjPushTask.getMessageBody().contains(threeId)))
                .collect(Collectors.toList());
            handleDeleteClassTime(deleteXiaogjPushTasks, classTimeId);
        }
    }

    /**
     * 处理已同步的课次
     *
     * @param classCourseReq 班级课程请求
     * @param classTimeId    课次ID
     * @param userName       操作用户
     */
    private void handleSyncedClassTime(ClassCourseReq classCourseReq, Long classTimeId,
        String userName) {
        log.info("班级管理授权校区,课次已同步校管家,课次ID:{}", classTimeId);
        SsPushXiaogjEventReq ssPushXiaogjEventReq = xiaoGuanJiaService.ssPushXiaogjMessage(
            classCourseReq);

        // 保存排课校管家日志
        xiaogjLogManager.saveXiaogjLog(ssPushXiaogjEventReq, classCourseReq, userName);
    }

    /**
     * 处理新增排课
     *
     * @param classCourseReq 班级课程请求
     * @param classTimeId    课次ID
     */
    private void handleNewClassTime(ClassCourseReq classCourseReq, Long classTimeId) {
        CourseData courseData = classCourseReq.getCreateCourseList()
            .get(0).getCCourseData().get(0);
        String cStartTime = courseData.getCStartTime();
        String xgjClassRoomId = courseData.getCClassroomID();

        SsXiaogjPushTask ssXiaogjPushTask = convertSsXiaogjPushTask(
            classCourseReq, cStartTime, xgjClassRoomId);
        log.info("班级管理授权校区,课次未同步校管家(新增排课),课次ID:{},ssXiaogjPushTask:{}",
            classTimeId, ssXiaogjPushTask);
        ssXiaogjPushTaskMapper.insert(ssXiaogjPushTask);
    }

    /**
     * 处理删除排课
     *
     * @param ssXiaogjPushTasks 校管家推送任务列表
     * @param classTimeId       课次ID
     */
    private void handleDeleteClassTime(List<SsXiaogjPushTask> ssXiaogjPushTasks, Long classTimeId) {
        List<Long> idList = ssXiaogjPushTasks.stream()
            .map(SsXiaogjPushTask::getId)
            .collect(Collectors.toList());
        log.info("班级管理授权校区,课次未同步校管家(删除排课),课次ID:{},任务ID:{}",
            classTimeId, idList);
        int deleted = ssXiaogjPushTaskMapper.deleteByIds(idList);
        log.info(
            "班级管理授权校区,课次未同步校管家(删除排课),课次ID:{},删除排课任务成功,删除条数:{}",
            classTimeId, deleted);
    }

    private SsXiaogjPushTask convertSsXiaogjPushTask(ClassCourseReq classCourseReq,
        String cStartTime, String xgjClassRoomId) {
        String eventId = UUID.randomUUID().toString();
        Long timestamp = System.currentTimeMillis();
        SsPushXiaogjEventReq<ClassCourseReq> ssPushXiaogjEventReq = new SsPushXiaogjEventReq<>();
        // 双师排课推送校管家请求参数
        ssPushXiaogjEventReq.setData(classCourseReq);
        ssPushXiaogjEventReq.setEventId(eventId);
        ssPushXiaogjEventReq.setEventKey(
            SsPushXiaogjType.CLASS_COURSE.eventKey);
        ssPushXiaogjEventReq.setEventTimestamp(timestamp);

        String jsonStr = JSONUtil.toJsonStr(ssPushXiaogjEventReq);
        SsXiaogjPushTask ssXiaogjPushTask = new SsXiaogjPushTask();
        ssXiaogjPushTask.setCourseScheduleId(0L);
        ssXiaogjPushTask.setClassTimeId(classCourseReq.getClassTimeId());
        ssXiaogjPushTask.setAttendClassStartTime(
            DateUtil.parse(cStartTime));
        ssXiaogjPushTask.setXgjClassRoomId(xgjClassRoomId);
        ssXiaogjPushTask.setXgjEventId(eventId);
        ssXiaogjPushTask.setXgjEventKey(
            SsPushXiaogjType.CLASS_COURSE.eventKey);
        ssXiaogjPushTask.setMessageBody(jsonStr);
        return ssXiaogjPushTask;
    }

    /**
     * 记录更改班级授权设备修改日志
     *
     * @param ssClassDTO
     * @param oldDeviceIdList
     * @param newDeviceIdList
     * @return void
     * <AUTHOR>
     * @date 2024/10/21 10:29
     */
    private void saveSsAuthRoomLog(SsClassDTO ssClassDTO, List<Long> oldDeviceIdList,
        List<Long> newDeviceIdList) {

        SsAuthRoomLog ssAuthRoomLog = new SsAuthRoomLog();
        ssAuthRoomLog.setBusinessId(ssClassDTO.getId());
        ssAuthRoomLog.setEditAuthType(AuthTypeEnum.AUTHTYPE_0.CODE);
        ssAuthRoomLog.setOldValue(StringUtil.join(oldDeviceIdList, ','));
        ssAuthRoomLog.setNewValue(StringUtil.join(newDeviceIdList, ','));
        ssAuthRoomLogMapper.insert(ssAuthRoomLog);

    }

    /**
     * ======================本次授权删除设备相关处理====================== 1.更新校区授权班级信息为未预约 2.更新校区预约记录表为取消预约
     * 3.删除班级下对应未开始课次授权设备信息 4.删除班级下对应未开始课次授权设备对应学生信息 5.同步校管家删除取消授权设备未开始课次信息
     *
     * @param ssClassDTO         授权参数
     * @param deleteDeviceIdList 删除设备ID集合
     * @param campusClassList    校管家同步参数
     * @return void
     * <AUTHOR>
     * @date 2024/10/17 10:48
     */
    private void deleteAuthDeviceDispose(SsClassDTO ssClassDTO,
        List<Long> deleteDeviceIdList,
        List<ClassCourseReq> campusClassList) {

        // 1.更新校区授权班级信息为未预约
        ssClassAuthRoomMapper.update(Wrappers.lambdaUpdate(SsClassAuthRoom.class)
            .eq(SsClassAuthRoom::getClassId, ssClassDTO.getId())
            .in(SsClassAuthRoom::getDeviceId, deleteDeviceIdList)
            .and(wrapper -> wrapper.isNull(SsClassAuthRoom::getClassTimeIds)
                .or()
                .eq(SsClassAuthRoom::getClassTimeIds, "")
            )
            .set(SsClassAuthRoom::getAppointmentStatus, AppointmentEnum.TYPE_0.CODE)
        );

        // 2.更新校区预约记录表为取消预约
        ssAppointmentClassLogMapper.update(Wrappers.lambdaUpdate(SsAppointmentClassLog.class)
            .eq(SsAppointmentClassLog::getClassId, ssClassDTO.getId())
            .in(SsAppointmentClassLog::getDeviceId, deleteDeviceIdList)
            .set(SsAppointmentClassLog::getCancelAppointmentTime, LocalDateTime.now())
        );

        // 查询班级下指定授权设备进行中,未开始的课次信息
        SsClassTimeDTO classTimeDto = new SsClassTimeDTO();
        classTimeDto.setClassId(ssClassDTO.getId());
        classTimeDto.setAuthDeviceIdList(deleteDeviceIdList);
        classTimeDto.setAttendClassState(AttendClassStateEnum.ATTEND_CLASS_STATE_ENUM_5.CODE);
        classTimeDto.setIsSyncXiaogj(ssClassDTO.getIsSyncXiaogj());
        List<SsClassTimeVO> classTimeVoList = ssClassTimeMapper.getDeviceClassTimeByClassId(
            classTimeDto);
        log.debug(
            "班级管理授权校区,删除授权设备信息,班级ID:{},班级名称:{},删除设备ID:{},删除课次信息:{}",
            ssClassDTO.getId(),
            ssClassDTO.getClassName(),
            deleteDeviceIdList,
            JSONUtil.toJsonStr(classTimeVoList));
        if (CollectionUtils.isNotEmpty(classTimeVoList)) {

            List<Long> classTimeAuthRoomIdList = classTimeVoList.stream()
                .map(SsClassTimeVO::getClassTimeAuthRoomId)
                .distinct()
                .collect(Collectors.toList());

            // 3.删除班级下对应未开始课次授权设备信息
            ssClassTimeAuthRoomMapper.delete(Wrappers.lambdaQuery(SsClassTimeAuthRoom.class)
                .eq(SsClassTimeAuthRoom::getClassId, ssClassDTO.getId())
                .in(SsClassTimeAuthRoom::getId, classTimeAuthRoomIdList)
            );

            // 4.删除班级下对应未开始课次授权设备对应学生信息
            ssClassTimeStudentMapper.delete(Wrappers.lambdaQuery(SsClassTimeStudent.class)
                .in(SsClassTimeStudent::getClassTimeAuthRoomId, classTimeAuthRoomIdList)
            );

            // 5.同步校管家删除取消授权设备未开始课次信息
            if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClassDTO.getIsSyncXiaogj())) {

                for (SsClassTimeVO classTimeVo : classTimeVoList) {

                    // 同步校管家删除课次参数封装
                    List<DeleteCourseReq> deleteCourseInfoList = xiaoGuanJiaService.syncXiaogjDeleteClassTimeParam(
                        classTimeVo.getXgjClassId(),
                        classTimeVo.getXgjClassTimeId());

                    // 同步校管家班级参数封装
                    ClassCourseReq classCourseReq = xiaoGuanJiaService.syncXiaogjClassParam(
                        CreateClassType.CREATE_CLASS_TYPE.CODE,
                        classTimeVo.getXgjClassId(),
                        null,
                        null,
                        null,
                        null,
                        deleteCourseInfoList
                    );
                    classCourseReq.setClassTimeId(classTimeVo.getId());
                    campusClassList.add(classCourseReq);

                }

            }

        }

    }

    /**
     * ======================新增相关处理====================== 1.新增授权班级信息 2.保存预约记录
     * 3.新增班级下对应课次进行中,未开始课次信息(除去已单课次授权课次) 4.新增班级下对应课次进行中,未开始课次的学生信息(除去已单课次授权课次)
     * 5.同步校管家创建/编辑班级信息,班级下进行中,未开始课次信息(除去已单课次授权课次)
     *
     * @param ssClassDTO
     * @param saveAuthDeviceList
     * @param campusClassList
     * @return void
     * <AUTHOR>
     * @date 2024/10/17 14:47
     */
    private void saveAuthDeviceDispose(SsClassDTO ssClassDTO,
        List<SsClassAuthInfoDTO> saveAuthDeviceList, List<ClassCourseReq> campusClassList) {

        // 远程查询主讲老师信息
        R<List<LecturerVO>> lecturerList = remoteLecturerService.getLecturerList(new LecturerDTO());

        Map<Long, String> lecturerMap = new HashMap<>();
        if (lecturerList.isOk() && CollectionUtils.isNotEmpty(lecturerList.getData())) {
            lecturerMap = lecturerList.getData()
                .stream()
                .collect(Collectors.toMap(LecturerVO::getId, LecturerVO::getXgjLecturerId));
        }

        for (SsClassAuthInfoDTO classAuthRoomDto : saveAuthDeviceList) {

            // 校管家校区, 教室, 主讲 ID
            String xgjCampusId = classAuthRoomDto.getXgjCampusId();
            String xgjClassRoomId = classAuthRoomDto.getXgjClassRoomId();

            // 单课次授权ID集合
            List<Long> classTimeIdList = new ArrayList<>();

            // 查询是否存在历史取消预约数据, 存在则修改状态, 不存在新增
            List<SsClassAuthRoom> campusAllClassAuthRoomList = ssClassAuthRoomMapper.selectList(
                Wrappers.lambdaQuery(SsClassAuthRoom.class)
                    .eq(SsClassAuthRoom::getClassId, ssClassDTO.getId())
                    .eq(SsClassAuthRoom::getCampusId, classAuthRoomDto.getCampusId())
            );

            List<SsClassAuthRoom> ssClassAuthRoomList = campusAllClassAuthRoomList.stream()
                .filter(e -> classAuthRoomDto.getDeviceId().equals(e.getDeviceId()))
                .collect(Collectors.toList());

            SsClassAuthRoom ssClassAuthRoom = new SsClassAuthRoom();

            if (CollectionUtils.isNotEmpty(ssClassAuthRoomList)) {

                // 如果本次授权校区的设备历史授权过,则只修改设备授权状态,本次授权校区设备更改重新同步本次授权设备至校管家
                ssClassAuthRoom.setId(ssClassAuthRoomList.get(0).getId());
                ssClassAuthRoom.setClassRoomId(classAuthRoomDto.getClassRoomId());
                ssClassAuthRoom.setDeviceId(classAuthRoomDto.getDeviceId());
                ssClassAuthRoom.setXgjClassRoomId(xgjClassRoomId);
                ssClassAuthRoom.setAppointmentTime(LocalDateTime.now());
                ssClassAuthRoom.setClassTimeIds("");
                ssClassAuthRoom.setAppointmentStatus(AppointmentEnum.TYPE_1.CODE);
                ssClassAuthRoomMapper.updateById(ssClassAuthRoom);

                // 本次授权设备已单课次授权过的课次id
                if (StringUtils.isNotBlank(ssClassAuthRoomList.get(0).getClassTimeIds())) {
                    String[] classTimeIdArr = ssClassAuthRoomList.get(0).getClassTimeIds()
                        .split(",");
                    for (String str : classTimeIdArr) {
                        classTimeIdList.add(Long.parseLong(str.trim()));
                    }

                }

                // 同步校管家班级参数封装
                ClassCourseReq classCourseReq = xiaoGuanJiaService.syncXiaogjClassParam(
                    CreateClassType.CREATE_CLASS_TYPE_1.CODE,
                    ssClassAuthRoomList.get(0).getXgjClassId(),
                    xgjCampusId,
                    ssClassDTO.getClassName(),
                    null,
                    null,
                    null
                );
                campusClassList.add(classCourseReq);

            } else {
                // 1.新增授权班级信息
                ssClassAuthRoom.setClassId(ssClassDTO.getId());
                ssClassAuthRoom.setCampusId(classAuthRoomDto.getCampusId());
                ssClassAuthRoom.setClassRoomId(classAuthRoomDto.getClassRoomId());
                ssClassAuthRoom.setDeviceId(classAuthRoomDto.getDeviceId());
                ssClassAuthRoom.setAppointmentTime(LocalDateTime.now());
                ssClassAuthRoom.setXgjCampusId(xgjCampusId);
                ssClassAuthRoom.setXgjClassRoomId(xgjClassRoomId);
                ssClassAuthRoom.setAppointmentStatus(AppointmentEnum.TYPE_1.CODE);
                // 同步校管家生成校管家校区班级ID
                if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(
                    ssClassDTO.getIsSyncXiaogj())) {
                    ssClassAuthRoom.setXgjClassId(
                        CollectionUtils.isNotEmpty(campusAllClassAuthRoomList)
                            ? campusAllClassAuthRoomList.get(0).getXgjClassId()
                            : UUID.randomUUID().toString());
                }
                ssClassAuthRoomMapper.insert(ssClassAuthRoom);

                // 是否同步校管家校验
                if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(
                    ssClassDTO.getIsSyncXiaogj())) {

                    // 同步校管家班级参数封装
                    ClassCourseReq classCourseReq = xiaoGuanJiaService.syncXiaogjClassParam(
                        CollectionUtils.isEmpty(campusAllClassAuthRoomList)
                            ? CreateClassType.CREATE_CLASS_TYPE_0.CODE
                            : CreateClassType.CREATE_CLASS_TYPE_1.CODE,
                        ssClassAuthRoom.getXgjClassId(),
                        xgjCampusId,
                        ssClassDTO.getClassName(),
                        null,
                        null,
                        null
                    );
                    campusClassList.add(classCourseReq);
                }

            }

            // 2.保存预约记录
            SsAppointmentClassLog appointmentClassLog = new SsAppointmentClassLog();
            appointmentClassLog.setClassId(ssClassDTO.getId());
            appointmentClassLog.setCampusId(classAuthRoomDto.getCampusId());
            appointmentClassLog.setClassRoomId(classAuthRoomDto.getClassRoomId());
            appointmentClassLog.setDeviceId(classAuthRoomDto.getDeviceId());
            appointmentClassLog.setAppointmentTime(LocalDateTime.now());
            appointmentClassLog.setXgjCampusId(xgjCampusId);
            appointmentClassLog.setXgjClassRoomId(xgjClassRoomId);
            ssAppointmentClassLogMapper.insert(appointmentClassLog);

            // 3.新增班级下对应课次进行中,未开始课次信息(除去已单课次授权课次)
            SsClassTimeQuery ssClassTimeQuery = new SsClassTimeQuery();
            ssClassTimeQuery.setClassId(ssClassDTO.getId());
            ssClassTimeQuery.setNotClassTimeIdList(classTimeIdList);
            ssClassTimeQuery.setAttendClassState(
                AttendClassStateEnum.ATTEND_CLASS_STATE_ENUM_5.CODE);
            List<SsClassTimeVO> notStartClassTimeVoList = ssClassTimeMapper.getClassTimeByClassId(
                ssClassTimeQuery);

            for (SsClassTimeVO ssClassTimeVo : notStartClassTimeVoList) {
                SsClassTimeAuthRoom ssClassTimeAuthRoom = new SsClassTimeAuthRoom();
                ssClassTimeAuthRoom.setClassId(ssClassTimeVo.getClassId());
                ssClassTimeAuthRoom.setClassTimeId(ssClassTimeVo.getId());
                // 同步校管家生成校管家校区课次ID
                if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(
                    ssClassDTO.getIsSyncXiaogj())) {
                    ssClassTimeAuthRoom.setXgjClassTimeId(UUID.randomUUID().toString());
                }
                ssClassTimeAuthRoom.setCampusId(classAuthRoomDto.getCampusId());
                ssClassTimeAuthRoom.setClassRoomId(classAuthRoomDto.getClassRoomId());
                ssClassTimeAuthRoom.setDeviceId(classAuthRoomDto.getDeviceId());
                ssClassTimeAuthRoom.setXgjCampusId(xgjCampusId);
                ssClassTimeAuthRoom.setXgjClassRoomId(xgjClassRoomId);
                ssClassTimeAuthRoomMapper.insert(ssClassTimeAuthRoom);

                // 4.新增班级下对应课次进行中,未开始课次的学生信息(除去已单课次授权课次)
                // 查询授权校区班级学生
                List<SsClassAuthRoomStudent> classAuthRoomStudentList = ssClassAuthRoomStudentMapper.selectList(
                    Wrappers.lambdaQuery(SsClassAuthRoomStudent.class)
                        .eq(SsClassAuthRoomStudent::getCampusId, classAuthRoomDto.getCampusId())
                        .eq(SsClassAuthRoomStudent::getClassId, ssClassTimeVo.getClassId())
                );

                for (SsClassAuthRoomStudent classAuthRoomStudent : classAuthRoomStudentList) {
                    // 新增课次学生信息
                    SsClassTimeStudent ssClassTimeStudent = new SsClassTimeStudent();
                    ssClassTimeStudent.setClassId(ssClassTimeVo.getClassId());
                    ssClassTimeStudent.setClassTimeAuthRoomId(ssClassTimeAuthRoom.getId());
                    ssClassTimeStudent.setClassTimeId(ssClassTimeVo.getId());
                    ssClassTimeStudent.setCampusId(classAuthRoomDto.getCampusId());
                    ssClassTimeStudent.setClassRoomId(classAuthRoomDto.getClassRoomId());
                    ssClassTimeStudent.setDeviceId(classAuthRoomDto.getDeviceId());
                    ssClassTimeStudent.setStudentId(classAuthRoomStudent.getStudentId());
                    ssClassTimeStudent.setStudentName(classAuthRoomStudent.getStudentName());
                    ssClassTimeStudent.setStudentMobile(classAuthRoomStudent.getStudentMobile());
                    ssClassTimeStudentMapper.insert(ssClassTimeStudent);
                }

            }

            // 5.同步校管家创建/编辑班级信息,班级下进行中,未开始课次信息(除去已单课次授权课次)
            if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClassDTO.getIsSyncXiaogj())) {

                // 查询班级下指定授权设备未开始,进行中的课次信息
                SsClassTimeDTO saveClassTimeDto = new SsClassTimeDTO();
                saveClassTimeDto.setClassId(ssClassDTO.getId());
                saveClassTimeDto.setNotClassTimeIdList(classTimeIdList);
                saveClassTimeDto.setDeviceId(classAuthRoomDto.getDeviceId());
                saveClassTimeDto.setAttendClassState(
                    AttendClassStateEnum.ATTEND_CLASS_STATE_ENUM_5.CODE);
                saveClassTimeDto.setIsSyncXiaogj(ssClassDTO.getIsSyncXiaogj());
                List<SsClassTimeVO> classTimeVoList = ssClassTimeMapper.getDeviceClassTimeByClassId(
                    saveClassTimeDto);

                if (CollectionUtils.isNotEmpty(classTimeVoList)) {

                    for (SsClassTimeVO ssClassTimeVo : classTimeVoList) {

                        // 同步校管家创建课次详情参数封装
                        List<CourseData> courseDataList = xiaoGuanJiaService.syncXiaogjCreateClassTimeCourseDateParam(
                            ssClassTimeVo.getAttendClassDate().getDayOfWeek().getValue(),
                            DateUtil.format(ssClassTimeVo.getAttendClassStartTime()),
                            DateUtil.format(ssClassTimeVo.getAttendClassEndTime()),
                            DateUtil.format(ssClassTimeVo.getAttendClassDate()),
                            ssClassTimeVo.getXgjClassRoomId(),
                            DateUtil.format(ssClassTimeVo.getAttendClassDate()) + " "
                                + DateUtil.format(ssClassTimeVo.getAttendClassStartTime()),
                            DateUtil.format(ssClassTimeVo.getAttendClassDate()) + " "
                                + DateUtil.format(ssClassTimeVo.getAttendClassEndTime()),
                            ssClassTimeVo.getXgjClassTimeId(),
                            lecturerMap.get(ssClassTimeVo.getLecturerId())
                        );

                        // 同步校管家创建课次参数封装
                        List<CreateCourseReq> createCourseInfoList = xiaoGuanJiaService.syncXiaogjCreateClassTimeParam(
                            ssClassTimeVo.getXgjClassId(),
                            DateUtil.format(ssClassTimeVo.getAttendClassDate()),
                            DateUtil.format(ssClassTimeVo.getAttendClassDate()),
                            courseDataList
                        );

                        // 同步校管家班级参数封装
                        ClassCourseReq classCourseReq = xiaoGuanJiaService.syncXiaogjClassParam(
                            CreateClassType.CREATE_CLASS_TYPE.CODE,
                            ssClassAuthRoom.getXgjClassId(),
                            xgjCampusId,
                            ssClassDTO.getClassName(),
                            createCourseInfoList,
                            null,
                            null
                        );
                        classCourseReq.setClassTimeId(ssClassTimeVo.getId());
                        campusClassList.add(classCourseReq);

                    }
                }

            }


        }


    }

    /**
     * ======================本次授权编辑设备相关处理====================== 1.同步校管家更新授权校区班级信息
     *
     * @param ssClassDTO
     * @param updateDeviceIdList
     * @param campusClassList
     * @return void
     * <AUTHOR>
     * @date 2024/10/17 11:18
     */
    private void updateAuthDeviceDispose(SsClassDTO ssClassDTO, List<Long> updateDeviceIdList,
        List<ClassCourseReq> campusClassList) {

        // 查询更新授权信息ID
        List<SsClassAuthRoom> ssClassAuthRoomList = ssClassAuthRoomMapper.selectList(
            Wrappers.lambdaQuery(SsClassAuthRoom.class)
                .eq(SsClassAuthRoom::getClassId, ssClassDTO.getId())
                .in(SsClassAuthRoom::getDeviceId, updateDeviceIdList)
                .and(wrapper -> wrapper.isNull(SsClassAuthRoom::getClassTimeIds)
                    .or()
                    .eq(SsClassAuthRoom::getClassTimeIds, "")
                )
        );
        log.debug(
            "班级管理授权校区,编辑授权设备信息,班级ID:{},班级名称:{},更新设备ID:{},班级授权设备列表:{},同步校管家更新授权校区班级信息",
            ssClassDTO.getId(),
            ssClassDTO.getClassName(),
            updateDeviceIdList,
            JSONUtil.toJsonStr(ssClassAuthRoomList));

        // 1.同步校管家更新授权校区班级信息
        if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClassDTO.getIsSyncXiaogj())) {

            for (SsClassAuthRoom ssClassAuthRoom : ssClassAuthRoomList) {

                // 同步校管家班级参数封装
                ClassCourseReq classCourseReq = xiaoGuanJiaService.syncXiaogjClassParam(
                    CreateClassType.CREATE_CLASS_TYPE_1.CODE,
                    ssClassAuthRoom.getXgjClassId(),
                    ssClassAuthRoom.getXgjCampusId(),
                    ssClassDTO.getClassName(),
                    null,
                    null,
                    null
                );

                campusClassList.add(classCourseReq);

            }

        }

    }

    /**
     * 验证班级是否可操作
     *
     * @param classId
     * @return com.yuedu.ydsf.eduConnect.entity.SsClass
     * <AUTHOR>
     * @date 2024/10/31 10:00
     */
    @Override
    public SsClass checkClassState(Long classId) {

        SsClass ssClass = ssClassMapper.selectById(classId);

        if (Objects.isNull(ssClass)) {
            throw new BizException("未查询到此班级信息!");
        }

        if (SsClassStateEnum.STATE_1.CODE.equals(ssClass.getClassState())) {
            throw new BizException("该班级已结业,无法操作!");
        }

        return ssClass;

    }


}
