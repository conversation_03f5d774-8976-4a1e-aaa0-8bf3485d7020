package com.yuedu.ydsf.eduConnect.manager;

import com.yuedu.ydsf.eduConnect.api.dto.SsDeviceDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.entity.SsDeviceAudioConfig;
import com.yuedu.ydsf.eduConnect.entity.SsDeviceConfig;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/16
 */
public interface SsDeviceManager {


    /**
     * 远程获取教室、校区相关信息
     *
     * @param devices
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.Long, java.lang.String>>
     * <AUTHOR>
     * @date 2024/10/16 9:58
     */
    Map<String, Map<Long, String>> fetchRemoteData(List<SsDevice> devices);


    /**
     * 实体类转换VO类
     *
     * @param device
     * @param campus
     * @param classRoom
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO
     * <AUTHOR>
     * @date 2024/10/16 10:00
     */
    SsDeviceVO convertToDeviceVO(SsDevice device, Map<Long, String> campus,
        Map<Long, String> classRoom);

    /**
     * 移除设备缓存
     *
     * @param ssDeviceDTO
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 10:04
     */
    void removeDeviceCache(SsDeviceDTO ssDeviceDTO);

    /**
     * 缓存设备信息
     *
     * @param deviceVO
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 10:12
     */
    void cacheDeviceInfo(SsDeviceVO deviceVO);

    /**
     * 获取设备配置信息
     *
     * @param configId
     * @return com.yuedu.ydsf.eduConnect.entity.SsDeviceConfig
     * <AUTHOR>
     * @date 2024/10/16 10:19
     */
    SsDeviceConfig getDeviceConfigById(Long configId);

    /**
     * 获取设备音频配置信息
     *
     * @param audioConfigId
     * @return com.yuedu.ydsf.eduConnect.entity.SsDeviceAudioConfig
     * <AUTHOR>
     * @date 2024/10/16 10:19
     */
    SsDeviceAudioConfig getAudioConfigById(Long audioConfigId);

    /**
     * 删除设备之前的校验
     * <AUTHOR>
     * @date 2024/10/28 9:03
     * @param delDto
     * @return void
     */
    void handleDeviceStatus(SsDeviceDTO delDto,SsDevice ssDevice);
}
