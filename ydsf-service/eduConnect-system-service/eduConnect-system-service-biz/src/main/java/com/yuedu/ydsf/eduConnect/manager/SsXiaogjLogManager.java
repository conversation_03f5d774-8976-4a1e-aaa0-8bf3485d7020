package com.yuedu.ydsf.eduConnect.manager;

import com.yuedu.ydsf.eduConnect.entity.SsXiaogjPushTask;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.SsPushXiaogjEventReq;

/**
 * 校管家日志表 公共服务类
 * <AUTHOR>
 * @date 2024/10/28 16:08
 */
public interface SsXiaogjLogManager {


    /**
     * 保存排课校管家日志
     * @param ssPushXiaogjEventReq
     * @param classCourseReq
     * @return void
     * <AUTHOR>
     * @date 2024/10/28 16:24
     */
    void saveXiaogjLog(SsPushXiaogjEventReq ssPushXiaogjEventReq, ClassCourseReq classCourseReq, String userName);

    void saveXiaogjLog(SsXiaogjPushTask ssXiaogjPushTaskEntity, String userName);

}
