package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent;

import java.util.List;

/**
 * 班级授权校区学生表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 09:56:19
 */
public interface SsClassAuthRoomStudentService extends IService<SsClassAuthRoomStudent> {

    // 根据班级ID和校区ID列表查询班级授权校区学生信息
    List<SsClassAuthRoomStudent> listByClassIdAndCampusId(Long classId, Long campusId);
}
