package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDetailVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 直播间计划明细 控制类
 *
 * <AUTHOR>
 * @date 2024-12-03 09:49:55
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/LiveRoomPlanDetailVersion" )
@Tag(description = "ea_live_room_plan_detail_version" , name = "直播间计划明细管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LiveRoomPlanDetailVersionController {

    private final  LiveRoomPlanDetailVersionService liveRoomPlanDetailVersionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param liveRoomPlanDetailVersion 直播间计划明细
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("edusystem_LiveRoomPlanDetailVersion_view")
    public R getLiveRoomPlanDetailVersionPage(@ParameterObject Page page, @ParameterObject LiveRoomPlanDetailVersion liveRoomPlanDetailVersion) {
        LambdaQueryWrapper<LiveRoomPlanDetailVersion> wrapper = Wrappers.lambdaQuery();
        return R.ok(liveRoomPlanDetailVersionService.page(page, wrapper));
    }


    /**
     * 通过条件查询直播间计划明细
     * @param liveRoomPlanDetailVersion 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("edusystem_LiveRoomPlanDetailVersion_view")
    public R getDetails(@ParameterObject LiveRoomPlanDetailVersion liveRoomPlanDetailVersion) {
        return R.ok(liveRoomPlanDetailVersionService.list(Wrappers.query(liveRoomPlanDetailVersion)));
    }

    /**
     * 新增直播间计划明细
     * @param liveRoomPlanDetailVersion 直播间计划明细
     * @return R
     */
    @Operation(summary = "新增直播间计划明细" , description = "新增直播间计划明细" )
    @PostMapping("/add")
    @HasPermission("edusystem_LiveRoomPlanDetailVersion_add")
    public R save(@RequestBody LiveRoomPlanDetailVersion liveRoomPlanDetailVersion) {
        return R.ok(liveRoomPlanDetailVersionService.save(liveRoomPlanDetailVersion));
    }

    /**
     * 修改直播间计划明细
     * @param liveRoomPlanDetailVersion 直播间计划明细
     * @return R
     */
    @Operation(summary = "修改直播间计划明细" , description = "修改直播间计划明细" )
    @PutMapping("/edit")
    @HasPermission("edusystem_LiveRoomPlanDetailVersion_edit")
    public R updateById(@RequestBody LiveRoomPlanDetailVersion liveRoomPlanDetailVersion) {
        return R.ok(liveRoomPlanDetailVersionService.updateById(liveRoomPlanDetailVersion));
    }

    /**
     * 通过id删除直播间计划明细
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除直播间计划明细" , description = "通过id删除直播间计划明细" )
    @DeleteMapping("/delete")
    @HasPermission("edusystem_LiveRoomPlanDetailVersion_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(liveRoomPlanDetailVersionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param liveRoomPlanDetailVersion 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_LiveRoomPlanDetailVersion_export")
    public List<LiveRoomPlanDetailVersion> exportExcel(LiveRoomPlanDetailVersion liveRoomPlanDetailVersion,Long[] ids) {
        return liveRoomPlanDetailVersionService.list(Wrappers.lambdaQuery(liveRoomPlanDetailVersion).in(ArrayUtil.isNotEmpty(ids), LiveRoomPlanDetailVersion::getId, ids));
    }

    /**
     * 导入excel 表
     * @param liveRoomPlanDetailVersionList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_LiveRoomPlanDetailVersion_export")
    public R importExcel(@RequestExcel List<LiveRoomPlanDetailVersion> liveRoomPlanDetailVersionList, BindingResult bindingResult) {
        return R.ok(liveRoomPlanDetailVersionService.saveBatch(liveRoomPlanDetailVersionList));
    }


    /**
     * 通过直播间计划Id获取直播间计划信息
     *
     * @param planId 教学计划id
     * @return 结果
     */
    @Operation(summary = "通过直播间计划Id获取直播间计划信息", description = "通过直播间计划Id获取直播间计划信息")
    @PostMapping("/selectLiveRoomPlanDetailVersionList/{planId}")
    @Inner
    public R<List<LiveRoomPlanDetailVersionVO>> selectLiveRoomPlanDetailVersionList(
        @PathVariable Long planId) {
        return R.ok(liveRoomPlanDetailVersionService.selectLiveRoomPlanDetailVersionList(planId));
    }
}
