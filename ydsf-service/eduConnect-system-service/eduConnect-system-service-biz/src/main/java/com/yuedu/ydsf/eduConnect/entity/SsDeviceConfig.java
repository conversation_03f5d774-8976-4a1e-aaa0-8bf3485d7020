package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备配置参数表
 *
 * <AUTHOR>
 * @date 2024/09/28
 */
@TableName("ss_device_config")
@Data
@EqualsAndHashCode(callSuper = true)
public class SsDeviceConfig extends Model<SsDeviceConfig> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 教师端大流宽度
     */
    private Integer tBw;

    /**
     * 教师端大流高度
     */
    private Integer tBh;

    /**
     * 教师端大码率
     */
    private Integer tBb;

    /**
     * 教师端大帧率
     */
    private Integer tBf;

    /**
     * 教师端小流宽度
     */
    private Integer tSw;

    /**
     * 教师端小流高度
     */
    private Integer tSh;

    /**
     * 教师端小码率
     */
    private Integer tSb;

    /**
     * 教师端小帧率
     */
    private Integer tSf;

    /**
     * 学生端大流宽度
     */
    private Integer sBw;

    /**
     * 学生端大流高度
     */
    private Integer sBh;

    /**
     * 学生端大码率
     */
    private Integer sBb;

    /**
     * 学生端大帧率
     */
    private Integer sBf;

    /**
     * 学生端小流宽度
     */
    private Integer sSw;

    /**
     * 学生端小流高度
     */
    private Integer sSh;

    /**
     * 学生端小码率
     */
    private Integer sSb;

    /**
     * 学生端小帧率
     */
    private Integer sSf;

    /**
     * 教师是否订阅大流:0-是;1-否
     * @type ss_device_config_t_hd
     */
    private Byte tHd;

    /**
     * 学生端是否订阅大流:0-是;1-否
     * @type ss_device_config_s_hd
     */
    private Byte sHd;

    /**
     * 九空格展示学生数量
     */
    private Integer sShowNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 编辑时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifer;

    /**
     * 日志开关是否开启:0-关闭;1-开启
     * @type ss_device_config_log_enable
     */
    private Byte logEnable;

    /**
     * 是否删除:0-未删除;1-已删除
     * @type ss_device_config_del_flag
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
}
