package com.yuedu.ydsf.eduConnect.manager.impl;

import com.yuedu.ydsf.eduConnect.config.MessageUtils;
import com.yuedu.ydsf.eduConnect.manager.XiaoGuanJiaService;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.MqTypeEnums;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.SsPushXiaogjType;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.XiaoGuanJiaConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * @author: zhangchuanfu
 * @date: 2024/10/14
 **/
@Slf4j
@Service
public class XiaoGuanJiaServiceImpl implements XiaoGuanJiaService {

    @Override
    public SsPushXiaogjEventReq<ClassCourseReq> ssPushXiaogjMessage(ClassCourseReq classCourseReq) {

        SsPushXiaogjEventReq<ClassCourseReq> ssPushXiaogjEventReq = new SsPushXiaogjEventReq<>();

        try {
            String uuid = UUID.randomUUID().toString();
            Long timestamp = System.currentTimeMillis();

            // 双师排课推送校管家请求参数
            ssPushXiaogjEventReq.setData(classCourseReq);
            ssPushXiaogjEventReq.setEventId(uuid);
            ssPushXiaogjEventReq.setEventKey(SsPushXiaogjType.CLASS_COURSE.eventKey);
            ssPushXiaogjEventReq.setEventTimestamp(timestamp);

            MqReq<SsPushXiaogjEventReq<ClassCourseReq>> mqReq = new MqReq<>(
                    MqTypeEnums.SS_XIAOGJ_PUSH_MSG.TYPE,
                    MqTypeEnums.SS_XIAOGJ_PUSH_MSG.TOPTIC, ssPushXiaogjEventReq);
            log.debug("双师推送校管家排课请求参数:mqReq={}", mqReq);
            MessageUtils.sendMessage(mqReq.getType(), mqReq.getTopic(), mqReq.getData());
        } catch (Exception e) {
            log.error("双师推送校管家排课发送队列错误:{}", e.getMessage(), e);
        }

        return ssPushXiaogjEventReq;
    }

    @Override
    public boolean ssPushXiaogjMessage(String msgId, String classCourseReqJson) {
        try {
            log.debug("双师推送校管家排课请求参数:msgId={}", msgId);
            // 设置MessageId。
            MessageProperties messageProperties = new MessageProperties();
            messageProperties.setMessageId(msgId);
            // 创建Message。
            Message message = new Message(classCourseReqJson.getBytes(StandardCharsets.UTF_8), messageProperties);
            MessageUtils.sendMessage(MqTypeEnums.SS_XIAOGJ_PUSH_MSG.TYPE, MqTypeEnums.SS_XIAOGJ_PUSH_MSG.TOPTIC, message);
            return true;
        } catch (Exception e) {
            log.error("双师推送校管家排课发送队列错误:msgId={}", msgId, e);
            return false;
        }
    }

    /**
     * 同步校管家班级参数封装
     *
     * @param classType           班级操作类型
     * @param xgjClassId          校管家班级ID
     * @param xgjCampusId         校管家校区ID
     * @param className           班级名称
     * @param createCourseReqList 班级下创建排课信息
     * @param updateCourseReqList 班级下更新排课信息
     * @param deleteCourseReqList 班级下删除课次信息
     * @return com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq
     * <AUTHOR>
     * @date 2024/10/17 15:51
     */
    @Override
    public ClassCourseReq syncXiaogjClassParam(Integer classType,
                                               String xgjClassId,
                                               String xgjCampusId,
                                               String className,
                                               List<CreateCourseReq> createCourseReqList,
                                               List<UpdateCourseReq> updateCourseReqList,
                                               List<DeleteCourseReq> deleteCourseReqList) {

        ClassCourseReq classCourseReq = new ClassCourseReq();
        classCourseReq.setTripartiteId(xgjClassId);
        classCourseReq.setCShiftID(XiaoGuanJiaConstant.DEFAULT_COURSE_ID);
        classCourseReq.setCCampusID(xgjCampusId);
        classCourseReq.setCName(className);
        classCourseReq.setType(classType);
        classCourseReq.setCreateCourseList(createCourseReqList);
        classCourseReq.setUpdateCourseList(updateCourseReqList);
        classCourseReq.setDeleteCourseList(deleteCourseReqList);

        return classCourseReq;

    }

    /**
     * 同步校管家创建课次参数封装
     *
     * @param xgjClassId     校管家班级ID
     * @param cStartTime     课次开始时间
     * @param cEndTime       课次结束时间
     * @param courseDataList 课次详情
     * @return java.util.List<com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq>
     * <AUTHOR>
     * @date 2024/10/17 16:22
     */
    @Override
    public List<CreateCourseReq> syncXiaogjCreateClassTimeParam(String xgjClassId,
                                                                String cStartTime,
                                                                String cEndTime,
                                                                List<CreateCourseReq.CourseData> courseDataList) {

        List<CreateCourseReq> createCourseInfoList = new ArrayList<>();
        CreateCourseReq createCourseReq = new CreateCourseReq();
        createCourseReq.setTripartiteId(xgjClassId);
        createCourseReq.setCStartTime(cStartTime);
        createCourseReq.setCEndTime(cEndTime);
        createCourseReq.setCCourseData(courseDataList);
        createCourseReq.setCCourseMode(1);
        createCourseInfoList.add(createCourseReq);

        return createCourseInfoList;

    }

    /**
     * 同步校管家创建课次详情参数封装
     *
     * @param cWeekday       周几
     * @param cWeekStartTime 上课开始时间点 HH:mm:ss
     * @param cWeekEndTime   上课结束时间点 HH:mm:ss
     * @param cDate          上课日期 yyyy-MM-dd
     * @param xgjClassRoomId 校管家教室ID
     * @param cStartTime     上课开始时间 yyyy-MM-dd HH:mm:ss
     * @param cEndTime       上课结束时间 yyyy-MM-dd HH:mm:ss
     * @param xgjClassTimeId 校管家课次ID
     * @param xgjLecturerId  校管家主讲ID
     * @return java.util.List<com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq.CourseData>
     * <AUTHOR>
     * @date 2024/10/17 16:35
     */
    @Override
    public List<CreateCourseReq.CourseData> syncXiaogjCreateClassTimeCourseDateParam(int cWeekday,
                                                                                     String cWeekStartTime,
                                                                                     String cWeekEndTime,
                                                                                     String cDate,
                                                                                     String xgjClassRoomId,
                                                                                     String cStartTime,
                                                                                     String cEndTime,
                                                                                     String xgjClassTimeId,
                                                                                     String xgjLecturerId) {

        List<CreateCourseReq.CourseData> courseDataList = new ArrayList<>();
        CreateCourseReq.CourseData courseData = CreateCourseReq.CourseData.builder()
                .cWeekday(cWeekday)
                .cWeekStartTime(cWeekStartTime)
                .cWeekEndTime(cWeekEndTime)
                .cDate(cDate)
                .cClassroomID(xgjClassRoomId)
                .cStartTime(cStartTime)
                .cEndTime(cEndTime)
                .threeId(xgjClassTimeId)
                .speaker(xgjLecturerId)
                .build();
        courseDataList.add(courseData);

        return courseDataList;
    }

    /**
     * 同步校管家删除课次参数封装
     *
     * @param xgjClassId     校管家班级ID
     * @param xgjClassTimeId 校管家课次ID
     * @return java.util.List<com.yuedu.ydsf.eduConnect.system.proxy.dto.DeleteCourseReq>
     * <AUTHOR>
     * @date 2024/10/17 16:08
     */
    @Override
    public List<DeleteCourseReq> syncXiaogjDeleteClassTimeParam(String xgjClassId,
                                                                String xgjClassTimeId) {

        List<DeleteCourseReq> deleteCourseInfoList = new ArrayList<>();
        DeleteCourseReq deleteCourseReq = new DeleteCourseReq();
        deleteCourseReq.setTripartiteId(xgjClassId);
        deleteCourseReq.setThreeId(xgjClassTimeId);
        deleteCourseInfoList.add(deleteCourseReq);

        return deleteCourseInfoList;

    }


}
