package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseService;
import com.yuedu.ydsf.eduConnect.api.dto.RecordVideoTaskDTO;
import com.yuedu.ydsf.eduConnect.api.query.RecordVideoTaskQuery;
import com.yuedu.ydsf.eduConnect.api.vo.RecordVideoTaskVO;
import com.yuedu.ydsf.eduConnect.entity.RecordVideoTask;

/**
 * @ClassName RecordVideoTaskService
 * @Description 录课任务服务类
 * <AUTHOR>
 * @Date 2024/11/29 16:48:01
 * @Version v0.0.1
 */
public interface RecordVideoTaskNewService extends MPJBaseService<RecordVideoTask> {

    /**
     * 生成录课任务(新版)
     *
     * @return 结果
     */
    int saveRecordVideoTask();

  /**
   * 录课任务管理-获取录课任务列表
   *
   * <AUTHOR>
   * @date 2025/4/22 10:00
   * @param page
   * @param recordVideoTaskQuery
   * @return com.baomidou.mybatisplus.core.metadata.IPage
   */
  IPage<RecordVideoTaskVO> getRecordVideoTaskListPage(Page page, RecordVideoTaskQuery recordVideoTaskQuery);

  /**
   * 录课任务管理-匹配已有录课资源
   * <AUTHOR>
   * @date 2025/4/22 14:57
   * @param recordVideoTaskDTO
   * @return void
   */
    void matchResources(RecordVideoTaskDTO recordVideoTaskDTO);
}
