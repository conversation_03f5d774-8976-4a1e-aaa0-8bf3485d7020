package com.yuedu.ydsf.eduConnect.manager;

import com.yuedu.ydsf.eduConnect.api.dto.SsClassDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassVO;
import com.yuedu.ydsf.eduConnect.entity.SsClass;
import java.util.List;

/**
 * 班级信息表 公共服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 10:44:48
 */
public interface SsClassManager {


    /**
     * 实体类转换VO类
     * @param ssClassList
     * @return java.util.List<com.yuedu.ydsf.eduConncet.api.vo.SsClassVO>
     * <AUTHOR>
     * @date 2024/10/10 10:55
     */
    List<SsClassVO> entityConvertVo(List<SsClass> ssClassList);

    /**
     * 处理授权校区并同步校管家
     * @param ssClassDTO
     * @return void
     * <AUTHOR>
     * @date 2024/10/15 16:50
     */
    void bindClassAuthRoom(SsClassDTO ssClassDTO);

    /**
     * 验证班级是否可操作
     * @param classId
     * @return com.yuedu.ydsf.eduConnect.entity.SsClass
     * <AUTHOR>
     * @date 2024/10/31 10:00
     */
    SsClass checkClassState(Long classId);


}
