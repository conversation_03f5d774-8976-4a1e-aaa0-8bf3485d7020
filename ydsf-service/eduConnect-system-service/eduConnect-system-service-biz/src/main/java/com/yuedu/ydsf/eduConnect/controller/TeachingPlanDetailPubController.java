package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanDetailPubDTO;
import com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDetailPubService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 已发布的教学计划明细表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-29 15:14:17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/TeachingPlanDetailPub" )
@Tag(description = "ea_teaching_plan_detail_pub" , name = "已发布的教学计划明细表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TeachingPlanDetailPubController {

    private final  TeachingPlanDetailPubService teachingPlanDetailPubService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param teachingPlanDetailPub 已发布的教学计划明细表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("edusystem_TeachingPlanDetailPub_view")
    public R getTeachingPlanDetailPubPage(@ParameterObject Page page, @ParameterObject TeachingPlanDetailPub teachingPlanDetailPub) {
        LambdaQueryWrapper<TeachingPlanDetailPub> wrapper = Wrappers.lambdaQuery();
        return R.ok(teachingPlanDetailPubService.page(page, wrapper));
    }


    /**
     * 通过条件查询已发布的教学计划明细表
     * @param teachingPlanDetailPub 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("edusystem_TeachingPlanDetailPub_view")
    public R getDetails(@ParameterObject TeachingPlanDetailPub teachingPlanDetailPub) {
        return R.ok(teachingPlanDetailPubService.list(Wrappers.query(teachingPlanDetailPub)));
    }

    /**
     * 新增已发布的教学计划明细表
     * @param teachingPlanDetailPub 已发布的教学计划明细表
     * @return R
     */
    @Operation(summary = "新增已发布的教学计划明细表" , description = "新增已发布的教学计划明细表" )
    @PostMapping("/add")
    @HasPermission("edusystem_TeachingPlanDetailPub_add")
    public R save(@RequestBody TeachingPlanDetailPub teachingPlanDetailPub) {
        return R.ok(teachingPlanDetailPubService.save(teachingPlanDetailPub));
    }

    /**
     * 修改已发布的教学计划明细表
     * @param teachingPlanDetailPub 已发布的教学计划明细表
     * @return R
     */
    @Operation(summary = "修改已发布的教学计划明细表" , description = "修改已发布的教学计划明细表" )
    @PutMapping("/edit")
    @HasPermission("edusystem_TeachingPlanDetailPub_edit")
    public R updateById(@RequestBody TeachingPlanDetailPub teachingPlanDetailPub) {
        return R.ok(teachingPlanDetailPubService.updateById(teachingPlanDetailPub));
    }

    /**
     * 通过id删除已发布的教学计划明细表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除已发布的教学计划明细表" , description = "通过id删除已发布的教学计划明细表" )
    @DeleteMapping("/delete")
    @HasPermission("edusystem_TeachingPlanDetailPub_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(teachingPlanDetailPubService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param teachingPlanDetailPub 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_TeachingPlanDetailPub_export")
    public List<TeachingPlanDetailPub> exportExcel(TeachingPlanDetailPub teachingPlanDetailPub,Long[] ids) {
        return teachingPlanDetailPubService.list(Wrappers.lambdaQuery(teachingPlanDetailPub).in(ArrayUtil.isNotEmpty(ids), TeachingPlanDetailPub::getId, ids));
    }

    /**
     * 导入excel 表
     * @param teachingPlanDetailPubList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_TeachingPlanDetailPub_export")
    public R importExcel(@RequestExcel List<TeachingPlanDetailPub> teachingPlanDetailPubList, BindingResult bindingResult) {
        return R.ok(teachingPlanDetailPubService.saveBatch(teachingPlanDetailPubList));
    }

    /**
     * 查询老师是否有未结束的排课
     * @param lectureId 老师id
     * @return true:有未结束的排课 false:无未结束的排课
     */
    @Operation(summary = "查询老师是否有未结束的排课", description = "查询老师是否有未结束的排课")
    @GetMapping("/hasUnfinishedClass/{lectureId}")
    @Inner(value = false)
    public R<Boolean> hasUnfinishedClass(@PathVariable Long lectureId) {
        return R.ok(teachingPlanDetailPubService.hasUnfinishedClass(lectureId));
    }

    /**
     * 通过课程, 主讲老师 查询对应时间最早的直播间计划/教学计划
     * @param teachingPlanDetailPubDTO
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO>>
     * <AUTHOR>
     * @date 2024/12/11 14:53
     */
    @Operation(summary = "通过课程, 主讲老师 查询对应时间最早的直播间计划/教学计划", description = "通过课程, 主讲老师 查询对应时间最早的直播间计划/教学计划")
    @PostMapping("/getAtTheEarliestCoursePlanList")
    @Inner
    public R<List<AtTheEarliestAttendClassDetailVO>> getAtTheEarliestCoursePlanList(@RequestBody TeachingPlanDetailPubDTO teachingPlanDetailPubDTO) {
        List<AtTheEarliestAttendClassDetailVO> teachingPlanDetailPubVOList = teachingPlanDetailPubService.getAtTheEarliestCoursePlanList(teachingPlanDetailPubDTO);
        return R.ok(teachingPlanDetailPubVOList);
    }

    /**
     * 专门为点播课场景：通过课程查询该课程下所有教学计划中每个课节的最早上课时间（不限制老师）
     * <AUTHOR>
     * @date 2025/6/12 10:14
     * @param courseId
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO>>
     */
    @Operation(summary = "专门为点播课场景：通过课程查询该课程下所有教学计划中每个课节的最早上课时间", description = "专门为点播课场景：通过课程查询该课程下所有教学计划中每个课节的最早上课时间（不限制老师）")
    @PostMapping("/getAtTheEarliestCoursePlanListForVod")
    @Inner
    public R<List<AtTheEarliestAttendClassDetailVO>> getAtTheEarliestCoursePlanListForVod(@RequestBody Long courseId) {
        List<AtTheEarliestAttendClassDetailVO> teachingPlanDetailPubVOList = teachingPlanDetailPubService.getAtTheEarliestCoursePlanListForVod(courseId);
        return R.ok(teachingPlanDetailPubVOList);
    }



    /**
     * 通过计划Id查询教学计划详情列表
     *
     * @param planId 教学计划id
     * @return 结果
     */
    @Operation(summary = "通过计划Id查询教学计划详情列表", description = "通过计划Id查询教学计划详情列表")
    @GetMapping("/getAllByPlanId/{planId}")
    @Inner(value = false)
    public R<List<LiveRoomPlanDetailVersionVO>> getAllByPlanId(@PathVariable Long planId) {
        return R.ok(teachingPlanDetailPubService.getAllByPlanId(planId));
    }

    /**
     * 通过教学计划ID查询教学计划明细,声网频道
     * @param teachingPlanIdList
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO>>
     * <AUTHOR>
     * @date 2024/12/17 19:24
     */
    @Operation(summary = "通过教学计划ID查询教学计划明细,声网频道", description = "通过教学计划ID查询教学计划明细,声网频道")
    @PostMapping("/getTeachingPlanDetailPubLiveChannel")
    @Inner
    public R<List<TeachingPlanDetailPubVO>> getTeachingPlanDetailPubLiveChannel(@RequestBody List<Long> teachingPlanIdList) {
        List<TeachingPlanDetailPubVO> teachingPlanDetailPubVOList = teachingPlanDetailPubService.getTeachingPlanDetailPubLiveChannel(teachingPlanIdList);
        return R.ok(teachingPlanDetailPubVOList);
    }


    /**
     * 通过教学计划ID查询教学计划明细列表
     *
     * @param teachingPlanId 教学计划Id
     * @return 结果
     */
    @Operation(summary = "通过教学计划ID查询教学计划明细,声网频道", description = "通过教学计划ID查询教学计划明细,声网频道")
    @PostMapping("/getTeachingPlanDetailPub")
    @Inner
    public R<List<TeachingPlanDetailPubVO>> getTeachingPlanDetailPub(
        @RequestBody Long teachingPlanId) {
        return R.ok(teachingPlanDetailPubService.getTeachingPlanDetailPub(teachingPlanId));
    }

    /**
     * 通过教学计划ID查询教学计划明细列表
     *
     * @param teachingPlanIdList 教学计划Id列表
     * @return 结果
     */
    @Operation(summary = "通过教学计划ID查询教学计划明细列表", description = "通过教学计划ID查询教学计划明细列表")
    @PostMapping("/getByTeachingPlanIdList")
    @Inner
    public R<List<TeachingPlanDetailPubDTO>> getByTeachingPlanIdList(@Validated @NotEmpty
        @RequestBody List<Long> teachingPlanIdList) {
        List<TeachingPlanDetailPubDTO> teachingPlanDetailPubDTOS = teachingPlanDetailPubService.getTeachingPlanDetailPubDTOList(
                teachingPlanIdList)
            .stream()
            .map(teachingPlanDetailPub -> {
                TeachingPlanDetailPubDTO teachingPlanDetailPubDTO = new TeachingPlanDetailPubDTO();
                BeanUtils.copyProperties(teachingPlanDetailPub, teachingPlanDetailPubDTO);
                return teachingPlanDetailPubDTO;
            }).toList();
        return R.ok(teachingPlanDetailPubDTOS);
    }

}
