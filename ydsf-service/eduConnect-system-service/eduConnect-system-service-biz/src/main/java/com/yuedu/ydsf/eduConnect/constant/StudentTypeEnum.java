package com.yuedu.ydsf.eduConnect.constant;

import lombok.AllArgsConstructor;

/**
 * 学生课次类型枚举
 * 
 * <AUTHOR>
 * @date 2025/03/11
 */
@AllArgsConstructor
public enum StudentTypeEnum {
    /**
     * 班级学生
     */
    STUDENT_TYPE_1(1, "班级学生"),
    /**
     * 调课学生
     */
    STUDENT_TYPE_2(2, "调课学生"),
    /**
     * 添加考勤学生
     */
    STUDENT_TYPE_3(3, "添加考勤学生");

    public final Integer code;

    public final String desc;
}