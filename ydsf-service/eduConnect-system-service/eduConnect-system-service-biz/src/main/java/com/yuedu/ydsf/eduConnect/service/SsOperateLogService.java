package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.query.SsOperateLogQuery;
import com.yuedu.ydsf.eduConnect.entity.SsOperateLog;

/**
 * 操作记录表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-04 10:00:15
 */
public interface SsOperateLogService extends IService<SsOperateLog> {


    /**
     * 分页查询操作日志
     * @param page
     * @param ssOperateLogQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2024/11/15 16:08
     */
    IPage getSsOperateLogPage(Page page, SsOperateLogQuery ssOperateLogQuery);

}
