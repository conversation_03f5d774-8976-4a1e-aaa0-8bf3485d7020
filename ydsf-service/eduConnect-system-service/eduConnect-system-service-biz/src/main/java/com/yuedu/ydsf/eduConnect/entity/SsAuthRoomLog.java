package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 班级/课次授权修改记录表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-09 09:54:29
 */
@Data
@TableName("ss_auth_room_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "班级/课次授权修改记录表实体类")
public class SsAuthRoomLog extends Model<SsAuthRoomLog> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 业务表ID
	*/
    @Schema(description="业务表ID")
    private Long businessId;

	/**
	* 修改授权类型: 0-班级授权; 1-课次授权;
	*/
    @Schema(description="修改授权类型: 0-班级授权; 1-课次授权;")
    private Integer editAuthType;

	/**
	* 旧授权设备ID值(多个教室以英文逗号分割)
	*/
    @Schema(description="旧授权设备ID值(多个教室以英文逗号分割)")
    private String oldValue;

	/**
	* 新授权设备ID值(多个教室以英文逗号分割)
	*/
    @Schema(description="新授权设备ID值(多个教室以英文逗号分割)")
    private String newValue;

	/**
	* 创建时间
	*/
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑者")
    private String modifer;
}
