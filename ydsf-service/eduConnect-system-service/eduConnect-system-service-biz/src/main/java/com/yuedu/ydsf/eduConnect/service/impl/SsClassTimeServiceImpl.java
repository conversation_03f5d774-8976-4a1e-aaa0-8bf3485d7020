package com.yuedu.ydsf.eduConnect.service.impl;

import static com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum.REQUEST_ERROR;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.api.feign.RemoteLecturerService;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.entity.LecturerEntity;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.api.constant.AppointmentEnum;
import com.yuedu.ydsf.eduConnect.api.constant.AttendClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.AuthState;
import com.yuedu.ydsf.eduConnect.api.constant.ClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsOutCollideEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncAgoraEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncXiaogjEnum;
import com.yuedu.ydsf.eduConnect.api.constant.RoomCodeTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.SsClassStateEnum;
import com.yuedu.ydsf.eduConnect.api.constant.SysErrorCodeEnum;
import com.yuedu.ydsf.eduConnect.api.dto.ClassAuthRoomDTO;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsInRoomVo;
import com.yuedu.ydsf.eduConnect.entity.SsClass;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeStudent;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjPushTask;
import com.yuedu.ydsf.eduConnect.manager.SsClassTimeManager;
import com.yuedu.ydsf.eduConnect.manager.SsXiaogjLogManager;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsXiaogjPushTaskMapper;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeService;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.XiaoGuanJiaConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.DeleteCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.SsPushXiaogjEventReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.UpdateCourseReq;
import com.yuedu.ydsf.eduConnect.manager.XiaoGuanJiaService;
import com.yuedu.ydsf.eduConnect.util.DateUtil;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 课次信息表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:12:52
 */
@Slf4j
@Service
@AllArgsConstructor
public class SsClassTimeServiceImpl extends ServiceImpl<SsClassTimeMapper, SsClassTime> implements
    SsClassTimeService {

    private SsClassTimeMapper ssClassTimeMapper;

    private SsClassTimeManager ssClassTimeManager;

    private final RemoteLecturerService remoteLecturerService;

    private final XiaoGuanJiaService xiaoGuanJiaService;

    private final SsXiaogjLogManager xiaogjLogManager;

    private final SsXiaogjPushTaskMapper ssXiaogjPushTaskMapper;

    @Override
    public IPage<SsClassTimeVO> page(Page page, SsClassTimeQuery ssClassTimeQuery) {
        return ssClassTimeManager.fillData(ssClassTimeMapper.page(page, ssClassTimeQuery));
    }

    @Override
    public List<SsDeviceVO> selectAuthClassRoom(Long id) {
        return Optional.ofNullable(getById(id))
            .map(s -> {
                return ssClassTimeManager.selectAuthClassRoom(s);
            })
            .orElseThrow(() -> new BizException(REQUEST_ERROR, "数据不存在"));
    }

    @Override
    public Boolean updateAuthClassTimeRoom(SsClassTimeDTO ssClassTimeDTO) {
        return Optional.ofNullable(getById(ssClassTimeDTO.getId()))
            .filter(s -> {
                return LocalDateTime.of(s.getAttendClassDate(), s.getAttendClassEndTime())
                    .isAfter(LocalDateTime.now());
            })
            .map(s -> {
                return ssClassTimeManager.updateAuthClassTimeRoom(s,
                    ssClassTimeDTO.getAuthDeviceList());
            })
            .orElseThrow(
                () -> new BizException(REQUEST_ERROR, "当前课次不存在或者已结束，不允许操作"));
    }

    @Override
    public Boolean countByClassIdAndTimeRange(Long classRoomId, LocalDate attendClassDate,
        LocalTime attendClassStartTime, LocalTime attendClassEndTime) {
        QueryWrapper<SsClassTime> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("class_room_id", classRoomId)
            .eq("attend_class_date", attendClassDate)
            .ge("attend_class_start_time", attendClassStartTime)
            .le("attend_class_end_time", attendClassEndTime);
        return this.count(queryWrapper) > 0;
    }

    /**
     * 监课通过房间id进入监课端
     *
     * @param classTimeQuery
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsInRoomVo
     * <AUTHOR>
     * @date 2024/10/14 10:12
     */
    @Override
    public SsInRoomVo inRoomByRoomUuid(SsClassTimeQuery classTimeQuery) {
        SsClassTime ssClassTime = this.getOne(Wrappers.<SsClassTime>lambdaQuery()
            .eq(SsClassTime::getRoomUuid, classTimeQuery.getRoomUuid()));

        if (ssClassTime == null) {
            throw new BizException("课次不存在");
        }

        // 上课开始时间(yyyy-MM-dd HH:mm:ss)
        LocalDateTime attendClassDateStartTime = LocalDateTime.of(
            ssClassTime.getAttendClassDate(),
            ssClassTime.getAttendClassStartTime()
        );

        // 上课结束时间(yyyy-MM-dd HH:mm:ss)
        LocalDateTime attendClassDateEndTime = LocalDateTime.of(
            ssClassTime.getAttendClassDate(),
            ssClassTime.getAttendClassEndTime()
        );

        // 计算上课结束时间与上课开始时间持续时长(单位:秒)
        long durationDiff = ChronoUnit.SECONDS.between(attendClassDateStartTime,
            attendClassDateEndTime);

        long userUuid = System.currentTimeMillis();

        // 封装通过上课码进入直播间参数
        SsInRoomVo ssInRoomVo = new SsInRoomVo();
        ssInRoomVo.setAppId(ssClassTimeManager.getAgoraProperties().getAgoraAppId());
        ssInRoomVo.setRoomUuid(ssClassTime.getRoomUuid());
        ssInRoomVo.setRtmToken(
            ssClassTimeManager.getAgoraRtmToken(ssClassTime.getRoomUuid(),
                String.valueOf(userUuid),
                Short.valueOf(String.valueOf(DeviceTypeEnum.DEVICETYPE_4.code))));
        ssInRoomVo.setUserUuid(userUuid);
        ssInRoomVo.setUserName("监课端" + userUuid);
        ssInRoomVo.setRoleType(String.valueOf(DeviceTypeEnum.DEVICETYPE_4.code));
        ssInRoomVo.setRoomName(ssClassTime.getBooksName());
        ssInRoomVo.setDuration(durationDiff);
        ssInRoomVo.setClassDateTime(
            attendClassDateStartTime.format(
                DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN))
                + StrPool.DASHED +
                attendClassDateEndTime.format(
                    DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));

        // 异步更新监课开始时间
        CompletableFuture.runAsync(() ->
                this.update(Wrappers.lambdaUpdate(SsClassTime.class)
                    .eq(SsClassTime::getRoomUuid, classTimeQuery.getRoomUuid())
                    .set(SsClassTime::getSupervisionClassStartTime, LocalDateTime.now())),
            ssClassTimeManager.getAsyncExecutor()
        );

        return ssInRoomVo;
    }

    /**
     * 查询班级课程安排
     *
     * @param page
     * @param ssClassTimeQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2024/10/12 15:47
     */
    @Override
    public IPage<SsClassTimeVO> getPageClassTimeByClassId(Page page,
        SsClassTimeQuery ssClassTimeQuery) {

        if (Objects.isNull(ssClassTimeQuery.getClassId())) {
            throw new CheckedException("班级ID不能为空");
        }

        // 查询班级课程安排
        IPage<SsClassTimeVO> classTimeByClassIdPage = ssClassTimeMapper.getClassTimeByClassIdPage(
            page, ssClassTimeQuery);

        // 获取主讲老师数据
        List<Long> lecturerIdList = classTimeByClassIdPage.getRecords().stream()
            .map(SsClassTimeVO::getLecturerId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        Map<Long, String> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(lecturerIdList)) {
            LecturerDTO lecturerDTO = new LecturerDTO();
            lecturerDTO.setIds(lecturerIdList);
            R<List<LecturerVO>> lecturerList = remoteLecturerService.getLecturerList(lecturerDTO);
            if (lecturerList.isOk() && CollectionUtils.isNotEmpty(lecturerList.getData())) {
                collect = lecturerList.getData().stream()
                    .collect(Collectors.toMap(LecturerVO::getId, LecturerVO::getLecturerName));
            }

        }

        for (SsClassTimeVO ssClassTimeVO : classTimeByClassIdPage.getRecords()) {

            // 主讲老师名称
            ssClassTimeVO.setLecturerName(collect.get(ssClassTimeVO.getLecturerId()));

            // 获取课程状态
            ssClassTimeManager.setClassTimeAttendStateAndData(ssClassTimeVO);
        }

        return classTimeByClassIdPage;
    }

    /**
     * 获取主讲端/教室端上课码
     *
     * @param ssClassTimeDto
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/15 9:04
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String genRoomTimeCode(SsClassTimeDTO ssClassTimeDto) {
        return Optional.ofNullable(this.getById(ssClassTimeDto.getId())).map(ssClassTime -> {
            if (Objects.equals(ssClassTime.getAttendClassType(),
                ClassTypeEnum.ATTENDCLASSTYPE_0.CODE) && CharSequenceUtil.isBlank(
                ssClassTime.getRoomUuid())) {
                throw new BizException("未同步声网创建房间, 暂时无法生成上课码");
            }

            String existingCode = null;
            if (RoomCodeTypeEnum.ROOM_CODE_TYPE_ENUM_1.CODE.equals(
                ssClassTimeDto.getRoomCodeType())) {
                existingCode = ssClassTime.getLecturerRoomCode();
            } else if (RoomCodeTypeEnum.ROOM_CODE_TYPE_ENUM_2.CODE.equals(
                ssClassTimeDto.getRoomCodeType())) {
                existingCode = ssClassTime.getClassRoomCode();
            }

            if (CharSequenceUtil.isNotBlank(existingCode)) {
                return existingCode;
            }

            var roomTimeCode = generateRoomTimeCode(ssClassTimeDto);

            var updateSuccess = this.update(
                new LambdaUpdateWrapper<SsClassTime>().eq(SsClassTime::getId,
                    ssClassTimeDto.getId()).set(RoomCodeTypeEnum.ROOM_CODE_TYPE_ENUM_1.CODE.equals(
                        ssClassTimeDto.getRoomCodeType()), SsClassTime::getLecturerRoomCode,
                    roomTimeCode).set(RoomCodeTypeEnum.ROOM_CODE_TYPE_ENUM_2.CODE.equals(
                        ssClassTimeDto.getRoomCodeType()), SsClassTime::getClassRoomCode,
                    roomTimeCode));

            if (!updateSuccess) {
                throw new BizException("更新上课码失败");
            }

            return roomTimeCode;
        }).orElseThrow(() -> new BizException("课次不存在"));
    }


    /**
     * 生成上课码逻辑
     *
     * @param ssClassTimeDto
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/15 9:17
     */
    private String generateRoomTimeCode(SsClassTimeDTO ssClassTimeDto) {
        String roomCode;
        do {
            // 生成5位随机数
            String randomStr = RandomUtil.randomNumbers(5);
            roomCode = ssClassTimeDto.getRoomCodeType() + randomStr;

            // 检查是否存在
            long count = this.count(new LambdaQueryWrapper<SsClassTime>()
                .eq(RoomCodeTypeEnum.ROOM_CODE_TYPE_ENUM_1.CODE.equals(
                        ssClassTimeDto.getRoomCodeType()),
                    SsClassTime::getLecturerRoomCode, roomCode)
                .eq(RoomCodeTypeEnum.ROOM_CODE_TYPE_ENUM_2.CODE.equals(
                        ssClassTimeDto.getRoomCodeType()),
                    SsClassTime::getClassRoomCode, roomCode));

            if (count == 0) {
                break;
            }
        } while (true);

        return roomCode;
    }

    /**
     * 新增课次
     *
     * @param ssClassTimeDto
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 11:21
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addClassTime(SsClassTimeDTO ssClassTimeDto) {
        // 查询原课次信息和班级信息
        SsClassTime ssClassTime =
            Optional.ofNullable(getById(ssClassTimeDto.getId()))
                .orElseThrow(() -> new BizException("课次不存在"));
        SsClass ssClass =
            Optional.ofNullable(ssClassTimeManager.ClassGetById(ssClassTime.getClassId()))
                .orElseThrow(() -> new BizException("班级不存在"));

        checkCurrentClassIsFinish(ssClass);

        // 校验课次时间
        validateClassTime(ssClassTimeDto);

        // 同步至声网创建课堂
        ssClassTimeManager.syncAgoraClassRoom(ssClassTimeDto);

        // 保存新课次
        SsClassTime newClassTime = saveNewClassTime(ssClassTimeDto);

        // 处理授权和学生信息
        processAuthorizationAndStudents(ssClassTime, newClassTime, ssClass);

        // 同步校管家授权校区班级排课
        syncToXiaogjAsync(ssClass, newClassTime, true);
    }

    /**
     * 校验班级是否已经结业
     *
     * @param ssClass
     * @return void
     * <AUTHOR>
     * @date 2024/10/17 11:25
     */
    private void checkCurrentClassIsFinish(SsClass ssClass) {
        // 校验当前班级的状态
        if (SsClassStateEnum.STATE_1.CODE.equals(ssClass.getClassState())) {
            throw new BizException("该班级已结业,操作失败!");
        }
    }

    private void validateClassTime(SsClassTimeDTO ssClassTimeDto) {
        checkClassTimeDateTimeCollide(ssClassTimeDto);
    }

    private SsClassTime saveNewClassTime(SsClassTimeDTO ssClassTimeDto) {
        SsClassTime newClassTime = new SsClassTime();
        BeanUtils.copyProperties(ssClassTimeDto, newClassTime);
        // 新增课次时将 原来的主讲端还有教室端的上课码 置空
        newClassTime.setLecturerRoomCode(null);
        newClassTime.setClassRoomCode(null);
        newClassTime.setSupervisionClassStartTime(null);
        newClassTime.setSupervisionClassEndTime(null);
        newClassTime.setId(null);
        if (StringUtils.isBlank(ssClassTimeDto.getRoomUuid())) {
            newClassTime.setRoomUuid(null);
        }
        formatStartAndEndTime(newClassTime);
        log.info("新增课次信息:{}", newClassTime);
        ssClassTimeManager.saveNewClassTime(newClassTime);
        return newClassTime;
    }

    private void processAuthorizationAndStudents(SsClassTime ssClassTime, SsClassTime newClassTime,
        SsClass ssClass) {
        log.info("处理授权和学生信息，班级名称:{},课次ID:{}", ssClass.getClassName(),
            ssClassTime.getId());
        SsClassTimeDTO ssClassTimeDto = new SsClassTimeDTO();
        ssClassTimeDto.setClassId(ssClassTime.getClassId());
        ssClassTimeDto.setId(ssClassTime.getId());
        ssClassTimeDto.setIsSyncXiaogj(ssClass.getIsSyncXiaogj());
        List<SsClassTimeVO> classTimeVoList =
            ssClassTimeManager.selectDeviceClassTimeByClassId(ssClassTimeDto);

        if (CollectionUtils.isEmpty(classTimeVoList)) {
            return;
        }

        List<SsClassAuthRoomStudent> ssClassAuthRoomStudentList =
            ssClassTimeManager.ssClassAuthRoomStudentSelectList(
                ssClass.getId(),
                classTimeVoList.stream()
                    .map(SsClassTimeVO::getAuthCampusId)
                    .distinct()
                    .collect(Collectors.toList()));

        List<SsClassTimeStudent> saveSsClassTimeStudentList = new ArrayList<>();

        classTimeVoList.forEach(
            ssClassTimeVo -> {
                SsClassTimeAuthRoom ssClassTimeAuthRoom = processAuthorizationForCampus(
                    newClassTime, ssClassTimeVo);
                processStudentsForCampus(
                    newClassTime, ssClassTimeVo, ssClassAuthRoomStudentList,
                    saveSsClassTimeStudentList, ssClassTimeAuthRoom);
            });

        if (!saveSsClassTimeStudentList.isEmpty()) {
            ssClassTimeManager.ssClassTimeStudentSaveBatch(saveSsClassTimeStudentList);
        }
    }

    private SsClassTimeAuthRoom processAuthorizationForCampus(
        SsClassTime newClassTime, SsClassTimeVO ssClassTimeVo) {
        if (StrUtil.isNotBlank(ssClassTimeVo.getClassTimeIds())) {
            SsClassAuthRoom ssClassAuthRoom = new SsClassAuthRoom();
            ssClassAuthRoom.setId(ssClassTimeVo.getClassAuthRoomId());
            ssClassAuthRoom.setClassTimeIds(
                String.format("%s,%s", ssClassTimeVo.getClassTimeIds(), newClassTime.getId()));
            ssClassTimeManager.ssClassAuthRoomupdateById(ssClassAuthRoom);
        }

        SsClassTimeAuthRoom ssClassTimeAuthRoom = new SsClassTimeAuthRoom();
        ssClassTimeAuthRoom.setClassId(newClassTime.getClassId());
        ssClassTimeAuthRoom.setClassTimeId(newClassTime.getId());
        if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClassTimeVo.getIsSyncXiaogj())) {
            ssClassTimeAuthRoom.setXgjClassTimeId(UUID.randomUUID().toString());
        }
        ssClassTimeAuthRoom.setCampusId(ssClassTimeVo.getAuthCampusId());
        ssClassTimeAuthRoom.setClassRoomId(ssClassTimeVo.getAuthClassRoomId());
        ssClassTimeAuthRoom.setDeviceId(ssClassTimeVo.getAuthDeviceId());
        ssClassTimeAuthRoom.setXgjCampusId(ssClassTimeVo.getXgjCampusId());
        ssClassTimeAuthRoom.setXgjClassRoomId(ssClassTimeVo.getXgjClassRoomId());
        ssClassTimeManager.ssClassTimeAuthRoomSave(ssClassTimeAuthRoom);
        return ssClassTimeAuthRoom;
    }

    private void processStudentsForCampus(
        SsClassTime newClassTime,
        SsClassTimeVO ssClassTimeVo,
        List<SsClassAuthRoomStudent> ssClassAuthRoomStudentList,
        List<SsClassTimeStudent> saveSsClassTimeStudentList,
        SsClassTimeAuthRoom ssClassTimeAuthRoom) {
        ssClassAuthRoomStudentList.stream()
            .filter(e -> e.getCampusId().equals(ssClassTimeVo.getAuthCampusId()))
            .forEach(
                ssClassAuthRoomStudent -> {
                    SsClassTimeStudent classTimeStudent = new SsClassTimeStudent();
                    classTimeStudent.setClassId(ssClassTimeVo.getClassId());
                    classTimeStudent.setClassTimeAuthRoomId(ssClassTimeAuthRoom.getId());
                    classTimeStudent.setClassTimeId(newClassTime.getId());
                    classTimeStudent.setStudentId(ssClassAuthRoomStudent.getStudentId());
                    classTimeStudent.setStudentName(ssClassAuthRoomStudent.getStudentName());
                    classTimeStudent.setStudentMobile(ssClassAuthRoomStudent.getStudentMobile());
                    classTimeStudent.setCampusId(ssClassTimeVo.getAuthCampusId());
                    classTimeStudent.setClassRoomId(ssClassTimeVo.getAuthClassRoomId());
                    classTimeStudent.setDeviceId(ssClassTimeVo.getAuthDeviceId());
                    saveSsClassTimeStudentList.add(classTimeStudent);
                });
    }

    private void syncToXiaogjAsync(SsClass ssClass, SsClassTime classTime, boolean isNewClassTime) {
        log.info("同步校管家排课信息，班级名称:{},课次ID:{},是否新增课次:{},是否推送校管家:{}",
            ssClass.getClassName(), classTime.getId(),
            isNewClassTime, ssClass.getIsSyncXiaogj());
        if (!IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClass.getIsSyncXiaogj())) {
            return;
        }

        String userName = SecurityUtils.getUser().getUsername();
        //判断事物是否提交
        List<ClassCourseReq> classCourseReqList =
            buildClassCourseReqList(ssClass, classTime, isNewClassTime);
        log.info("同步校管家排课信息，数据条数:{}", classCourseReqList.size());
        CompletableFuture.runAsync(
            () -> {
                for (ClassCourseReq classCourseReq : classCourseReqList) {

                    // 双师排课推送校管家消息队列公共方法
                    SsPushXiaogjEventReq ssPushXiaogjEventReq = xiaoGuanJiaService.ssPushXiaogjMessage(
                        classCourseReq);

                    // 保存排课校管家日志
                    xiaogjLogManager.saveXiaogjLog(ssPushXiaogjEventReq, classCourseReq, userName);

                }

            },
            ssClassTimeManager.getAsyncExecutor()
        );
    }

    private List<ClassCourseReq> buildClassCourseReqList(
        SsClass ssClass, SsClassTime classTime, boolean isNewClassTime) {
        LecturerVO lecturerVO = ssClassTimeManager.getLecturerInfoById(classTime.getLecturerId());

        SsClassTimeDTO classTimeDto = new SsClassTimeDTO();
        classTimeDto.setClassId(classTime.getClassId());
        classTimeDto.setId(classTime.getId());
        classTimeDto.setIsSyncXiaogj(ssClass.getIsSyncXiaogj());

        List<SsClassTimeVO> classTimeVoList =
            ssClassTimeManager.selectDeviceClassTimeByClassId(classTimeDto);

        return classTimeVoList.stream()
            .map(
                ssClassTimeVo ->
                    buildClassCourseReq(ssClassTimeVo, classTime, lecturerVO, !isNewClassTime))
            .collect(Collectors.toList());
    }

    private ClassCourseReq buildClassCourseReq(
        SsClassTimeVO ssClassTimeVo, SsClassTime classTime, LecturerVO lecturerVO,
        boolean isUpdate) {
        LocalDateTime classStartDateTime =
            LocalDateTime.of(classTime.getAttendClassDate(), classTime.getAttendClassStartTime());
        LocalDateTime classEndDateTime =
            LocalDateTime.of(classTime.getAttendClassDate(), classTime.getAttendClassEndTime());
        String formattedStartTime = formatDateTime(classStartDateTime);
        String formattedEndTime = formatDateTime(classEndDateTime);

        ClassCourseReq classCourseReq = new ClassCourseReq();
        classCourseReq.setTripartiteId(ssClassTimeVo.getXgjClassId());
        classCourseReq.setType(ClassCourseReq.CreateClassType.CREATE_CLASS_TYPE.CODE);

        if (isUpdate) {
            UpdateCourseReq updateCourseReq =
                buildUpdateCourseReq(
                    ssClassTimeVo, classTime, lecturerVO, formattedStartTime, formattedEndTime);
            classCourseReq.setUpdateCourseList(Collections.singletonList(updateCourseReq));
        } else {
            CreateCourseReq createCourseReq =
                buildCreateCourseReq(
                    ssClassTimeVo, classTime, lecturerVO, formattedStartTime, formattedEndTime);
            classCourseReq.setCreateCourseList(Collections.singletonList(createCourseReq));
        }

        return classCourseReq;
    }

    private UpdateCourseReq buildUpdateCourseReq(
        SsClassTimeVO ssClassTimeVo,
        SsClassTime classTime,
        LecturerVO lecturerVO,
        String formattedStartTime,
        String formattedEndTime) {
        UpdateCourseReq updateCourseReq = new UpdateCourseReq();
        updateCourseReq.setTripartiteId(ssClassTimeVo.getXgjClassId());
        updateCourseReq.setCTeacherUserID(
            Optional.ofNullable(lecturerVO.getXgjLecturerId()).orElse(""));
        updateCourseReq.setThreeId(ssClassTimeVo.getXgjClassTimeId());
        updateCourseReq.setCStartTime(formattedStartTime);
        updateCourseReq.setCEndTime(formattedEndTime);
        updateCourseReq.setCShiftID(XiaoGuanJiaConstant.DEFAULT_COURSE_ID);
        updateCourseReq.setCClassroomID(ssClassTimeVo.getXgjClassRoomId());
        updateCourseReq.setCWeekStartTime(formatTime(classTime.getAttendClassStartTime()));
        updateCourseReq.setCWeekEndTime(formatTime(classTime.getAttendClassEndTime()));
        updateCourseReq.setCWeekday(classTime.getAttendClassDate().getDayOfWeek().getValue());
        updateCourseReq.setCDate(formatDate(classTime.getAttendClassDate()));
        return updateCourseReq;
    }

    /**
     * 查看讲师空闲时间
     *
     * @param page
     * @param ssClassTimeQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2024/10/18 14:04
     */
    @Override
    public IPage selectLecturerFreeTime(Page page,
        SsClassTimeQuery ssClassTimeQuery) {

        IPage pages = new Page();

        List<Map<String, Object>> resultMapList = new ArrayList<>();

        // 查询主讲老师数据
        LecturerDTO lecturerDTO = new LecturerDTO();
        lecturerDTO.setId(ssClassTimeQuery.getLecturerId());
        PageDTO pageDTO = new PageDTO();
        BeanUtils.copyProperties(page, pageDTO);
        lecturerDTO.setPage(pageDTO);
        R<Page<LecturerEntity>> ssLecturerVOList = remoteLecturerService.getPageList(lecturerDTO);

        if (!ssLecturerVOList.isOk() || CollectionUtils.isEmpty(
            ssLecturerVOList.getData().getRecords())) {
            return pages;
        }

        // 创建时间类型的Map，以便重用
        Map<String, Object> timeTypeMap = new HashMap<>();
        String[] timeTypeArr = {"morning", "afternoon", "evening"};
        for (String s : timeTypeArr) {
            timeTypeMap.put(s, null);
        }

        for (LecturerEntity ssLecturer : ssLecturerVOList.getData().getRecords()) {
            Map<String, Object> lecturerMap = new HashMap<>();
            lecturerMap.put("lecturerId", ssLecturer.getId());
            lecturerMap.put("lecturerName", ssLecturer.getLecturerName());

            // 为每个星期的天数添加时间类型的Map
            for (int j = 1; j <= 7; j++) {
                // 使用构造器直接复制
                lecturerMap.put("week" + j, new HashMap<>(timeTypeMap));
            }

            resultMapList.add(lecturerMap);
        }

        // 查询指定时间段已排课信息
        SsClassTimeQuery classTimeQuery = new SsClassTimeQuery();
        classTimeQuery.setAttendClassType(ssClassTimeQuery.getAttendClassType());
        classTimeQuery.setSelectAttendClassStartTime(
            ssClassTimeQuery.getSelectAttendClassStartTime());
        classTimeQuery.setSelectAttendClassEndTime(ssClassTimeQuery.getSelectAttendClassEndTime());
        List<SsClassTimeVO> ssClassTimeVoList = ssClassTimeMapper.getClassTimeByClassId(
            classTimeQuery);

        // 排课信息按任课老师,周展示处理
        for (Map<String, Object> resultMap : resultMapList) {

            for (SsClassTimeVO ssClassTimeVo : ssClassTimeVoList) {

                // 查询上课日期为周几
                int week = ssClassTimeVo.getAttendClassDate().getDayOfWeek().getValue();

                // 给任课老师封装周排课数据
                if (resultMap.get("lecturerId").equals(ssClassTimeVo.getLecturerId())) {

                    // 校验排课时间为上午, 下午, 晚上
                    String timeType = DateUtil.isTime(
                        DateUtil.format(ssClassTimeVo.getAttendClassStartTime()));

                    // 本次课次信息追加
                    Map<String, Object> weekMap = (Map<String, Object>) resultMap.get(
                        "week" + week);
                    if (Objects.isNull(weekMap)) {
                        weekMap = new HashMap<>();
                    }

                    // 获取已封装课次后在追加本次课次
                    List<Map<String, Object>> timeTypeMapList = (List<Map<String, Object>>) weekMap.get(
                        timeType);
                    if (CollectionUtils.isEmpty(timeTypeMapList)) {
                        timeTypeMapList = new ArrayList<>();
                    }

                    // 本次课次信息封装
                    Map<String, Object> timeMap = new HashMap<>();
                    timeMap.put("id", ssClassTimeVo.getId());
                    timeMap.put("classId", ssClassTimeVo.getClassId());
                    timeMap.put("className", ssClassTimeVo.getClassName());
                    timeMap.put("lecturerId", ssClassTimeVo.getLecturerId());
                    timeMap.put("lecturerName", ssClassTimeVo.getLecturerName());
                    timeMap.put("deviceId", ssClassTimeVo.getDeviceId());
                    timeMap.put("deviceName", ssClassTimeVo.getLiveDeviceName());
                    timeMap.put("classRoomId", ssClassTimeVo.getClassRoomId());
                    timeMap.put("classRoomName", ssClassTimeVo.getClassRoomName());
                    timeMap.put("attendClassDate",
                        DateUtil.format(ssClassTimeVo.getAttendClassDate()));
                    timeMap.put("startTime",
                        DateUtil.format(ssClassTimeVo.getAttendClassStartTime()));
                    timeMap.put("endTime", DateUtil.format(ssClassTimeVo.getAttendClassEndTime()));
                    timeTypeMapList.add(timeMap);

                    // 通过开始时间进行排序
                    timeTypeMapList = timeTypeMapList.stream()
                        .sorted(Comparator.comparing(map -> {
                            return (String) map.get("startTime");
                        }))
                        .collect(Collectors.toList());

                    weekMap.put(timeType, timeTypeMapList);

                    // 最新封装后参数赋值
                    resultMap.put("week" + week, weekMap);

                }

            }

        }

        pages.setCurrent(ssLecturerVOList.getData().getCurrent());
        pages.setSize(ssLecturerVOList.getData().getSize());
        pages.setTotal(ssLecturerVOList.getData().getTotal());
        pages.setRecords(resultMapList);

        return pages;

    }


    private CreateCourseReq buildCreateCourseReq(
        SsClassTimeVO ssClassTimeVo,
        SsClassTime classTime,
        LecturerVO lecturerVO,
        String formattedStartTime,
        String formattedEndTime) {
        CreateCourseReq createCourseReq = new CreateCourseReq();
        createCourseReq.setTripartiteId(ssClassTimeVo.getXgjClassId());
        createCourseReq.setCStartTime(formattedStartTime);
        createCourseReq.setCEndTime(formattedEndTime);
        createCourseReq.setCCourseMode(1);

        CreateCourseReq.CourseData courseData =
            CreateCourseReq.CourseData.builder()
                .cWeekday(classTime.getAttendClassDate().getDayOfWeek().getValue())
                .cWeekStartTime(formattedStartTime)
                .cWeekEndTime(formattedEndTime)
                .cDate(formatDate(classTime.getAttendClassDate()))
                .cClassroomID(ssClassTimeVo.getXgjClassRoomId())
                .cStartTime(formattedStartTime)
                .cEndTime(formattedEndTime)
                .threeId(ssClassTimeVo.getXgjClassTimeId())
                .speaker(Optional.ofNullable(lecturerVO.getXgjLecturerId()).orElse(""))
                .build();

        createCourseReq.setCCourseData(Collections.singletonList(courseData));
        return createCourseReq;
    }

    private String formatTime(LocalTime time) {
        return time.format(DateTimeFormatter.ofPattern(DatePattern.NORM_TIME_PATTERN));
    }

    private String formatDate(LocalDate date) {
        return date.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
    }

    private String formatDateTime(LocalDateTime dateTime) {
        return cn.hutool.core.date.DateUtil.format(dateTime, DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * 校验课次时间是否正确
     *
     * @param ssClassTimeDto
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 13:45
     */
    private void checkClassTimeDateTime(SsClassTimeDTO ssClassTimeDto) {
        // 点播课
        if (ClassTypeEnum.ATTENDCLASSTYPE_1.CODE.equals(ssClassTimeDto.getAttendClassType())
            && Objects.isNull(ssClassTimeDto.getRecordingId())) {
            throw new BizException("点播资源ID不能为空！");
        }

        // 上课开始时间
        LocalDateTime attendClassDateStartTime =
            LocalDateTime.of(
                ssClassTimeDto.getAttendClassDate(), ssClassTimeDto.getAttendClassStartTime());
        // 上课结束时间
        LocalDateTime attendClassDateEndTime =
            LocalDateTime.of(
                ssClassTimeDto.getAttendClassDate(), ssClassTimeDto.getAttendClassEndTime());

        // 校验上课时间
        if (attendClassDateEndTime.isBefore(attendClassDateStartTime)) {
            throw new BizException("上课结束时间不能在上课开始时间之前！");
        }

        if (attendClassDateStartTime.isBefore(LocalDateTime.now())) {
            throw new BizException("上课开始时间不能在当前时间之前！");
        }
    }

    /**
     * 检查上课时间是否有无冲突
     *
     * @param ssClassTimeDto
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 13:58
     */
    private void checkClassTimeDateTimeCollide(SsClassTimeDTO ssClassTimeDto) {
        // 校验是否跳过时间冲突
        if (IsOutCollideEnum.IS_OUT_COLLIDE_ENUM_0.CODE.equals(ssClassTimeDto.getIsOutCollide())) {
            // 查询是否存在同一个班级, 同一个时间课次(重叠时间也算存在)
            LambdaQueryWrapper<SsClassTime> queryWrapper =
                new LambdaQueryWrapper<SsClassTime>()
                    .ne(SsClassTime::getId, ssClassTimeDto.getId())
                    .eq(SsClassTime::getClassId, ssClassTimeDto.getClassId())
                    .eq(SsClassTime::getAttendClassDate, ssClassTimeDto.getAttendClassDate())
                    .and(
                        wrapper ->
                            wrapper
                                .le(
                                    SsClassTime::getAttendClassStartTime,
                                    ssClassTimeDto.getAttendClassEndTime())
                                .ge(
                                    SsClassTime::getAttendClassEndTime,
                                    ssClassTimeDto.getAttendClassStartTime()));

            long count = this.count(queryWrapper);

            if (count > 0) {
                throw new BizException(SysErrorCodeEnum.COURSE_SCHEDULE_CONFLICT,
                    "该时间段已有课程,无法排课！");
            }
        }
    }

    /**
     * 编辑课次
     *
     * @param ssClassTimeDto
     * @return void
     * <AUTHOR>
     * @date 2024/10/17 10:50
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateClassTime(SsClassTimeDTO ssClassTimeDto) {
        // 查询修改前课次信息
        SsClassTime classTime = getById(ssClassTimeDto.getId());
        if (classTime == null) {
            throw new BizException(REQUEST_ERROR, "课次不存在");
        }

        // 校验课次是否可进行操作
        checkClassTimeIsOperable(classTime, false);

        // 查询班级信息
        SsClass ssClass = ssClassTimeManager.getClassById(classTime.getClassId());

        // 校验班级的结业状态
        checkCurrentClassIsFinish(ssClass);

        // 校验课次时间是否正确
        validateClassTimeData(ssClassTimeDto);

        // 更新课次信息
        updateClassTimeInfo(ssClassTimeDto, classTime);
        if (!IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClass.getIsSyncXiaogj())) {
            return;
        }
        //查询当前课次已授权校区
        List<ClassAuthRoomDTO> classAuthRoomList = this.selectAuthClassRoom(classTime.getId())
            .stream()
            .filter(ssDeviceVO -> ssDeviceVO.getAuthState().equals(AuthState.AUTH_STATE_1.code))
            .map(ssDeviceVO -> {
                ClassAuthRoomDTO classAuthRoomDTO = new ClassAuthRoomDTO();
                classAuthRoomDTO.setCampusId(ssDeviceVO.getCampusId());
                classAuthRoomDTO.setClassRoomId(ssDeviceVO.getClassRoomId());
                classAuthRoomDTO.setDeviceId(ssDeviceVO.getId());
                classAuthRoomDTO.setXgjClassRoomId(ssDeviceVO.getXgjClassRoomId());
                return classAuthRoomDTO;
            }).collect(Collectors.toList());
        log.info("修改课次信息，班级名称:{},课次ID:{},授权教室:{}", ssClass.getClassName(),
            classTime.getId(), JSONUtil.toJsonStr(classAuthRoomList));
        if (CollectionUtils.isEmpty(classAuthRoomList)) {
            return;
        }
        int updateXiaogjPushTask = ssClassTimeManager.getUpdateXiaogjPushTask(classTime,
            classAuthRoomList, ssClass);
        // 如果当前课次任务还未同步到校管家，不进行同步
        if (updateXiaogjPushTask > 0) {
            return;
        }
        // 同步到校管家
        syncToXiaogjAsync(ssClass, classTime, false);
    }

    private void checkClassTimeIsOperable(SsClassTime classTime, Boolean isDelete) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime attendClassStartTime =
            LocalDateTime.of(classTime.getAttendClassDate(), classTime.getAttendClassStartTime());
        LocalDateTime attendClassEndTime =
            LocalDateTime.of(classTime.getAttendClassDate(), classTime.getAttendClassEndTime());
        // 如果是删除操作，需要检查课次是否进行中
        if (Boolean.TRUE.equals(isDelete)
            && now.isAfter(attendClassStartTime)
            && now.isBefore(attendClassEndTime)) {
            throw new BizException("课次正在进行中,无法删除!");
        }

        // 检查课次是否已结束
        if (attendClassEndTime.isBefore(now)) {
            throw new BizException("选择的数据存在已结束课次,无法操作!");
        }
    }

    private void validateClassTimeData(SsClassTimeDTO ssClassTimeDto) {
        checkClassTimeDateTime(ssClassTimeDto);
        checkClassTimeDateTimeCollide(ssClassTimeDto);
    }

    private void updateClassTimeInfo(SsClassTimeDTO ssClassTimeDto, SsClassTime classTime) {
        boolean needUpdateAgora = isNeedUpdateAgora(ssClassTimeDto, classTime);
        if (needUpdateAgora) {
            ssClassTimeManager.syncAgoraClassRoom(ssClassTimeDto);
        }
        // 根据选择的直播间教室查询对应的设备
        ssClassTimeManager.updateLiveDevice(ssClassTimeDto);
        BeanUtils.copyProperties(ssClassTimeDto, classTime);
        updateAgoraRelatedFields(classTime, ssClassTimeDto, needUpdateAgora);
        formatStartAndEndTime(classTime);
        updateById(classTime);
        // 若直播间RoomUid为null单独更新RoomUuid字段
        if (StringUtils.isBlank(ssClassTimeDto.getRoomUuid())) {
            update(
                Wrappers.lambdaUpdate(SsClassTime.class)
                    .set(SsClassTime::getRoomUuid, null)
                    .eq(SsClassTime::getId, ssClassTimeDto.getId()));
        }
    }

    /**
     * 格式化课次表开始时间和结束时间
     *
     * @param classTime
     * @return void
     * <AUTHOR>
     * @date 2024/11/7 14:25
     */
    private void formatStartAndEndTime(SsClassTime classTime) {
        if (Objects.nonNull(classTime)
            && Objects.nonNull(classTime.getAttendClassDate())
            && Objects.nonNull(classTime.getAttendClassStartTime())
            && Objects.nonNull(classTime.getAttendClassEndTime())) {
            classTime.setAttendTimeStartTime(
                LocalDateTime.of(classTime.getAttendClassDate(),
                    classTime.getAttendClassStartTime()));
            classTime.setAttendTimeEndTime(
                LocalDateTime.of(classTime.getAttendClassDate(),
                    classTime.getAttendClassEndTime()));
        }
    }

    private boolean isNeedUpdateAgora(SsClassTimeDTO ssClassTimeDto, SsClassTime classTime) {
        return AttendClassTypeEnum.ATTEND_CLASS_TYPE_0.CODE.equals(classTime.getAttendClassType())
            && (!ssClassTimeDto.getAttendClassDate().equals(classTime.getAttendClassDate())
            || !ssClassTimeDto.getAttendClassStartTime().equals(classTime.getAttendClassStartTime())
            || !ssClassTimeDto.getAttendClassEndTime().equals(classTime.getAttendClassEndTime()));
    }

    private void updateAgoraRelatedFields(
        SsClassTime ssClassTime, SsClassTimeDTO ssClassTimeDto, boolean needUpdateAgora) {
        if (needUpdateAgora && StrUtil.isBlank(ssClassTimeDto.getRoomUuid())) {
            ssClassTime.setRoomUuid(null);
            ssClassTime.setIsSyncAgora(IsSyncAgoraEnum.ISSYNCAGORA_0.CODE());
        }
    }

    /**
     * 删除课次
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/10/18 13:54
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteClassTime(Long[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return;
        }

        List<SsClassTime> classTimeList = listByIds(Arrays.asList(ids));
        if (CollUtil.isEmpty(classTimeList)) {
            throw new BizException("未找到要删除的课次");
        }

        // 校验课次是否可进行操作
        classTimeList.forEach(e -> checkClassTimeIsOperable(e, true));

        // 查询班级信息和课次授权信息
        Map<Long, SsClass> classMap =
            ssClassTimeManager.getClassMapByIds(
                classTimeList.stream().map(SsClassTime::getClassId).collect(Collectors.toSet()));

        List<SsClassTimeVO> classTimeVoList =
            ssClassTimeManager.selectDeviceClassTimeByIds(Arrays.asList(ids));

        // 删除课次相关数据
        removeByIds(Arrays.asList(ids));
        ssClassTimeManager.deleteClassTimeAuthRoomByIds(ids);
        ssClassTimeManager.deleteClassTimeStudent(ids);

        // 处理授权和同步校管家
        handleAuthorizationAndSyncXiaogj(classTimeVoList, classMap);
    }

    private void handleAuthorizationAndSyncXiaogj(
        List<SsClassTimeVO> classTimeVoList, Map<Long, SsClass> classMap) {
        List<SsClassAuthRoom> updateAuthRoomList = new ArrayList<>();
        List<ClassCourseReq> classCourseReqList = new ArrayList<>();

        for (SsClassTimeVO ssClassTimeVo : classTimeVoList) {
            // 处理授权校区
            if (CharSequenceUtil.isNotBlank(ssClassTimeVo.getClassTimeIds())) {
                SsClassAuthRoom ssClassAuthRoom = new SsClassAuthRoom();
                ssClassAuthRoom.setId(ssClassTimeVo.getClassAuthRoomId());
                ssClassAuthRoom.setClassTimeIds(
                    removeIdFromClassTimeIds(ssClassTimeVo.getClassTimeIds(),
                        ssClassTimeVo.getId().toString())
                );
                if (CharSequenceUtil.isBlank(ssClassAuthRoom.getClassTimeIds())) {
                    ssClassAuthRoom.setAppointmentTime(null);
                    ssClassAuthRoom.setAppointmentStatus(AppointmentEnum.TYPE_0.CODE);
                }
                updateAuthRoomList.add(ssClassAuthRoom);
            }

            // 处理同步校管家
            SsClass ssClass = classMap.get(ssClassTimeVo.getClassId());
            if (ssClass != null
                && IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClass.getIsSyncXiaogj())) {
                //删除未同步校管家的课次任务
                int update = ssXiaogjPushTaskMapper.delete(
                    Wrappers.lambdaUpdate(SsXiaogjPushTask.class)
                        .eq(SsXiaogjPushTask::getClassTimeId, ssClassTimeVo.getId())
                        .eq(SsXiaogjPushTask::getTaskStatus, 0)
                );
                log.info(
                    "删除课次时删除未同步校管家的课次任务,课次ID:{},班级名称:{},关联书籍:{},删除数量:{}",
                    ssClassTimeVo.getId(), ssClass.getClassName(), ssClassTimeVo.getBooksName(),
                    update);
                ClassCourseReq classCourseReq = new ClassCourseReq();
                classCourseReq.setTripartiteId(ssClassTimeVo.getXgjClassId());
                classCourseReq.setType(ClassCourseReq.CreateClassType.CREATE_CLASS_TYPE.CODE);
                DeleteCourseReq deleteCourseReq = new DeleteCourseReq();
                deleteCourseReq.setTripartiteId(ssClassTimeVo.getXgjClassId());
                deleteCourseReq.setThreeId(ssClassTimeVo.getXgjClassTimeId());
                classCourseReq.setDeleteCourseList(Collections.singletonList(deleteCourseReq));
                classCourseReqList.add(classCourseReq);
            }
        }

        // 批量更新授权校区
        if (!updateAuthRoomList.isEmpty()) {
            ssClassTimeManager.updateBatchClassAuthRoom(updateAuthRoomList);
        }

        // 异步同步校管家
        if (!classCourseReqList.isEmpty()) {
            String userName = SecurityUtils.getUser().getUsername();
            CompletableFuture.runAsync(
                () -> {
                    for (ClassCourseReq classCourseReq : classCourseReqList) {

                        // 双师排课推送校管家消息队列公共方法
                        SsPushXiaogjEventReq ssPushXiaogjEventReq =
                            xiaoGuanJiaService.ssPushXiaogjMessage(classCourseReq);

                        // 保存排课校管家日志
                        xiaogjLogManager.saveXiaogjLog(ssPushXiaogjEventReq, classCourseReq,
                            userName);
                    }
                },
                ssClassTimeManager.getAsyncExecutor());
        }
    }

    /**
     * 去除id返回正确ID列表
     *
     * @param classTimeIds
     * @param idToRemove
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/12/4 17:39
     */
    private String removeIdFromClassTimeIds(String classTimeIds, String idToRemove) {
        if (StringUtils.isBlank(classTimeIds)) {
            return "";
        }

        // 记录处理过程
        log.info("开始处理课次ID移除,原始classTimeIds:{}, 需要移除的id:{}", classTimeIds,
            idToRemove);

        String[] ids = classTimeIds.split(",");
        StringBuilder result = new StringBuilder();

        for (String id : ids) {
            if (!id.trim().equals(idToRemove.trim())) {
                if (!result.isEmpty()) {
                    result.append(",");
                }
                result.append(id.trim());
            }
        }

        String finalResult = result.toString();
        log.info("课次ID移除处理完成,处理结果:{}", finalResult);

        return finalResult;
    }

    /**
     * 获取监课链接
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/21 13:43
     */
    @Override
    public SsClassTimeVO getSupervisionUrl(SsClassTimeQuery classTimeQuery) {
        return ssClassTimeManager.getSupervisionUrl(classTimeQuery);
    }

    /**
     * 通过ID 获取课次详情
     *
     * @param id
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO
     * <AUTHOR>
     * @date 2024/10/21 14:06
     */
    @Override
    public SsClassTimeVO getClassTimeInfo(Long id) {
        return ssClassTimeManager.getClassTimeInfo(id);
    }


    /**
     * 获取课次对应的上课码
     *
     * @param id
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO
     * <AUTHOR>
     * @date 2024/10/23 16:25
     */
    @Override
    public SsClassTimeVO getRoomTimeCode(Long id) {
        SsClassTime classTime = ssClassTimeMapper.selectById(id);
        if (Objects.isNull(classTime)) {
            throw new BizException("课次不存在");
        }
        SsClassTimeVO ssClassTimeVO = new SsClassTimeVO();
        BeanUtils.copyProperties(classTime, ssClassTimeVO);
        return ssClassTimeVO;
    }

    /**
     * 获取教室日历
     *
     * @param classTimeQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2024/10/24 14:32
     */
    @Override
    public IPage classRoomFreeTimeCalendar(SsClassTimeQuery classTimeQuery) {
        return ssClassTimeManager.classRoomFreeTimeCalendar(classTimeQuery);
    }

    @Override
    public List<SsClassTime> listByCourseScheduleId(Long courseScheduleId) {
        return this.list(new LambdaQueryWrapper<SsClassTime>().eq(SsClassTime::getCourseScheduleId,
            courseScheduleId));
    }

    /**
     * SP1自动创建声网房间
     *
     * @return void
     * <AUTHOR>
     * @date 2025/2/14 10:10
     */
    @Override
    public void autoCreateSP1Channel() {
        // 查询未同步至声网并且距离开课前24小时内课次数据
        List<SsClassTimeVO> classTimeList = ssClassTimeMapper.selectSyncAgora();
        for (SsClassTimeVO ssClassTimeVo : classTimeList) {
            SsClassTimeDTO ssClassTimeDto = new SsClassTimeDTO();
            BeanUtils.copyProperties(ssClassTimeVo, ssClassTimeDto);
            // 同步至声网创建课堂
            ssClassTimeManager.syncAgoraClassRoom(ssClassTimeDto);
            // 同步声网成功, 更新业务状态
            SsClassTime ssClassTime = new SsClassTime();
            ssClassTime.setId(ssClassTimeDto.getId());
            ssClassTime.setRoomUuid(ssClassTimeDto.getRoomUuid());
            ssClassTime.setIsSyncAgora(ssClassTimeDto.getIsSyncAgora());
            ssClassTimeMapper.updateById(ssClassTime);
        }
    }
}
