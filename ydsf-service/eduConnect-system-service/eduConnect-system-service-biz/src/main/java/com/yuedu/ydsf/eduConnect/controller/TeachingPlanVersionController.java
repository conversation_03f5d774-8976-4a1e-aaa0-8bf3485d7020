package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanVersionVO;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanVersion;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanVersionService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教学计划发布版本记录表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-29 14:43:44
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/TeachingPlanVersion" )
@Tag(description = "ea_teaching_plan_version" , name = "教学计划发布版本记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TeachingPlanVersionController {

    private final  TeachingPlanVersionService teachingPlanVersionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param teachingPlanVersion 教学计划发布版本记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("admin_TeachingPlanVersion_view")
    public R getTeachingPlanVersionPage(@ParameterObject Page page, @ParameterObject TeachingPlanVersion teachingPlanVersion) {
        LambdaQueryWrapper<TeachingPlanVersion> wrapper = Wrappers.lambdaQuery();
        return R.ok(teachingPlanVersionService.page(page, wrapper));
    }


    /**
     * 通过条件查询教学计划发布版本记录表
     * @param teachingPlanVersion 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("admin_TeachingPlanVersion_view")
    public R getDetails(@ParameterObject TeachingPlanVersion teachingPlanVersion) {
        return R.ok(teachingPlanVersionService.list(Wrappers.query(teachingPlanVersion)));
    }

    /**
     * 新增教学计划发布版本记录表
     * @param teachingPlanVersion 教学计划发布版本记录表
     * @return R
     */
    @Operation(summary = "新增教学计划发布版本记录表" , description = "新增教学计划发布版本记录表" )
    @PostMapping("/add")
    @HasPermission("admin_TeachingPlanVersion_add")
    public R save(@RequestBody TeachingPlanVersion teachingPlanVersion) {
        return R.ok(teachingPlanVersionService.save(teachingPlanVersion));
    }

    /**
     * 修改教学计划发布版本记录表
     * @param teachingPlanVersion 教学计划发布版本记录表
     * @return R
     */
    @Operation(summary = "修改教学计划发布版本记录表" , description = "修改教学计划发布版本记录表" )
    @PutMapping("/edit")
    @HasPermission("admin_TeachingPlanVersion_edit")
    public R updateById(@RequestBody TeachingPlanVersion teachingPlanVersion) {
        return R.ok(teachingPlanVersionService.updateById(teachingPlanVersion));
    }

    /**
     * 通过id删除教学计划发布版本记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除教学计划发布版本记录表" , description = "通过id删除教学计划发布版本记录表" )
    @DeleteMapping("/delete")
    @HasPermission("admin_TeachingPlanVersion_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(teachingPlanVersionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param teachingPlanVersion 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("admin_TeachingPlanVersion_export")
    public List<TeachingPlanVersion> exportExcel(TeachingPlanVersion teachingPlanVersion,Long[] ids) {
        return teachingPlanVersionService.list(Wrappers.lambdaQuery(teachingPlanVersion).in(ArrayUtil.isNotEmpty(ids), TeachingPlanVersion::getId, ids));
    }

    /**
     * 导入excel 表
     * @param teachingPlanVersionList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("admin_TeachingPlanVersion_export")
    public R importExcel(@RequestExcel List<TeachingPlanVersion> teachingPlanVersionList, BindingResult bindingResult) {
        return R.ok(teachingPlanVersionService.saveBatch(teachingPlanVersionList));
    }

    /**
     * 通过id列表查询详情
     * @param ids id列表
     * @return List<TeachingPlanPub>
     */
    @Operation(summary = "通过id列表查询详情" , description = "通过id列表查询详情")
    @PostMapping("/getInfoByList")
    @Inner
    public R<List<TeachingPlanVersionVO>> getInfoByList(@RequestBody List<Long> ids){
        return R.ok(BeanUtil.copyToList(teachingPlanVersionService.list(
            Wrappers.lambdaQuery(TeachingPlanVersion.class)
                .in(TeachingPlanVersion::getId, ids)), TeachingPlanVersionVO.class));
    }
}
