package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.constant.AppointmentEnum;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import java.util.List;

/**
 * 班级授权教室表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 09:06:41
 */
public interface SsClassAuthRoomService extends IService<SsClassAuthRoom> {

    /**
     * 查询班级下得授权教室，过滤单课次授权的校区
     *
     * @param classId         班级ID
     * @param appointmentEnum 授权状态
     */
    List<SsClassAuthRoom> listNotAloneAuthRoom(Long classId, AppointmentEnum appointmentEnum);

}
