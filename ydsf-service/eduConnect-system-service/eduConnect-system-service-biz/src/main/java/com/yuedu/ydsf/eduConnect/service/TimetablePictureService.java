package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.dto.TimetablePictureDTO;
import com.yuedu.ydsf.eduConnect.api.query.TimetablePictureQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TimetablePictureVO;
import com.yuedu.ydsf.eduConnect.entity.TimetablePicture;
import java.io.Serializable;
import java.util.List;

/**
* 上课拍照记录服务接口
*
* <AUTHOR>
* @date  2025/02/13
*/
public interface TimetablePictureService extends IService<TimetablePicture> {



    /**
     * 上课拍照记录分页查询
     *
     * @param page 分页对象
     * @param timetablePictureQuery 上课拍照记录
     * @return IPage 分页结果
     */
    IPage<TimetablePictureVO> page(Page page, TimetablePictureQuery timetablePictureQuery);


    /**
     * 根据ID获得上课拍照记录信息
     *
     * @param id id
     * @return TimetablePictureVO 详细信息
     */
    TimetablePictureVO getInfoById(Serializable id);


    /**
     * 新增上课拍照记录
     *
     * @param timetablePictureDTO 上课拍照记录
     * @return boolean 执行结果
     */
    boolean add(TimetablePictureDTO timetablePictureDTO);


    /**
     * 修改上课拍照记录
     *
     * @param timetablePictureDTO 上课拍照记录
     * @return boolean 执行结果
     */
    boolean edit(TimetablePictureDTO timetablePictureDTO);


    /**
     * 导出excel 上课拍照记录表格
     *
     * @param timetablePictureQuery 查询条件
     * @param ids 导出指定ID
     * @return List<TimetablePictureVO> 结果集合
     */
    List<TimetablePictureVO> export(TimetablePictureQuery timetablePictureQuery, Long[] ids);


    /**
     *  拍照识别处理
     *
     * <AUTHOR>
     * @date 2025年02月14日 10时41分
     */
    void pictureRecognitionHandle();


    /**
     *  拍照识别处理
     *
     * <AUTHOR>
     * @date 2025年03月10日 17时13分
     */
    void handlerPicture(com.yuedu.ydsf.eduConnect.live.api.dto.TimetablePictureDTO eventDTO);
}
