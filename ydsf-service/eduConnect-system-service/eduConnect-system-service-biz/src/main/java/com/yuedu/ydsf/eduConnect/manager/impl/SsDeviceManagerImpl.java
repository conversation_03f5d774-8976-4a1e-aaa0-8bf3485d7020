package com.yuedu.ydsf.eduConnect.manager.impl;

import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.api.feign.RemoteClassRoomService;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.dto.ClassRoomDTO;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.SsDeviceConstant;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.dto.SsDeviceDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsCourseScheduleVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.entity.SsDeviceAudioConfig;
import com.yuedu.ydsf.eduConnect.entity.SsDeviceConfig;
import com.yuedu.ydsf.eduConnect.manager.SsDeviceManager;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsCourseScheduleMapper;
import com.yuedu.ydsf.eduConnect.service.SsDeviceAudioConfigService;
import com.yuedu.ydsf.eduConnect.service.SsDeviceConfigService;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/16
 */
@Slf4j
@Component
@AllArgsConstructor
public class SsDeviceManagerImpl implements SsDeviceManager {

    private final RemoteCampusService remoteCampusService;

    private final RemoteClassRoomService remoteClassRoomService;

    private final RedisTemplate<String, Object> redisTemplate;

    private final SsDeviceConfigService deviceConfigService;

    private final SsDeviceAudioConfigService audioConfigService;

    private final SsClassTimeAuthRoomMapper classTimeAuthRoomMapper;

    private final SsCourseScheduleMapper courseScheduleMapper;



    /**
     * 远程获取教室、校区相关信息
     *
     * @param devices
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.Long, java.lang.String>>
     * <AUTHOR>
     * @date 2024/10/15 16:23
     */
    @Override
    public Map<String, Map<Long, String>> fetchRemoteData(List<SsDevice> devices) {

        List<Long> campusIds = devices.stream().map(SsDevice::getCampusId).filter(Objects::nonNull)
            .distinct().collect(Collectors.toList());

        List<Long> classRoomIds = devices.stream().map(SsDevice::getClassRoomId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());

        CompletableFuture<Map<Long, String>> campusFuture = CompletableFuture.supplyAsync(() -> {
            try {
                CampusDTO campusDTO = new CampusDTO();
                campusDTO.setSchoolIdList(campusIds);
                return remoteCampusService.getCampusList(campusDTO).getData().stream()
                    .collect(Collectors.toMap(CampusVO::getId, CampusVO::getCampusName));
            } catch (Exception e) {
                log.error("获取校区信息失败", e);
                return Collections.emptyMap();
            }
        });

        CompletableFuture<Map<Long, String>> classRoomFuture = CompletableFuture.supplyAsync(() -> {
            try {
                ClassRoomDTO classRoomDTO = new ClassRoomDTO();
                classRoomDTO.setClassRoomIdList(classRoomIds);
                return remoteClassRoomService.getList(classRoomDTO).getData().stream()
                    .collect(Collectors.toMap(ClassRoomVO::getId, ClassRoomVO::getClassRoomName));
            } catch (Exception e) {
                log.error("获取教室信息失败", e);
                return Collections.emptyMap();
            }
        });

        try {
            Map<String, Map<Long, String>> result = new HashMap<>();
            result.put("campus", campusFuture.get());
            result.put("classRoom", classRoomFuture.get());
            return result;
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取远程数据失败", e);
            Thread.currentThread().interrupt();
            return Collections.emptyMap();
        }
    }


    /**
     * 实体类转换VO类
     *
     * @param record
     * @return com.yuedu.ydsf.eduConncet.api.vo.SsDeviceVO
     * <AUTHOR>
     * @date 2024/9/27 15:59
     */
    @Override
    public SsDeviceVO convertToDeviceVO(SsDevice record, Map<Long, String> campusMap,
        Map<Long, String> classRoomMap) {
        SsDeviceVO deviceVO = new SsDeviceVO();
        BeanUtils.copyProperties(record, deviceVO);

        // 处理特定的枚举转换
        if (record.getDeviceType() != null) {
            deviceVO.setDeviceType(String.valueOf(record.getDeviceType()));
        }
        if (record.getDeviceState() != null) {
            deviceVO.setDeviceState(String.valueOf(record.getDeviceState()));
        }
        if (record.getDeviceActive() != null) {
            deviceVO.setDeviceActive(String.valueOf(record.getDeviceActive()));
        }
        if (record.getDeviceArrears() != null) {
            deviceVO.setDeviceArrears(String.valueOf(record.getDeviceArrears()));
        }
        if (record.getIsOnLine() != null) {
            deviceVO.setIsOnLine(String.valueOf(record.getIsOnLine()));
        }
        if (record.getIndateForever() != null) {
            deviceVO.setIndateForever(String.valueOf(record.getIndateForever()));
        }
        if (record.getAgoraRecordingType() != null) {
            deviceVO.setAgoraRecordingType(String.valueOf(record.getAgoraRecordingType()));
        }
        // 设置校区和教室信息
        if (record.getCampusId() != null) {
            deviceVO.setCampusName(campusMap.get(record.getCampusId()));
        }
        if (record.getClassRoomId() != null) {
            deviceVO.setClassRoomName(classRoomMap.get(record.getClassRoomId()));
        }
        return deviceVO;
    }


    /**
     * 删除设备缓存
     *
     * @param deviceDto
     * @return void
     * <AUTHOR>
     * @date 2024/9/28 14:42
     */
    @Override
    public void removeDeviceCache(SsDeviceDTO deviceDto) {
        String key = String.format(SsDeviceConstant.SS_DEVICE_INFO_NEW_KEY, deviceDto.getDeviceNo());
        redisTemplate.delete(key);
        log.info("删除新系统缓存设备key, {}", key);
        String oldKey = String.format(SsDeviceConstant.SS_DEVICE_INFO_OLD_KEY, deviceDto.getDeviceNo());
        redisTemplate.delete(oldKey);
        log.info("删除旧系统缓存设备key, {}", oldKey);
    }

    /**
     * 缓存设备信息
     *
     * @param deviceVO
     * @return void
     * <AUTHOR>
     * @date 2024/9/28 14:42
     */
    @Override
    public void cacheDeviceInfo(SsDeviceVO deviceVO) {
        // 缓存设备的redis 的有效时间
        long tokenValid = 259200L;
        // 将登陆的会员的信息缓存
        redisTemplate.opsForValue()
            .set(String.format(SsDeviceConstant.SS_DEVICE_INFO_NEW_KEY, deviceVO.getDeviceNo()), deviceVO,
                tokenValid, TimeUnit.SECONDS);
    }

    /**
     * 获取设备配置信息
     *
     * @param configId
     * @return com.yuedu.ydsf.eduConnect.entity.SsDeviceConfig
     * <AUTHOR>
     * @date 2024/10/16 10:20
     */
    @Override
    public SsDeviceConfig getDeviceConfigById(Long configId) {
        return deviceConfigService.getById(configId);
    }

    /**
     * 获取设备音频配置信息
     *
     * @param audioConfigId
     * @return com.yuedu.ydsf.eduConnect.entity.SsDeviceAudioConfig
     * <AUTHOR>
     * @date 2024/10/16 10:20
     */
    @Override
    public SsDeviceAudioConfig getAudioConfigById(Long audioConfigId) {
        return audioConfigService.getById(audioConfigId);
    }

  /**
   * 删除设备之前的校验
   *
   * <AUTHOR>
   * @date 2024/10/28 9:03
   * @param delDto
   * @return void
   */
  @Override
  public void handleDeviceStatus(SsDeviceDTO delDto, SsDevice ssDevice) {

    SsClassTimeDTO ssClassTimeDTO = new SsClassTimeDTO();
    ssClassTimeDTO.setAttendTimeEndTime(LocalDateTime.now());
    ssClassTimeDTO.setDeviceId(delDto.getId());
    // 学生端的设备查看今后有无排课信息
    if (Objects.nonNull(ssDevice.getDeviceType())
        && Objects.equals(DeviceTypeEnum.DEVICETYPE_2.code, ssDevice.getDeviceType().intValue())) {
      List<SsClassTimeVO> classTimeList =
          classTimeAuthRoomMapper.getClassTimeListByDeviceId(ssClassTimeDTO);
      if (CollectionUtils.isNotEmpty(classTimeList)) {
        throw new BizException(String.format("%s设备号已存在课次信息，操作失败！", delDto.getDeviceNo()));
      }
    }
    // 查看教师端今后有无排课信息
    else {
      List<SsCourseScheduleVO> courseList =
          courseScheduleMapper.getCourseListByDeviceId(ssClassTimeDTO);
      if (CollectionUtils.isNotEmpty(courseList)) {
        throw new BizException(String.format("%s设备号已存在课次信息，操作失败！", delDto.getDeviceNo()));
      }
    }
  }
}
