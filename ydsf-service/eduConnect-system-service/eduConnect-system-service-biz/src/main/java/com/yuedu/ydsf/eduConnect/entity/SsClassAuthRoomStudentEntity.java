package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 班级授权校区学生表
 *
 * <AUTHOR>
 * @date 2024-10-09 09:54:03
 */
@Data
@TableName("ss_class_auth_room_student")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "班级授权校区学生表")
public class SsClassAuthRoomStudentEntity extends Model<SsClassAuthRoomStudentEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 校区ID
	*/
    @Schema(description="校区ID")
    private Long campusId;

	/**
	* 校管家学生ID
	*/
    @Schema(description="校管家学生ID")
    private String studentId;

	/**
	* 校管家学生手机号
	*/
    @Schema(description="校管家学生手机号")
    private String studentMobile;

	/**
	* 校管家学生名称
	*/
    @Schema(description="校管家学生名称")
    private String studentName;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
    @Schema(description="编辑者")
    private String modifer;

	/**
	* 删除标识
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识")
    private Integer delFlag;
}