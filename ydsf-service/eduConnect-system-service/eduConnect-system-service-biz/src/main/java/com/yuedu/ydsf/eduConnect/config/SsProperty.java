package com.yuedu.ydsf.eduConnect.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 双师配置类
 *
 * @date 2024/10/21 13:52
 * @project @Title: SsProperty.java
 */
@Configuration
@ConfigurationProperties(prefix = "ss.config")
@Data
public class SsProperty {

  /** 监课链接前缀 */
  private String observationUrl;

    /**
     * 教务创建直播间计划、教学计划是否校验排课日期
     */
    private Boolean jwCheckPlanDateEnable;

}
