package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.query.CourseLiveQuery;
import com.yuedu.ydsf.eduConnect.api.vo.CourseLiveVO;
import com.yuedu.ydsf.eduConnect.entity.CourseLive;
import java.util.List;

/**
 * 门店已约直播课 服务类
 *
 * <AUTHOR>
 * @date 2024-12-26 14:41:20
 */
public interface CourseLiveService extends IService<CourseLive> {

    /**
     * 查询教学计划是否已有约课
     * @param teachingPlanIdList 教学计划ID
     * @return long
     */
    long countByTeachingPlanId(List<Long> teachingPlanIdList);



    /**
     *  双师后台直播约课查询接口
     *
     * <AUTHOR>
     * @date 2025年02月25日 09时00分
     */
    IPage<CourseLiveVO> livePage(Page page, CourseLiveQuery query);
}
