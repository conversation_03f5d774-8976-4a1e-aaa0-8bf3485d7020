package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 排课书籍表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:30:10
 */
@Data
@TableName("ss_course_schedule_books")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "排课书籍表实体类")
public class SsCourseScheduleBooks extends Model<SsCourseScheduleBooks> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 排课ID
	*/
    @Schema(description="排课ID")
    private Long courseScheduleId;

	/**
	* 第几次上课书籍
	*/
    @Schema(description="第几次上课书籍")
    private Integer howManyTimes;

	/**
	* 书籍ID
	*/
    @Schema(description="书籍ID")
    private String booksId;

	/**
	* 书籍名称
	*/
    @Schema(description="书籍名称")
    private String booksName;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑者")
    private String modifer;

	/**
	* 删除标识
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识")
    private Integer delFlag;
}
