package com.yuedu.ydsf.eduConnect.util.interiorsXiaogj;

/**
 * 对接内部校管家API接口
 * <AUTHOR>
 * @date 2024年01月19日 09时52分
 */
public class InteriorsXiaogjApiUrlConstant {

    /**
     * 获取校区信息
     */
    public static final String GET_CAMPUS = "/xgj-web/ss/getDepartAll";

    /**
     * 获取教室信息
     */
    public static final String GET_CLASS_ROOM = "/xgj-web/ss/getClassRoomList";

    /**
     * 获取主讲老师信息
     */
    public static final String GET_LECTURER = "/xgj-web/ss/getEmployeeGrade";

    /**
     * 000000总部校区ID
     */
    public static final String HEAD_CAMPUS_ID = "ADB741F2-5CCF-4DE3-8A77-B25B088B0C2E";

}
