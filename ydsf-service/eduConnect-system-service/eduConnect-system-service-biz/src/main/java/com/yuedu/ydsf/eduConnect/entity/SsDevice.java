package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备表
 *
 * <AUTHOR>
 * @date 2024/09/26
 */
@TableName("ss_device")
@Data
@EqualsAndHashCode(callSuper = true)
public class SsDevice extends Model<SsDevice> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 校区ID
     */
    private Long campusId;

    /**
     * 教室ID
     */
    private Long classRoomId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备号
     */
    private String deviceNo;

    /**
     * 设备类型:1-主讲端;2-教室端
     *
     * @type ss_device_device_type
     */
    private Byte deviceType;

    /**
     * 设备状态:0-启用;1-禁用
     *
     * @type ss_device_device_state
     */
    private Byte deviceState;

    /**
     * 设备是否激活:0-未激活;1-已激活
     *
     * @type ss_device_device_active
     */
    private Byte deviceActive;

    /**
     * 设备欠费状态:0-正常;1-其他状态为欠费
     *
     * @type ss_device_device_arrears
     */
    private Byte deviceArrears;

    /**
     * 是否在线:0-否;1-是
     *
     * @type ss_device_is_on_line
     */
    private Byte isOnLine;

    /**
     * 设备是否永久:0-否;1-是
     *
     * @type ss_device_indate_forever
     */
    private Byte indateForever;

    /**
     * 有效期开始时间
     */
    private LocalDateTime indateStart;

    /**
     * 有效期结束时间
     */
    private LocalDateTime indateEnd;

    /**
     * 设备配置表ID
     */
    private Long configId;

    /**
     * 音频配置ID
     */
    private Long audioConfigId;

    /**
     * 主讲端录课方式:0-页面录制;1-云端录制
     *
     * @type ss_device_agora_recording_type
     */
    private Byte agoraRecordingType;

    /**
     * 直播背景图路径
     */
    private String liveBackground;

    /**
     * 终端SDK版本:0-webSDK;1-Electron
     *
     * @type ss_device_sdk_type
     */
    private Byte sdkType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 编辑时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifer;

    /**
     * 设备是否是由UUID生成不为空代表注册设备码是由uuid生成
     */
    private String deviceUuid;

    /**
     * 是否删除:0-未删除;1-已删除
     *
     * @type ss_device_del_flag
     */
    //@TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
}
