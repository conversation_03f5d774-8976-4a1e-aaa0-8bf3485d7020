package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.api.feign.RemoteClassRoomService;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.dto.ClassRoomDTO;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.common.core.util.JSONUtils;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.SpringContextHolder;
import com.yuedu.ydsf.common.operatelog.annotation.OperateLog;
import com.yuedu.ydsf.eduConnect.api.constant.ActiveEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceBindStateEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceStateEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IndateForeverEnum;
import com.yuedu.ydsf.eduConnect.api.constant.OperateCategoryEnum;
import com.yuedu.ydsf.eduConnect.api.constant.OperateLogStrTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.OperateTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.SsDeviceConstant;
import com.yuedu.ydsf.eduConnect.api.dto.SsDeviceDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsDeviceQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceAudioSettingVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceConfigVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceSettingVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsReadDeviceVo;
import com.yuedu.ydsf.eduConnect.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.entity.SsDeviceAudioConfig;
import com.yuedu.ydsf.eduConnect.entity.SsDeviceConfig;
import com.yuedu.ydsf.eduConnect.manager.SsDeviceManager;
import com.yuedu.ydsf.eduConnect.mapper.SsDeviceMapper;
import com.yuedu.ydsf.eduConnect.service.SsDeviceAudioConfigService;
import com.yuedu.ydsf.eduConnect.service.SsDeviceConfigService;
import com.yuedu.ydsf.eduConnect.service.SsDeviceService;
import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.AgoraProperties;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import com.yuedu.ydsf.eduConnect.util.DateUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 设备表服务层
 *
 * <AUTHOR>
 * @date 2024/09/26
 */
@Slf4j
@Service
@AllArgsConstructor
public class SsDeviceServiceImpl extends ServiceImpl<SsDeviceMapper, SsDevice> implements
    SsDeviceService {

    private final SsDeviceManager deviceManager;

    private final RemoteCampusService remoteCampusService;

    private final RemoteClassRoomService remoteClassRoomService;

    private final AgoraProperties agoraProperties;

    private final SsDeviceConfigService deviceConfigService;

    private final SsDeviceAudioConfigService audioConfigService;

  /**
   * 设备表分页查询
   *
   * @param page 分页对象
   * @param ssDeviceQuery 设备表
   * @return IPage 分页结果
   */
  public IPage<SsDeviceVO> page(Page page, SsDeviceQuery ssDeviceQuery) {
    Page<SsDevice> selectPage =
        baseMapper.selectPage(
            page,
            Wrappers.<SsDevice>lambdaQuery()
                .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                .like(
                    StringUtils.isNotEmpty(ssDeviceQuery.getDeviceNo()),
                    SsDevice::getDeviceNo,
                    ssDeviceQuery.getDeviceNo())
                .like(
                    StringUtils.isNotEmpty(ssDeviceQuery.getDeviceName()),
                    SsDevice::getDeviceName,
                    ssDeviceQuery.getDeviceName())
                .eq(
                    Objects.nonNull(ssDeviceQuery.getCampusId()),
                    SsDevice::getCampusId,
                    ssDeviceQuery.getCampusId())
                .eq(
                    Objects.nonNull(ssDeviceQuery.getDeviceType()),
                    SsDevice::getDeviceType,
                    ssDeviceQuery.getDeviceType())
                .eq(
                    Objects.nonNull(ssDeviceQuery.getDeviceArrears()),
                    SsDevice::getDeviceArrears,
                    ssDeviceQuery.getDeviceArrears())
                .eq(
                    Objects.nonNull(ssDeviceQuery.getDeviceState()),
                    SsDevice::getDeviceState,
                    ssDeviceQuery.getDeviceState())
                .orderByDesc(SsDevice::getId));

    List<SsDevice> devices = selectPage.getRecords();
    Map<String, Map<Long, String>> remoteData = deviceManager.fetchRemoteData(devices);

    return selectPage.convert(
        device ->
            deviceManager.convertToDeviceVO(
                device, remoteData.get("campus"), remoteData.get("classRoom")));
  }

  /**
   * 新增设备表
   *
   * @param ssDeviceDTO 设备表
   * @return boolean 执行结果
   */
  public boolean add(SsDeviceDTO ssDeviceDTO) {
    if (this.exists(
        Wrappers.lambdaQuery(SsDevice.class)
            .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code)
            .eq(SsDevice::getDeviceNo, ssDeviceDTO.getDeviceNo()))) {
      throw new CheckedException("注册失败，设备码重复！");
    }
    SsDevice ssDevice = new SsDevice();
    BeanUtils.copyProperties(ssDeviceDTO, ssDevice);
    return save(ssDevice);
  }

    /**
     * 修改设备表
     *
     * @param ssDeviceDTO 设备表
     * @return boolean 执行结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean edit(SsDeviceDTO ssDeviceDTO) {
        SsDevice device = beforeUpdate(ssDeviceDTO);
        SsDevice ssDevice = new SsDevice();
        BeanUtils.copyProperties(ssDeviceDTO, ssDevice);
        boolean updated = this.updateById(ssDevice);
        if (Objects.equals(ssDeviceDTO.getIndateForever().intValue(),
            IndateForeverEnum.INDATEFOREVER_1.code)) {
            this.update(Wrappers.lambdaUpdate(SsDevice.class)
                .eq(SsDevice::getId, ssDeviceDTO.getId())
                .set(SsDevice::getIndateStart, null)
                .set(SsDevice::getIndateEnd, null));
        }
        deviceManager.removeDeviceCache(ssDeviceDTO);
        // 修改操作记录
        SpringContextHolder.getBean(SsDeviceServiceImpl.class).editOperateLogAspect(device,ssDeviceDTO);
        return updated;
    }

    /**
     * 更新之前的校验操作
     *
     * @param ssDeviceDTO
     * @return void
     * <AUTHOR>
     * @date 2024/9/29 14:00
     */
    private SsDevice beforeUpdate(SsDeviceDTO ssDeviceDTO) {
        long deviceCount = this.count(Wrappers.lambdaQuery(SsDevice.class)
            .eq(SsDevice::getDeviceNo, ssDeviceDTO.getDeviceNo())
            .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code)
            .ne(SsDevice::getId, ssDeviceDTO.getId()));
        if (deviceCount > 0) {
            throw new CheckedException("设备号已存在请重新输入！");
        }
        // 修改了关键的字段如校区、教室这个字段查看有无排课信息有则不允许修改
        SsDevice ssDevice = getById(ssDeviceDTO.getId());
        if (Objects.nonNull(ssDevice)) {
            // 校验是不是修改了设备类型
            boolean typeChanged = !Objects.equals(ssDevice.getDeviceType(), ssDeviceDTO.getDeviceType());
            // 校验校区或教室是否发生变化
            boolean campusChanged = !Objects.equals(ssDevice.getCampusId(), ssDeviceDTO.getCampusId());
            boolean classRoomChanged = !Objects.equals(ssDevice.getClassRoomId(), ssDeviceDTO.getClassRoomId());
            if (campusChanged || classRoomChanged || typeChanged) {
                //deviceManager.handleDeviceStatus(ssDeviceDTO,ssDevice);
            }
        }
        if (Objects.equals(DeviceTypeEnum.DEVICETYPE_1.code, ssDeviceDTO.getDeviceType().intValue())) {
            handleMainLecturerDevice(ssDeviceDTO);
        }

        if (Objects.equals(DeviceTypeEnum.DEVICETYPE_2.code, ssDeviceDTO.getDeviceType().intValue())) {
            handleClassroomDevice(ssDeviceDTO);
        }
        return ssDevice;
    }

  /**
   * 处理主讲端
   *
   * @param ssDeviceDTO
   * @return void
   * <AUTHOR>
   * @date 2024/9/29 10:49
   */
  private void handleMainLecturerDevice(SsDeviceDTO ssDeviceDTO) {
    // 主讲端存储的教室为系统管理的live_room字典值
    ssDeviceDTO.setClassRoomId(ssDeviceDTO.getClassRoomId());
    // 主讲端默认放到总部校区下
    ssDeviceDTO.setCampusId(1L);
    // 检验一直直播间只允许绑定一个设备
    long liveRoomCount =
        this.count(
            Wrappers.lambdaQuery(SsDevice.class)
                .eq(SsDevice::getClassRoomId, ssDeviceDTO.getClassRoomId())
                .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                .ne(SsDevice::getId, ssDeviceDTO.getId()));
    if (liveRoomCount > 0) {
      throw new BizException("选择的直播间已绑定其他设备！");
    }
  }

  /**
   * 处理教室端设备
   *
   * @param ssDeviceDTO
   * @return void
   * <AUTHOR>
   * @date 2024/9/29 10:48
   */
  private void handleClassroomDevice(SsDeviceDTO ssDeviceDTO) {
    if (Objects.isNull(ssDeviceDTO.getCampusId())
        || Objects.isNull(ssDeviceDTO.getClassRoomId())) {
        throw new BizException("校区或教室不允许为空！");
    }

    long classroomCount =
        this.count(
            Wrappers.lambdaQuery(SsDevice.class)
                .eq(SsDevice::getClassRoomId, ssDeviceDTO.getClassRoomId())
                .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                .ne(SsDevice::getId, ssDeviceDTO.getId()));
        if (classroomCount > 0) {
            throw new CheckedException("当前教室下已存在绑定设备！");
        }
    }

    /**
     * 导出excel 设备表表格
     *
     * @param ssDeviceQuery 查询条件
     * @param ids           导出指定ID
     * @return List<SsDeviceVO> 结果集合
     */
    public List<SsDeviceVO> export(SsDeviceQuery ssDeviceQuery,
        Long[] ids) {
        return list(Wrappers.<SsDevice>lambdaQuery()
            .in(ArrayUtil.isNotEmpty(ids), SsDevice::getId, ids)).stream()
            .map(entity -> {
                SsDeviceVO ssDeviceVO = new SsDeviceVO();
                BeanUtils.copyProperties(entity, ssDeviceVO);
                return ssDeviceVO;
            })
            .toList();
    }

    /**
     * 通过id查询设备表
     *
     * @param id
     * @return com.yuedu.ydsf.eduConncet.api.vo.SsDeviceVO
     * <AUTHOR>
     * @date 2024/9/28 9:13
     */
    @Override
    public SsDeviceVO getInfoById(Serializable id) {
        SsDevice device = getById(id);
        if (Objects.isNull(device)) {
            throw new CheckedException("设备信息异常！");
        }
        Map<String, Map<Long, String>> remoteData = deviceManager.fetchRemoteData(Collections.singletonList(device));
        return deviceManager.convertToDeviceVO(device, remoteData.get("campus"), remoteData.get("classRoom"));
    }

  /**
   * 解绑设备
   *
   * @param ids
   * @return void
   * <AUTHOR>
   * @date 2024/9/28 9:25
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void unbind(Long[] ids) {
    List<SsDevice> devices = listByIds(Arrays.asList(ids));
    devices.forEach(
        device -> {
          SsDeviceDTO delDto = new SsDeviceDTO();
          BeanUtils.copyProperties(device, delDto);
          //deviceManager.handleDeviceStatus(delDto,device); 2025年3月21日11:50:30去除校验
          update(
              Wrappers.lambdaUpdate(SsDevice.class)
                  .eq(SsDevice::getId, device.getId())
                  .set(SsDevice::getClassRoomId, SsDeviceConstant.NOT_BIND));
          deviceManager.removeDeviceCache(delDto);
          // 修改操作记录
          SpringContextHolder.getBean(SsDeviceServiceImpl.class).editOperateLogAspect(device,delDto);
        });
  }

    /**
     * 启用/禁用设备状态
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/9/28 9:46
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeDeviceState(Long[] ids) {
        Arrays.stream(ids)
            .forEach(id -> {
                SsDevice device = this.getById(id);
                if (device == null) {
                    throw new CheckedException("设备不存在，ID: " + id);
                }
                Integer newState = (Objects.equals(device.getDeviceState().intValue(),
                    DeviceStateEnum.DEVICESTATE_0.code)) ?
                    DeviceStateEnum.DEVICESTATE_1.code :
                    DeviceStateEnum.DEVICESTATE_0.code;
                this.update(Wrappers.lambdaUpdate(SsDevice.class)
                    .eq(SsDevice::getId, id)
                    .set(SsDevice::getDeviceState, newState));
                SsDeviceDTO delDto = new SsDeviceDTO();
                BeanUtils.copyProperties(device, delDto);
                deviceManager.removeDeviceCache(delDto);
                // 修改操作记录
                SpringContextHolder.getBean(SsDeviceServiceImpl.class).editOperateLogAspect(device,delDto);
              });
    }

    /**
     * 通过id删除设备表
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/9/28 10:05
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDevice(Long[] ids) {
        List<SsDevice> devices = this.listByIds(Arrays.asList(ids));
        devices.forEach(device -> {
            SsDeviceDTO delDto = new SsDeviceDTO();
            BeanUtils.copyProperties(device, delDto);
            // 删除前校验当前时间之后是否存在未上完的课 2025年3月21日11:50:30去除校验
            //deviceManager.handleDeviceStatus(delDto,device);
            this.removeById(device.getId());
            deviceManager.removeDeviceCache(delDto);
            // 保存操作日志
            SpringContextHolder.getBean(SsDeviceServiceImpl.class).deleteOperateLogAspect(device);
        });
    }

    @Override
    public SsDevice getDeviceByClassRoomId(Long classRoomId) throws BizException {
        SsDevice ssDevice = getOne(Wrappers.lambdaQuery(SsDevice.class)
            .eq(SsDevice::getClassRoomId, classRoomId)
            .eq(SsDevice::getDeviceState, DeviceStateEnum.DEVICESTATE_0.code)
            .eq(SsDevice::getDeviceActive, ActiveEnum.DEVICEACTIVE_1.code)
            .last("LIMIT 1"));
        if (Objects.isNull(ssDevice)) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "教室设备不存在");
        }
        return ssDevice;
    }


    /**
     * 查询所有教室端有效设备
     * @param ssDeviceQuery
     * @return java.util.List<com.yuedu.ydsf.eduConncet.api.vo.SsDeviceVO>
     * <AUTHOR>
     * @date 2024/10/9 16:25
     */
    @Override
    public List<SsDeviceVO> getClassRoomDeviceList(SsDeviceQuery ssDeviceQuery) {

        // 查询所有校区
        R<List<CampusVO>> campusList = remoteCampusService.getCampusList(new CampusDTO());
        List<CampusVO> campusVOList = new ArrayList<>();
        if (campusList.isOk() && CollectionUtils.isNotEmpty(campusList.getData())) {
            campusVOList.addAll(campusList.getData().stream().filter(e ->
                !e.getCampusNo().startsWith("N")).toList());
        }

        // 查询所有教室
        R<List<ClassRoomVO>> classRoomList = remoteClassRoomService.getList(new ClassRoomDTO());
        List<ClassRoomVO> classRoomVOList = new ArrayList<>();
        if (classRoomList.isOk() && CollectionUtils.isNotEmpty(classRoomList.getData())) {
            classRoomVOList.addAll(classRoomList.getData().stream().filter(e ->
                StringUtils.isNotEmpty(e.getXgjClassRoomId())).toList());
        }

        // 模糊搜索校区名/教室名
        List<Long> campusIdList = new ArrayList<>();
        List<Long> classRoomIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(ssDeviceQuery.getSearchContent())) {
            campusIdList = campusVOList.stream()
                .filter(e -> e.getCampusName().contains(ssDeviceQuery.getSearchContent()))
                .map(CampusVO::getId)
                .collect(Collectors.toList());

            classRoomIdList = classRoomVOList.stream()
                .filter(e -> e.getClassRoomName().contains(ssDeviceQuery.getSearchContent()))
                .map(ClassRoomVO::getId)
                .collect(Collectors.toList());


        }

        // 查询设备信息
        List<Long> finalCampusIdList = campusIdList;
        List<Long> finalClassRoomIdList = classRoomIdList;
         List<SsDevice> ssDeviceList = list(Wrappers.lambdaQuery(SsDevice.class)
            .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code)
            .eq(SsDevice::getDeviceType, DeviceTypeEnum.DEVICETYPE_2.code)
            .ne(SsDevice::getClassRoomId, DeviceBindStateEnum.DEVICE_BIND_STATE_ENUM.code)
            .and(StringUtils.isNotBlank(ssDeviceQuery.getSearchContent()),
                wrapper -> wrapper.like(SsDevice::getDeviceNo, ssDeviceQuery.getSearchContent())
                    .or()
                    .like(SsDevice::getDeviceName, ssDeviceQuery.getSearchContent())
                    .or()
                    .in(CollectionUtils.isNotEmpty(finalCampusIdList), SsDevice::getCampusId, finalCampusIdList)
                    .or()
                    .in(CollectionUtils.isNotEmpty(finalClassRoomIdList), SsDevice::getClassRoomId, finalClassRoomIdList)
            )
            .orderByDesc(SsDevice::getId)
        );

        List<SsDeviceVO> ssDeviceVOList = new ArrayList<>();
        for (SsDevice ssDevice : ssDeviceList) {

            CampusVO campusVO = campusVOList.stream()
                .filter(e -> e.getId().equals(ssDevice.getCampusId()))
                .findFirst()
                .orElse(new CampusVO());

            ClassRoomVO classRoomVO = classRoomVOList.stream()
                .filter(e -> e.getId().equals(ssDevice.getClassRoomId()))
                .findFirst()
                .orElse(new ClassRoomVO());
            if (StringUtils.isEmpty(campusVO.getXgjCampusId()) ||
                StringUtils.isEmpty(classRoomVO.getXgjClassRoomId())){
                continue; // 跳过没有对应校管家教室ID的教室
            }

            SsDeviceVO ssDeviceVO = new SsDeviceVO();
            BeanUtils.copyProperties(ssDevice, ssDeviceVO);
            ssDeviceVO.setCampusName(campusVO.getCampusName());
            ssDeviceVO.setXgjCampusId(campusVO.getXgjCampusId());
            ssDeviceVO.setClassRoomName(classRoomVO.getClassRoomName());
            ssDeviceVO.setXgjClassRoomId(classRoomVO.getXgjClassRoomId());

            ssDeviceVOList.add(ssDeviceVO);
        }

        return ssDeviceVOList;
    }

    /**
   * 设备管理修改日志保存
   *
   * <AUTHOR>
   * @date 2024/11/20 15:19
   * @param ssDevice
   * @param ssDeviceDTO
   * @return void
   */
  @OperateLog(
      name = "设备管理",
      operateCategory = OperateCategoryEnum.CATEGORY_ENUM_4,
      operateType = OperateTypeEnum.OPERATE_TYPE_ENUM_2,
      operateLogStrType = OperateLogStrTypeEnum.OPERATE_LOG_STR_TYPE_ENUM_2,
      oldVal = "#ssDevice",
      spel = "@ssDeviceMapper.selectById(#ssDevice.id)",
      objectId = "#ssDevice.id")
  public void editOperateLogAspect(SsDevice ssDevice, SsDeviceDTO ssDeviceDTO) {}

  /**
   * 设备管理删除日志保存
   *
   * <AUTHOR>
   * @date 2024/11/20 15:20
   * @param ssDevice
   * @return void
   */
  @OperateLog(
      name = "设备管理",
      operateCategory = OperateCategoryEnum.CATEGORY_ENUM_4,
      operateType = OperateTypeEnum.OPERATE_TYPE_ENUM_3,
      oldVal = "未删除",
      newVal = "已删除",
      objectId = "#ssDevice.id")
  public void deleteOperateLogAspect(SsDevice ssDevice) {}

    /**
     * 获取设备以及音频配置信息
     *
     * @param device
     * @return com.yuedu.ydsf.eduConncet.api.vo.SsDeviceConfigVO
     * <AUTHOR>
     * @date 2024/9/28 15:36
     */
    private SsDeviceConfigVO getDeviceConfig(SsDevice device) {
        SsDeviceConfigVO configVO = new SsDeviceConfigVO();
        configVO.setAppId(agoraProperties.getAgoraAppId());

        if (device.getConfigId() != null) {
            SsDeviceConfig deviceConfig = getDeviceConfigById(device.getConfigId());
            if (deviceConfig != null) {
                configVO.setDeviceConfig(fromSsDeviceConfig(device.getDeviceType(), deviceConfig));
            }
        }

        if (device.getAudioConfigId() != null) {
            SsDeviceAudioConfig audioConfig = getAudioConfigById(device.getAudioConfigId());
            if (audioConfig != null) {
                SsDeviceAudioSettingVO audioConfigVO = new SsDeviceAudioSettingVO();
                String parameters = audioConfig.getParameters();
                List<String> jsonObjects = JSONUtils.splitJsonObjects(parameters);
                audioConfigVO.setParametersArr(jsonObjects);
                audioConfigVO.setAdjustRecordingSignalVolume(audioConfig.getAdjustRecordingSignalVolume());
                configVO.setAudioConfig(audioConfigVO);
            }
        }
        return configVO;
    }

    /**
     * 获取设备配置
     *
     * <AUTHOR>
     * @date 2024/11/4 9:14
     * @param configId
     * @return com.yuedu.ydsf.eduConnect.live.entity.SsDeviceConfig
     */
    private SsDeviceConfig getDeviceConfigById(Long configId) {
        return deviceConfigService.getById(configId);
    }

    /**
     * 从SsDeviceConfig转换到设备需要的
     *
     * @param deviceType
     * @param deviceConfig
     * @return com.yuedushufang.ss.api.domain.vo.SsDeviceSettingVo
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2024/5/24 15:48
     */
    public static SsDeviceSettingVO fromSsDeviceConfig(Byte deviceType, SsDeviceConfig deviceConfig) {
        SsDeviceSettingVO deviceSettingVo = new SsDeviceSettingVO();
        if (Objects.nonNull(deviceType)) {
            if (Objects.equals(deviceType.intValue(), DeviceTypeEnum.DEVICETYPE_1.code)) {
                deviceSettingVo.setBw(deviceConfig.getTBw());
                deviceSettingVo.setBh(deviceConfig.getTBh());
                deviceSettingVo.setBb(deviceConfig.getTBb());
                deviceSettingVo.setBf(deviceConfig.getTBf());
                deviceSettingVo.setSw(deviceConfig.getTSw());
                deviceSettingVo.setSh(deviceConfig.getTSh());
                deviceSettingVo.setSb(deviceConfig.getTSb());
                deviceSettingVo.setSf(deviceConfig.getTSf());
                deviceSettingVo.setHd(deviceConfig.getTHd());
            } else if (Objects.equals(deviceType.intValue(), DeviceTypeEnum.DEVICETYPE_2.code)) {
                deviceSettingVo.setBw(deviceConfig.getSBw());
                deviceSettingVo.setBh(deviceConfig.getSBh());
                deviceSettingVo.setBb(deviceConfig.getSBb());
                deviceSettingVo.setBf(deviceConfig.getSBf());
                deviceSettingVo.setSw(deviceConfig.getSSw());
                deviceSettingVo.setSh(deviceConfig.getSSh());
                deviceSettingVo.setSb(deviceConfig.getSSb());
                deviceSettingVo.setSf(deviceConfig.getSSf());
                deviceSettingVo.setHd(deviceConfig.getSHd());
            }
        }
        deviceSettingVo.setLogEnable(deviceConfig.getLogEnable());
        deviceSettingVo.setSShowNumber(deviceConfig.getSShowNumber());
        return deviceSettingVo;
    }

    /**
     * 获取音频配置
     *
     * <AUTHOR>
     * @date 2024/11/4 9:15
     * @param audioConfigId
     * @return com.yuedu.ydsf.eduConnect.live.entity.SsDeviceAudioConfig
     */
    private SsDeviceAudioConfig getAudioConfigById(Long audioConfigId) {
        return audioConfigService.getById(audioConfigId);
    }

  /**
   * 获取设备列表
   *
   * <AUTHOR>
   * @date 2025/2/10 14:35
   * @param page
   * @param pageSize
   * @return com.yuedu.ydsf.eduConnect.api.vo.SsReadDeviceVo
   */
  @Override
  public SsReadDeviceVo pageRead(Integer page, Integer pageSize) {
    log.info("结算系统开始查询设备列表数据, page:{}, pageSize:{}", page, pageSize);
    SsReadDeviceVo ssReadDeviceVo = new SsReadDeviceVo();
    try {
      Page<SsDevice> pageObj = new Page<>();
      pageObj.setCurrent(page);
      pageObj.setSize(pageSize);
      Page<SsDevice> ssDevicePage =
          baseMapper.selectPage(
              pageObj,
              Wrappers.<SsDevice>lambdaQuery()
                  .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                  .eq(SsDevice::getDeviceState, DeviceStateEnum.DEVICESTATE_0.code)
                  .eq(SsDevice::getDeviceType, DeviceTypeEnum.DEVICETYPE_2.code)
                  .orderByDesc(SsDevice::getId));
      // 设置返回数据
      ssReadDeviceVo.setTotal(ssDevicePage.getTotal());
      ssReadDeviceVo.setList(
          ssDevicePage.getRecords().stream()
              .map(
                  device -> {
                    SsReadDeviceVo.DeviceInfo deviceInfo = new SsReadDeviceVo.DeviceInfo();
                    deviceInfo.setDevice_id(device.getId());
                    deviceInfo.setDevice_unique(device.getDeviceNo());
                    deviceInfo.setClass_name(device.getDeviceName());
                    // 如果设备永久有效或未设置过期时间,默认设置100年
                    // 设置过期时间逻辑
                    deviceInfo.setExpire_date(
                        // 判断是否永久有效
                        Objects.equals(
                                device.getIndateForever(), IndateForeverEnum.INDATEFOREVER_1.code)
                            ?
                            // 如果是永久有效,则设置为当前时间+100年
                            DateUtils.toDate(
                                Objects.nonNull(device.getCtime())
                                    ? device.getCtime().plusYears(100)
                                    : LocalDateTime.now().plusYears(100))
                            :
                            // 如果不是永久有效,则判断是否设置了过期时间
                            Objects.nonNull(device.getIndateEnd())
                                ?
                                // 如果设置了过期时间,则使用过期时间
                                DateUtils.toDate(device.getIndateEnd())
                                :
                                // 如果未设置过期时间,默认为当前时间+100年
                                DateUtils.toDate(
                                    Objects.nonNull(device.getCtime())
                                        ? device.getCtime().plusYears(100)
                                        : LocalDateTime.now().plusYears(100)));
                    return deviceInfo;
                  })
              .collect(Collectors.toList()));

      log.info("结算系统设备列表查询完成,当前页数据量:{}", ssDevicePage.getPages());

    } catch (Exception e) {
      log.error("结算系统查询设备列表异常", e);
      throw new BizException("结算系统查询设备列表失败");
    }
    return ssReadDeviceVo;
  }

  /**
   * 结算更新设备状态
   *
   * <AUTHOR>
   * @date 2025/2/10 15:11
   * @param deviceDto
   * @return void
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void settlementUpdateDeviceInfo(SsDeviceDTO deviceDto) {
    log.info("开始更新设备状态信息, deviceDto: {}", deviceDto);

    try {
      // 1. 参数校验
      if (Objects.isNull(deviceDto) || Objects.isNull(deviceDto.getId())) {
        log.error("更新设备状态参数异常, deviceDto为空或id为空");
        throw new BizException("参数异常:设备ID不能为空");
      }

      // 2. 查询设备信息
      SsDevice device = this.getById(deviceDto.getId());
      if (Objects.isNull(device)) {
        log.error("设备信息不存在, deviceId: {}", deviceDto.getId());
        throw new BizException(String.format("设备ID为[%s]的数据不存在!", deviceDto.getId()));
      }
      log.info("查询到原设备信息: {}", device);

      // 3. 构建更新对象
      SsDevice updateDevice = new SsDevice();
      updateDevice.setId(deviceDto.getId());
      updateDevice.setDeviceArrears(deviceDto.getDeviceArrears());
      // 4. 更新设备信息
      boolean updated =
          this.update(
              Wrappers.lambdaUpdate(SsDevice.class)
                  .eq(SsDevice::getId, deviceDto.getId())
                  .set(SsDevice::getDeviceArrears, deviceDto.getDeviceArrears()));
      if (!updated) {
        log.error("更新设备状态失败, deviceId: {}", deviceDto.getId());
        throw new BizException("更新设备状态失败");
      }
      log.info("更新设备状态成功, deviceId: {}, 更新内容: {}", deviceDto.getId(), updateDevice);

      // 5. 清除设备缓存
      SsDeviceDTO cacheDto = new SsDeviceDTO();
      BeanUtils.copyProperties(device, cacheDto);
      deviceManager.removeDeviceCache(cacheDto);
      log.info("清除设备缓存成功, deviceNo: {}", device.getDeviceNo());

      // 6. 记录操作日志
      SpringContextHolder.getBean(SsDeviceServiceImpl.class)
          .editOperateLogAspect(device, deviceDto);
      log.info("记录操作日志成功");
    } catch (BizException e) {
      log.error("业务异常: {}", e.getMessage());
      throw e;
    } catch (Exception e) {
      log.error("更新设备状态发生未知异常", e);
      throw new BizException("系统异常,更新设备状态失败");
    }
  }
}
