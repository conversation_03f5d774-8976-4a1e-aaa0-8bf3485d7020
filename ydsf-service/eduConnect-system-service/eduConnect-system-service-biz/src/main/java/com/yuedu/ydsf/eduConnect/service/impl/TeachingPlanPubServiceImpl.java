package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.api.feign.RemoteCourseAuthStoreService;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteStageService;
import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.api.constant.ClosedEnum;
import com.yuedu.ydsf.eduConnect.api.constant.StageProductEnum;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanPubQuery;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanQuery;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanToLiveRoomVO;
import com.yuedu.ydsf.eduConnect.constant.LiveRoomPlanVersionConstants;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanPub;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailVersionMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanVersionMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDetailPubMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanPubMapper;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDetailVersionService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanPubService;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 已发布的教学计划表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-29 09:26:30
 */
@Slf4j
@Service
@AllArgsConstructor
public class TeachingPlanPubServiceImpl extends
    ServiceImpl<TeachingPlanPubMapper, TeachingPlanPub> implements TeachingPlanPubService {

    @Resource
    private final RemoteStageService remoteStageService;

    @Resource
    private LiveRoomPlanVersionMapper liveRoomPlanVersionMapper;

    @Resource
    private final RemoteCourseService remoteCourseService;

    @Resource
    private TeachingPlanPubMapper teachingPlanPubMapper;

    @Resource
    private TeachingPlanDetailPubMapper planDetailPubMapper;


    @Resource
    private LiveRoomPlanDetailVersionService detailVersionService;

    @Resource
    private LiveRoomPlanDetailVersionMapper liveRoomPlanDetailVersionMapper;

    @Resource
    private RemoteCourseAuthStoreService remoteCourseAuthStoreService;


    /**
     * 通过指定条件查询教学计划列表
     *
     * @param teachingPlanQuery 查询条件
     * @return 结果
     */
    @Override
    public List<TeachingPlanPubVO> getTeachingPlanList(TeachingPlanQuery teachingPlanQuery) {
        List<TeachingPlanPubVO> planPubVOList = teachingPlanPubMapper.getTeachingPlanList(
            teachingPlanQuery);
        log.info("教学计划列表:{}", JSON.toJSONString(planPubVOList));
        if (!CollectionUtils.isEmpty(planPubVOList)) {
            //直播间计划Id列表
            List<Long> liveRoomPlanId = planPubVOList.stream()
                .map(TeachingPlanPubVO::getLiveRoomPlanId).distinct().collect(Collectors.toList());
            List<LiveRoomPlanDetailVersionVO> versionList = liveRoomPlanDetailVersionMapper.getLiveRoomPlanDetailVersionList(
                liveRoomPlanId);
            log.info("直播间计划详情列表:{}", JSON.toJSONString(versionList));
            if (!CollectionUtils.isEmpty(versionList)) {
                for (TeachingPlanPubVO planPubVO : planPubVOList) {
                    versionList.stream().filter(
                        versionVO -> Objects.equals(
                            planPubVO.getLiveRoomPlanId(),
                            versionVO.getPlanId())).forEach(
                        liveRoomPlanDetailVersionVO -> {
                            planPubVO.setClassStartTime(
                                liveRoomPlanDetailVersionVO.getClassStartTime());
                            planPubVO.setClassEndTime(
                                liveRoomPlanDetailVersionVO.getClassEndTime());
                        });
                }
            }
        }
        return planPubVOList;
    }


    /**
     * 根据教学计划id查询直播间计划名称
     *
     * @param teachingPlanIdList 教学计划id集合
     * @return List<TeachingPlanToLiveRoomVO>
     */
    @Override
    public List<TeachingPlanToLiveRoomVO> getTeachingPlanToLiveRoom(List<Long> teachingPlanIdList) {
        if (CollectionUtils.isEmpty(teachingPlanIdList)) {
            return Collections.emptyList();
        }
        List<TeachingPlanPub> teachingPlanPubList = teachingPlanPubMapper.selectList(
            Wrappers.lambdaQuery(TeachingPlanPub.class)
                .in(TeachingPlanPub::getTeachingPlanId, teachingPlanIdList));

        // 每个教学计划只保留最新的一条记录
        Map<Long, TeachingPlanPub> latestPlans = teachingPlanPubList.stream()
            .collect(Collectors.toMap(
                TeachingPlanPub::getTeachingPlanId,
                plan -> plan,
                (existing, replacement) ->
                    existing.getCreateTime().isAfter(replacement.getCreateTime()) ? existing
                        : replacement
            ));

        if (MapUtils.isEmpty(latestPlans)) {
            return Collections.emptyList();
        }
        // 获取所有直播间的计划版本
        List<LiveRoomPlanVersion> liveRoomPlanVersions = liveRoomPlanVersionMapper.selectList(
            Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                .in(LiveRoomPlanVersion::getPlanId,
                    latestPlans.values().stream().map(TeachingPlanPub::getLiveRoomPlanId)
                        .collect(Collectors.toList()))
                .eq(LiveRoomPlanVersion::getOnlineVersion,
                    LiveRoomPlanVersionConstants.ONLINE_VERSION));

        // 将直播间的计划ID映射到对应的计划名称
        Map<Long, LiveRoomPlanVersion> liveRoomPlanVersionsMap = liveRoomPlanVersions.stream()
            .collect(Collectors.toMap(
                LiveRoomPlanVersion::getPlanId,
                plan -> plan
            ));

        // 构建返回结果
        List<TeachingPlanToLiveRoomVO> teachingPlanToLiveRoomVOList = new ArrayList<>();
        for (TeachingPlanPub teachingPlanPub : latestPlans.values()) {
            TeachingPlanToLiveRoomVO vo = new TeachingPlanToLiveRoomVO();
            vo.setTeachingPlanId(teachingPlanPub.getTeachingPlanId());
            vo.setLiveRoomPlanId(teachingPlanPub.getLiveRoomPlanId());
            LiveRoomPlanVersion liveRoomPlanVersion = liveRoomPlanVersionsMap.get(
                teachingPlanPub.getLiveRoomPlanId());
            if (ObjectUtil.isNotEmpty(liveRoomPlanVersion)) {
                vo.setPlanName(liveRoomPlanVersion.getPlanName());
            }
            teachingPlanToLiveRoomVOList.add(vo);
        }

        return teachingPlanToLiveRoomVOList;
    }

    //    /**
//     * 通过指定条件查询教学计划列表
//     *
//     * @param teachingPlanQuery 查询条件
//     * @return 结果
//     */
//    @Override
//    public List<TeachingPlanPubVO> getTeachingPlanList(TeachingPlanQuery teachingPlanQuery) {
//        List<TeachingPlanPubVO> planPubVOList = teachingPlanPubMapper.getTeachingPlanList(
//            teachingPlanQuery);
//        if(!planPubVOList.isEmpty()){
//            // todo 获取阶段id列表,获取阶段详情
//            //获取
//        }
//        log.info("教学计划列表:{}", JSON.toJSONString(planPubVOList));
//        return planPubVOList;
//    }


    /**
     * 查询小程序约点播课-选择课程列表
     *
     * @param teachingPlanPubQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>
     * <AUTHOR>
     * @date 2024/12/9 11:24
     */
    @Override
    public List<TeachingPlanPubVO> getTeachingPlanCourseList(
        TeachingPlanPubQuery teachingPlanPubQuery) {
        List<Long> authCourseIdList = null;
        //小程序请求
        if (Objects.nonNull(StoreContextHolder.getStoreId())) {
            List<Long> authorizedCourseIdList = getAuthorizedCourseIdList(
                StoreContextHolder.getStoreId(), teachingPlanPubQuery.getCourseType());
            if (Objects.nonNull(teachingPlanPubQuery.getCourseId())) {
                // 如果传入了课程ID，则只查询该课程
                if (!authorizedCourseIdList.contains(teachingPlanPubQuery.getCourseId())) {
                    log.info("当前门店未授权当前课程,课程ID: {}",
                        teachingPlanPubQuery.getCourseId());
                    throw new BizException("您没有授权当前课程,请联系总部教务");
                }
                authCourseIdList = Collections.singletonList(teachingPlanPubQuery.getCourseId());
            } else {
                authCourseIdList = authorizedCourseIdList;
            }
            if (CollectionUtils.isEmpty(authCourseIdList)) {
                log.info("当前门店未授权课程,返回空列表");
                throw new BizException("您没有授权的课程,请联系总部教务");
            }
        }

        // 通过主讲老师,课程包分组查询未关闭已排教学计划
        List<TeachingPlanPub> teachingPlanPubListGroup = this.list(
            Wrappers.lambdaQuery(TeachingPlanPub.class)
                .eq(Objects.nonNull(teachingPlanPubQuery.getLectureId()),
                    TeachingPlanPub::getLectureId, teachingPlanPubQuery.getLectureId())
                .eq(Objects.nonNull(teachingPlanPubQuery.getStage()), TeachingPlanPub::getStage,
                    teachingPlanPubQuery.getStage())
                .in(CollectionUtil.isNotEmpty(authCourseIdList), TeachingPlanPub::getCourseId,
                    authCourseIdList)
                .like(StringUtils.isNotBlank(teachingPlanPubQuery.getCourseName()),
                    TeachingPlanPub::getCourseName, teachingPlanPubQuery.getCourseName())
                .groupBy(TeachingPlanPub::getLectureId)
                .groupBy(TeachingPlanPub::getCourseId)
                .orderByDesc(TeachingPlanPub::getCreateTime)
        );

        // 查询课程
        List<Integer> courseIdList = teachingPlanPubListGroup.stream()
            .map(TeachingPlanPub::getCourseId).map(Long::intValue).toList();
        CourseDTO courseDTO = new CourseDTO();
        courseDTO.setCourseIdList(courseIdList);
        R<List<CourseVO>> courseVOList = remoteCourseService.getCourseListByIds(courseDTO);
        log.debug("课程信息返回参数:[{}]", JSONObject.toJSONString(courseVOList));

        // 查询双师阶段信息
        R<List<StageVO>> stageInfoList = remoteStageService.getStageList(
            StageProductEnum.STAGE_PRODUCT_ENUM_1.code);
        log.debug("阶段信息返回参数:[{}]", JSONObject.toJSONString(stageInfoList));

        List<TeachingPlanPubVO> teachingPlanPubVOS = new ArrayList<>();

        for (TeachingPlanPub teachingPlanPub : teachingPlanPubListGroup) {

            TeachingPlanPubVO teachingPlanPubVO = new TeachingPlanPubVO();
            BeanUtils.copyProperties(teachingPlanPub, teachingPlanPubVO);

            if (courseVOList.isOk() && Objects.nonNull(courseVOList.getData())) {

                CourseVO courseVO = courseVOList.getData().stream()
                    .filter(e -> e.getId().equals(teachingPlanPubVO.getCourseId().intValue()))
                    .findFirst()
                    .orElse(new CourseVO());

                teachingPlanPubVO.setStage(courseVO.getStageId());

                // 过滤指定阶段信息
                if (stageInfoList.isOk() && CollectionUtils.isNotEmpty(stageInfoList.getData())) {

                    StageVO stageVO = stageInfoList.getData().stream()
                        .filter(e -> e.getId().equals(courseVO.getStageId()))
                        .findFirst()
                        .orElse(new StageVO());

                    teachingPlanPubVO.setStageName(stageVO.getStageName());
                }

            }

            // 阶段查询条件
            if (Objects.nonNull(teachingPlanPubQuery.getStage())) {

                if (teachingPlanPubQuery.getStage().equals(teachingPlanPubVO.getStage())) {
                    teachingPlanPubVOS.add(teachingPlanPubVO);
                }

            } else {
                teachingPlanPubVOS.add(teachingPlanPubVO);
            }

        }

        return teachingPlanPubVOS;

    }

    /**
     * 查询小程序约直播播课-选择课程列表
     *
     * @param teachingPlanPubQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>
     * <AUTHOR>
     * @date 2024/12/17 10:16
     */
    @Override
    public List<TeachingPlanPubVO> getTeachingPlanCourseLiveList(
        TeachingPlanPubQuery teachingPlanPubQuery) {
        log.info("查询小程序约直播课-选择课程列表入参:{}", JSON.toJSONString(teachingPlanPubQuery));
        List<Long> courseIdList = getAuthorizedCourseIdList(
            StoreContextHolder.getStoreId(), teachingPlanPubQuery.getCourseType());
        if (CollectionUtils.isEmpty(courseIdList)) {
            log.info("当前门店未授权课程,返回空列表");
            throw new BizException("您没有授权的课程,请联系总部教务");
        }

        // 查询所有的教学计划
        List<TeachingPlanPub> teachingPlanPubList =
            this.list(
                Wrappers.lambdaQuery(TeachingPlanPub.class)
                    .eq(TeachingPlanPub::getClosed, ClosedEnum.CLOSED_ENUM_0.code)
                    .eq(
                        Objects.nonNull(teachingPlanPubQuery.getLectureId()),
                        TeachingPlanPub::getLectureId,
                        teachingPlanPubQuery.getLectureId())
                    .eq(
                        Objects.nonNull(teachingPlanPubQuery.getStage()),
                        TeachingPlanPub::getStage,
                        teachingPlanPubQuery.getStage())
                    .in(TeachingPlanPub::getCourseId, courseIdList));

        if (CollectionUtils.isEmpty(teachingPlanPubList)) {
            log.info("未查询到教学计划数据");
            throw new BizException("没有发布的课程,请联系总部教务");
        }

        // 获取所有直播间计划ID
        List<Long> liveRoomPlanIds =
            teachingPlanPubList.stream()
                .map(TeachingPlanPub::getLiveRoomPlanId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        log.debug("获取到的直播间计划ID列表:{}", JSON.toJSONString(liveRoomPlanIds));

        // 查询在线版本的直播间计划
        List<LiveRoomPlanVersion> planVersions =
            liveRoomPlanVersionMapper.selectList(
                Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                    .in(LiveRoomPlanVersion::getPlanId, liveRoomPlanIds)
                    .eq(
                        LiveRoomPlanVersion::getOnlineVersion,
                        LiveRoomPlanVersionConstants.ONLINE_VERSION));

        if (CollectionUtils.isEmpty(planVersions)) {
            log.debug("未查询到在线版本的直播间计划");
            return Collections.emptyList();
        }

        // 构建计划ID和版本号的映射
        Map<Long, Integer> planVersionMap =
            planVersions.stream()
                .collect(
                    Collectors.toMap(
                        LiveRoomPlanVersion::getPlanId,
                        LiveRoomPlanVersion::getVersion,
                        (v1, v2) -> v1));

        List<TeachingPlanPubVO> resultList = new ArrayList<>();

        // 处理每个教学计划
        for (TeachingPlanPub teachingPlanPub : teachingPlanPubList) {
            try {
                Long planId = teachingPlanPub.getLiveRoomPlanId();
                Integer version = planVersionMap.get(planId);

                if (planId == null || version == null) {
                    log.warn("教学计划缺少直播计划信息,planId:{}",
                        teachingPlanPub.getTeachingPlanId());
                    continue;
                }

                // 查询直播计划明细
                List<LiveRoomPlanDetailVersion> detailList =
                    liveRoomPlanDetailVersionMapper.selectList(
                        Wrappers.lambdaQuery(LiveRoomPlanDetailVersion.class)
                            .eq(LiveRoomPlanDetailVersion::getPlanId, planId)
                            .eq(LiveRoomPlanDetailVersion::getVersion, version)
                            .orderByAsc(LiveRoomPlanDetailVersion::getClassStartDateTime));

                if (CollectionUtils.isEmpty(detailList)) {
                    log.warn("未查询到直播计划明细,planId:{},version:{}", planId, version);
                    continue;
                }

                // 获取出现次数最多的time_slot_id
                Map<Long, Long> timeSlotCount =
                    detailList.stream()
                        .collect(
                            Collectors.groupingBy(
                                LiveRoomPlanDetailVersion::getTimeSlotId, Collectors.counting()));

                Long mostCommonTimeSlotId =
                    timeSlotCount.entrySet().stream()
                        .max(Entry.comparingByValue())
                        .map(Entry::getKey)
                        .orElse(null);

                TeachingPlanPubVO planPubVO = new TeachingPlanPubVO();
                BeanUtils.copyProperties(teachingPlanPub, planPubVO);

                // 获取第一个和最后一个时间
                LocalDate firstTime = detailList.get(0).getClassDate();
                // 如果只有一条数据，直接使用第一条数据的日期
                LocalDate lastClassDate =
                    detailList.size() > 1
                        ? detailList.get(detailList.size() - 1).getClassDate()
                        : firstTime;

                // 拼接时间段
                String timeRange =
                    firstTime.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))
                        + StrPool.DASHED
                        + lastClassDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                planPubVO.setLiveRoomPlanDuration(timeRange);
                planPubVO.setTimeSlotId(mostCommonTimeSlotId);
                planPubVO.setLiveRoomPlanName(
                    planVersions.stream()
                        .filter(e -> Objects.equals(e.getPlanId(), planPubVO.getLiveRoomPlanId()))
                        .findFirst()
                        .map(LiveRoomPlanVersion::getPlanName)
                        .orElse(null));
                resultList.add(planPubVO);
                log.debug("处理教学计划成功,planId:{},timeRange:{}", planId, timeRange);
            } catch (Exception e) {
                log.error(
                    "处理教学计划异常,planId:{},error:{}", teachingPlanPub.getTeachingPlanId(),
                    e.getMessage(), e);
            }
        }
        // 按照开始时间排序
        resultList.sort(Comparator.comparing(TeachingPlanPubVO::getLiveRoomPlanDuration));

        return resultList;
    }

    /**
     * 根据教学计划发布表的id集合批量获取对应的信息
     *
     * @param teachingPlanIds
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>
     * <AUTHOR>
     * @date 2024/12/19 10:20
     */
    @Override
    public List<TeachingPlanPubVO> getTeachingPlanPubByTeachingPlanIds(List<Long> teachingPlanIds) {
        log.info("根据教学计划ID集合查询教学计划发布信息,teachingPlanIds:{}",
            JSON.toJSONString(teachingPlanIds));

        // 1. 查询教学计划发布信息
        List<TeachingPlanPubVO> planPubVOS =
            teachingPlanPubMapper.getTeachingPlanPubByTeachingPlanIds(teachingPlanIds);
        if (CollectionUtils.isEmpty(planPubVOS)) {
            log.info("未查询到教学计划发布信息");
            return Collections.emptyList();
        }
        log.info("查询到教学计划发布信息数量:{}", planPubVOS.size());

        // 2. 遍历处理每个教学计划
        for (TeachingPlanPubVO planPubVO : planPubVOS) {

            try {
                if (planPubVO.getLiveRoomPlanId() == null
                    || planPubVO.getLiveRoomPlanVersion() == null) {
                    log.warn("教学计划缺少直播计划信息,planId:{}", planPubVO.getTeachingPlanId());
                    continue;
                }

                // 3. 查询直播计划明细,按照课次顺序排序
                List<LiveRoomPlanDetailVersion> detailList =
                    detailVersionService.list(
                        Wrappers.lambdaQuery(LiveRoomPlanDetailVersion.class)
                            .eq(LiveRoomPlanDetailVersion::getPlanId, planPubVO.getLiveRoomPlanId())
                            .eq(LiveRoomPlanDetailVersion::getVersion,
                                planPubVO.getLiveRoomPlanVersion()));

                if (CollectionUtils.isEmpty(detailList)) {
                    log.warn(
                        "未查询到直播计划明细信息,planId:{},version:{}",
                        planPubVO.getLiveRoomPlanId(),
                        planPubVO.getLiveRoomPlanVersion());
                    continue;
                }

                // 4. 获取第一节课和最后一节课的上课日期
                LocalDate firstClassDate = detailList.get(0).getClassDate();
                LocalDate lastClassDate = detailList.get(detailList.size() - 1).getClassDate();

                // 5. 拼接持续时间字符串: yyyy/MM/dd-yyyy/MM/dd
                String duration =
                    String.format(
                        "%s-%s",
                        firstClassDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd")),
                        lastClassDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd")));

                // 6. 设置持续时间
                planPubVO.setLiveRoomPlanDuration(duration);

                log.info("设置教学计划持续时间成功,planId:{},duration:{}",
                    planPubVO.getTeachingPlanId(), duration);

            } catch (Exception e) {
                log.error(
                    "处理教学计划持续时间异常,planId:{},error:{}", planPubVO.getTeachingPlanId(),
                    e.getMessage(), e);
            }
        }
        log.info("根据教学计划ID集合查询教学计划发布信息查询成功返回,{}",
            planPubVOS.stream().map(TeachingPlanPubVO::getTeachingPlanId).toList());
        return planPubVOS;
    }

    /**
     * 更新已发布的教学计划的关闭状态
     *
     * @return void
     * <AUTHOR>
     * @date 2024/12/24 15:55
     */
    @Override
    public void updateTeachingPlanPubClose() {

        log.info("开始执行更新已发布教学计划关闭状态任务");
        try {
            // 1. 查询所有未关闭的教学计划
            List<TeachingPlanPub> unClosedPlans =
                this.list(
                    Wrappers.lambdaQuery(TeachingPlanPub.class)
                        .eq(TeachingPlanPub::getClosed, ClosedEnum.CLOSED_ENUM_0.code));

            if (CollectionUtils.isEmpty(unClosedPlans)) {
                log.info("未查询到未关闭的教学计划");
                return;
            }
            log.info("查询到未关闭的教学计划数量:{}", unClosedPlans.size());

            // 2. 获取所有未关闭教学计划的ID
            List<Long> planIds = unClosedPlans.stream().map(TeachingPlanPub::getTeachingPlanId)
                .toList();

            // 3. 查询每个教学计划对应的最后一节课结束时间
            List<TeachingPlanDetailPub> lastLessons =
                planDetailPubMapper.selectList(
                    Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                        .in(TeachingPlanDetailPub::getPlanId, planIds)
                        .orderByDesc(TeachingPlanDetailPub::getClassEndDateTime));

            if (CollectionUtils.isEmpty(lastLessons)) {
                log.info("未查询到教学计划详情信息");
                return;
            }

            // 4. 当前时间
            LocalDateTime now = LocalDateTime.now();

            // 5. 需要关闭的教学计划ID列表
            List<Long> needCloseIds = new ArrayList<>();

            // 6. 遍历处理每个未关闭的教学计划
            for (TeachingPlanPub plan : unClosedPlans) {
                try {
                    // 获取当前教学计划的最后一节课
                    Optional<TeachingPlanDetailPub> lastLesson =
                        lastLessons.stream()
                            .filter(lesson -> Objects.equals(lesson.getPlanId(),
                                plan.getTeachingPlanId()))
                            .findFirst();

                    if (lastLesson.isPresent()) {
                        LocalDateTime endTime = lastLesson.get().getClassEndDateTime();
                        // 如果最后一节课的结束时间在当前时间之前,则需要关闭
                        if (endTime != null && endTime.isBefore(now)) {
                            needCloseIds.add(plan.getTeachingPlanId());
                            log.info("教学计划需要关闭,planId:{},最后课程结束时间:{}",
                                plan.getTeachingPlanId(), endTime);
                        }
                    }
                } catch (Exception e) {
                    log.error("处理教学计划关闭状态异常,planId:{},error:{}",
                        plan.getTeachingPlanId(), e.getMessage(), e);
                }
            }

            // 7. 批量更新需要关闭的教学计划
            if (!needCloseIds.isEmpty()) {
                boolean updated =
                    this.update(
                        Wrappers.lambdaUpdate(TeachingPlanPub.class)
                            .in(TeachingPlanPub::getTeachingPlanId, needCloseIds)
                            .set(TeachingPlanPub::getClosed, ClosedEnum.CLOSED_ENUM_1.code));

                log.info("批量更新教学计划关闭状态完成,需要关闭数量:{},更新结果:{}",
                    needCloseIds.size(), updated);
            }

        } catch (Exception e) {
            log.error("更新教学计划关闭状态任务执行异常,error:{}", e.getMessage(), e);
        }
        log.info("更新已发布教学计划关闭状态任务执行完成");
    }

    @Override
    public List<Long> getByStoreIdAndCourseType(Long storeId, Integer courseType) {
        if (Objects.isNull(storeId) || Objects.isNull(courseType)) {
            return Collections.emptyList();
        }
        List<Long> courseIdList = getCourseIdList(storeId, courseType);
        if (CollectionUtils.isEmpty(courseIdList)) {
            return Collections.emptyList();
        }
        return this.list(
                Wrappers.lambdaQuery(TeachingPlanPub.class)
                    .in(TeachingPlanPub::getCourseId, courseIdList))
            .stream()
            .map(TeachingPlanPub::getTeachingPlanId)
            .toList();
    }


    /**
     * 获取门店授权的课程类型数据（含历史授权过的课程ID）
     *
     * @param courseType 课程类型
     * @return 授权的课程ID列表
     */
    private List<Long> getCourseIdList(Long storeId, Integer courseType) {
        if (Objects.isNull(courseType)) {
            return Collections.emptyList();
        }
        try {
            R<List<Long>> listR = remoteCourseAuthStoreService.listAuthCourseIds(
                courseType.longValue(), storeId);
            if (listR.getCode() != 0 || org.apache.commons.collections4.CollectionUtils.isEmpty(
                listR.getData())) {
                return Collections.emptyList();
            } else {
                return listR.getData();
            }
        } catch (Exception e) {
            log.error("查询门店授权的课程类型数据失败, courseType: {}", courseType, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取门店授权中的课程ID列表（仅当前有效授权）
     *
     * @param courseType 课程类型
     * @return 授权的课程ID列表
     */
    private List<Long> getAuthorizedCourseIdList(Long storeId, Integer courseType) {
        try {
            R<List<Long>> listR = remoteCourseAuthStoreService.listCurrentAuthCourseIds(
                Objects.nonNull(courseType) ? courseType.longValue() : null, storeId);
            if (listR.getCode() != 0 || org.apache.commons.collections4.CollectionUtils.isEmpty(
                listR.getData())) {
                return Collections.emptyList();
            } else {
                return listR.getData();
            }
        } catch (Exception e) {
            log.error("查询门店授权的课程类型数据失败, courseType: {}", courseType, e);
            return Collections.emptyList();
        }
    }
}
