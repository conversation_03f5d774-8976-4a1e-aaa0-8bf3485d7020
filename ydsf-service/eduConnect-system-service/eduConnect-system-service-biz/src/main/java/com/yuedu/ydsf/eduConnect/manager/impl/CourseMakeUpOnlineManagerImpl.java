package com.yuedu.ydsf.eduConnect.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.api.feign.RemoteClassService;
import com.yuedu.store.api.feign.RemoteEmployeeService;
import com.yuedu.store.api.feign.RemoteStoreCourseHoursLogService;
import com.yuedu.store.api.feign.RemoteStudentService;
import com.yuedu.store.constant.enums.CourseHoursOperationEnum;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.store.vo.StoreCourseHoursLogVO;
import com.yuedu.store.vo.StudentVO;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.dto.LessonOrderDTO;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.config.AsyncConfiguration;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.file.core.FileProperties;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.CourseTypeEnum;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanDetailPubService;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineExportVO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.constant.AdjustStatusEnum;
import com.yuedu.ydsf.eduConnect.constant.TimetableCourseTypeEnum;
import com.yuedu.ydsf.eduConnect.entity.CourseMakeUpOnline;
import com.yuedu.ydsf.eduConnect.entity.TimetableEventAlarmDeatils;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteTimetableService;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BClassTimeStudentVO;
import com.yuedu.ydsf.eduConnect.manager.CourseMakeUpOnlineManager;
import com.yuedu.ydsf.eduConnect.mapper.TimetableEventAlarmDeatilsMapper;
import com.yuedu.ydsf.eduConnect.mapper.TimetablePictureMapper;
import lombok.Data;
import org.javers.common.collections.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.yuedu.ydsf.eduConnect.api.constant.AttendClassEnum.ATTEND_CLASS_0;
import static com.yuedu.ydsf.eduConnect.api.constant.AttendClassEnum.ATTEND_CLASS_1;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/04/28
 **/
@Component
public class CourseMakeUpOnlineManagerImpl implements CourseMakeUpOnlineManager {

    @Autowired
    private RemoteClassService remoteClassService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteCampusService remoteCampusService;

    @Autowired
    private RemoteLessonService remoteLessonService;

    @Autowired
    private RemoteTeachingPlanDetailPubService remoteTeachingPlanDetailPubService;

    @Autowired
    private RemoteTimetableService remoteTimetableService;

    @Autowired
    private RemoteStoreCourseHoursLogService remoteStoreCourseHoursLogService;


    @Autowired
    private RemoteStudentService remoteStudentService;


    @Autowired
    private AsyncConfiguration asyncConfiguration;

    @Autowired
    private RemoteCourseService remoteCourseService;



    @Override
    public IPage<CourseMakeUpOnlineVO> fillData(IPage<CourseMakeUpOnlineVO> page) {
        if(CollectionUtils.isEmpty(page.getRecords())){
            return page;
        }

        Map<Long, CampusVO> storeCache = new HashMap();
        Map<Long, ClassVO> classCache = new HashMap();
        Map<String, LessonVO> lessonCache = new HashMap();
        Map<String, TeachingPlanDetailPubVO> teachingPlanDetailPubCache = new HashMap();
        Map<Long, CourseMakeUpOnlineVO> classTimeStudentCache = new HashMap();
        Map<Long,CourseMakeUpOnlineVO> storeCourseHoursLogCache = new HashMap();
        Map<Long,TimetableVO> timetableEventAlarmDeatilsCache = new HashMap();



        CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
            CampusDTO campusDTO = new CampusDTO();
            campusDTO.setSchoolIdList(
                page.getRecords().stream().map(CourseMakeUpOnlineVO::getStoreId).distinct().toList());
            R<List<CampusVO>> campusList = remoteCampusService.getCampusList(campusDTO);
            if (campusList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(campusList.getData())) {
                campusList.getData().forEach(s -> {
                    storeCache.put(s.getId(), s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
            R<List<ClassVO>> classByIdList = remoteClassService
                .getClassByIdList(
                    page.getRecords().stream().map(CourseMakeUpOnlineVO::getClassId).distinct().toList());
            if (classByIdList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(classByIdList.getData())) {
                Map<Long, EmployeeVO> employeeCache = new HashMap();
                R<List<EmployeeVO>> employeeMapByIdList = remoteEmployeeService
                    .getEmployeeMapByIdList(classByIdList.getData().stream().filter(s->!Objects.isNull(s.getHeadTeacherId())).map(ClassVO::getHeadTeacherId).distinct().toList());
                if (employeeMapByIdList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(employeeMapByIdList.getData())){
                    employeeMapByIdList.getData().forEach(s->{
                        employeeCache.put(s.getUserId(),s);
                    });
                }

                classByIdList.getData().forEach(s -> {
                    classCache.put(Long.valueOf(s.getId()), s);
                    if(!Objects.isNull(employeeCache.get(s.getHeadTeacherId()))){
                        s.setHeadTeacherName(employeeCache.get(s.getHeadTeacherId()).getNickName());
                    }
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task4 = CompletableFuture.runAsync(() -> {
            R<List<LessonVO>> lessonListByOrder = remoteLessonService.getLessonListByOrder(
                page.getRecords().stream().map(s->s.getCourseId()).distinct().map(s -> {
                    LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
                    //lessonOrderDTO.setCourseId(s.getCourseId());
                    lessonOrderDTO.setCourseId(s);
                    //lessonOrderDTO.setLessonOrderList(Lists.asList(s.getLessonOrder()));
                    return lessonOrderDTO;
                }).toList());
            if (lessonListByOrder.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(lessonListByOrder.getData())){
                lessonListByOrder.getData().forEach(s->{
                    lessonCache.put(String.format("%s#%s",s.getCourseId(),s.getLessonOrder()),s);
                });
            }

        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task5 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.getRecords().stream()
                .filter(s -> LocalDateTime.now().isAfter(LocalDateTime.of(s.getClassDate(),s.getClassEndTime())))
                .map(CourseMakeUpOnlineVO::getTeachingPlanId).toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }
            R<List<TeachingPlanDetailPubVO>> teachingPlanDetailPubLiveChannel = remoteTeachingPlanDetailPubService.getTeachingPlanDetailPubLiveChannel(
                list);
            if(teachingPlanDetailPubLiveChannel.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(teachingPlanDetailPubLiveChannel.getData())){
                teachingPlanDetailPubLiveChannel.getData().forEach(s->{
                    teachingPlanDetailPubCache.put(String.format("%s#%s#%s",s.getPlanId(),s.getCourseId(),s.getLessonOrder()),s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task6 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.getRecords().stream()
                .map(s-> Long.parseLong(String.format("%s%s%s", CourseTypeEnum.COURSE_TYPE_ENUM_4.code,s.getLessonOrder(),s.getId())))
                .distinct()
                .toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }

            R<List<BClassTimeStudentVO>> bClassTimeStudentList = remoteTimetableService.getBClassTimeStudentList(
                list);
            if(bClassTimeStudentList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(bClassTimeStudentList.getData())){
                bClassTimeStudentList.getData().stream().collect(Collectors.groupingBy(BClassTimeStudentVO::getLessonNo)).forEach((k,v)->{
                    CourseMakeUpOnlineVO courseMakeUpOnlineVO = new CourseMakeUpOnlineVO();
                    courseMakeUpOnlineVO.setExpectedNumber(0);
                    courseMakeUpOnlineVO.setActualNumber(0);
                    v.forEach(s->{
                        if(AdjustStatusEnum.ADJUST_STATUS_0.code.equals(s.getAdjustStatus())){
                            courseMakeUpOnlineVO.setExpectedNumber(courseMakeUpOnlineVO.getExpectedNumber()+1);
                            if(CheckInStatusEnum.CHECK_IN_STATUS_1.code.equals(s.getCheckInStatus())){
                                courseMakeUpOnlineVO.setActualNumber(courseMakeUpOnlineVO.getActualNumber()+1);
                            }
                        }
                    });

                    classTimeStudentCache.put(k,courseMakeUpOnlineVO);
                });
            }

        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task7 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.getRecords().stream().map(CourseMakeUpOnlineVO::getId).distinct()
                .toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }

            R<List<StoreCourseHoursLogVO>> storeCourseHoursLogs = remoteStoreCourseHoursLogService.getStudentConsumeListByIds(
                list);

            if(storeCourseHoursLogs.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(storeCourseHoursLogs.getData())){
                storeCourseHoursLogs.getData().stream().collect(Collectors.groupingBy(StoreCourseHoursLogVO::getTimetableId)).forEach((k,v)->{
                    CourseMakeUpOnlineVO courseMakeUpOnlineVO = new CourseMakeUpOnlineVO();
                    courseMakeUpOnlineVO.setGiftNumber(0);
                    courseMakeUpOnlineVO.setTrialNumber(0);
                    courseMakeUpOnlineVO.setConsumeNumber(0);
                    v.forEach(s->{
                        if(CourseHoursOperationEnum.GIFT.getDesc().equals(s.getOperationType())){
                            courseMakeUpOnlineVO.setGiftNumber(courseMakeUpOnlineVO.getGiftNumber()+s.getCourseHours());
                        }
                        if(CourseHoursOperationEnum.TRIAL.getDesc().equals(s.getOperationType())){
                            courseMakeUpOnlineVO.setTrialNumber(courseMakeUpOnlineVO.getTrialNumber()+s.getCourseHours());
                        }
                        if(CourseHoursOperationEnum.ENROLL.getDesc().equals(s.getOperationType())){
                            courseMakeUpOnlineVO.setConsumeNumber(courseMakeUpOnlineVO.getConsumeNumber()+s.getCourseHours());
                        }

                    });
                    storeCourseHoursLogCache.put(k,courseMakeUpOnlineVO);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture.allOf(task1,task2,task4,task5,task6,task7).join();



        return page.convert(s->{
            CourseMakeUpOnlineVO courseMakeUpOnlineVO = new CourseMakeUpOnlineVO();
            courseMakeUpOnlineVO.setExpectedNumber(0);
            courseMakeUpOnlineVO.setActualNumber(0);
            courseMakeUpOnlineVO.setGiftNumber(0);
            courseMakeUpOnlineVO.setTrialNumber(0);
            courseMakeUpOnlineVO.setConsumeNumber(0);

            s.setStoreName(storeCache.getOrDefault(s.getStoreId(),new CampusVO()).getCampusName());
            s.setTeacherName(classCache.getOrDefault(s.getClassId(),new ClassVO()).getHeadTeacherName());
            s.setClassName(classCache.getOrDefault(s.getClassId(),new ClassVO()).getCName());
            s.setCourseType(TimetableCourseTypeEnum.TIMETABLE_COURSE_TYPE_4.code);
            if(LocalDateTime.now().isBefore(LocalDateTime.of(s.getClassDate(),s.getClassEndTime()))){
                LessonVO orDefault = lessonCache.getOrDefault(String.format("%s#%s", s.getCourseId(), s.getLessonOrder()), new LessonVO());
                s.setLessonName(orDefault.getLessonName());
            }else {
                TeachingPlanDetailPubVO orDefault = teachingPlanDetailPubCache.getOrDefault(
                    String.format("%s#%s#%s", s.getTeachingPlanId(), s.getCourseId(),
                        s.getLessonOrder()), new TeachingPlanDetailPubVO());
                s.setLessonName(orDefault.getLessonName());
            }

            Long  lessionNo = Long.parseLong(
                String.format("%s%s%s", CourseTypeEnum.COURSE_TYPE_ENUM_4.code, s.getLessonOrder(),
                    s.getId()));
            s.setExpectedNumber(classTimeStudentCache.getOrDefault(lessionNo,courseMakeUpOnlineVO).getExpectedNumber());
            s.setActualNumber(classTimeStudentCache.getOrDefault(lessionNo,courseMakeUpOnlineVO).getActualNumber());
            s.setGiftNumber(Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),courseMakeUpOnlineVO).getGiftNumber()));
            s.setTrialNumber(Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),courseMakeUpOnlineVO).getTrialNumber()));
            s.setConsumeNumber(Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),courseMakeUpOnlineVO).getConsumeNumber()));

            return s;
        });
    }

    @Override
    public CourseMakeUpOnlineVO fillInfoData(CourseMakeUpOnline courseMakeUpOnline) {
        CourseMakeUpOnlineVO courseMakeUpOnlineVO = new CourseMakeUpOnlineVO();
        courseMakeUpOnlineVO.setActualNames(new ArrayList<>());
        courseMakeUpOnlineVO.setExpectedNames(new ArrayList<>());
        BeanUtil.copyProperties(courseMakeUpOnline,courseMakeUpOnlineVO);

        R<List<BClassTimeStudentVO>> bClassTimeStudentList = remoteTimetableService.getBClassTimeStudentList(
            List.of( Long.parseLong(String.format("%s%s%s", CourseTypeEnum.COURSE_TYPE_ENUM_4.code,courseMakeUpOnlineVO.getLessonOrder(),courseMakeUpOnlineVO.getId()))));
        if(bClassTimeStudentList.isOk() && CollectionUtil.isNotEmpty(bClassTimeStudentList.getData())){
            Map<Long,String> studentCache = new HashMap<>();
            List<Long> list = bClassTimeStudentList.getData().stream()
                .map(BClassTimeStudentVO::getStudentId).distinct().toList();
            if(CollectionUtils.isNotEmpty(list)){
                R<List<StudentVO>> studentListByIds = remoteStudentService.getStudentListByIds(
                    list);
                if(studentListByIds.isOk() && CollectionUtil.isNotEmpty(studentListByIds.getData())){
                    studentListByIds.getData().forEach(s->{
                        studentCache.put(s.getUserId(),s.getName());
                    });
                }

            }
            bClassTimeStudentList.getData().forEach(s->{
                if(AdjustStatusEnum.ADJUST_STATUS_0.code.equals(s.getAdjustStatus())){
                    courseMakeUpOnlineVO.getExpectedNames().add(studentCache.getOrDefault(s.getStudentId(),"-"));
                    if(CheckInStatusEnum.CHECK_IN_STATUS_1.code.equals(s.getCheckInStatus())){
                        courseMakeUpOnlineVO.getActualNames().add(studentCache.getOrDefault(s.getStudentId(),"-"));
                    }
                }
            });

        }

        return courseMakeUpOnlineVO;
    }

    @Override
    public List<CourseMakeUpOnlineExportVO> fillExportData(List<CourseMakeUpOnline> page) {
        if(CollectionUtils.isEmpty(page)){
            return new ArrayList<>();
        }

        Map<Long, CampusVO> storeCache = new HashMap();
        Map<Long, ClassVO> classCache = new HashMap();
        Map<String, LessonVO> lessonCache = new HashMap();
        Map<String, TeachingPlanDetailPubVO> teachingPlanDetailPubCache = new HashMap();
        Map<Long, CourseMakeUpOnlineVO> classTimeStudentCache = new HashMap();
        Map<Long,CourseMakeUpOnlineVO> storeCourseHoursLogCache = new HashMap();
        Map<Long, CourseVO> courseCache = new HashMap();


        CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
            CampusDTO campusDTO = new CampusDTO();
            campusDTO.setSchoolIdList(
                page.stream().map(CourseMakeUpOnline::getStoreId).distinct().toList());
            R<List<CampusVO>> campusList = remoteCampusService.getCampusList(campusDTO);
            if (campusList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(campusList.getData())) {
                campusList.getData().forEach(s -> {
                    storeCache.put(s.getId(), s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
            R<List<ClassVO>> classByIdList = remoteClassService
                .getClassByIdLists(
                    page.stream().map(CourseMakeUpOnline::getClassId).distinct().toList());
            if (classByIdList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(classByIdList.getData())) {
                Map<Long, EmployeeVO> employeeCache = new HashMap();
                R<List<EmployeeVO>> employeeMapByIdList = remoteEmployeeService
                    .getEmployeeMapByIdList(classByIdList.getData().stream().filter(s->!Objects.isNull(s.getHeadTeacherId())).map(ClassVO::getHeadTeacherId).distinct().toList());
                if (employeeMapByIdList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(employeeMapByIdList.getData())){
                    employeeMapByIdList.getData().forEach(s->{
                        employeeCache.put(s.getUserId(),s);
                    });
                }

                classByIdList.getData().forEach(s -> {
                    classCache.put(Long.valueOf(s.getId()), s);
                    if(!Objects.isNull(employeeCache.get(s.getHeadTeacherId()))){
                        s.setHeadTeacherName(employeeCache.get(s.getHeadTeacherId()).getNickName());
                    }
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task4 = CompletableFuture.runAsync(() -> {
            R<List<LessonVO>> lessonListByOrder = remoteLessonService.getLessonListByOrder(
                page.stream().map(s->s.getCourseId()).distinct().map(s -> {
                    LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
                    //lessonOrderDTO.setCourseId(s.getCourseId());
                    lessonOrderDTO.setCourseId(s);
                  //  lessonOrderDTO.setLessonOrderList(Lists.asList(s.getLessonOrder()));
                    return lessonOrderDTO;
                }).toList());
            if (lessonListByOrder.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(lessonListByOrder.getData())){
                lessonListByOrder.getData().forEach(s->{
                    lessonCache.put(String.format("%s#%s",s.getCourseId(),s.getLessonOrder()),s);
                });
            }

        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task5 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.stream()
                .filter(s -> LocalDateTime.now().isAfter(LocalDateTime.of(s.getClassDate(),s.getClassEndTime())))
                .map(CourseMakeUpOnline::getTeachingPlanId).distinct().toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }
            R<List<TeachingPlanDetailPubVO>> teachingPlanDetailPubLiveChannel = remoteTeachingPlanDetailPubService.getTeachingPlanDetailPubLiveChannel(
                list);
            if(teachingPlanDetailPubLiveChannel.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(teachingPlanDetailPubLiveChannel.getData())){
                teachingPlanDetailPubLiveChannel.getData().forEach(s->{
                    teachingPlanDetailPubCache.put(String.format("%s#%s#%s",s.getPlanId(),s.getCourseId(),s.getLessonOrder()),s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task6 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.stream()
                .map(s-> Long.parseLong(String.format("%s%s%s", CourseTypeEnum.COURSE_TYPE_ENUM_4.code,s.getLessonOrder(),s.getId())))
                .distinct()
                .toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }

            R<List<BClassTimeStudentVO>> bClassTimeStudentList = remoteTimetableService.getBClassTimeStudentList(
                list);
            if(bClassTimeStudentList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(bClassTimeStudentList.getData())){
                bClassTimeStudentList.getData().stream().collect(Collectors.groupingBy(BClassTimeStudentVO::getLessonNo)).forEach((k,v)->{
                    CourseMakeUpOnlineVO courseMakeUpOnlineVO = new CourseMakeUpOnlineVO();
                    courseMakeUpOnlineVO.setExpectedNumber(0);
                    courseMakeUpOnlineVO.setActualNumber(0);
                    v.forEach(s->{
                        if(AdjustStatusEnum.ADJUST_STATUS_0.code.equals(s.getAdjustStatus())){
                            courseMakeUpOnlineVO.setExpectedNumber(courseMakeUpOnlineVO.getExpectedNumber()+1);
                            if(CheckInStatusEnum.CHECK_IN_STATUS_1.code.equals(s.getCheckInStatus())){
                                courseMakeUpOnlineVO.setActualNumber(courseMakeUpOnlineVO.getActualNumber()+1);
                            }
                        }
                    });

                    classTimeStudentCache.put(k,courseMakeUpOnlineVO);
                });
            }

        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task7 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.stream().map(CourseMakeUpOnline::getId).distinct()
                .toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }

            R<List<StoreCourseHoursLogVO>> storeCourseHoursLogs = remoteStoreCourseHoursLogService.getStudentConsumeListByIds(
                list);

            if(storeCourseHoursLogs.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(storeCourseHoursLogs.getData())){
                storeCourseHoursLogs.getData().stream().collect(Collectors.groupingBy(StoreCourseHoursLogVO::getTimetableId)).forEach((k,v)->{
                    CourseMakeUpOnlineVO courseMakeUpOnlineVO = new CourseMakeUpOnlineVO();
                    courseMakeUpOnlineVO.setGiftNumber(0);
                    courseMakeUpOnlineVO.setTrialNumber(0);
                    courseMakeUpOnlineVO.setConsumeNumber(0);
                    v.forEach(s->{
                        if(CourseHoursOperationEnum.GIFT.getDesc().equals(s.getOperationType())){
                            courseMakeUpOnlineVO.setGiftNumber(courseMakeUpOnlineVO.getGiftNumber()+s.getCourseHours());
                        }
                        if(CourseHoursOperationEnum.TRIAL.getDesc().equals(s.getOperationType())){
                            courseMakeUpOnlineVO.setTrialNumber(courseMakeUpOnlineVO.getTrialNumber()+s.getCourseHours());
                        }
                        if(CourseHoursOperationEnum.ENROLL.getDesc().equals(s.getOperationType())){
                            courseMakeUpOnlineVO.setConsumeNumber(courseMakeUpOnlineVO.getConsumeNumber()+s.getCourseHours());
                        }

                    });
                    storeCourseHoursLogCache.put(k,courseMakeUpOnlineVO);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task8 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.stream().map(CourseMakeUpOnline::getCourseId).distinct()
                .toList();
            if(CollectionUtils.isEmpty(list)){
                return;
            }

            R<Map<Long, CourseVO>> courseMapByIdList = remoteCourseService.getCourseMapByIdList(
                list);

            if(courseMapByIdList.isOk() && CollectionUtils.isNotEmpty(courseMapByIdList.getData())){
                courseMapByIdList.getData().forEach((k,v)->{
                    courseCache.put(k,v);
                });
            }


        },asyncConfiguration.getAsyncExecutor());

        CompletableFuture.allOf(task1,task2,task4,task5,task6,task7,task8).join();

        return page.stream().map(s->{

            CourseMakeUpOnlineExportVO courseMakeUpOnlineExportVO = new CourseMakeUpOnlineExportVO();

            CourseMakeUpOnlineVO courseMakeUpOnlineVO = new CourseMakeUpOnlineVO();
            courseMakeUpOnlineVO.setExpectedNumber(0);
            courseMakeUpOnlineVO.setActualNumber(0);
            courseMakeUpOnlineVO.setGiftNumber(0);
            courseMakeUpOnlineVO.setTrialNumber(0);
            courseMakeUpOnlineVO.setConsumeNumber(0);

            courseMakeUpOnlineExportVO.setStoreName(storeCache.getOrDefault(s.getStoreId(),new CampusVO()).getCampusName());
            courseMakeUpOnlineExportVO.setClassName(classCache.getOrDefault(s.getClassId(),new ClassVO()).getCName());
            courseMakeUpOnlineExportVO.setCourseTypeName(TimetableCourseTypeEnum.TIMETABLE_COURSE_TYPE_4.desc);
            courseMakeUpOnlineExportVO.setCourseName(courseCache.getOrDefault(s.getCourseId(),new CourseVO()).getCourseName());
            courseMakeUpOnlineExportVO.setClassStartTimeStr(s.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));


            if(LocalDateTime.now().isBefore(LocalDateTime.of(s.getClassDate(),s.getClassEndTime()))){
                LessonVO orDefault = lessonCache.getOrDefault(String.format("%s#%s", s.getCourseId(), s.getLessonOrder()), new LessonVO());
                courseMakeUpOnlineExportVO.setLessonName(orDefault.getLessonName());
            }else {
                TeachingPlanDetailPubVO orDefault = teachingPlanDetailPubCache.getOrDefault(
                    String.format("%s#%s#%s", s.getTeachingPlanId(), s.getCourseId(),
                        s.getLessonOrder()), new TeachingPlanDetailPubVO());
                courseMakeUpOnlineExportVO.setLessonName(orDefault.getLessonName());
            }

            Long  lessionNo = Long.parseLong(
                String.format("%s%s%s", CourseTypeEnum.COURSE_TYPE_ENUM_4.code, s.getLessonOrder(),
                    s.getId()));
            courseMakeUpOnlineExportVO.setExpectedNumber(classTimeStudentCache.getOrDefault(lessionNo,courseMakeUpOnlineVO).getExpectedNumber());
            courseMakeUpOnlineExportVO.setActualNumber(classTimeStudentCache.getOrDefault(lessionNo,courseMakeUpOnlineVO).getActualNumber());

            if(Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),courseMakeUpOnlineVO).getGiftNumber())
                + Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),courseMakeUpOnlineVO).getTrialNumber())
                + Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),courseMakeUpOnlineVO).getConsumeNumber()) > 0){
                courseMakeUpOnlineExportVO.setIsAttendClass(ATTEND_CLASS_0.desc);
            }else {
                courseMakeUpOnlineExportVO.setIsAttendClass(ATTEND_CLASS_1.desc);
            }

            if(Objects.nonNull(s.getClassDate()) && Objects.nonNull(s.getClassStartTime()) && Objects.nonNull(s.getClassEndTime())){
                courseMakeUpOnlineExportVO.setSourceTimeStr(String.format("%s %s - %s",
                    s.getClassDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    s.getClassStartTime().format(DateTimeFormatter.ofPattern("HH:mm")),
                    s.getClassEndTime().format(DateTimeFormatter.ofPattern("HH:mm")))
                );
            }

            if(Objects.nonNull(s.getValidityStartTime()) && Objects.nonNull(s.getValidityEndTime())){
                courseMakeUpOnlineExportVO.setEffectiveTimeStr(String.format("%s 至 %s",
                    s.getValidityStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    s.getValidityEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                ));
            }

            return courseMakeUpOnlineExportVO;
        }).toList();
    }
}
