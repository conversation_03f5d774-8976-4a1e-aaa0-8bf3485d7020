package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.entity.SsVideoPlayToken;

/**
 * 视频播放凭证 服务类
 *
 * <AUTHOR>
 * @date 2025-03-11 08:47:54
 */
public interface SsVideoPlayTokenService extends IService<SsVideoPlayToken> {

    /**
     * 生成视频播放凭证
     *
     * @param ssVideoPlayToken 视频播放凭证
     * @return token
     */
    String generateToken(SsVideoPlayToken ssVideoPlayToken);

    /**
     * 获取视频播放链接
     *
     * @param token 视频播放凭证
     * @return 播放链接
     */
    String getPlayUrl(String token);

    /**
     * 更新视频播放次数
     * @param token 视频播放凭证
     */
    void updatePlayCount(String token);
}
