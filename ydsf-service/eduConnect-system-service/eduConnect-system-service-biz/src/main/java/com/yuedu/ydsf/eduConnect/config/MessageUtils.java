package com.yuedu.ydsf.eduConnect.config;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeansException;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * TODO
 *
 * <AUTHOR>
 * @Date 2022-09-23 13:45
 * @Version 1.0.0
 */
@ImportAutoConfiguration(RabbitMqConfiguration.class)
public class MessageUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        MessageUtils.applicationContext = applicationContext;
    }

    /**
     * 获得消息队列模板
     *
     * @Author: KL
     * @Date: 2022-09-23 13:43
     */
    public static RabbitTemplate getRabbitTemplate() {
        RabbitTemplate rabbitTemplate = MessageUtils.applicationContext.getBean(RabbitTemplate.class);
        if (rabbitTemplate == null)
            throw new RuntimeException("RabbitTemplate 未注入到容器中,获取失败");
        return rabbitTemplate;
    }


    /**
     * 发送信息
     *
     * @Author: KL
     * @Date: 2022-09-23 13:52
     */
    public static <T> void sendMessage(String exchange, String key, T t) {
        getRabbitTemplate().convertAndSend(exchange, key, t);
    }


    
    /**
     * 发送消息
     * 
     * @Author: KL
     * @Date: 2022-09-23 16:44
     */
    public static void sendMessage(String exchange, String key, Message message) {
        getRabbitTemplate().convertAndSend(exchange, key, message);
    }

}
