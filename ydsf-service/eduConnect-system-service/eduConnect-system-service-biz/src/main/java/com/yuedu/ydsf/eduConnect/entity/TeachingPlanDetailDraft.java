package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 教学计划明细草稿表 实体类
 *
 * <AUTHOR>
 * @date 2024-12-03 09:52:18
 */
@Data
@TableName("ea_teaching_plan_detail_draft")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "教学计划明细草稿表实体类")
public class TeachingPlanDetailDraft extends Model<TeachingPlanDetailDraft> {


	/**
	* 主键id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键id")
    private Long id;

	/**
	* 教学计划id
	*/
    @Schema(description="教学计划id")
    private Long planId;

	/**
	* 草稿主讲id
	*/
    @Schema(description="草稿主讲id")
    private Long lectureId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;

    /**
     * 上课时段ID
     */
    @Schema(description="上课时段ID")
    private Long timeSlotId;

    /**
     * 主讲姓名
     */
    @Schema(description="主讲姓名")
    private String lectureName;

    /**
     *  修改备注
     */
    @Schema(description="修改备注")
    private String editRemark;
}
