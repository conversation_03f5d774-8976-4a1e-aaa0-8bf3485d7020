package com.yuedu.ydsf.eduConnect.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.dto.SsDeviceDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsDeviceQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsReadDeviceVo;
import com.yuedu.ydsf.eduConnect.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.service.SsDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备表控制层
 *
 * <AUTHOR>
 * @date 2024/09/26
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/ssDevice")
@Tag(description = "ss_device", name = "设备表")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Validated
public class SsDeviceController {

    @Value("${ss.config.js-system.token}")
    private String jsSystemToken;

    private final SsDeviceService ssDeviceService;

    /**
     * 设备表分页查询
     *
     * @param page          分页对象
     * @param ssDeviceQuery 设备表
     * @return R
     */
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('edusystem_ssDevice_view')")
    @Operation(summary = "分页查询", description = "设备表分页查询")
    public R<IPage<SsDeviceVO>> page(@ParameterObject Page page,
                                     @ParameterObject SsDeviceQuery ssDeviceQuery)
    {
        return R.ok(ssDeviceService.page(page, ssDeviceQuery));
    }

    /**
     * 通过id查询设备表
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询设备表")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('edusystem_ssDevice_view')")
    public R getById(@PathVariable Serializable id) {
        SsDeviceVO deviceVO = ssDeviceService.getInfoById(id);
        return R.ok(deviceVO);
    }

    /**
     * 新增设备表
     *
     * @param ssDeviceDTO 设备表
     * @return R
     */
    @PostMapping
    @PreAuthorize("@pms.hasPermission('edusystem_ssDevice_add')")
    @Operation(summary = "新增设备表", description = "新增设备表")
    public R add(@Validated(V_A.class) @RequestBody SsDeviceDTO ssDeviceDTO) {
        return R.ok(ssDeviceService.add(ssDeviceDTO));
    }

    /**
     * 修改设备表
     *
     * @param ssDeviceDTO 设备表
     * @return R
     */
    @PutMapping
    @PreAuthorize("@pms.hasPermission('edusystem_ssDevice_edit')")
    @Operation(summary = "修改设备表", description = "修改设备表")
    public R edit(@Validated(V_E.class) @RequestBody SsDeviceDTO ssDeviceDTO) {
        return R.ok(ssDeviceService.edit(ssDeviceDTO));
    }

    /**
     * 通过id删除设备表
     *
     * @param ids id列表
     * @return R
     */
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('edusystem_ssDevice_del')")
    @Operation(summary = "删除设备表", description = "删除设备表")
    public R delete(@RequestBody @NotEmpty(message = "请选择设备") Long[] ids) {
        ssDeviceService.deleteDevice(ids);
        return R.ok();
    }

    /**
     * 导出excel 设备表表格
     *
     * @param ssDeviceQuery 查询条件
     * @param ids           导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('edusystem_ssDevice_export')")
    @Operation(summary = "导出设备表表格", description = "导出设备表表格")
    public List<SsDeviceVO> export(SsDeviceQuery ssDeviceQuery,
                                   Long[] ids)
    {
        return ssDeviceService.export(ssDeviceQuery, ids);
    }

    /**
     * 解绑设备
     *
     * @param ids
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/9/28 9:23
     */
    @PutMapping("/unbind")
    @PreAuthorize("@pms.hasPermission('edusystem_ssDevice_unbind')")
    @Operation(summary = "解绑设备", description = "解绑设备")
    public R unbind(@RequestBody @NotEmpty(message = "请选择设备") Long[] ids) {
        ssDeviceService.unbind(ids);
        return R.ok();
    }

    /**
     * 启用/禁用设备状态
     *
     * @param ids
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/9/28 9:45
     */
    @PutMapping("/changeDeviceState")
    @Operation(summary = "启用/禁用设备状态", description = "启用/禁用设备状态")
    public R changeDeviceState(@RequestBody @NotEmpty(message = "请选择设备") Long[] ids) {
        ssDeviceService.changeDeviceState(ids);
        return R.ok();
    }

    /**
     * 查询所有教室端有效设备
     * @param ssDeviceQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/9 16:23
     */
    @Operation(summary = "查询所有教室端有效设备" , description = "查询所有教室端有效设备" )
    @GetMapping("/getClassRoomDeviceList" )
    public R<List<SsDeviceVO>> getClassRoomDeviceList(@ParameterObject SsDeviceQuery ssDeviceQuery) {
        List<SsDeviceVO> ssDeviceVOList = ssDeviceService.getClassRoomDeviceList(ssDeviceQuery);
        return R.ok(ssDeviceVOList);
    }

  /**
   * 结算系统获取设备列表
   * <AUTHOR>
   * @date 2025/2/10 15:04
   * @param page
   * @param page_size
   * @param request
   * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.api.vo.SsReadDeviceVo>
   */
  @PostMapping(value = "/listDevice")
  @Inner(value = false)
  public R<SsReadDeviceVo> listDevice(
      @RequestParam Integer page, @RequestParam Integer page_size, HttpServletRequest request) {
    String token = request.getHeader("token");
    if (token == null || !token.equals("PTw3RKj2GARPOD2ju5oPAiiu8zsJPBob")) {
      throw new BizException("无效token！请求失败！");
    }
    if (Objects.isNull(page) || Objects.isNull(page_size)) {
      throw new BizException("参数异常！");
    }
    SsReadDeviceVo readDeviceVo = ssDeviceService.pageRead(page, page_size);
    return R.ok(readDeviceVo, "获取成功");
  }

  /**
   * 结算系统更新设备状态
   *
   * <AUTHOR>
   * @date 2025/2/10 15:06
   * @param device_id
   * @param status
   * @param request
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @PostMapping(value = "/settlementUpdateDeviceInfo")
  @Inner(value = false)
  public R settlementUpdateDeviceInfo(
      @RequestParam Long device_id, @RequestParam Integer status, HttpServletRequest request) {
    String token = request.getHeader("token");
    if (token == null || !token.equals(jsSystemToken)) {
      throw new BizException("无效token！请求失败！");
    }
    if (Objects.isNull(device_id) || Objects.isNull(status)) {
      throw new BizException("参数异常！");
    }
    SsDeviceDTO deviceDto = new SsDeviceDTO();
    deviceDto.setId(device_id);
    // 更新为欠费状态
    deviceDto.setDeviceArrears(status.byteValue());
    ssDeviceService.settlementUpdateDeviceInfo(deviceDto);
    return R.ok("更新成功");
  }

  /**
   * 根据教室id获取所关联的设备
   *
   * <AUTHOR>
   * @date 2025/4/14 9:51
   * @param classRoomId
   * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO>
   */
  @GetMapping(value = "/getDeviceByClassRoomId/{classRoomId}")
  @Inner(value = false)
  public R<SsDeviceVO> getDeviceByClassRoomId(@PathVariable Long classRoomId) {
    SsDeviceVO deviceVO = new SsDeviceVO();
    SsDevice ssDevice = ssDeviceService.getDeviceByClassRoomId(classRoomId);
    if (Objects.nonNull(ssDevice)) {
      BeanUtils.copyProperties(ssDevice, deviceVO);
    }
    return R.ok(deviceVO);
  }
}
