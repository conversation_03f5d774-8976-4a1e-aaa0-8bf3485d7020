package com.yuedu.ydsf.eduConnect.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yuedu.ydsf.eduConnect.service.InteriorsXiaogjService;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDetailSyncService;
import com.yuedu.ydsf.eduConnect.service.RecordVideoTaskNewService;
import com.yuedu.ydsf.eduConnect.service.SsXiaogjLogService;
import jakarta.annotation.Resource;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName JobXgjSystemService
 * @Description 同步小管家信息
 * <AUTHOR>
 * @Date 2024/9/21 10:54
 * @Version v0.0.1
 */
@Slf4j
@Service
public class EduConnectSystemJobService {

    @Resource
    private RecordVideoTaskNewService recordVideoTaskNewService;

    @Resource
    private InteriorsXiaogjService interiorsXiaogjService;

    @Resource
    private SsXiaogjLogService ssXiaogjLogService;

    @Resource
    private LiveRoomPlanDetailSyncService liveRoomPlanDetailSyncService;

    /**
     * 从校管家批量获取员工信息并插入本地表
     */
    @XxlJob("generateRecordVideoTask")
    public void getAndInsertEmployee() {
        log.info("生成录课任务开始");
        recordVideoTaskNewService.saveRecordVideoTask();
        log.info("生成录课任务结束");
    }

    /**
     * 同步校管家校区
     * <AUTHOR>
     * @date 2025/2/11 9:38
     */
    @XxlJob("syncXgjCampusSchedule")
    public void syncXgjCampusSchedule() throws IOException {
        log.info("同步校管家校区数据开始");
        interiorsXiaogjService.syncXgjCampusSchedule();
        log.info("同步校管家校区数据结束");
    }

    /**
     * 同步校管家教室
     * <AUTHOR>
     * @date 2025/2/11 9:38
     */
    @XxlJob("syncXgjClassRoomSchedule")
    public void syncXgjClassRoomSchedule() throws IOException {
        log.info("同步校管家教室数据开始");
        interiorsXiaogjService.syncXgjClassRoomSchedule();
        log.info("同步校管家教室数据结束");
    }

    /**
     * 同步校管家主讲老师
     * <AUTHOR>
     * @date 2025/2/11 9:38
     */
    @XxlJob("syncXgjLecturerSchedule")
    public void syncXgjLecturerSchedule() throws IOException {
        log.info("同步校管家主讲老师数据开始");
        interiorsXiaogjService.syncXgjLecturerSchedule();
        log.info("同步校管家主讲老师数据结束");
    }

    /**
     * 同步校管家班级排课异常数据再次推送
     * @return void
     * <AUTHOR>
     * @date 2025/2/24 14:40
     */
    @XxlJob("syncFailXiaogjClassCourseSchedule")
    public void syncFailXiaogjClassCourseSchedule() {

        // 参数示例: requestId=1&responseCode=400
        String param = XxlJobHelper.getJobParam();
        log.info("同步校管家班级排课异常数据再次推送开始, 自定义请求参数为{}", param);
        ssXiaogjLogService.syncFailXiaogjClassCourseSchedule(param);
        log.info("同步校管家班级排课异常数据再次推送结束");
    }

  /**
   * 同步更新直播间排期到门店约课课表
   *
   * <AUTHOR>
   * @date 2024/12/08
   * @return void
   */
  @XxlJob("syncLiveRoomPlanDetailToTimetable")
  public void syncLiveRoomPlanDetailToTimetable() {
    log.info("开始执行同步直播间排期到门店约课课表定时任务");

    try {
      liveRoomPlanDetailSyncService.syncLiveRoomPlanDetailToTimetable();
      XxlJobHelper.log("同步直播间排期到门店约课课表定时任务执行成功！");
      XxlJobHelper.handleSuccess();
    } catch (Exception e) {
      log.error("同步直播间排期到门店约课课表定时任务执行失败", e);
      XxlJobHelper.log("同步直播间排期到门店约课课表定时任务执行失败：" + e.getMessage());
      XxlJobHelper.handleFail();
    }
  }
}