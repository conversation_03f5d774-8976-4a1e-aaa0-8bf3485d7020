package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanDraftQuery;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDraftVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDraft;
import java.util.ArrayList;
import java.util.List;

/**
 * 直播间计划草稿表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-29 08:28:00
 */
public interface LiveRoomPlanDraftService extends IService<LiveRoomPlanDraft> {

    /**
     * 查询直播间计划草稿表
     *
     * @return List<LiveRoomPlanDraftVO>
     */
    LiveRoomPlanDraftVO getDetails(Integer Id);

    /**
     * 分页查询
     *
     * @param page 分页条件
     * @param
     * @return page
     */
    Page<LiveRoomPlanDraftVO> getPage(Page page, LiveRoomPlanDraftQuery liveRoomPlanDraftQuery);

  /**
   * 新增加直播间计划
   *
   * <AUTHOR>
   * @date 2024/12/4 8:53
   * @param liveRoomPlanDraftQuery
   * @return void
   */
  void savePlan(LiveRoomPlanDraftQuery liveRoomPlanDraftQuery);

  /**
   * 修改直播间计划
   *
   * <AUTHOR>
   * @date 2024/12/4 9:06
   * @param liveRoomPlanDraftQuery
   * @return void
   */
  void editPlan(LiveRoomPlanDraftQuery liveRoomPlanDraftQuery);

  /**
   * 删除直播间计划
   *
   * <AUTHOR>
   * @date 2024/12/4 10:02
   * @param list
   * @return void
   */
  void deletePlan(ArrayList<Long> list);

  /**
   * 发布直播间计划
   * @param ids 计划ID列表
   * @param forcePublish 是否强制发布
   */
  void publish(List<Long> ids, Boolean forcePublish);
}
