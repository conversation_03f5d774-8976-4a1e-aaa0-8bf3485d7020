package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 排课规则表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:29:07
 */
@Data
@TableName("ss_course_schedule_rule")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "排课规则表实体类")
public class SsCourseScheduleRule extends Model<SsCourseScheduleRule> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 直播设备ID
	*/
    @Schema(description="直播设备ID")
    private Long deviceId;

	/**
	* 排课ID
	*/
    @Schema(description="排课ID")
    private Long courseScheduleId;

	/**
	* 上课周几(字典类型:week_type)
	*/
    @Schema(description="上课周几(字典类型:week_type)")
    private Integer attendClassWeek;

	/**
	* 上课开始时间（HH:mm）
	*/
    @Schema(description="上课开始时间（HH:mm）")
    private Object attendClassStartTime;

	/**
	* 上课结束时间（HH:mm）
	*/
    @Schema(description="上课结束时间（HH:mm）")
    private Object attendClassEndTime;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑者")
    private String modifer;

	/**
	* 删除标识
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识")
    private Integer delFlag;
}
