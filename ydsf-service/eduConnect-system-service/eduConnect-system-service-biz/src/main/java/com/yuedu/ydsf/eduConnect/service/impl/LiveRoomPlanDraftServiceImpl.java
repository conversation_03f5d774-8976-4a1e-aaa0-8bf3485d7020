package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.AsyncHelper;
import com.yuedu.ydsf.common.core.util.UserContextHolder;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.PlanStatusEnum;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanDraftQuery;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailDraftVO;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDraftVO;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomScheduleConflictVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDraftVO;
import com.yuedu.ydsf.eduConnect.config.SsProperty;
import com.yuedu.ydsf.eduConnect.constant.LiveRoomPlanVersionConstants;
import com.yuedu.ydsf.eduConnect.constant.LiveRoomStatusTypeEnum;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersionNo;
import com.yuedu.ydsf.eduConnect.manager.LiveRoomPlanDraftManager;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanVersionMapper;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDetailDraftService;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDetailVersionService;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDraftService;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanVersionNoService;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanVersionService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDraftService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 直播间计划草稿表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-29 08:28:00
 */
@Slf4j
@Service
@AllArgsConstructor
public class LiveRoomPlanDraftServiceImpl extends
    ServiceImpl<LiveRoomPlanDraftMapper, LiveRoomPlanDraft> implements LiveRoomPlanDraftService {

    private final LiveRoomPlanDetailDraftService liveRoomPlanDetailDraftService;

    private final TeachingPlanDraftService teachingPlanDraftService;

    private final LiveRoomPlanDetailVersionService liveRoomPlanDetailVersionService;

    private final LiveRoomPlanVersionNoService versionNoService;

    private final LiveRoomPlanVersionService liveRoomPlanVersionService;

    private final LiveRoomPlanDraftManager liveRoomPlanDraftManager;

    private final LiveRoomPlanVersionMapper liveRoomPlanVersionMapper;

    private final LiveRoomPlanDetailDraftMapper liveRoomPlanDetailDraftMapper;

    private final SsProperty ssProperty;

    private final LiveRoomPlanDraftMapper liveRoomPlanDraftMapper;


    /**
     * 通过条件查询直播间计划草稿表
     *
     * @return LiveRoomPlanVO
     */
    @Override
    public LiveRoomPlanDraftVO getDetails(Integer id) {
        LiveRoomPlanDraftVO detailsVO = new LiveRoomPlanDraftVO();
        LiveRoomPlanDraft entity = getOne(Wrappers.lambdaQuery(LiveRoomPlanDraft.class)
            .eq(LiveRoomPlanDraft::getId, id)
        );
        if (ObjectUtil.isEmpty(entity)) {
            throw new BizException(" 该id没有对应直播间计划");
        }
        //查询直播数
        List<Long> ids = List.of(id.longValue());
        Map<Long, Integer> planCountMap = liveRoomPlanDetailDraftService.countPlans(ids);
        BeanUtil.copyProperties(entity, detailsVO);
        List<LiveRoomPlanDetailDraftVO> planDetails = liveRoomPlanDetailDraftService.listPlans(id);
        if (ObjectUtil.isNotEmpty(planDetails)) {
            detailsVO.setPlanDetails(planDetails);
            detailsVO.setPlanCount(planCountMap.getOrDefault(id.longValue(), 0));
        }

        return detailsVO;
    }

    /**
     * 分页查询
     *
     * @param pageRequest 分页
     * @return page
     */
    @Override
    public Page<LiveRoomPlanDraftVO> getPage(Page pageRequest, LiveRoomPlanDraftQuery liveRoomPlanDraftQuery) {
        Page<LiveRoomPlanDraft> page = this.page(pageRequest,
            Wrappers.lambdaQuery(LiveRoomPlanDraft.class)
                .eq(ObjectUtil.isNotEmpty(liveRoomPlanDraftQuery.getLiveRoomId()), LiveRoomPlanDraft::getLiveRoomId, liveRoomPlanDraftQuery.getLiveRoomId())
                .likeRight(StringUtils.isNotBlank(liveRoomPlanDraftQuery.getPlanName()), LiveRoomPlanDraft::getPlanName, liveRoomPlanDraftQuery.getPlanName())
                .orderByAsc(LiveRoomPlanDraft::getPlanStatus)
                .orderByDesc(LiveRoomPlanDraft::getUpdateTime));

        List<Long> ids = page.getRecords().stream().map(LiveRoomPlanDraft::getId).toList();
        LocalDateTime now = LocalDateTime.now();
        Map<Long, LocalDateTime> publishTimeMap;
        Map<Long, String> publishMap;
        Map<Long, Long> planDetailsMap;
        Map<Long, Integer> planCountMap;
        Map<Long, Integer> planEndMap;
        if (ObjectUtil.isNotEmpty(ids)) {
            List<TeachingPlanDraftVO> planDetailsList = teachingPlanDraftService.listByIds(ids);
            planDetailsMap = planDetailsList.stream()
                .collect(Collectors.toMap(
                    TeachingPlanDraftVO::getLiveRoomPlanId,
                    TeachingPlanDraftVO::getId,
                    (first, second) -> first)
                );
            planCountMap = liveRoomPlanDetailDraftService.countPlans(ids);
            List<LiveRoomPlanVersion> publishList = liveRoomPlanVersionMapper.selectList(
                Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                    .in(LiveRoomPlanVersion::getPlanId, ids)
                    .eq(LiveRoomPlanVersion::getOnlineVersion, YesNoEnum.YES.getCode()));
            publishTimeMap = publishList.stream().collect(Collectors.toMap(
                LiveRoomPlanVersion::getPlanId,
                LiveRoomPlanVersion::getCreateTime
            ));
            publishMap = publishList.stream().collect(Collectors.toMap(
                LiveRoomPlanVersion::getPlanId,
                tp -> {
                    String createBy = tp.getCreateBy();
                    if (createBy == null) {
                        createBy = "";
                    }
                    return createBy;
                }
            ));
            List<LiveRoomPlanDetailDraft> detailList = liveRoomPlanDetailDraftMapper.selectList(
                new QueryWrapper<LiveRoomPlanDetailDraft>()
                    .select("plan_id", "MAX(class_end_date_time) as class_end_date_time")
                    .in("plan_id", ids)
                    .groupBy("plan_id"));
            planEndMap = detailList.stream()
                .collect(Collectors.toMap(
                    LiveRoomPlanDetailDraft::getPlanId,
                    draft -> draft.getClassEndDateTime().isBefore(now) ? 1 : 0
                ));
        } else {
            planCountMap = null;
            planDetailsMap = null;
            publishTimeMap = null;
            publishMap = null;
            planEndMap = null;
        }

        Page<LiveRoomPlanDraftVO> resultPage = new Page<>(page.getCurrent(), page.getSize(),
            page.getTotal());
        List<LiveRoomPlanDraftVO> resultRecords = page.getRecords().stream()
            .map(entity -> {
                LiveRoomPlanDraftVO vo = new LiveRoomPlanDraftVO();
                BeanUtil.copyProperties(entity, vo);
                vo.setHaveTeaching(planDetailsMap.containsKey(entity.getId()) ? 1 : 0);
                vo.setTeachingId(planDetailsMap.getOrDefault(entity.getId(), 0L));
                vo.setPlanCount(planCountMap.getOrDefault(entity.getId(), 0));
                vo.setPublish(publishMap.getOrDefault(entity.getId(), ""));
                if (ObjectUtil.isNotEmpty(planEndMap.get(entity.getId()))) {
                    vo.setPlanEnd(planEndMap.get(entity.getId()));
                }

                if (ObjectUtil.isNotEmpty(publishTimeMap.get(entity.getId()))) {
                    vo.setPublishTime(publishTimeMap.get(entity.getId()));
                }
                return vo;
            }).toList();
        resultPage.setRecords(resultRecords);
        return resultPage;
    }

    /**
     * 新增加直播间计划
     *
     * @param liveRoomPlanDraftQuery
     * @return void
     * <AUTHOR>
     * @date 2024/12/4 8:53
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePlan(LiveRoomPlanDraftQuery liveRoomPlanDraftQuery) {
        log.info("开始新增直播间计划, param={}", liveRoomPlanDraftQuery);
        LiveRoomPlanDraft liveRoomPlanDraft = new LiveRoomPlanDraft();
        BeanUtil.copyProperties(liveRoomPlanDraftQuery, liveRoomPlanDraft);
        liveRoomPlanDraftMapper.insert(liveRoomPlanDraft);
    }

    /**
     * 修改直播间计划
     *
     * @param liveRoomPlanDraftQuery
     * @return void
     * <AUTHOR>
     * @date 2024/12/4 9:07
     */
    @Override
    public void editPlan(LiveRoomPlanDraftQuery liveRoomPlanDraftQuery) {
        log.info("开始修改直播间计划, param={}", liveRoomPlanDraftQuery);
        // 校验直播间计划有没有关联教学计划
        // liveRoomPlanDraftManager.checkAssociatedTeachingPlan(liveRoomPlanDraftQuery.getId());
        // 已结束的直播间计划不可编辑
        LiveRoomPlanDraft roomPlanDraft = getById(liveRoomPlanDraftQuery.getId());
        if (Objects.isNull(roomPlanDraft)) {
            log.error("直播间计划不存在, id={}", liveRoomPlanDraftQuery.getId());
            throw new BizException("直播间计划不存在");
        }
        // 检查计划是否已结束
        liveRoomPlanDraftManager.checkPlanEndStatus(roomPlanDraft.getId());
        //门店已约课的直播间计划不可编辑 2025年5月8日16:10:30去除验证
        //        long bookCount = teachingPlanDraftService.countByLiveRoomPlanId(
        //            liveRoomPlanDraftQuery.getId());
        //        if (bookCount > 0) {
        //            log.error("直播间计划关联的教学计划已被门店约课, id={}",
        //                liveRoomPlanDraftQuery.getId());
        //            throw new BizException("直播间计划关联的教学计划已被门店约课,不可编辑");
        //        }

        // 修改完更新成未发布状态
        liveRoomPlanDraftMapper.update(
            Wrappers.lambdaUpdate(LiveRoomPlanDraft.class)
                .eq(LiveRoomPlanDraft::getId, liveRoomPlanDraftQuery.getId())
                .set(
                    Objects.nonNull(liveRoomPlanDraftQuery.getPlanName()),
                    LiveRoomPlanDraft::getPlanName,
                    liveRoomPlanDraftQuery.getPlanName())
                .set(
                    Objects.nonNull(liveRoomPlanDraftQuery.getStage()),
                    LiveRoomPlanDraft::getStage,
                    liveRoomPlanDraftQuery.getStage())
                .set(
                    Objects.nonNull(liveRoomPlanDraftQuery.getLiveRoomId()),
                    LiveRoomPlanDraft::getLiveRoomId,
                    liveRoomPlanDraftQuery.getLiveRoomId())
                .set(LiveRoomPlanDraft::getPublisherName, StringUtils.EMPTY)
                .set(LiveRoomPlanDraft::getPlanStatus, PlanStatusEnum.STATUS_ENUM_0.code)
                .set(LiveRoomPlanDraft::getUpdateBy, SecurityUtils.getUser().getName())
                .set(LiveRoomPlanDraft::getUpdateTime, LocalDateTime.now()));
    }

    /**
     * 处理计划版本信息
     *
     * @param planId 计划ID
     * @return void
     */
    private Integer handlePlanVersion(Long planId) {
        log.info("开始处理计划版本信息, planId={}", planId);
        try {
            LiveRoomPlanVersionNo planVersionNo = new LiveRoomPlanVersionNo();
            planVersionNo.setPlanId(planId);
            versionNoService.save(planVersionNo);
            log.info("直播间计划信息, planId={},编辑之后版本为 {}", planId, planVersionNo.getId());

            // 查询计划信息
            LiveRoomPlanDraft planDraft = getById(planId);
            if (planDraft == null) {
                log.error("未找到计划信息, planId={}", planId);
                throw new BizException("未找到计划信息");
            }
            // 如果此次是发布的操作，则将此planId下以往的修改为不是线上版本
            liveRoomPlanVersionService.update(
                Wrappers.lambdaUpdate(LiveRoomPlanVersion.class)
                    .eq(LiveRoomPlanVersion::getPlanId, planId)
                    .eq(LiveRoomPlanVersion::getOnlineVersion, 1)
                    .set(LiveRoomPlanVersion::getOnlineVersion, 0));

            // 保存计划版本信息
            LiveRoomPlanVersion planVersion = new LiveRoomPlanVersion();
            BeanUtil.copyProperties(planDraft, planVersion);
            planVersion.setId(null);
            planVersion.setCreateBy(null);
            planVersion.setCreateTime(null);
            planVersion.setUpdateBy(null);
            planVersion.setUpdateTime(null);
            planVersion.setVersion(planVersionNo.getId().intValue());
            planVersion.setPlanId(planId);
            planVersion.setOnlineVersion(1);
            liveRoomPlanVersionService.save(planVersion);
            log.info("处理计划版本信息完成");
            return planVersion.getVersion();
        } catch (Exception e) {
            e.getStackTrace();
            log.error("处理计划版本信息异常", e);
            throw new BizException("处理计划版本信息失败:" + e.getMessage());
        }
    }

    /**
     * 删除直播间计划
     *
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2024/12/4 10:02
     */
    @Override
    public void deletePlan(ArrayList<Long> list) {
        log.info("开始删除直播间计划, planIds={}", list);
        try {
            if (CollectionUtils.isEmpty(list)) {
                log.error("删除直播间计划参数为空");
                throw new BizException("参数不能为空");
            }

            // 并行检查是否存在关联的教学计划
            List<CompletableFuture<Void>> futures =
                list.stream()
                    .map(
                        planId ->
                            AsyncHelper.runAsync(
                                () -> {
                                    List<LiveRoomPlanDetailDraft> roomPlanDetailDrafts =
                                        liveRoomPlanDetailDraftService.list(
                                            Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                                                .eq(LiveRoomPlanDetailDraft::getPlanId, planId)
                                                .orderByDesc(
                                                    LiveRoomPlanDetailDraft::getLessonOrder));
                                    if (CollectionUtils.isNotEmpty(roomPlanDetailDrafts)) {
                                        LiveRoomPlanDetailDraft liveRoomPlanDetailDraft =
                                            roomPlanDetailDrafts.get(0);
                                        if (liveRoomPlanDetailDraft
                                            .getClassEndDateTime()
                                            .isBefore(LocalDateTime.now())) {
                                            log.error(
                                                "直播间计划已结束, planId={}, endTime={}",
                                                planId,
                                                liveRoomPlanDetailDraft.getClassEndDateTime());
                                            throw new BizException("该直播间计划已结束,不可操作");
                                        }
                                    }
                                    liveRoomPlanDraftManager.checkAssociatedTeachingPlan(planId);
                                }))
                    .toList();

            // 等待所有检查完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 批量逻辑删除
            boolean success =
                liveRoomPlanDraftMapper.update(
                    Wrappers.lambdaUpdate(LiveRoomPlanDraft.class)
                        .in(LiveRoomPlanDraft::getId, list)
                        .set(LiveRoomPlanDraft::getUpdateBy, SecurityUtils.getUser().getName())
                        .set(LiveRoomPlanDraft::getDelFlag, DelFlagEnum.DELFLAG_1.code)
                        .set(LiveRoomPlanDraft::getUpdateTime, LocalDateTime.now())) > 0;

            // 批量删除已发布的直播间发布表
            liveRoomPlanVersionService.update(
                Wrappers.lambdaUpdate(LiveRoomPlanVersion.class)
                    .in(LiveRoomPlanVersion::getPlanId, list)
                    .set(LiveRoomPlanVersion::getUpdateBy, SecurityUtils.getUser().getName())
                    .set(LiveRoomPlanVersion::getDelFlag, DelFlagEnum.DELFLAG_1.code)
                    .set(LiveRoomPlanVersion::getUpdateTime, LocalDateTime.now()));

            if (!success) {
                log.error("删除直播间计划失败");
                throw new BizException("删除失败");
            }
            log.info("删除直播间计划成功, planIds={}", list);
        } catch (BizException e) {
            log.warn("删除直播间计划明细业务异常", e);
            throw e;
        } catch (Exception e) {
            e.getStackTrace();
            log.error("删除直播间计划异常", e);
            throw new BizException("删除直播间计划失败:" + e.getMessage());
        }
    }

    /**
     * 发布直播间计划
     *
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2024/12/5 13:56
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(List<Long> list, Boolean forcePublish) {
        log.info("开始发布直播间计划, planIds={}", list);
        if (CollectionUtils.isEmpty(list) || list.size() > 1) {
            log.error("发布直播间计划参数不合法, planIds={}", list);
            throw new BizException("参数不合法,每次只允许发布一个直播间计划");
        }
        Long planId = list.get(0);
        try {
            if (!forcePublish) {
                // 检查排期冲突
                List<LiveRoomScheduleConflictVO> conflicts = checkScheduleConflicts(list);
                if (!conflicts.isEmpty()) {
                    // 将冲突信息格式化为提示消息
                    String conflictMsg = formatConflictMessage(conflicts);
                    throw new BizException(LiveRoomStatusTypeEnum.CONFLICT_CODE,
                        conflictMsg); // 自定义异常，携带冲突信息
                }
            }
            // 并行校验每个计划下是否存在有效的排期明细
            // 校验当前是否是已发布状态
            LiveRoomPlanDraft roomPlanDraft = getById(planId);
            if (Objects.nonNull(roomPlanDraft)
                && Objects.equals(roomPlanDraft.getPlanStatus(),
                PlanStatusEnum.STATUS_ENUM_1.code)) {
                throw new BizException("当前计划已是发布状态!");
            }
            // 查询计划下的有效排期明细
            List<LiveRoomPlanDetailDraft> details =
                liveRoomPlanDetailDraftService.list(
                    Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                        .eq(LiveRoomPlanDetailDraft::getPlanId, planId));

            if (CollectionUtils.isEmpty(details)) {
                log.error("直播间计划下不存在有效的排期明细, planId={}", planId);
                throw new BizException("计划下不存在有效的排期明细,不允许发布!");
            }
            // 校验直播间计划明细中是否有过期的排期
            checkHasExpiredDetail(details);


            // 是首次发布还是二次发布
            boolean isEdit = liveRoomPlanVersionService.count(
                Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                    .eq(LiveRoomPlanVersion::getPlanId, planId)) > 0;

            // 批量修改计划状态为已发布
            boolean updateSuccess =
                liveRoomPlanDraftMapper.update(
                    Wrappers.lambdaUpdate(LiveRoomPlanDraft.class)
                        .in(LiveRoomPlanDraft::getId, list)
                        .set(LiveRoomPlanDraft::getPlanStatus, PlanStatusEnum.STATUS_ENUM_1.code)
                        .set(LiveRoomPlanDraft::getPublisherName,
                            SecurityUtils.getUser().getName())) > 0;

            if (!updateSuccess) {
                log.error("修改计划发布状态失败");
                throw new BizException("发布失败");
            }

            Integer newPlanVersion = handlePlanVersion(planId);
            // 查询计划明细
            List<LiveRoomPlanDetailDraft> detailDrafts =
                liveRoomPlanDetailDraftService.list(
                    Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                        .eq(LiveRoomPlanDetailDraft::getPlanId, planId)
                        .orderByAsc(LiveRoomPlanDetailDraft::getLessonOrder));

            // 保存明细版本信息
            if (CollectionUtils.isNotEmpty(detailDrafts)) {
                List<LiveRoomPlanDetailVersion> detailVersions =
                    detailDrafts.stream()
                        .map(
                            draft -> {
                                LiveRoomPlanDetailVersion version = new LiveRoomPlanDetailVersion();
                                BeanUtil.copyProperties(draft, version);
                                version.setId(null);
                                version.setVersion(newPlanVersion);
                                return version;
                            })
                        .toList();

                liveRoomPlanDetailVersionService.saveBatch(detailVersions);
            }
            // 直播间计划二次发布需要重新去匹配已关联教学计划对应的明细对应
            if (isEdit){
                teachingPlanDraftService.rematchCoursewareWithLessonPlans(planId, null);
            }
            log.info("发布直播间计划成功, planIds={}", list);
        } catch (BizException bizException) {
            throw bizException;
        } catch (Exception e) {
            e.getStackTrace();
            log.error("发布直播间计划异常", e);
            throw new BizException("发布直播间计划失败:" + e.getMessage());
        }
    }

    /**
     * 校验明细中是否存在过期的排期明细
     *
     * @param details
     * @return void
     * <AUTHOR>
     * @date 2025/1/20 10:03
     */
    private void checkHasExpiredDetail(List<LiveRoomPlanDetailDraft> details) {
        if (!ssProperty.getJwCheckPlanDateEnable()) {
            return;
        }
        // 检查是否存在过期的排期
        LocalDateTime now = LocalDateTime.now();
        List<LiveRoomPlanDetailDraft> expiredDetails =
            details.stream().filter(detail -> detail.getClassStartDateTime().isBefore(now))
                .toList();

        if (!expiredDetails.isEmpty()) {
            log.warn("存在过期的排期明细,expiredDetails={}", expiredDetails);
            throw new BizException("直播间计划已开始，不允许操作!");
        }
    }

    /**
     * 检查直播间排期冲突
     */
    private List<LiveRoomScheduleConflictVO> checkScheduleConflicts(List<Long> planIds) {
        List<LiveRoomScheduleConflictVO> conflicts = new ArrayList<>();

        for (Long planId : planIds) {
            // 获取直播间计划信息
            LiveRoomPlanDraft currentPlan = getById(planId);
            if (currentPlan == null) {
                continue;
            }
            // 查询当前直播间下得所有的对应的直播间计划
            List<LiveRoomPlanDraft> liveRoomPlans =
                list(
                    Wrappers.lambdaQuery(LiveRoomPlanDraft.class)
                        .eq(LiveRoomPlanDraft::getLiveRoomId, currentPlan.getLiveRoomId()));
            // 获取当前计划的所有排期明细
            List<LiveRoomPlanDetailDraft> currentDetails =
                liveRoomPlanDetailDraftService.list(
                    Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                        .eq(LiveRoomPlanDetailDraft::getPlanId, planId));

            for (LiveRoomPlanDetailDraft detail : currentDetails) {
                // 查询同一直播间在同一时间段是否已有其他排期
                List<LiveRoomPlanDetailDraft> conflictDetails =
                    liveRoomPlanDetailDraftMapper.selectList(
                        Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                            .in(
                                LiveRoomPlanDetailDraft::getPlanId,
                                liveRoomPlans.stream().map(LiveRoomPlanDraft::getId).toList())
                            .lt(
                                LiveRoomPlanDetailDraft::getClassStartDateTime,
                                detail.getClassEndDateTime())
                            .gt(
                                LiveRoomPlanDetailDraft::getClassEndDateTime,
                                detail.getClassStartDateTime()));

                if (CollectionUtils.size(conflictDetails) > 1) {
                    // 格式化时间范围
                    String timeRange =
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
                            .format(detail.getClassStartDateTime())
                            + "-"
                            + DateTimeFormatter.ofPattern("HH:mm")
                            .format(detail.getClassEndDateTime());

                    conflicts.add(
                        LiveRoomScheduleConflictVO.builder()
                            .conflictTime(detail.getClassStartDateTime())
                            .timeRange(timeRange)
                            .build());
                }
            }
        }

        return conflicts;
    }

    /**
     * 格式化冲突信息
     */
    private String formatConflictMessage(List<LiveRoomScheduleConflictVO> conflicts) {
        StringBuilder msg = new StringBuilder("检测到以下排期冲突：\n");
        for (LiveRoomScheduleConflictVO conflict : conflicts) {
            msg.append(String.format("【%s】排期冲突\n", conflict.getTimeRange()));
        }
        msg.append("是否继续发布？");
        return msg.toString();
    }
}
