package com.yuedu.ydsf.eduConnect.manager.impl;

import com.yuedu.ydsf.eduConnect.api.constant.OperateTypeEnum;
import com.yuedu.ydsf.eduConnect.entity.SsOperateLog;
import com.yuedu.ydsf.eduConnect.manager.SsOperateLogManager;
import com.yuedu.ydsf.eduConnect.mapper.SsOperateLogMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 操作记录表 公共服务实现类
 * <AUTHOR>
 * @date 2024/11/4 15:48
 */
@Slf4j
@Component
@AllArgsConstructor
public class SsOperateLogManagerImpl implements SsOperateLogManager {

    private final SsOperateLogMapper ssOperateLogMapper;

    /**
     * 保存操作记录
     * @param category 操作类别
     * @param type 操作类型
     * @param detail 日志详情
     * @param operateId 操作人ID
     * @param createBy 操作人账号
     * @param operateName 操作人中文名
     * @param objectId 业务ID
     * @return void
     * <AUTHOR>
     * @date 2024/11/4 15:59
     */
    @Override
    public void saveOperateLog(Integer category,
        Integer type,
        String detail,
        Long operateId,
        String createBy,
        String operateName,
        Long objectId) {

        SsOperateLog ssOperateLog = new SsOperateLog();
        ssOperateLog.setObjectId(objectId);
        ssOperateLog.setCategory(category);
        ssOperateLog.setType(type);
        ssOperateLog.setDetail(detail);
        ssOperateLog.setOperateId(operateId);
        ssOperateLog.setCreateBy(createBy);
        ssOperateLog.setOperateName(operateName);
        ssOperateLogMapper.insert(ssOperateLog);
    }


}
