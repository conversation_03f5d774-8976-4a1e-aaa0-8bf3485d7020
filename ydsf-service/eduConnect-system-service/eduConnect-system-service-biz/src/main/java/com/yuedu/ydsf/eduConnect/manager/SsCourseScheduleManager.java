package com.yuedu.ydsf.eduConnect.manager;

import com.yuedu.ydsf.eduConnect.entity.SsClass;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.bo.SsClassTimeBO;
import java.util.List;

/**
 * @author: zhangchuanfu
 * @date: 2024/09/29
 **/
public interface SsCourseScheduleManager {

    /**
     * 创建排课信息成功MQ消息
     * @param courseScheduleId 排课ID
     */
    void sendCreatedCourseScheduleMessage(Long courseScheduleId);

    /**
     * 同步声网创建房间
     * @param courseScheduleId 排课ID
     */
    void syncAgoraRooms(Long courseScheduleId);

    /**
     * 同步校管家
     * @param courseScheduleId 排课ID
     */
    void syncXiaoGuanJia(Long courseScheduleId);

    /**
     *  报错创建课程时，同步校管家
     *
     * <AUTHOR>
     * @date 2025年01月14日 20时16分
     */
    void saveClassTimeStendsAndSyncXgj(SsClass ssClass, List<SsClassTimeBO> classTimes);

}
