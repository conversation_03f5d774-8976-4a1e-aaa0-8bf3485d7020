package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomStudentMapper;
import com.yuedu.ydsf.eduConnect.service.SsClassAuthRoomStudentService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 班级授权校区学生表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 09:56:19
 */
@Slf4j
@Service
public class SsClassAuthRoomStudentServiceImpl extends
    ServiceImpl<SsClassAuthRoomStudentMapper, SsClassAuthRoomStudent> implements
    SsClassAuthRoomStudentService {

    private final SsClassAuthRoomStudentMapper ssClassAuthRoomStudentMapper;

    public SsClassAuthRoomStudentServiceImpl(
        SsClassAuthRoomStudentMapper ssClassAuthRoomStudentMapper) {
        this.ssClassAuthRoomStudentMapper = ssClassAuthRoomStudentMapper;
    }

    @Override
    public List<SsClassAuthRoomStudent> listByClassIdAndCampusId(Long classId,
        Long campusId) {
        return ssClassAuthRoomStudentMapper.selectList(
            Wrappers.lambdaQuery(SsClassAuthRoomStudent.class)
                .eq(SsClassAuthRoomStudent::getClassId, classId)
                .eq(SsClassAuthRoomStudent::getCampusId, campusId));
    }
}
