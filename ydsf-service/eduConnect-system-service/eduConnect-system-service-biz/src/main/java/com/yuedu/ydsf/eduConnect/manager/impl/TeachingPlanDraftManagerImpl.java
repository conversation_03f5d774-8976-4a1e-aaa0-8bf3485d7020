package com.yuedu.ydsf.eduConnect.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.PlanStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.TeachingPlanOperateEnum;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO.EditLessonDTO;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanDraftQuery;
import com.yuedu.ydsf.eduConnect.config.SsProperty;
import com.yuedu.ydsf.eduConnect.constant.MqConstants;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.manager.TeachingPlanDraftManager;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailVersionMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanVersionMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDetailDraftMapper;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * TeachingPlanDraftManagerImpl
 *
 * @date 2024/12/6 11:12
 * @project @Title: TeachingPlanDraftManagerImpl.java
 */
@Slf4j
@Service
public class TeachingPlanDraftManagerImpl implements TeachingPlanDraftManager {

    private final RemoteLessonService remoteLessonService;

    private final RemoteCourseService remoteCourseService;

    private final LiveRoomPlanDetailDraftMapper liveRoomPlanDetailDraftMapper;

    private final TeachingPlanDetailDraftMapper teachingPlanDetailDraftMapper;

    private final LiveRoomPlanVersionMapper liveRoomPlanVersionMapper;
    private final LiveRoomPlanDetailVersionMapper liveRoomPlanDetailVersionMapper;

    @Value("${rocketmq.topics.teaching_plan_operate_topic}")
    private String teachingPlanOperateTopic;

    private final RocketMQClientTemplate rocketMQClientTemplate;

    private final SsProperty ssProperty;

    /**
     * 匹配选择的直播间计划与选择的课程的id到 教学计划草稿表
     *
     * @param teachingPlanDraftQuery
     * @return void
     * <AUTHOR>
     * @date 2024/12/6 11:19
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleTeachingDetailDraft(
        TeachingPlanDraftQuery teachingPlanDraftQuery, boolean isEdit) {
        log.info(
            "开始处理教学计划草稿,参数:teachingPlanDraftQuery={}", teachingPlanDraftQuery);
        try {
            if (teachingPlanDraftQuery.getLiveRoomPlanId() == null) {
                log.error("直播间排期计划ID不能为空:teachingPlanDraftQuery={}",
                    teachingPlanDraftQuery);
                throw new IllegalArgumentException("直播间排期计划ID不能为空");
            }
            //以下是更新教学计划草稿数据
            //查询直播间计划使用的版本
            LiveRoomPlanVersion liveRoomPlanVersion = liveRoomPlanVersionMapper.selectOne(
                Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                    .eq(LiveRoomPlanVersion::getPlanId, teachingPlanDraftQuery.getLiveRoomPlanId())
                    .eq(LiveRoomPlanVersion::getOnlineVersion, YesNoEnum.YES.getCode()));
            log.info("查询到直播间计划版本数据,planId={},版本号={}",
                teachingPlanDraftQuery.getLiveRoomPlanId(),
                liveRoomPlanVersion.getVersion());

            //已过期的直播间计划明细
            List<LiveRoomPlanDetailVersion> liveRoomPlanDetailVersionList = liveRoomPlanDetailVersionMapper.selectList(
                Wrappers.lambdaQuery(LiveRoomPlanDetailVersion.class)
                    .eq(LiveRoomPlanDetailVersion::getPlanId,
                        teachingPlanDraftQuery.getLiveRoomPlanId())
                    .eq(LiveRoomPlanDetailVersion::getVersion, liveRoomPlanVersion.getVersion())
                    .le(LiveRoomPlanDetailVersion::getClassStartDateTime, LocalDateTime.now()));
            log.info("查询到已过期的直播间计划明细数据,planId={},版本号={},数据条数={}",
                teachingPlanDraftQuery.getLiveRoomPlanId(), liveRoomPlanVersion.getVersion(),
                CollectionUtils.isEmpty(liveRoomPlanDetailVersionList) ? 0
                    : liveRoomPlanDetailVersionList.size());
            //已过期的直播间计划明细的lessonOrder，已过期的不删除
            List<Integer> lessonOrderList = liveRoomPlanDetailVersionList.stream()
                .map(LiveRoomPlanDetailVersion::getLessonOrder).toList();
            log.info("已过期的直播间计划明细的lessonOrder,lessonOrderList={}", lessonOrderList);
            if (isEdit){
                int deleteCount =
                    teachingPlanDetailDraftMapper.delete(
                        Wrappers.lambdaQuery(TeachingPlanDetailDraft.class)
                            .eq(TeachingPlanDetailDraft::getPlanId, teachingPlanDraftQuery.getId())
                            .notIn(CollectionUtils.isNotEmpty(lessonOrderList),
                                TeachingPlanDetailDraft::getLessonOrder, lessonOrderList));
                log.info("删除原教学计划草稿数据,planId={},删除条数={}", teachingPlanDraftQuery.getId(),
                    deleteCount);
            }

            // 查询直播间排期计划明细,排除已过期的明细
            List<LiveRoomPlanDetailDraft> detailDrafts =
                liveRoomPlanDetailDraftMapper.selectList(
                    Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                        .eq(
                            LiveRoomPlanDetailDraft::getPlanId,
                            teachingPlanDraftQuery.getLiveRoomPlanId())
                        .notIn(isEdit && CollectionUtils.isNotEmpty(lessonOrderList),
                            LiveRoomPlanDetailDraft::getLessonOrder, lessonOrderList)
                        .eq(LiveRoomPlanDetailDraft::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                        .orderByAsc(LiveRoomPlanDetailDraft::getLessonOrder));
            log.info(
                "查询到直播间排期计划明细数据,planId={},数据条数={}",
                teachingPlanDraftQuery.getLiveRoomPlanId(),
                CollectionUtils.isEmpty(detailDrafts) ? 0 : detailDrafts.size());

            if (CollectionUtils.isNotEmpty(detailDrafts)) {
                List<TeachingPlanDetailDraft> batchInsertList = new ArrayList<>(
                    detailDrafts.size());

                for (LiveRoomPlanDetailDraft detailDraft : detailDrafts) {
                    //如果直播间计划明细已过期，不再插入教学计划草稿
                    if ((ssProperty.getJwCheckPlanDateEnable() || Boolean.TRUE.equals(
                        teachingPlanDraftQuery.getIsEdit())) &&
                        detailDraft.getClassStartDateTime().isBefore(LocalDateTime.now())) {
                        log.info(
                            "直播间计划明细已过期,不再插入教学计划草稿,planId={},lessonOrder={}",
                            teachingPlanDraftQuery.getId(), detailDraft.getLessonOrder());
                        continue;
                    }
                    TeachingPlanDetailDraft teachingPlanDetailDraft = new TeachingPlanDetailDraft();
                    teachingPlanDetailDraft.setPlanId(teachingPlanDraftQuery.getId());
                    teachingPlanDetailDraft.setLectureId(teachingPlanDraftQuery.getLectureId());
                    teachingPlanDetailDraft.setLectureName(
                        teachingPlanDraftQuery.getLectureName());
                    teachingPlanDetailDraft.setLessonOrder(detailDraft.getLessonOrder());
                    teachingPlanDetailDraft.setTimeSlotId(detailDraft.getTimeSlotId());
                    batchInsertList.add(teachingPlanDetailDraft);
                }
                if (CollectionUtils.isEmpty(batchInsertList)) {
                    log.info("直播间计划明细全部已过期,不再插入教学计划草稿,planId={}",
                        teachingPlanDraftQuery.getId());
                }
                // 批量插入提升性能
                try {
                    teachingPlanDetailDraftMapper.insert(batchInsertList);
                    log.info(
                        "批量插入教学计划草稿数据成功,planId={},插入条数={}",
                        teachingPlanDraftQuery.getId(),
                        batchInsertList.size());
                } catch (Exception e) {
                    log.error("批量插入教学计划草稿数据异常,planId={}",
                        teachingPlanDraftQuery.getId(), e);
                    throw new BizException("保存教学计划草稿失败", e);
                }
            }
            log.info("处理教学计划草稿完成,planId={}", teachingPlanDraftQuery.getId());
        } catch (Exception e) {
            log.error(
                "处理教学计划草稿异常,参数:teachingPlanDraftQuery={}",
                teachingPlanDraftQuery,
                e);
            throw new BizException("处理教学计划草稿失败", e);
        }
    }

    /**
     * 远程调用获取课节信息
     *
     * @param coursePublishQuery
     * @return java.util.List<com.yuedu.teaching.vo.LessonVO>
     * <AUTHOR>
     * @date 2024/12/6 19:05
     */
    @Override
    public List<LessonVO> getPublishLessonList(CoursePublishQuery coursePublishQuery) {
        log.info("开始获取课节信息, 参数: coursePublishQuery={}", coursePublishQuery);
        try {
            // 远程调用获取课节信息
            R<List<LessonVO>> lessonResult = remoteLessonService.getPublishLessonList(
                coursePublishQuery);

            // 结果校验
            if (!lessonResult.isOk()) {
                log.error(
                    "获取课节信息失败, 远程调用返回错误, courseId: {}, errorMsg: {}",
                    coursePublishQuery.getCourseId(),
                    lessonResult.getMsg());
                throw new BizException("获取课节信息失败: " + lessonResult.getMsg());
            }

            List<LessonVO> lessonList = lessonResult.getData();
            if (CollectionUtils.isEmpty(lessonList)) {
                log.warn("未查询到课节信息, courseId: {}", coursePublishQuery.getCourseId());
                return Collections.emptyList();
            }

            log.info(
                "获取课节信息成功, courseId: {}, 课节数量: {}", coursePublishQuery.getCourseId(),
                lessonList.size());
            return lessonList;

        } catch (Exception e) {
            log.error(
                "获取课节信息异常, courseId: {}, error: {}", coursePublishQuery.getCourseId(),
                e.getMessage(), e);
            throw new BizException("获取课节信息失败", e);
        }
    }

    /**
     * 发送教学计划操作消息到MQ
     *
     * @param teachingPlanOperateMqDTO 教学计划操作DTO
     * <AUTHOR>
     * @date 2024/12/6 20:33
     */
    public void sendMqToRecordTask(TeachingPlanOperateMqDTO teachingPlanOperateMqDTO) {
        log.info("开始发送教学计划操作消息, 参数: teachingPlanOperateMqDTO={}",
            teachingPlanOperateMqDTO);

        try {
            // 构建消息
            Message<TeachingPlanOperateMqDTO> message =
                MessageBuilder.withPayload(teachingPlanOperateMqDTO).build();
            log.info("发送教学计划操作消息,topic={},message={}", teachingPlanOperateTopic, message);

            // 发送消息到RocketMQ
            rocketMQClientTemplate.syncSendDelayMessage(
                teachingPlanOperateTopic + MqConstants.COLON_TAG, message,
                Duration.of(MqConstants.THREE_SECOND,
                    ChronoUnit.SECONDS));
            log.info("教学计划操作消息发送成功,planId={}", teachingPlanOperateMqDTO.getPlanId());

        } catch (Exception e) {
            log.error("教学计划操作消息发送失败, teachingPlanOperateMqDTO={}, error:",
                teachingPlanOperateMqDTO, e);
            throw new BizException("发送教学计划操作消息失败", e);
        }
    }

    /**
     * 构建并发送教学计划操作MQ消息
     *
     * @param teachingPlanDraft 教学计划
     * @param operateType       操作类型
     * @param courseIdOld       原课程ID(可选)
     * @param lectureIdOld      原讲师ID(可选)
     * @param lessonIdOldList   原教学明细ID(可选)
     */
    @Async
    @Override
    public void buildAndSendOperateMqMessage(
        TeachingPlanDraft teachingPlanDraft,
        Integer operateType,
        Long courseIdOld,
        Long lectureIdOld,
        List<Long> lessonIdOldList,
        List<EditLessonDTO> editLessonDTOList) {
        log.info("开始构建并发送教学计划操作MQ消息, teachingPlanDraft={}, operateType={}",
            teachingPlanDraft,
            operateType);
        try {
            // 只有发布状态下才进行推送
            if (!Objects.equals(teachingPlanDraft.getPlanStatus(),
                PlanStatusEnum.STATUS_ENUM_1.code)) {
                log.info("教学计划未发布,不发送MQ消息, teachingPlanDraft={}", teachingPlanDraft);
                return;
            }
            TeachingPlanOperateMqDTO mqDTO = new TeachingPlanOperateMqDTO();
            mqDTO.setPlanId(teachingPlanDraft.getId());
            mqDTO.setCourseId(teachingPlanDraft.getCourseId());
            mqDTO.setLectureId(teachingPlanDraft.getLectureId());
            mqDTO.setLectureName(teachingPlanDraft.getLectureName());
            mqDTO.setOperateType(operateType);
            // 编辑操作时设置原值
            if (TeachingPlanOperateEnum.EDIT_PLAN.code.equals(operateType)) {
                mqDTO.setCourseIdOld(courseIdOld);
                mqDTO.setLectureIdOld(lectureIdOld);
                mqDTO.setLessonIdOldList(lessonIdOldList);
            }
            if (CollectionUtils.isNotEmpty(editLessonDTOList)) {
                mqDTO.setEditLessonDTOList(editLessonDTOList);
            }
            this.sendMqToRecordTask(mqDTO);
        } catch (Exception e) {
            log.error("发送教学计划操作MQ消息异常, planId={}", teachingPlanDraft.getId(), e);
            throw new BizException("发送MQ消息失败: " + e.getMessage());
        }
    }

    /**
     * 根据课程id获取最新的课程相关信息
     *
     * @param courseId
     * @return com.yuedu.teaching.vo.CourseVO
     * <AUTHOR>
     * @date 2024/12/17 9:08
     */
    @Override
    public CourseVO getCourseLatest(Long courseId) {

        log.info("开始获取最新课程信息, courseId: {}", courseId);

        try {
            if (Objects.isNull(courseId)) {
                log.error("课程ID不能为空");
                throw new IllegalArgumentException("课程ID不能为空");
            }

            // 构建查询参数
            CourseDTO courseDTO = new CourseDTO();
            courseDTO.setCourseIdList(Collections.singletonList(courseId.intValue()));

            // 远程调用获取课程信息
            R<List<CourseVO>> courseResult = remoteCourseService.getCourseListByIds(courseDTO);

            // 结果校验
            if (!courseResult.isOk()) {
                log.error("获取课程信息失败, courseId: {}, errorMsg: {}", courseId,
                    courseResult.getMsg());
            }

            List<CourseVO> courseList = courseResult.getData();
            if (CollectionUtils.isEmpty(courseList)) {
                log.warn("未查询到课程信息, courseId: {}", courseId);
                return null;
            }

            // 由于是根据ID查询,应该只返回一条记录
            CourseVO courseVO = courseList.get(0);
            log.info("获取课程信息成功, courseId: {}, courseName: {}", courseId,
                courseVO.getCourseName());

            return courseVO;

        } catch (Exception e) {
            log.error("获取课程信息异常, courseId: {}", courseId, e);
            throw new BizException("获取课程信息失败: " + e.getMessage());
        }
    }

    public TeachingPlanDraftManagerImpl(RemoteLessonService remoteLessonService,
        RemoteCourseService remoteCourseService,
        LiveRoomPlanDetailDraftMapper liveRoomPlanDetailDraftMapper,
        TeachingPlanDetailDraftMapper teachingPlanDetailDraftMapper,
        LiveRoomPlanVersionMapper liveRoomPlanVersionMapper,
        LiveRoomPlanDetailVersionMapper liveRoomPlanDetailVersionMapper,
        RocketMQClientTemplate rocketMQClientTemplate, SsProperty ssProperty) {
        this.remoteLessonService = remoteLessonService;
        this.remoteCourseService = remoteCourseService;
        this.liveRoomPlanDetailDraftMapper = liveRoomPlanDetailDraftMapper;
        this.teachingPlanDetailDraftMapper = teachingPlanDetailDraftMapper;
        this.liveRoomPlanVersionMapper = liveRoomPlanVersionMapper;
        this.liveRoomPlanDetailVersionMapper = liveRoomPlanDetailVersionMapper;
        this.rocketMQClientTemplate = rocketMQClientTemplate;
        this.ssProperty = ssProperty;
    }
}
