package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.query.SettlementCycleQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SettlementCycleVO;
import com.yuedu.ydsf.eduConnect.entity.SettlementCycle;
import java.util.List;

/**
 * 结算周期 服务类
 *
 * <AUTHOR>
 * @date 2025-04-21 15:28:49
 */
public interface SettlementCycleService extends IService<SettlementCycle> {

    /**
     * 查询所有结算周期
     * @return 结算周期列表
     */
    List<SettlementCycleVO> listAll();

    /**
     * 锁定或解锁考勤
     */
    void lockOrUnlockCheckin(SettlementCycle settlementCycleQuery);

    /**
     * 设置开始日期和截止日期
     */
    void updateById(SettlementCycleVO settlementCycleVO);

}
