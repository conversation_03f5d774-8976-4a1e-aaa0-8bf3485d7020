package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.permission.api.dto.SysUserDTO;
import com.yuedu.permission.api.feign.RemoteLeadTeacherService;
import com.yuedu.permission.api.vo.SysUserVO;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteCoursewareService;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.dto.CoursewareVersionDTO;
import com.yuedu.teaching.dto.LessonNameDTO;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.CoursewareVersionVO;
import com.yuedu.teaching.vo.LessonNameVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.CourseVodDisableEnum;
import com.yuedu.ydsf.eduConnect.api.dto.CourseVodDTO;
import com.yuedu.ydsf.eduConnect.api.query.CourseVodQuery;
import com.yuedu.ydsf.eduConnect.api.vo.CourseVodVO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseVodVideoVO;
import com.yuedu.ydsf.eduConnect.constant.CourseVodConstants;
import com.yuedu.ydsf.eduConnect.entity.CourseVod;
import com.yuedu.ydsf.eduConnect.entity.CourseVodVideo;
import com.yuedu.ydsf.eduConnect.entity.RecordVideoTask;
import com.yuedu.ydsf.eduConnect.entity.Recording;
import com.yuedu.ydsf.eduConnect.live.api.constant.LessonConstant;
import com.yuedu.ydsf.eduConnect.mapper.CourseVodMapper;
import com.yuedu.ydsf.eduConnect.mapper.CourseVodVideoMapper;
import com.yuedu.ydsf.eduConnect.mapper.RecordVideoTaskMapper;
import com.yuedu.ydsf.eduConnect.mapper.RecordingMapper;
import com.yuedu.ydsf.eduConnect.service.CourseVodService;
import java.time.LocalDateTime;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 点播课程库 服务类
 *
 * <AUTHOR>
 * @date 2024-12-02 11:05:41
 */
@Slf4j
@Service
@AllArgsConstructor
public class CourseVodServiceImpl extends ServiceImpl<CourseVodMapper, CourseVod> implements CourseVodService {
    private final RemoteCourseService remoteCourseService;

    private final RemoteLessonService remoteLessonService;

    private final RemoteLeadTeacherService remoteLeadTeacherService;

    private final RemoteCoursewareService remoteCoursewareService;

    private final RecordVideoTaskMapper recordVideoTaskMapper;

    private final CourseVodVideoMapper courseVodVideoMapper;

    private final RecordingMapper recordingMapper;

    /**
     * 获取点播课程库列表
     *
     * @param courseVodQuery 查询参数
     * @return List<CourseVodVO>
     */
    @Override
    public List<CourseVodVO> getCourseVodList(CourseVodQuery courseVodQuery) {
        List<CourseVod> courseVodList = baseMapper.selectList(Wrappers.lambdaQuery(CourseVod.class)
                .eq(ObjectUtil.isNotNull(courseVodQuery.getStage()),CourseVod::getStage, courseVodQuery.getStage())
                .eq(ObjectUtil.isNotNull(courseVodQuery.getCourseId()),CourseVod::getCourseId,courseVodQuery.getCourseId())
                .eq(ObjectUtil.isNotNull(courseVodQuery.getLessonOrder()),CourseVod::getLessonOrder,courseVodQuery.getLessonOrder())
            .orderByDesc(CourseVod::getUpdateTime));

        if(CollUtil.isEmpty(courseVodList)){
            return List.of();
        }

        // key courseVodId value recordVideoTaskId
        List<CourseVodVideo> courseVodVideoList = courseVodVideoMapper.selectList(Wrappers.lambdaQuery(CourseVodVideo.class)
            .in(CourseVodVideo::getCourseVodId, courseVodList.stream().map(CourseVod::getId).toList()));

        List<Long> recordVideoTaskIdList = courseVodVideoList
            .stream().map(CourseVodVideo::getRecordVideoTaskId).toList();

        List<RecordVideoTask> recordVideoTaskList = recordVideoTaskMapper.selectList(Wrappers.lambdaQuery(RecordVideoTask.class)
            .in(RecordVideoTask::getId, recordVideoTaskIdList));

        // key recordVideoTaskId
        Map<Long, RecordVideoTask> recordVideoTaskMap = recordVideoTaskList.stream()
            .collect(Collectors.toMap(RecordVideoTask::getId, recordVideoTask -> recordVideoTask));

        Map<Long, RecordVideoTask> courseVodIdToRecordVideoTaskMap =new HashMap<>();

        // key courseVodId
        courseVodVideoList.forEach(courseVodVideo -> courseVodIdToRecordVideoTaskMap.putIfAbsent(courseVodVideo.getCourseVodId()
            , recordVideoTaskMap.get(courseVodVideo.getRecordVideoTaskId())));

        List<CourseVodVO> courseVodVOList = BeanUtil.copyToList(courseVodList, CourseVodVO.class);

        courseVodVOList.forEach(courseVodVO -> {
            RecordVideoTask recordVideoTask = courseVodIdToRecordVideoTaskMap.get(
                courseVodVO.getId());
            if (ObjectUtil.isNotEmpty(recordVideoTask)){
                courseVodVO.setCourseVersion(recordVideoTask.getCourseVersion());
                courseVodVO.setRecordVideoTaskId(recordVideoTask.getId());
                courseVodVO.setCourseVersion(recordVideoTask.getCourseVersion());
            }
        });

        // 要返回的VOList，此处取每个courseId+LessonOrder最新的版本
        List<CourseVodVO> newCourseVodVOList = courseVodVOList.stream()
            .collect(Collectors.groupingBy(
                courseVodVO -> new AbstractMap.SimpleEntry<>(courseVodVO.getCourseId(),
                    courseVodVO.getLessonOrder()),
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> list.stream()
                        // 确保 courseVersion 不为 null
                        .filter(vo -> Objects.nonNull(vo.getCourseVersion()))
                        .max(Comparator.comparing(CourseVodVO::getCourseVersion))
                        // 提供默认值或处理 null
                        .orElse(null)
                )
            ))
            .values()
            .stream()
            .filter(Objects::nonNull)
            .toList();

        // 获取每个点播课程对应的课程信息
        fetchCourseInfo(newCourseVodVOList, recordVideoTaskList);

        // 获取每个点播课程对应的课节信息
        fetchLessonInfo(newCourseVodVOList, recordVideoTaskList);

        // 给每个点播课程库设置关联录课视屏数量
        fetchCourseVodCount(newCourseVodVOList);

        newCourseVodVOList = newCourseVodVOList.stream()
            .sorted(Comparator.comparing(CourseVodVO::getUpdateTime))
            .toList();

        return newCourseVodVOList;
    }

    /**
     * 编辑点播课程库启用/停用状态
     *
     * @param id 点播课程id
     * @return Boolean
     */
    @Override
    public Boolean editCourseVodDisable(Long id) {
        return this.update(Wrappers.lambdaUpdate(CourseVod.class)
                .eq(CourseVod::getId, id)
                .set(CourseVod::getDisable, CourseVodConstants.IS_DISABLE));
    }

    /**
     * 获取点播课程关联视屏列表
     *
     * @param courseId 课程id
     * @param lessonOrder 课节顺序
     * @return List<CourseVodVideoVO>
     */
    @Override
    public List<CourseVodVideoVO> getCourseVodInfoList(Long courseId,Integer lessonOrder) {

        List<CourseVod> courseVodList = baseMapper.selectList(Wrappers.lambdaQuery(CourseVod.class)
                        .eq(CourseVod::getCourseId,courseId)
                        .eq(CourseVod::getLessonOrder,lessonOrder));

        List<Long> courseVodIdList = courseVodList.stream().map(CourseVod::getId).toList();

        // 如果没有关联点播课程，则直接返回空
        if(CollUtil.isEmpty(courseVodList)){
            return List.of();
        }

        List<CourseVodVideo> courseVodVideoList = courseVodVideoMapper.selectList(Wrappers.lambdaQuery(CourseVodVideo.class)
            .in(CourseVodVideo::getCourseVodId, courseVodIdList)
            .orderByDesc(CourseVodVideo::getUpdateTime));

        // 从courseVodVideo处获取的recordVideoTaskId,再建立起从点播课程视屏id和RecordVideoTask的关联
        Map<Long, RecordVideoTask> recordVideoTaskMap = new HashMap<>();

        List<RecordVideoTask> recordVideoTaskList = recordVideoTaskMapper.selectList(Wrappers.lambdaQuery(RecordVideoTask.class)
            .in(RecordVideoTask::getId, courseVodVideoList.stream().map(CourseVodVideo::getRecordVideoTaskId).toList()));

        // 根据recordVideoTaskId关联
        Map<Long, RecordVideoTask> recordVideoTaskByIdMap = recordVideoTaskList.stream()
                .collect(Collectors.toMap(RecordVideoTask::getId, recordVideoTask -> recordVideoTask));

        courseVodVideoList.forEach(courseVodVideo -> recordVideoTaskMap.putIfAbsent(courseVodVideo.getId(), recordVideoTaskByIdMap.get(courseVodVideo.getRecordVideoTaskId())));

        // 获取视频播放地址
        List<CourseVodVideoVO> courseVodVideoVOList = BeanUtil.copyToList(courseVodVideoList, CourseVodVideoVO.class);

        // 查询课件版本和发布时间
        fetchCoursewareDetails(courseVodVideoVOList, courseVodVideoList, recordVideoTaskByIdMap, recordVideoTaskList);

        // 查询主讲老师
        fetchLecturerNames(courseVodVideoVOList, courseVodVideoList, recordVideoTaskByIdMap,recordVideoTaskMap);

        // 给每个点播课视频设置状态
        fetchCourseVodDisable(courseVodList, courseVodVideoVOList,courseVodVideoList);

        return courseVodVideoVOList;
    }

    /**
     * 调用课程远程服务，查询课程信息
     *
     * @param recordVideoTaskList 录课任务集合
     * @param courseVodVOList 点播课程库视图集合
     */
    private void fetchCourseInfo(List<CourseVodVO> courseVodVOList,List<RecordVideoTask> recordVideoTaskList){
        List<Integer> versionList = recordVideoTaskList.stream()
            .map(RecordVideoTask::getCourseVersion)
            .distinct()
            .toList();

        CourseDTO courseDTO = new CourseDTO();
        courseDTO.setVersionList(versionList);

        R<List<CourseVO>> courseList = remoteCourseService.getCourseListByVersion(courseDTO);

        if(!courseList.isOk()){
            log.error("调用课程服务根据课程id获取已发布课程列表失败,查询的课程版本集合:{}", versionList);
            return;
        }

        if(CollUtil.isEmpty(courseList.getData())){
            return;
        }

        // 构建 courseId 到 CourseVO 的映射
        Map<Integer, Map<Integer, CourseVO>> courseIdToCourseByVersionMap = courseList.getData().stream()
            .collect(Collectors.groupingBy(
                CourseVO::getId,
                Collectors.toMap(
                    CourseVO::getVersion,
                    Function.identity(),
                    (existing, replacement) -> existing
                )
            ));

        courseVodVOList.forEach(courseVodVO -> {
            Long courseId = courseVodVO.getCourseId();
            if(ObjectUtil.isEmpty(courseId)||ObjectUtil.isEmpty(courseVodVO.getCourseVersion())){
                return;
            }
            CourseVO courseVO = courseIdToCourseByVersionMap.get(courseId.intValue()).get(courseVodVO.getCourseVersion());
            if (ObjectUtil.isNull(courseVO)){
                return;
            }
            courseVodVO.setCourseName(courseVO.getCourseName());
        });
    }

    /**
     * 调用课节远程服务，查询课节信息
     *
     * @param recordVideoTaskList 录课任务集合
     * @param courseVodVOList 点播课程库视图集合
     */
    private void fetchLessonInfo(List<CourseVodVO> courseVodVOList, List<RecordVideoTask> recordVideoTaskList) {
        // 提取所有需要查询的版本号
        List<Integer> lessonVersionList = recordVideoTaskList.stream()
            .map(RecordVideoTask::getCourseVersion)
            .distinct()
            .toList();

        LessonNameDTO lessonNameDTO = new LessonNameDTO();
        lessonNameDTO.setVersionList(lessonVersionList);
        R<List<LessonNameVO>> lessonList = remoteLessonService.getLessonNameList(lessonNameDTO);
        if (!lessonList.isOk()) {
            log.error("调用课节服务获取课节名称列表失败,课节版本列表:{}", lessonVersionList);
            return;
        }

        if (CollUtil.isEmpty(lessonList.getData())) {
            return;
        }

        // 构建 courseId 和 lessonOrder 到 lessonName 的映射
        Map<String, String> courseIdToLessonOrderToLessonNameMap = lessonList.getData().stream()
            .collect(Collectors.toMap(
                lessonNameVO -> String.format(LessonConstant.LESSON_PUB, lessonNameVO.getCourseId(),
                    lessonNameVO.getLessonOrder(), lessonNameVO.getVersion()),
                    LessonNameVO::getLessonName
            ));

        Map<Long, String> lessonNameByTaskIdMap = recordVideoTaskList.stream()
            .filter(s -> courseIdToLessonOrderToLessonNameMap.containsKey(
                String.format(LessonConstant.LESSON_PUB, s.getCourseId(), s.getLessonOrder(),
                    s.getCourseVersion())))
            .collect(Collectors.toMap(
                RecordVideoTask::getId,
                recordVideoTask -> courseIdToLessonOrderToLessonNameMap.get(
                    String.format(LessonConstant.LESSON_PUB, recordVideoTask.getCourseId(),
                        recordVideoTask.getLessonOrder(), recordVideoTask.getCourseVersion())))
            );

        courseVodVOList.forEach(courseVodVO -> {
            String lessonName = lessonNameByTaskIdMap.get(
                courseVodVO.getRecordVideoTaskId());
            if (CharSequenceUtil.isNotBlank(lessonName)) {
                courseVodVO.setLessonName(lessonName);
            } else {
                courseVodVO.setLessonName(null);
            }
        });
    }


    /**
     * 根据课程id和课节顺序列表，获取每个课节对应的点播课程数量，以及获取最新更新时间
     *
     * @param courseVodVOList 点播课程VO集合
     */
    private void fetchCourseVodCount(List<CourseVodVO> courseVodVOList) {
        List<Long> courseIdList = courseVodVOList.stream()
            .map(CourseVodVO::getCourseId)
            .distinct()
            .toList();

        if (CollUtil.isEmpty(courseIdList)) {
            return;
        }

        List<CourseVod> courseVodList = baseMapper.selectList(Wrappers.lambdaQuery(CourseVod.class)
            .in(CourseVod::getCourseId, courseIdList));

        // 构建 courseId + lessonOrder 到 courseVod 的映射
        Map<Long, Map<Integer, List<CourseVod>>> courseIdToLessonOrderToCourseVodMap = courseVodList.stream()
            .collect(Collectors.groupingBy(
                CourseVod::getCourseId,
                Collectors.groupingBy(
                    CourseVod::getLessonOrder
                )
            ));

        // 构建 courseId + lessonOrder 到 courseVod 数量的映射
        Map<Long, Map<Integer, Integer>> courseIdToLessonOrderToCountMap = courseIdToLessonOrderToCourseVodMap.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().entrySet().stream()
                    .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().size()
                    ))
            ));

        // 构建 courseId + lessonOrder 到 最新更新时间 的映射
        Map<Long, Map<Integer, LocalDateTime>> courseIdToLessonOrderToLatestUpdateTimeMap = courseIdToLessonOrderToCourseVodMap.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().entrySet().stream()
                    .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().stream()
                            .max(Comparator.comparing(CourseVod::getUpdateTime))
                            .map(CourseVod::getUpdateTime)
                            .orElse(LocalDateTime.MIN)
                    ))
            ));

        // 填充 courseVodVOList
        courseVodVOList.forEach(courseVodVO -> {
            Long courseId = courseVodVO.getCourseId();
            Integer lessonOrder = courseVodVO.getLessonOrder();
            if (ObjectUtil.isNotNull(courseId) && ObjectUtil.isNotNull(lessonOrder)) {
                Map<Integer, Integer> lessonOrderToCountMap = courseIdToLessonOrderToCountMap.get(courseId);
                Map<Integer, LocalDateTime> lessonOrderToLatestUpdateTimeMap = courseIdToLessonOrderToLatestUpdateTimeMap.get(courseId);

                if (CollUtil.isNotEmpty(lessonOrderToCountMap) && lessonOrderToCountMap.containsKey(lessonOrder)) {
                    courseVodVO.setCourseVodVideoCount(lessonOrderToCountMap.get(lessonOrder));
                }

                if (CollUtil.isNotEmpty(lessonOrderToLatestUpdateTimeMap) && lessonOrderToLatestUpdateTimeMap.containsKey(lessonOrder)) {
                    courseVodVO.setUpdateTime(lessonOrderToLatestUpdateTimeMap.get(lessonOrder));
                }
            }
        });
    }



    /**
     * 调用teaching服务，查询课件版本和发布时间
     *
     * @param courseVodVideoVOList 查询的值，放入这个集合的VO
     * @param courseVodVideoList 点播课程视频集合
     * @param recordVideoTaskByIdMap 从courseVodVideo处获取的recordVideoTaskId,再建立起从点播课程视屏id和RecordVideoTask的关联
     * @param recordVideoTaskList 录课任务集合
     */
    private void fetchCoursewareDetails(List<CourseVodVideoVO> courseVodVideoVOList,List<CourseVodVideo> courseVodVideoList,Map<Long, RecordVideoTask> recordVideoTaskByIdMap,List<RecordVideoTask> recordVideoTaskList){
        // 课件请求类
        CoursewareVersionDTO coursewareVersionDTO = new CoursewareVersionDTO();

        coursewareVersionDTO.setVersionList(recordVideoTaskList
                .stream().map(RecordVideoTask::getCoursewareVersion).toList());

        R<List<CoursewareVersionVO>> coursewareList = remoteCoursewareService.getCoursewareVersionList(coursewareVersionDTO);

        if(!coursewareList.isOk()){
            log.error("调用课件服务根据版本id查询课件版本列表失败,课件版本列表:{}", coursewareVersionDTO.getVersionList());
            return;
        }

        if(CollUtil.isEmpty(coursewareList.getData())){
            return;
        }

        Map<Integer, CoursewareVersionVO> coursewareByVersionMap = coursewareList.getData().stream()
                .collect(Collectors.toMap(CoursewareVersionVO::getVersion, courseware -> courseware));

        courseVodVideoList.forEach(courseVodVideo -> {
            Integer coursewareVersion = recordVideoTaskByIdMap.get(courseVodVideo.getRecordVideoTaskId()).getCoursewareVersion();
            CoursewareVersionVO courseware = coursewareByVersionMap.get(coursewareVersion);
            if (ObjectUtil.isNotEmpty(courseware)) {
                courseVodVideoVOList.stream()
                        .filter(courseVodVideoVO -> courseVodVideo.getId().equals(courseVodVideoVO.getId()))
                        .findFirst()
                        .ifPresent(courseVodVideoVO -> {
                            courseVodVideoVO.setCoursewareName(courseware.getCoursewareName());
                            courseVodVideoVO.setCoursewareCreateTime(courseware.getCreateTime());
                        });
            }
        });

    }

    /**
     * 调用permission服务，查询主讲老师姓名
     *
     * @param courseVodVideoVOList 查询的值，放入这个集合的VO
     * @param courseVodVideoList 点播课程视频集合
     * @param recordVideoTaskByIdMap recordVideoTaskId和recordVideoTask关联Map
     */
    private void fetchLecturerNames(List<CourseVodVideoVO> courseVodVideoVOList,List<CourseVodVideo> courseVodVideoList,Map<Long, RecordVideoTask> recordVideoTaskByIdMap,Map<Long, RecordVideoTask> recordVideoTaskMap){
        // 主讲老师请求类
        SysUserDTO sysUserDTO = new SysUserDTO();

        // key courseVodVideo的id（点播课程视屏id）,value 主讲老师id
        Map<Long, Long> lecturerInfoMap = new HashMap<>();

        courseVodVideoList.forEach(courseVodVideo -> lecturerInfoMap.put(courseVodVideo.getId(),recordVideoTaskByIdMap.get(courseVodVideo.getRecordVideoTaskId()).getLectureId()));

        List<Long> lecturerIds = new ArrayList<>(lecturerInfoMap.values());
        sysUserDTO.setUserIdList(lecturerIds);

        R<List<SysUserVO>> lecturerInfoList = remoteLeadTeacherService.listById(sysUserDTO);

        if(!lecturerInfoList.isOk()){
            log.error("调用主讲老师服务根据id列表查询主讲老师失败,主讲老师id列表:{}", lecturerIds);
            return;
        }

        if(CollUtil.isEmpty(lecturerInfoList.getData())){
            return;
        }

        // 转换成Map key 主讲老师id，value 主讲老师姓名
        Map<Long, String> lecturerNameMap = lecturerInfoList.getData().stream()
                .collect(Collectors.toMap(SysUserVO::getUserId, SysUserVO::getName));

        courseVodVideoVOList.forEach(courseVodVideoVO -> {
            RecordVideoTask recordVideoTask = recordVideoTaskMap.get(courseVodVideoVO.getId());
            if (ObjectUtil.isNotEmpty(recordVideoTask)){
                courseVodVideoVO.setName(lecturerNameMap.get(recordVideoTask.getLectureId()));
            }
        });

    }

    /**
     * 给点播课程视频设置是否停用
     *
     * @param courseVodList 点播课程集合
     * @param courseVodVideoVOList 点播课程视频集合
     */
    private void fetchCourseVodDisable(List<CourseVod> courseVodList,List<CourseVodVideoVO> courseVodVideoVOList,List<CourseVodVideo> courseVodVideoList){
        // 将 courseVodList 转换为 Map，以便快速查找
        Map<Long, Integer> courseVodMap = courseVodList.stream()
                .collect(Collectors.toMap(CourseVod::getId, CourseVod::getDisable));

        // 将 courseVodId 和 courseVodVideoId 关联
        Map<Long, Long> courseVodIdToCourseVodVideoIdMap = courseVodVideoList.stream()
                .collect(Collectors.toMap(CourseVodVideo::getId,CourseVodVideo::getCourseVodId));

        courseVodVideoVOList.forEach(courseVodVideoVO -> courseVodVideoVO
            .setDisable(courseVodMap.get(courseVodIdToCourseVodVideoIdMap.get(courseVodVideoVO.getId()))));
    }

    /**
     * 查询点播课课程库
     * @param courseVodDTO
     * @return com.yuedu.ydsf.eduConnect.api.vo.CourseVodVO
     * <AUTHOR>
     * @date 2024/12/19 16:19
     */
    @Override
    public List<CourseVodVO> courseVodService(CourseVodDTO courseVodDTO) {

        List<CourseVodVO> courseVodVOList = new ArrayList<>();

        // 查询点播课程库
        List<CourseVod> courseVodList = this.list(Wrappers.lambdaQuery(CourseVod.class)
            .eq(CourseVod::getDisable, CourseVodDisableEnum.DISABLE_ENUM_0.code)
            .eq(Objects.nonNull(courseVodDTO.getCourseId()),CourseVod::getCourseId,courseVodDTO.getCourseId())
            .eq(Objects.nonNull(courseVodDTO.getLessonOrder()),CourseVod::getLessonOrder,courseVodDTO.getLessonOrder())
            .in(CollectionUtils.isNotEmpty(courseVodDTO.getLectureIdList()), CourseVod::getLectureId, courseVodDTO.getLectureIdList())
            .orderByDesc(CourseVod::getCreateTime)
        );

        if (CollectionUtils.isEmpty(courseVodList)) {
            return courseVodVOList;
        }

        // 查询点播课程视频
        List<Long> courseVodIdList = courseVodList.stream().map(CourseVod::getId).toList();
        List<CourseVodVideo> courseVodVideoList = courseVodVideoMapper.selectList(Wrappers.lambdaQuery(CourseVodVideo.class)
            .in(CourseVodVideo::getCourseVodId, courseVodIdList)
        );

        // 查询点播课程对应录课信息
        List<Long> recordingIdList = courseVodVideoList.stream().map(CourseVodVideo::getRecordingId).toList();
        List<Recording> recordingList = recordingMapper.selectBatchIds(recordingIdList);

        courseVodVOList = courseVodList.stream().map(entity -> {

            CourseVodVO courseVodVO = new CourseVodVO();
            BeanUtils.copyProperties(entity, courseVodVO);

            // 点播课程视频
            CourseVodVideo courseVodVideo = courseVodVideoList.stream()
                .filter(e -> e.getCourseVodId().equals(entity.getId()))
                .findFirst()
                .orElse(new CourseVodVideo());

            courseVodVO.setAliyunPlayUrl(courseVodVideo.getAliyunPlayUrl());
            courseVodVO.setMp4Url(courseVodVideo.getMp4Url());

            // 录课视频
            Recording recording = recordingList.stream()
                .filter(e -> e.getId().equals(courseVodVideo.getRecordingId()))
                .findFirst()
                .orElse(new Recording());

            courseVodVO.setAgoraCloudRecordId(recording.getAgoraCloudRecordId());

            return courseVodVO;

        }).toList();

        return courseVodVOList;

    }

}
