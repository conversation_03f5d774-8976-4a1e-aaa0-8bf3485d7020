package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.query.InformationResourceQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationResourceDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationResourceVO;
import com.yuedu.ydsf.eduConnect.entity.InformationResource;

import java.io.Serializable;
import java.util.List;

/**
* 资料资源表服务接口
*
* <AUTHOR>
* @date  2025/07/22
*/
public interface InformationResourceService extends IService<InformationResource> {



    /**
     * 资料资源表分页查询
     *
     * @param page 分页对象
     * @param informationResourceQuery 资料资源表
     * @return IPage 分页结果
     */
    IPage<InformationResourceVO> page(Page page, InformationResourceQuery informationResourceQuery);


    /**
     * 根据ID获得资料资源表信息
     *
     * @param id id
     * @return InformationResourceVO 详细信息
     */
    InformationResourceVO getInfoById(Serializable id);


    /**
     * 新增资料资源表
     *
     * @param informationResourceDTO 资料资源表
     * @return boolean 执行结果
     */
    boolean add(InformationResourceDTO informationResourceDTO);


    /**
     * 修改资料资源表
     *
     * @param informationResourceDTO 资料资源表
     * @return boolean 执行结果
     */
    boolean edit(InformationResourceDTO informationResourceDTO);


    /**
     * 导出excel 资料资源表表格
     *
     * @param informationResourceQuery 查询条件
     * @param ids 导出指定ID
     * @return List<InformationResourceVO> 结果集合
     */
    List<InformationResourceVO> export(InformationResourceQuery informationResourceQuery, Long[] ids);


    /**
     *  根据ID批量保存
     *
     * <AUTHOR>
     * @date 2025年07月22日 15时08分
     */
    boolean batchSave(List<InformationResourceDTO> informationResourceDTO);
}
