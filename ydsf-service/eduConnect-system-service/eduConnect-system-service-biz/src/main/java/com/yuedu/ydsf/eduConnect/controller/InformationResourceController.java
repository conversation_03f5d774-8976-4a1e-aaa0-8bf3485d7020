package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.eduConnect.service.InformationResourceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.eduConnect.api.query.InformationResourceQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationResourceDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationResourceVO;

import java.io.Serializable;
import java.util.List;

/**
* 资料资源表控制层
*
* <AUTHOR>
* @date  2025/07/22
*/

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/informationResource")
@Tag(description = "ss_information_resource" , name = "资料资源" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class InformationResourceController  {


    private final InformationResourceService informationResourceService;


    /**
     * 根据目录ID分页查询资料
     * @param page 分页对象
     * @param informationResourceQuery 资料资源表
     * @return R
     */
    @GetMapping("/page/{id}" )
    @HasPermission("edusys_informationResource_view")
    @Operation(summary = "根据目录ID分页查询资料" , description = "根据目录ID分页查询资料" )
    public R<IPage<InformationResourceVO>> page(@ParameterObject Page page,@PathVariable("id") Long id, @ParameterObject InformationResourceQuery informationResourceQuery) {
        informationResourceQuery.setInformationId(id);
        return R.ok(informationResourceService.page(page, informationResourceQuery));
    }



    /**
    * 根据目录ID保存资源
    * @param informationResourceDTO 资料资源表
    * @return R
    */
    @PostMapping("/add/{id}")
    @SysLog("新增资料资源表" )
    @HasPermission("edusys_informationResource_add" )
    @Operation(summary = "根据目录ID保存资源" , description = "根据目录ID保存资源" )
    public R add(@PathVariable("id") Long id, @RequestBody List<InformationResourceDTO> informationResourceDTO) {
        if (CollUtil.isEmpty(informationResourceDTO)) {
            return R.failed("资源集合不能为空！");
        }
        informationResourceDTO.forEach(informationResource -> informationResource.setInformationId(id));
        return R.ok(informationResourceService.batchSave(informationResourceDTO));
    }



    /**
     * 通过id查询获得详细信息
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询获得详细信息" , description = "通过id查询获得详细信息" )
    @GetMapping("/info/{id}" )
    @HasPermission("edusys_informationResource_info")
    public R<InformationResourceVO> getById(@PathVariable Serializable id) {
        return R.ok(informationResourceService.getInfoById(id));
    }

    /**
    * 根据资源Id修改资源信息
    * @param informationResourceDTO 资料资源表
    * @return R
    */
    @PutMapping("/edit")
    @SysLog("修改资料资源表" )
    @HasPermission("edusys_informationResource_edit" )
    @Operation(summary = "根据资源Id修改资源信息" , description = "根据资源Id修改资源信息" )
    public R edit(@Validated(V_E.class) @RequestBody InformationResourceDTO informationResourceDTO) {
         return R.ok(informationResourceService.edit(informationResourceDTO));
    }

    /**
    * 通过id删除资料资源
    * @return R
    */
    @DeleteMapping("/del/{id}")
    @SysLog("通过id删除资料资源" )
    @HasPermission("edusys_informationResource_del" )
    @Operation(summary = "通过id删除资料资源" , description = "通过id删除资料资源" )
    public R delete(@PathVariable("id") Long id){
         return R.ok(informationResourceService.removeById(id));
    }

}
