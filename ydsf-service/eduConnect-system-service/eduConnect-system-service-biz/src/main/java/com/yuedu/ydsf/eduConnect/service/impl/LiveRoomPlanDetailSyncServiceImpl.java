package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.constant.CourseTypeEnum;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.Timetable;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.TimetableMapper;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDetailSyncService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 直播间计划明细同步服务实现
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@Slf4j
@Service
@AllArgsConstructor
public class LiveRoomPlanDetailSyncServiceImpl implements LiveRoomPlanDetailSyncService {

  private final LiveRoomPlanDetailDraftMapper liveRoomPlanDetailDraftMapper;
  private final TeachingPlanDraftMapper teachingPlanDraftMapper;
  private final TimetableMapper timetableMapper;
  private final LiveRoomPlanDraftMapper liveRoomPlanDraftMapper;

  /**
   * 同步直播间排期到门店约课课表
   *
   * <AUTHOR>
   * @date 2024/12/08
   * @return void
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void syncLiveRoomPlanDetailToTimetable() {
    log.info("开始同步直播间排期到门店约课课表");

    try {
      List<Long> publishedLiveRoomPlanIds =
          liveRoomPlanDraftMapper
              .selectList(
                  Wrappers.lambdaQuery(LiveRoomPlanDraft.class)
                      .eq(LiveRoomPlanDraft::getPlanStatus, YesNoEnum.YES.getCode()) // 假设1表示已发布状态
                      .select(LiveRoomPlanDraft::getId))
              .stream()
              .map(LiveRoomPlanDraft::getId)
              .toList();

      // 1. 获取所有关联了教学计划的直播间计划
      List<TeachingPlanDraft> teachingPlanDrafts =
          teachingPlanDraftMapper.selectList(
              Wrappers.lambdaQuery(TeachingPlanDraft.class)
                  .isNotNull(TeachingPlanDraft::getLiveRoomPlanId)
                  .in(TeachingPlanDraft::getLiveRoomPlanId, publishedLiveRoomPlanIds));

      if (CollectionUtils.isEmpty(teachingPlanDrafts)) {
        log.info("未找到关联教学计划的直播间计划，无需同步");
        return;
      }

      // 提取直播间计划ID
      List<Long> liveRoomPlanIds =
          teachingPlanDrafts.stream()
              .map(TeachingPlanDraft::getLiveRoomPlanId)
              .distinct()
              .collect(Collectors.toList());

      log.info("找到关联教学计划的直播间计划数量: {}", liveRoomPlanIds.size());

      // 教学计划ID与直播间计划ID的映射关系
      Map<Long, Long> teachingPlanToLiveRoomPlanMap =
          teachingPlanDrafts.stream()
              .collect(
                  Collectors.toMap(TeachingPlanDraft::getId, TeachingPlanDraft::getLiveRoomPlanId));

      // 2. 获取所有未来的直播间排期明细
      List<LiveRoomPlanDetailDraft> allDetailDrafts =
          liveRoomPlanDetailDraftMapper.selectList(
              Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                  .in(LiveRoomPlanDetailDraft::getPlanId, liveRoomPlanIds)
                  .gt(LiveRoomPlanDetailDraft::getClassStartDateTime, LocalDateTime.now()));

      if (CollectionUtils.isEmpty(allDetailDrafts)) {
        log.info("未找到未来的直播间排期明细，无需同步");
        return;
      }

      // 按直播间计划ID分组的排期明细
      Map<Long, List<LiveRoomPlanDetailDraft>> planDetailMap =
          allDetailDrafts.stream()
              .collect(Collectors.groupingBy(LiveRoomPlanDetailDraft::getPlanId));

      // 3. 查询所有未来的门店约课课表信息
      List<Long> teachingPlanIds =
          teachingPlanDrafts.stream().map(TeachingPlanDraft::getId).collect(Collectors.toList());

      List<Timetable> allTimetables =
          timetableMapper.selectList(
              Wrappers.lambdaQuery(Timetable.class)
                  .in(Timetable::getTeachingPlanId, teachingPlanIds)
                  .gt(Timetable::getClassStartDateTime, LocalDateTime.now())
                  .eq(Timetable::getCourseType, CourseTypeEnum.COURSE_TYPE_ENUM_1.code) // 只处理直播课
              );

      if (CollectionUtils.isEmpty(allTimetables)) {
        log.info("未找到未来的门店约课课表信息，无需同步");
        return;
      }

      // 按教学计划ID和课次顺序分组的课表信息
      Map<String, Timetable> timetableMap = new HashMap<>();
      for (Timetable timetable : allTimetables) {
        String key = timetable.getTeachingPlanId() + "_" + timetable.getLessonOrder();
        timetableMap.put(key, timetable);
      }

      // 4. 同步更新课表信息
      List<Timetable> updateList = new ArrayList<>();

      for (TeachingPlanDraft teachingPlan : teachingPlanDrafts) {
        Long liveRoomPlanId = teachingPlan.getLiveRoomPlanId();
        List<LiveRoomPlanDetailDraft> detailDrafts = planDetailMap.get(liveRoomPlanId);

        if (CollectionUtils.isEmpty(detailDrafts)) {
          continue;
        }

        for (LiveRoomPlanDetailDraft detailDraft : detailDrafts) {
          String key = teachingPlan.getId() + "_" + detailDraft.getLessonOrder();
          Timetable timetable = timetableMap.get(key);

          if (timetable != null) {
            // 比较并更新时间
            LocalDate classDate = detailDraft.getClassStartDateTime().toLocalDate();
            LocalTime classStartTime = detailDraft.getClassStartTime();
            LocalTime classEndTime = detailDraft.getClassEndTime();
            LocalDateTime classStartDateTime = LocalDateTime.of(classDate, classStartTime);
            LocalDateTime classEndDateTime = LocalDateTime.of(classDate, classEndTime);

            // 检查时间是否已更改
            if (!classDate.equals(timetable.getClassDate())
                || !classStartTime.equals(timetable.getClassStartTime())
                || !classEndTime.equals(timetable.getClassEndTime())
                || !classStartDateTime.equals(timetable.getClassStartDateTime())
                || !classEndDateTime.equals(timetable.getClassEndDateTime())) {

              // 更新课表时间
              timetable.setClassDate(classDate);
              timetable.setClassStartTime(classStartTime);
              timetable.setClassEndTime(classEndTime);
              timetable.setClassStartDateTime(classStartDateTime);
              timetable.setClassEndDateTime(classEndDateTime);

              updateList.add(timetable);

              log.info(
                  "课表时间需要更新, timetableId: {}, lessonOrder: {}, 原时间: {}, 新时间: {}",
                  timetable.getId(),
                  timetable.getLessonOrder(),
                  timetable.getClassStartDateTime(),
                  classStartDateTime);
            }
          }
        }
      }

      // 批量更新课表
      if (!updateList.isEmpty()) {
        log.info("需要更新的课表数量: {}", updateList.size());
        for (Timetable timetable : updateList) {
          timetableMapper.updateById(timetable);
        }
        log.info("成功更新课表信息");
      } else {
        log.info("没有需要更新的课表信息");
      }

    } catch (Exception e) {
      log.error("同步直播间排期到门店约课课表失败", e);
      throw new BizException("同步直播间排期到门店约课课表失败: " + e.getMessage());
    }
  }
}
