package com.yuedu.ydsf.eduConnect.controller.callback;

import com.dtflys.forest.http.HttpStatus;
import com.yuedu.ydsf.eduConnect.service.SsRecordingService;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.AliConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.AliEventDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AliVodEventHandleService;
import com.yuedu.ydsf.eduConnect.system.proxy.vo.AliEventVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 阿里回调
 *
 * @author: KL
 * @date: 2024/10/11
 **/
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/ali")
@Tag(description = "阿里回调", name = "阿里回调")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AliCallbackController {


    private AliVodEventHandleService aliVodEventHandleService;

    private SsRecordingService ssRecordingService;

    /**
     * 阿里视频服务回调
     *
     * <AUTHOR>
     * @date 2024年10月11日 08时58分
     */
    @PostMapping("/vod/callback")
    @Operation(summary = "阿里VOD回调", description = "阿里VOD回调")
    public AliEventVO vodCallback(@RequestBody AliEventDTO eventDTO,
                                  HttpServletRequest request,
                                  HttpServletResponse response) {
        log.info("vodCallback: {}", eventDTO);
        if (!aliVodEventHandleService.verifySign(eventDTO, request.getHeader(AliConstant.X_VOD_TIMESTAMP),
                request.getHeader(AliConstant.X_VOD_SIGNATURE))
                || !aliVodEventHandleService.handlerEvent(eventDTO,dto->{
                    return ssRecordingService.handlerTranscode(dto.getVideoId(),dto.isSuccess());
        })) {
            response.setStatus(HttpStatus.BAD_REQUEST);
        }
        return AliEventVO.Ok();
    }


}
