package com.yuedu.ydsf.eduConnect.mq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yuedu.ydsf.eduConnect.live.api.constant.EventAlarmEnum;
import com.yuedu.ydsf.eduConnect.live.api.dto.TimetablePictureDTO;
import java.nio.charset.StandardCharsets;
import com.yuedu.ydsf.eduConnect.service.TimetablePictureService;
import com.yuedu.ydsf.eduConnect.service.TimetableService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/03/10
 **/
@Slf4j
@Service
@RocketMQMessageListener(topic = "${rocketmq.topics.timeable_event_alarm_topic}",
    consumerGroup = "${rocketmq.groups.timeable_event_alarm_group}", tag = "*",consumptionThreadCount = 2)
@ConditionalOnProperty(
    prefix = "rocketmq",
    name = "enabled",
    havingValue = "true",
    matchIfMissing = false)
@AllArgsConstructor
public class TimeableEventAlarmListener implements RocketMQListener {

    private final TimetablePictureService timetablePictureService;

    private final TimetableService timetableService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        String message = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("课中事件接收到的消息:topic: {} messageId:{},body:{}",messageView.getTopic(),messageView.getMessageId(), message);
        try{
            JSONObject jsonObject = JSON.parseObject(message);
            TimetablePictureDTO eventDTO =  jsonObject.toJavaObject(TimetablePictureDTO.class);
            if(EventAlarmEnum.EVENT_ALARM_STATUS_1.code.equals(eventDTO.getEvent())){
                handlerPicture(eventDTO);
            }else {
                handlerEventAlarm(eventDTO);
            }

        }catch (Exception e){
            log.error("解析事件消息内容失败:{}", e.getMessage());
        }

        return ConsumeResult.SUCCESS;
    }


    private void handlerEventAlarm(TimetablePictureDTO eventDTO){
        timetableService.handlerEventAlarm(eventDTO);
    }


    private void handlerPicture(TimetablePictureDTO eventDTO){
        timetablePictureService.handlerPicture(eventDTO);
    }
}
