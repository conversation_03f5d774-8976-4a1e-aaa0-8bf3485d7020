package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.dto.CourseVodDTO;
import com.yuedu.ydsf.eduConnect.api.query.CourseVodQuery;
import com.yuedu.ydsf.eduConnect.api.vo.CourseVodVO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseVodVideoVO;
import com.yuedu.ydsf.eduConnect.entity.CourseVod;
import java.util.List;

/**
 * 点播课程库 服务类
 *
 * <AUTHOR>
 * @date 2024-12-02 11:05:41
 */
public interface CourseVodService extends IService<CourseVod> {
    /**
     * 获取点播课程库列表
     *
     * @param courseVodQuery 查询参数
     * @return List<CourseVodVO>
     */
    List<CourseVodVO> getCourseVodList(CourseVodQuery courseVodQuery);

    /**
     * 编辑点播课程库启用/停用状态
     *
     * @param id 点播课程id
     * @return Boolean
     */
    Boolean editCourseVodDisable(Long id);

    /**
     * 获取点播课程关联视屏列表
     *
     * @param courseId 课程id
     * @param lessonOrder 课节顺序
     * @return List<CourseVodVideoVO>
     */
    List<CourseVodVideoVO> getCourseVodInfoList(Long courseId,Integer lessonOrder);

    /**
     * 查询点播课课程库
     * @param courseVodDTO
     * @return com.yuedu.ydsf.eduConnect.api.vo.CourseVodVO
     * <AUTHOR>
     * @date 2024/12/19 16:19
     */
    List<CourseVodVO> courseVodService(CourseVodDTO courseVodDTO);

}
