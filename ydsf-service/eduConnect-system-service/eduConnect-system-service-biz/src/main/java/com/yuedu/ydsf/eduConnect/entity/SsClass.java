package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 班级信息表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-09 10:44:48
 */
@Data
@TableName("ss_class")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "班级信息表实体类")
public class SsClass extends Model<SsClass> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 班级名称
	*/
    @Schema(description="班级名称")
    private String className;

	/**
	* 年级(字典类型: grade)
	*/
    @Schema(description="年级(字典类型: grade)")
    private Integer grade;

	/**
	* 班级状态(字典类型: class_state)
	*/
    @Schema(description="班级状态(字典类型: class_state)")
    private Integer classState;

	/**
	* 是否同步校管家(字典类型: is_sync_xiaogj)
	*/
    @Schema(description="是否同步校管家(字典类型: is_sync_xiaogj)")
    private Integer isSyncXiaogj;

	/**
	* 班级类型(字典类型: class_type)
	*/
    @Schema(description="班级类型(字典类型: class_type)")
    private Integer classType;

	/**
	* 创建时间
	*/
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑者")
    private String modifer;

	/**
	* 是否删除: 0-未删除;1-已删除
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除: 0-未删除;1-已删除")
    private Integer delFlag;

    /**
     * 教学计划Id
     */
    private Long teachingPlanId;
}
