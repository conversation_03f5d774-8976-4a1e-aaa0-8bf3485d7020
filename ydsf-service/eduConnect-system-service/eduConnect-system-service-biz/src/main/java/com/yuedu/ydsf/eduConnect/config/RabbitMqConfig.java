package com.yuedu.ydsf.eduConnect.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 校管家监听消费配置类
 * <AUTHOR>
 * @date 2025/2/17 8:47
 */
@Configuration
@ConfigurationProperties(prefix = "rabbitmq", ignoreUnknownFields = true)
@Data
public class RabbitMqConfig {

    /**
     * 监听消费睡眠时间
     */
    private Long listenerSleep;

    /**
     * sp1校管家排课学员回调消费开关: true-启用; false-禁用;
     */
    private Boolean courseStudentEnabled;

    /**
     * sp1双师系统校管家排课回调消费开关: true-启用; false-禁用;
     */
    private Boolean courseCallbackEnabled;


}
