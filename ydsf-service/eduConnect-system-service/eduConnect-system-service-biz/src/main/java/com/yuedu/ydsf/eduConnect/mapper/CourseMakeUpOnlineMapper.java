package com.yuedu.ydsf.eduConnect.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.query.CourseMakeUpOnlineQuery;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineVO;
import org.apache.ibatis.annotations.Mapper;
import com.yuedu.ydsf.eduConnect.entity.CourseMakeUpOnline;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
* 门店线上补课表持久层接口
*
* <AUTHOR>
* @date  2025/04/28
*/
@Mapper
public interface CourseMakeUpOnlineMapper extends YdsfBaseMapper<CourseMakeUpOnline> {


     /**
      *  分页查询
      *
      * <AUTHOR>
      * @date 2025年04月28日 13时48分
      */
     IPage<CourseMakeUpOnlineVO> page(Page page,@Param("query")  CourseMakeUpOnlineQuery courseMakeUpOnlineQuery);

     /**
      *  导出
      *
      * <AUTHOR>
      * @date 2025年07月08日 13时50分
      */
     List<CourseMakeUpOnline> export(@Param("query") CourseMakeUpOnlineQuery courseMakeUpOnlineQuery);
}




