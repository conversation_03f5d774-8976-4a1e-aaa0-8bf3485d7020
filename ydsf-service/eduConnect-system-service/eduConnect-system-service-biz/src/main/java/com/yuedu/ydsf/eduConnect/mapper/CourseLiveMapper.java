package com.yuedu.ydsf.eduConnect.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.query.CourseLiveQuery;
import com.yuedu.ydsf.eduConnect.api.vo.CourseLiveVO;
import com.yuedu.ydsf.eduConnect.entity.CourseLive;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 门店已约直播课 持久层
 *
 * <AUTHOR>
 * @date 2024-12-26 14:41:20
 */
@Mapper
public interface CourseLiveMapper extends YdsfBaseMapper<CourseLive> {


     IPage<CourseLiveVO> page(Page page, @Param("query")CourseLiveQuery query);

}
