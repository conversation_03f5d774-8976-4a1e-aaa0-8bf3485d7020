package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.entity.SsVideoPlayToken;
import com.yuedu.ydsf.eduConnect.mapper.SsVideoPlayTokenMapper;
import com.yuedu.ydsf.eduConnect.service.SsVideoPlayTokenService;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 视频播放凭证 服务类
 *
 * <AUTHOR>
 * @date 2025-03-11 08:47:54
 */
@Slf4j
@Service
public class SsVideoPlayTokenServiceImpl extends
    ServiceImpl<SsVideoPlayTokenMapper, SsVideoPlayToken> implements SsVideoPlayTokenService {


    public static final String UNDERLINE = "_";
    /**
     * 加密Key，为用户自定义的字符串，长度为16、24或32位
     */
    @Value("${ss.config.play-token.encrypt_key}")
    private String encrypt_key;

    /**
     * //加密偏移量，为用户自定义字符串，长度为16位，不能含有特殊字符
     */
    @Value("${ss.config.play-token.init_vector}")
    private String init_vector;

    /**
     * 根据传递的参数生成令牌 说明： 1、参数可以是业务方的用户ID、播放终端类型等信息 2、调用令牌接口时生成令牌Token
     */
    @Override
    public String generateToken(SsVideoPlayToken ssVideoPlayToken) {
        //查询是否已给当前门店授权视频播放权限
        SsVideoPlayToken videoPlayToken = this.getOne(
            Wrappers.lambdaQuery(SsVideoPlayToken.class)
                .eq(SsVideoPlayToken::getCampusId, ssVideoPlayToken.getCampusId())
                .eq(SsVideoPlayToken::getVideoId, ssVideoPlayToken.getVideoId())
                .gt(SsVideoPlayToken::getTokenExpire, LocalDateTime.now()));
        if (Objects.nonNull(videoPlayToken)) {
            log.info("当前门店已授权视频播放权限,{}", ssVideoPlayToken.getCampusId());
            return videoPlayToken.getToken();
        }
        long expire = ssVideoPlayToken.getTokenExpire().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        //自定义字符串，base的长度为16位字符（此例中，时间戳占13位，下划线（_）占1位，则还需传入2位字符。实际配置时也可按需全部更改，最终保证base为16、24或32位字符串即可。）
        //生成22位长度的校区ID，不足22位长度前面补0
        String base = ssVideoPlayToken.getCampusId() + UNDERLINE + ssVideoPlayToken.getVideoId() + UNDERLINE + expire;
        //生成token
        String token = encrypt(base, encrypt_key);  //arg1为要加密的自定义字符串，arg2为加密Key
        ssVideoPlayToken.setToken(token);
        //保存token，用于解密时校验token的有效性，例如：过期时间、token的使用次数
        saveToken(ssVideoPlayToken);
        return token;
    }

    @Override
    public String getPlayUrl(String token) {
        if (validateToken(token)) {
            SsVideoPlayToken ssVideoPlayToken = getToken(token);
            if (ssVideoPlayToken != null) {
                return ssVideoPlayToken.getPlayUrl();
            }
        }
        log.warn("token无效,{}", token);
        throw new BizException("视频链接已过期，无法观看，请联系总部教务处理");
    }

    @Override
    public void updatePlayCount(String token) {
        SsVideoPlayToken videoPlayToken = getToken(token);
        if (videoPlayToken != null) {
            videoPlayToken.setUseCount(videoPlayToken.getUseCount() + 1);
            this.updateById(videoPlayToken);
        } else {
            log.warn("更新视频播放次数,token不存在,{}", token);
            throw new BizException("凭证失效");
        }
    }

    /**
     * 验证token的有效性 说明： 1、解密接口在返回播放密钥前，需要先校验Token的合法性和有效性 2、强烈建议同时校验Token的过期时间以及Token的有效使用次数
     */
    public boolean validateToken(String token) {
        if (StringUtils.isEmpty(token)) {
            return false;
        }
        String base = decrypt(token, encrypt_key); //arg1为解密字符串，arg2为解密Key
        //先校验token的有效时间
        long expireTime = Long.parseLong(base.substring(base.lastIndexOf("_") + 1));
        if (System.currentTimeMillis() > expireTime) {
            log.info("token过期,{}", token);
            return false;
        }

        //判断是否已经使用过该token
        if (getToken(token) == null) {
            log.warn("token不存在,{}", token);
            return false;
        }
        //获取到业务属性信息，用于校验
        String businessInfo = base.substring(0, base.lastIndexOf("_"));
        String[] items = businessInfo.split("_");
        //校验业务信息的合法性，业务方实现
        return validateInfo(items);
    }

    /**
     * 保存Token到DB 业务方自行实现
     */
    public void saveToken(SsVideoPlayToken ssVideoPlayToken) {
        this.save(ssVideoPlayToken);
    }

    /**
     * 查询Token 业务方自行实现
     */
    public SsVideoPlayToken getToken(String token) {
        //从DB获取token信息，判断token的有效性，业务方可自行实现
        return this.getOne(
            Wrappers.lambdaQuery(SsVideoPlayToken.class).eq(SsVideoPlayToken::getToken, token));
    }

    /**
     * 校验业务信息的有效性，业务方可自行实现
     */
    public boolean validateInfo(String... infos) {
        // 校验信息的有效性，例如UID是否有效等
        return true;
    }

    /**
     * AES加密生成Token
     *
     * @param encryptStr 要加密的字符串
     * @param encryptKey 加密Key
     */
    public String encrypt(String encryptStr, String encryptKey) {
        IvParameterSpec e = new IvParameterSpec(init_vector.getBytes(StandardCharsets.UTF_8));
        SecretKeySpec secretKeySpec = new SecretKeySpec(encryptKey.getBytes(StandardCharsets.UTF_8),
            "AES");
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, e);
            byte[] encrypted = cipher.doFinal(encryptStr.getBytes());
            return Base64.encodeBase64String(encrypted);
        } catch (Exception ex) {
            log.error("AES加密失败", ex);
            throw new BizException("AES加密失败");
        }
    }

    /**
     * AES解密token
     *
     * @param encryptStr 解密字符串
     * @param decryptKey 解密Key
     */
    public String decrypt(String encryptStr, String decryptKey) {

        IvParameterSpec e = new IvParameterSpec(init_vector.getBytes(StandardCharsets.UTF_8));
        SecretKeySpec secretKeySpec = new SecretKeySpec(decryptKey.getBytes(StandardCharsets.UTF_8),
            "AES");
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, e);
            byte[] encryptByte = Base64.decodeBase64(encryptStr);
            byte[] decryptByte = cipher.doFinal(encryptByte);
            return new String(decryptByte);
        } catch (Exception ex) {
            log.warn("AES解密失败", ex);
            throw new BizException("AES解密失败");
        }
    }
}
