package com.yuedu.ydsf.eduConnect.controller;


import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.vo.SsVideoPlayTokenVO;
import com.yuedu.ydsf.eduConnect.service.SsVideoPlayTokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/no_login")
@Tag(description = "no_login", name = "不需要登录的请求")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NoLoginController {

    private final SsVideoPlayTokenService ssVideoPlayTokenService;

    /**
     * 通过token获取视频播放链接
     *
     * @param token 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过token获取视频播放链接", description = "通过token获取视频播放链接")
    @GetMapping("/playToken/playUrl")
    public R getPlayUrl1(@RequestParam(value = "token") String token) {
        log.info("通过token获取视频播放链接,{}", token);
        return R.ok(ssVideoPlayTokenService.getPlayUrl(token));
    }

    /**
     * 更新视频播放次数
     * @param ssVideoPlayTokenVO 更新视频播放次数
     * @return R
     */
    @Operation(summary = "修改视频播放凭证" , description = "修改视频播放凭证" )
    @PutMapping("/playToken/plusPlayCount")
    public R plusPlayCount(@Valid @NotNull @RequestBody SsVideoPlayTokenVO ssVideoPlayTokenVO) {
        log.info("更新视频播放次数,{}", ssVideoPlayTokenVO);
        if (StringUtils.isEmpty(ssVideoPlayTokenVO.getToken())) {
            return R.failed("token不能为空");
        }
        ssVideoPlayTokenService.updatePlayCount(ssVideoPlayTokenVO.getToken());
        return R.ok();
    }

}
