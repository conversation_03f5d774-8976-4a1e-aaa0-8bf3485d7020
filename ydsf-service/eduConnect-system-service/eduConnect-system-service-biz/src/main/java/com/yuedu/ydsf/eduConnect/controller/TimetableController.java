package com.yuedu.ydsf.eduConnect.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.constant.AttendClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.query.SsClassQuery;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.query.TimetableQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableExportVO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.service.TimetableService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/03/11
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("/timetable")
@Tag(description = "timetable", name = "课消")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TimetableController {

    private final TimetableService timetableService;


    /**
     *  课消分页查询
     *
     * <AUTHOR>
     * @date 2025年03月11日 14时56分
     */
    @Operation(summary = "直播课分页查询", description = "直播课分页查询")
    @GetMapping("/page")
    @HasPermission("edusystem_consumption_view")
    public R<IPage<TimetableVO>> getTimetablePage(@ParameterObject Page page, @ParameterObject TimetableQuery timetableQuery) {
        return R.ok(timetableService.page(page, timetableQuery));
    }


    /**
     *  查看上课出勤详情
     *
     * <AUTHOR>
     * @date 2025年03月12日 10时29分
     */
    @Operation(summary = "查看上课出勤详情", description = "查看上课出勤详情")
    @GetMapping("/getInfo/{id}")
    @HasPermission(value = "edusystem_consumption_details_view")
    public R<TimetableVO> getInfoById(@PathVariable("id") @Parameter( description = "课次ID") @NotNull( message = "课次ID不允许为空") Long id){
        return R.ok(timetableService.getInfoById(id));
    }


    /**
     *  根据门店ID查询已约课程ID列表
     *
     * <AUTHOR>
     * @date 2025年06月05日 16时28分
     */
    @GetMapping("/getCourseListByStoreId/{storeId}")
    @Inner
    R<List<Long>> getCourseListByStoreId(@PathVariable("storeId") Long storeId){
        return R.ok(timetableService.getCourseListByStoreId(storeId));
    }



    /**
     *  课消分页导出
     *
     * <AUTHOR>
     * @date 2025年07月08日 09时11分
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_consumption_export")
    @Idempotent(info = "导出执行中,请勿频繁操作！", expireTime = 5)
    public List<TimetableExportVO> exportTimetableList(@ParameterObject TimetableQuery timetableQuery) {
          if(Objects.isNull(timetableQuery.getClassStartDateTime()) || Objects.isNull(timetableQuery.getClassEndDateTime())){
              return new ArrayList<>();
          }

//        if(Objects.isNull(timetableQuery.getClassStartDateTime()) || Objects.isNull(timetableQuery.getClassEndDateTime())){
//            LocalDate today = LocalDate.now();
//            timetableQuery.setClassStartDateTime(LocalDateTime.of(today.with(TemporalAdjusters.firstDayOfMonth()),LocalTime.of(0,0,0)));
//            timetableQuery.setClassEndDateTime(LocalDateTime.of(today.with(TemporalAdjusters.lastDayOfMonth()),LocalTime.of(23,59,59)));
//        }else {
//            if( ChronoUnit.MONTHS.between(timetableQuery.getClassStartDateTime(), timetableQuery.getClassEndDateTime())> 1){
//                throw new BizException("导出的课消时间间隔不能超过1个月");
//            }
//        }

        return timetableService.export(timetableQuery);
    }
}
