package com.yuedu.ydsf.eduConnect.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineExportVO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineVO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.entity.CourseMakeUpOnline;
import java.util.List;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/04/28
 **/
public interface CourseMakeUpOnlineManager {

    /**
     *  填充补课数据
     *
     * <AUTHOR>
     * @date 2025年04月28日 13时43分
     */
    IPage<CourseMakeUpOnlineVO> fillData(IPage<CourseMakeUpOnlineVO> page);


    /**
     *  填充补课明细
     *
     * <AUTHOR>
     * @date 2025年04月28日 15时25分
     */
    CourseMakeUpOnlineVO fillInfoData(CourseMakeUpOnline courseMakeUpOnline);

    /**
     *  导出
     *
     * <AUTHOR>
     * @date 2025年07月08日 13时51分
     */
    List<CourseMakeUpOnlineExportVO> fillExportData(List<CourseMakeUpOnline> page);
}
