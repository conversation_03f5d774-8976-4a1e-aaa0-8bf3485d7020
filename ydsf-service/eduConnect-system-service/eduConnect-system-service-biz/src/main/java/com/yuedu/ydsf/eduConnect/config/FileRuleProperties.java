package com.yuedu.ydsf.eduConnect.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/07/22
 **/
@Data
@Component
@ConfigurationProperties(prefix = "information.file")
public class FileRuleProperties {

    private List<FileCategory> categories;

    @Data
    public static class FileCategory {
        private String category;
        private Integer type;
        private List<String> extensions;
    }
}
