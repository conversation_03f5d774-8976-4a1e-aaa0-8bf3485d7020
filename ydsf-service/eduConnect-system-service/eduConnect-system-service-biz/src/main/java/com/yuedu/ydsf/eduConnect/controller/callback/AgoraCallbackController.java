package com.yuedu.ydsf.eduConnect.controller.callback;


import com.alibaba.fastjson.JSON;
import com.yuedu.ydsf.eduConnect.service.SsRecordingService;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.AgoraConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.AgoraEventDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RecordFileUpload;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import com.yuedu.ydsf.eduConnect.system.proxy.vo.AgoraEventVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.Objects;

/**
 * 声网事件回调
 *
 * @author: KL
 * @date: 2024/09/30
 **/
@RestController
@AllArgsConstructor
@RequestMapping(value = "/agora")
@Tag(description = "声网回调", name = "声网回调")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AgoraCallbackController {


    private AgoraService agoraService;

    private SsRecordingService ssRecordingService;

    /**
     * 声网回调
     *
     * <AUTHOR>
     * @date 2024年09月30日 10时00分
     */
    @PostMapping("/record/callback")
    @Operation(summary = "声网录制回调", description = "声网录制回调")
    public AgoraEventVO recordCallback(@RequestBody String body,
        HttpServletRequest request,
        HttpServletResponse response) {

        if (!agoraService.verifySign(body, request.getHeader(AgoraConstant.AGORA_SIGNATURE))
            || !agoraService.handlerEvent(JSON.parseObject(body, AgoraEventDTO.class), dto -> {
            return ssRecordingService.recordUpload(dto.getRecordFileUpload());
        })) {
            response.setStatus(HttpStatus.BAD_REQUEST.value());
        }
        return AgoraEventVO.success();
    }
}
