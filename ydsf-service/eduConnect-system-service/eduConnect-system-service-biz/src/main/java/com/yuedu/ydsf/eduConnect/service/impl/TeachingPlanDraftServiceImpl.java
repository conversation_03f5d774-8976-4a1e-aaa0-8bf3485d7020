package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yuedu.permission.api.feign.RemoteLecturerInfoService;
import com.yuedu.permission.api.vo.LecturerInfoVO;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.UserContextHolder;
import com.yuedu.ydsf.common.data.util.TransactionUtils;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.api.constant.AttendClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.ClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncAgoraEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncXiaogjEnum;
import com.yuedu.ydsf.eduConnect.api.constant.PlanStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.SsClassStateEnum;
import com.yuedu.ydsf.eduConnect.api.constant.TeachingPlanOperateEnum;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO.EditLessonDTO;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanDraftQuery;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanVersionVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailDraftVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDraftVO;
import com.yuedu.ydsf.eduConnect.constant.LiveRoomPlanVersionConstants;
import com.yuedu.ydsf.eduConnect.entity.BCourseVod;
import com.yuedu.ydsf.eduConnect.entity.CourseLive;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import com.yuedu.ydsf.eduConnect.entity.SsClass;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.entity.SsLecturer;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailVersion;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanPub;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanVersion;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanVersionNo;
import com.yuedu.ydsf.eduConnect.entity.bo.SsClassTimeBO;
import com.yuedu.ydsf.eduConnect.manager.LiveChannelManager;
import com.yuedu.ydsf.eduConnect.manager.LiveRoomPlanDraftManager;
import com.yuedu.ydsf.eduConnect.manager.LiveRoomPlanVersionManager;
import com.yuedu.ydsf.eduConnect.manager.SsCourseScheduleManager;
import com.yuedu.ydsf.eduConnect.manager.TeachingPlanDraftManager;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanVersionMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsDeviceMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsLecturerMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDetailDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanPubMapper;
import com.yuedu.ydsf.eduConnect.service.BCourseVodService;
import com.yuedu.ydsf.eduConnect.service.CourseLiveService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDetailDraftService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDetailPubService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDetailVersionService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDraftService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanPubService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanVersionNoService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanVersionService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 教学计划草稿表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-29 09:24:03
 */
@Slf4j
@Service
@RefreshScope
@AllArgsConstructor
public class TeachingPlanDraftServiceImpl extends
    ServiceImpl<TeachingPlanDraftMapper, TeachingPlanDraft> implements TeachingPlanDraftService {

    private final TeachingPlanDetailDraftService teachingPlanDetailDraftService;
    private final LiveRoomPlanDraftManager liveRoomPlanDraftManager;
    private final LiveRoomPlanDraftMapper liveRoomPlanDraftMapper;
    private final TeachingPlanVersionService planVersionService;
    private final TeachingPlanVersionNoService versionNoService;
    private final TeachingPlanDraftManager teachingPlanDraftManager;
    private final TeachingPlanPubService teachingPlanPubService;
    private final LiveRoomPlanDetailDraftMapper liveRoomPlanDetailDraftMapper;
    private final TeachingPlanDetailPubService teachingPlanDetailPubService;
    private final TeachingPlanDetailVersionService teachingPlanDetailVersionService;
    private final TeachingPlanPubMapper teachingPlanPubMapper;
    private final TeachingPlanDraftMapper teachingPlanDraftMapper;
    private final LiveRoomPlanVersionManager liveRoomPlanVersionManager;
    private final LiveRoomPlanVersionMapper liveRoomPlanVersionMapper;
    private final RemoteCourseService remoteCourseService;
    private final LiveChannelManager liveChannelManager;
    private final CourseLiveService courseLiveService;
    private final BCourseVodService courseVodService;
    private final SsClassMapper ssClassMapper;
    private final RemoteLecturerInfoService remoteLecturerInfoService;
    private final SsLecturerMapper ssLecturerMapper;
    private final SsDeviceMapper ssDeviceMapper;
    private final SsClassTimeMapper ssClassTimeMapper;
    private final SsCourseScheduleManager ssCourseScheduleManager;
    private final TeachingPlanDetailDraftMapper teachingPlanDetailDraftMapper;


    @Value("${compatible.run:false}")
    private boolean startCompatible;

    /**
     * 通过条件查询教学计划
     *
     * @return List<TeachingPlanDraftVO>
     */
    @Override
    public List<TeachingPlanDraftVO> listByIds(List<Long> ids) {

        return baseMapper.selectList(Wrappers.lambdaQuery(TeachingPlanDraft.class)
                .in(TeachingPlanDraft::getLiveRoomPlanId, ids)
                .ne(TeachingPlanDraft::getPlanStatus, PlanStatusEnum.STATUS_ENUM_2.code)
            ).stream()
            .map(teachingPlanDraft -> {
                TeachingPlanDraftVO vo = new TeachingPlanDraftVO();
                BeanUtils.copyProperties(teachingPlanDraft, vo);
                return vo;
            })
            .toList();
    }

    /**
     * 分页查询
     *
     * @param pageRequest 分页
     * @param lectureId   主讲ID
     * @return page
     */
    @Override
    public Page<TeachingPlanDraftVO> getPage(Page pageRequest,TeachingPlanDraftQuery teachingPlanDraftQuery) {
        Page<TeachingPlanDraft> page = (Page<TeachingPlanDraft>) teachingPlanDraftMapper.selectCustomPage(
            pageRequest, teachingPlanDraftQuery);

        List<Long> ids = page.getRecords().stream().map(TeachingPlanDraft::getLiveRoomPlanId)
            .toList();
        List<Long> planIds = page.getRecords().stream().map(TeachingPlanDraft::getId).toList();

        //课节数量
        Map<Long, Integer> teachCountMap;
        Map<Long, String> teachDetailsMap;
        Map<Long, Long> teachRoomMap;
        Map<Long, LocalDateTime> publishTimeMap;
        Map<Long, String> publishMap;
        if (ObjectUtil.isNotEmpty(planIds)) {
            teachCountMap = teachingPlanDetailDraftService.countTeachs(planIds);
            List<TeachingPlanPub> publishList = teachingPlanPubMapper.selectList(
                Wrappers.lambdaQuery(TeachingPlanPub.class)
                    .in(TeachingPlanPub::getTeachingPlanId, planIds));
            publishTimeMap = publishList.stream().collect(Collectors.toMap(
                TeachingPlanPub::getTeachingPlanId,
                TeachingPlanPub::getUpdateTime
            ));
            publishMap = publishList.stream().collect(Collectors.toMap(
                TeachingPlanPub::getTeachingPlanId,
                tp -> {
                    String updateBy = tp.getUpdateBy();
                    if (updateBy == null) {
                        updateBy = "";
                    }
                    return updateBy;
                }
            ));
        } else {
            teachCountMap = null;
            publishTimeMap = null;
            publishMap = null;
        }
        if (ObjectUtil.isNotEmpty(ids)) {
            List<LiveRoomPlanVersionVO> teachDetailsList = liveRoomPlanVersionManager.listByIds(
                ids);
            teachDetailsMap = teachDetailsList.stream()
                .collect(Collectors.toMap(
                    LiveRoomPlanVersionVO::getPlanId,
                    LiveRoomPlanVersionVO::getPlanName
                ));
            teachRoomMap = teachDetailsList.stream()
                .collect(Collectors.toMap(
                    LiveRoomPlanVersionVO::getPlanId,
                    LiveRoomPlanVersionVO::getLiveRoomId
                ));
        } else {
            teachDetailsMap = null;
            teachRoomMap = null;
        }
        Page<TeachingPlanDraftVO> resultPage = new Page<>(page.getCurrent(), page.getSize(),
            page.getTotal());
        List<TeachingPlanDraftVO> resultRecords = page.getRecords().stream()
            .map(entity -> {
                TeachingPlanDraftVO vo = new TeachingPlanDraftVO();
                BeanUtil.copyProperties(entity, vo);
                vo.setTeachCount(teachCountMap.getOrDefault(entity.getId(), 0));
                vo.setPlanName(teachDetailsMap.getOrDefault(entity.getLiveRoomPlanId(), ""));
                vo.setLiveRoomId(teachRoomMap.getOrDefault(entity.getLiveRoomPlanId(), 0L));
                vo.setPublishBy(publishMap.getOrDefault(entity.getId(), ""));
                if (ObjectUtil.isNotEmpty(publishTimeMap.get(entity.getId()))) {
                    vo.setPublishTime(publishTimeMap.get(entity.getId()));
                }
                return vo;
            }).toList();
        resultPage.setRecords(resultRecords);
        return resultPage;
    }

    /**
     * 教学计划详情
     *
     * @return TeachingPlanDraftVO
     */
    @Override
    public TeachingPlanDraftVO getDetails(Integer id) {
        TeachingPlanDraftVO detailsVO = new TeachingPlanDraftVO();
        TeachingPlanDraft entity = getOne(Wrappers.lambdaQuery(TeachingPlanDraft.class)
            .eq(TeachingPlanDraft::getId, id)
        );
        if (ObjectUtil.isEmpty(entity)) {
            throw new BizException(" 该id没有对应教学计划");
        }
        //查询课节数
        List<Long> ids = List.of(id.longValue());
        Map<Long, Integer> planCountMap = teachingPlanDetailDraftService.countTeachs(ids);
        //直播间计划名称
        LiveRoomPlanVersion liveRoomPlan = liveRoomPlanVersionMapper.selectOne(
            Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                .eq(LiveRoomPlanVersion::getPlanId, entity.getLiveRoomPlanId())
                .eq(LiveRoomPlanVersion::getOnlineVersion, YesNoEnum.YES.getCode()));
        BeanUtil.copyProperties(entity, detailsVO);
        List<TeachingPlanDetailDraftVO> teachDetails = teachingPlanDetailDraftService.listPlans(id,
            entity.getCourseId(), entity.getLiveRoomPlanId());
        if (ObjectUtil.isNotEmpty(teachDetails)) {
            detailsVO.setTeachDetails(teachDetails);
            detailsVO.setTeachCount(planCountMap.getOrDefault(id.longValue(), 0));
            detailsVO.setLessonCount(teachDetails.get(0).getLessonCount());
            detailsVO.setPlanCount(teachDetails.get(0).getPlanCount());
        }
        if (ObjectUtil.isNotEmpty(liveRoomPlan)) {
            detailsVO.setPlanName(liveRoomPlan.getPlanName());
        }
        return detailsVO;
    }

  /**
   * 新增教学计划草稿表
   *
   * @param teachingPlanDraftQuery
   * @return void
   * <AUTHOR>
   * @date 2024/12/6 9:45
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void savePlan(TeachingPlanDraftQuery teachingPlanDraftQuery) {
    log.info("开始新增教学计划草稿, 参数: {}", teachingPlanDraftQuery);
    try {
      // 校验选择的直播间计划是否存在已过期的明细
      liveRoomPlanDraftManager.checkHasExpiredDetail(teachingPlanDraftQuery.getLiveRoomPlanId());

            // 检查是否已存在相同计划
            boolean exists =
                this.exists(
                    Wrappers.lambdaQuery(TeachingPlanDraft.class)
                        .eq(
                            TeachingPlanDraft::getLiveRoomPlanId,
                            teachingPlanDraftQuery.getLiveRoomPlanId()));

            if (exists) {
                log.warn("该直播间计划已存在教学计划, liveRoomPlanId: {}",
                    teachingPlanDraftQuery.getLiveRoomPlanId());
                throw new BizException("该直播间计划已存在教学计划");
            }

            TeachingPlanDraft teachingPlanDraft = new TeachingPlanDraft();
            BeanUtils.copyProperties(teachingPlanDraftQuery, teachingPlanDraft);
            // 设置初始状态为未发布
            teachingPlanDraft.setPlanStatus(PlanStatusEnum.STATUS_ENUM_0.code);
            // 处理所属阶段
            handlePlanStage(teachingPlanDraft);

            boolean saved = save(teachingPlanDraft);
            if (!saved) {
                log.error("保存教学计划失败, teachingPlanDraft: {}", teachingPlanDraft);
                throw new BizException("保存教学计划失败");
            }
            teachingPlanDraftQuery.setId(teachingPlanDraft.getId());
            // 改为使用 AsyncHelper
            teachingPlanDraftManager.handleTeachingDetailDraft(teachingPlanDraftQuery, false);
            log.info("新增教学计划草稿成功, id: {}", teachingPlanDraft.getId());
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.warn("新增教学计划草稿异常", e);
            throw new BizException("新增教学计划草稿失败: " + e.getMessage());
        }
    }

    /**
     * 编辑教学计划
     *
     * @param teachingPlanDraftQuery
     * @return void
     * <AUTHOR>
     * @date 2024/12/6 10:17
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editPlan(TeachingPlanDraftQuery teachingPlanDraftQuery) {
        log.info("开始编辑教学计划, 参数: {}", teachingPlanDraftQuery);
        try {
            // 查询原计划
            TeachingPlanDraft oldPlan = this.getById(teachingPlanDraftQuery.getId());
            if (Objects.isNull(oldPlan)) {
                throw new BizException("教学计划不存在");
            }

            // 检查计划状态
            liveRoomPlanDraftManager.checkPlanEndStatus(oldPlan.getLiveRoomPlanId());

            // 更新计划
            TeachingPlanDraft updatePlan = new TeachingPlanDraft();
            BeanUtils.copyProperties(teachingPlanDraftQuery, updatePlan);
            // 设置为未发布状态
            updatePlan.setPlanStatus(PlanStatusEnum.STATUS_ENUM_0.code);
            // 设置教学计划关联的直播间计划所对应的阶段
            handlePlanStage(updatePlan);

            boolean updated = updateById(updatePlan);
            if (!updated) {
                log.error("更新教学计划失败, updatePlan: {}", updatePlan);
                throw new BizException("更新教学计划失败");
            }
            // 异步处理课程id与直播间计划id 插入到教学计划ea_teaching_plan_detail_draft表中
            teachingPlanDraftQuery.setIsEdit(Boolean.TRUE);
            teachingPlanDraftManager.handleTeachingDetailDraft(
                teachingPlanDraftQuery, true);
            // 修改教学计划发布消息队列
            teachingPlanDraftManager.buildAndSendOperateMqMessage(
                updatePlan,
                TeachingPlanOperateEnum.EDIT_PLAN.code,
                oldPlan.getCourseId(),
                oldPlan.getLectureId(),
                new ArrayList<>(),
                null);
            log.info("编辑教学计划成功, id: {}", teachingPlanDraftQuery.getId());
        } catch (Exception e) {
            e.getStackTrace();
            log.error("编辑教学计划异常", e);
            throw new BizException("编辑教学计划失败: " + e.getMessage());
        }
    }

    /**
     * 设置教学计划关联的直播间计划所对应的阶段
     *
     * @param updatePlan
     * @return void
     * <AUTHOR>
     * @date 2024/12/9 13:36
     */
    private void handlePlanStage(TeachingPlanDraft updatePlan) {
        CourseDTO courseDTO = new CourseDTO();
        courseDTO.setCourseIdList(Collections.singletonList(updatePlan.getCourseId().intValue()));
        R<List<CourseVO>> listR = remoteCourseService.getCourseListByIds(courseDTO);
        if (listR.isOk() && CollectionUtils.isNotEmpty(listR.getData())) {
            CourseVO courseVO = listR.getData().get(0);
            updatePlan.setStage(courseVO.getStageId());
        }
    }

    /**
     * 处理计划版本信息
     *
     * @param planId
     * @return void
     * <AUTHOR>
     * @date 2024/12/6 10:52
     */
    private void handlePlanVersion(Long planId) {
        log.debug("开始处理教学计划版本信息, planId: {}", planId);
        try {
            TeachingPlanDraft currentPlan = this.getById(planId);
            if (Objects.isNull(currentPlan)) {
                log.warn("教学计划不存在, planId: {}", planId);
                return;
            }
            // 将生成的新版本号插入到版本号表
            TeachingPlanVersionNo teachingPlanVersionNo = new TeachingPlanVersionNo();
            teachingPlanVersionNo.setPlanId(planId);
            versionNoService.save(teachingPlanVersionNo);

            // 保存新版本
            TeachingPlanVersion versionRecord = new TeachingPlanVersion();
            BeanUtils.copyProperties(currentPlan, versionRecord);
            versionRecord.setId(null);
            versionRecord.setVersion(teachingPlanVersionNo.getId());
            versionRecord.setTeachingPlanId(planId);
            planVersionService.save(versionRecord);
            log.debug("保存教学计划版本成功, planId: {}, version: {}", planId,
                teachingPlanVersionNo.getId());

        } catch (Exception e) {
            e.getStackTrace();
            log.error("处理教学计划版本信息异常", e);
            throw new BizException("处理教学计划版本失败: " + e.getMessage());
        }
    }

    /**
     * 删除计划
     *
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2024/12/6 10:55
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deltePlan(ArrayList<Long> list) {
        log.info("开始删除教学计划, 待删除ID: {}", list);

        // 检查是否有门店预约直播课
        long bookCount = courseLiveService.countByTeachingPlanId(list);
        if (bookCount > 0) {
            throw new BizException("已有门店预约的教学计划不可删除");
        }

        long vodCount = courseVodService.count(Wrappers.lambdaQuery(BCourseVod.class)
            .in(BCourseVod::getTeachingPlanId, list));
        // 检查是否有门店预约点播课
        if (vodCount > 0) {
            throw new BizException("已有门店预约点播课的教学计划不可删除");
        }

        try {
            for (Long planId : list) {
                // 异步处理课程id与直播间计划id 插入到教学计划ea_teaching_plan_detail_draft表中
                TeachingPlanDraftQuery teachingPlanDraftQuery = new TeachingPlanDraftQuery();
                teachingPlanDraftQuery.setId(planId);

                log.info("删除教学计划草稿数据,planId={}", teachingPlanDraftQuery.getId());
                // 删除教学计划草稿数据
                int deleteCount =
                    teachingPlanDetailDraftMapper.delete(
                        Wrappers.lambdaQuery(TeachingPlanDetailDraft.class)
                            .eq(TeachingPlanDetailDraft::getPlanId,
                                teachingPlanDraftQuery.getId()));
                log.info("删除原教学计划草稿数据,planId={},删除条数={}",
                    teachingPlanDraftQuery.getId(),
                    deleteCount);

                // 删除教学计划发布消息队列
                TeachingPlanDraft plan = this.getById(planId);
                teachingPlanDraftManager.buildAndSendOperateMqMessage(
                    plan,
                    TeachingPlanOperateEnum.DELETE_PLAN.code,
                    null,
                    null,
                    new ArrayList<>(),
                    null);
            }
            // 执行软删除
            boolean success =
                this.update(
                    Wrappers.lambdaUpdate(TeachingPlanDraft.class)
                        .in(TeachingPlanDraft::getId, list)
                        .set(TeachingPlanDraft::getUpdateBy, SecurityUtils.getUser().getName())
                        .set(TeachingPlanDraft::getUpdateTime, LocalDateTime.now())
                        .set(TeachingPlanDraft::getDelFlag, DelFlagEnum.DELFLAG_1.code));

            teachingPlanPubService.update(
                Wrappers.lambdaUpdate(TeachingPlanPub.class)
                    .in(TeachingPlanPub::getTeachingPlanId, list)
                    .set(TeachingPlanPub::getUpdateBy, SecurityUtils.getUser().getName())
                    .set(TeachingPlanPub::getUpdateTime, LocalDateTime.now())
                    .set(TeachingPlanPub::getDelFlag, DelFlagEnum.DELFLAG_1.code));

            if (startCompatible) {
                deleteClassTime(list);
            }

            // 删除已发布教学计划明细
            teachingPlanDetailPubService.update(
                Wrappers.lambdaUpdate(TeachingPlanDetailPub.class)
                    .in(TeachingPlanDetailPub::getPlanId, list)
                    .set(TeachingPlanDetailPub::getUpdateBy, SecurityUtils.getUser().getName())
                    .set(TeachingPlanDetailPub::getUpdateTime, LocalDateTime.now())
                    .set(TeachingPlanDetailPub::getDelFlag, DelFlagEnum.DELFLAG_1.code));

            if (!success) {
                log.error("删除教学计划失败, planIds: {}", list);
                throw new BizException("删除教学计划失败");
            }

            log.info("删除教学计划成功, planIds: {}", list);
        } catch (Exception e) {
            e.getStackTrace();
            log.error("删除教学计划异常", e);
            throw new BizException("删除教学计划失败: " + e.getMessage());
        }
    }


    /**
     * 单独删除操作
     *
     * <AUTHOR>
     * @date 2025年01月17日 08时32分
     */
    private void deleteUpdateClassTime(List<Long> list) {
        log.info("spring1单独删除操作同步操作, planIds: {}", list);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(s -> {
                SsClassTime ssClassTime = new SsClassTime();
                ssClassTime.setIsSyncAgora(IsSyncAgoraEnum.ISSYNCAGORA_0.CODE());
                ssClassTime.setMtime(LocalDateTime.now());
                ssClassTime.setModifer(SecurityUtils.getUser().getName());
                ssClassTime.setTeachingPlanDetailsId(-1l);
                ssClassTimeMapper.update(ssClassTime, Wrappers.<SsClassTime>lambdaUpdate()
                    .eq(SsClassTime::getTeachingPlanDetailsId, s)
                    .and(d -> d.apply("room_uuid is null or room_uuid = ''"))
                );
            });
        }
    }

    /**
     * 删除课次操作
     *
     * <AUTHOR>
     * @date 2025年01月16日 08时37分
     */
    private void deleteClassTime(ArrayList<Long> list) {
        log.info("spring1开始删除课次同步操作, planIds: {}", list);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(planId -> {
                teachingPlanDetailPubService.list(Wrappers.<TeachingPlanDetailPub>lambdaQuery()
                    .eq(TeachingPlanDetailPub::getPlanId, planId)
                    .eq(TeachingPlanDetailPub::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                ).forEach(s -> {
                    SsClassTime ssClassTime = new SsClassTime();
                    ssClassTime.setIsSyncAgora(IsSyncAgoraEnum.ISSYNCAGORA_0.CODE());
                    ssClassTime.setMtime(LocalDateTime.now());
                    ssClassTime.setModifer(SecurityUtils.getUser().getName());
                    ssClassTime.setTeachingPlanDetailsId(-1l);
                    ssClassTimeMapper.update(ssClassTime, Wrappers.<SsClassTime>lambdaUpdate()
                        .eq(SsClassTime::getTeachingPlanDetailsId, s.getId())
                        .and(d -> d.apply("room_uuid is null or room_uuid = ''"))
                    );
                });
            });
        }
    }

    /**
     * 发布教学计划
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/12/6 14:50
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(Long[] ids, Boolean forcePublish) {
        log.info("开始发布教学计划, ids: {}", Arrays.toString(ids));

        try {
            checkTeachingPlanLessonNum(ids);
            // 1. 参数校验
            validatePublishParams(ids);

            // 2. 批量查询教学计划
            List<TeachingPlanDraft> teachingPlanDrafts = batchGetTeachingPlans(ids);

            // 3. 批量查询和校验直播间计划
            Map<Long, LiveRoomPlanDraft> liveRoomPlanMap = batchGetLiveRoomPlans(
                teachingPlanDrafts);

            // 4. 并行处理每个教学计划
            for (TeachingPlanDraft draft : teachingPlanDrafts) {
                draft.setPlanStatus(PlanStatusEnum.STATUS_ENUM_1.code);
                processTeachingPlan(draft,
                    liveRoomPlanMap.get(draft.getLiveRoomPlanId()));
            }

            log.info("教学计划批量发布成功, ids: {}", Arrays.toString(ids));
        } catch (Exception e) {
            e.getStackTrace();
            log.error("发布教学计划异常", e);
            throw new BizException("发布教学计划失败: " + e.getMessage());
        }
    }

    /**
     * 直播间计划数与课节数是否一致
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/12/6 19:57
     */
    private void checkTeachingPlanLessonNum(Long[] ids) {
        List<TeachingPlanDraft> planDrafts = batchGetTeachingPlans(ids);
        for (Long id : ids) {
            List<TeachingPlanDetailDraft> teachingPlanDetailDrafts = getTeachingPlanDetailDrafts(
                id);
            planDrafts.stream()
                .filter(plan -> plan.getId().equals(id))
                .findFirst()
                .ifPresent(
                    plan -> {
                        int teachingDetailNum = teachingPlanDetailDrafts.size();
                        int lessonNum = CollectionUtils.size(
                            getPublishLessonList(plan.getCourseId()));
                        if (lessonNum == 0) {
                            throw new BizException("课程下的课节数为空，发布失败！");
                        }
                        if (lessonNum != teachingDetailNum) {
                            log.info("查询到节数为: {},教学计划数为：{}", lessonNum,
                                teachingDetailNum);
                        }
                    });
        }
    }

    /**
     * 参数校验
     */
    private void validatePublishParams(Long[] ids) {
        if (Objects.isNull(ids) || ids.length == 0) {
            log.error("发布教学计划参数为空");
            throw new BizException("发布教学计划参数不能为空");
        }

        Set<Long> idSet = Arrays.stream(ids).collect(Collectors.toSet());
        if (idSet.size() != ids.length) {
            log.error("发布教学计划包含重复ID");
            throw new BizException("发布教学计划包含重复ID");
        }
    }

    /**
     * 批量获取教学计划
     */
    private List<TeachingPlanDraft> batchGetTeachingPlans(Long[] ids) {
        List<TeachingPlanDraft> teachingPlanDrafts =
            list(
                Wrappers.lambdaQuery(TeachingPlanDraft.class)
                    .in(TeachingPlanDraft::getId, Arrays.asList(ids)));
        if (teachingPlanDrafts.size() != ids.length) {
            log.error("部分教学计划不存在");
            throw new BizException("部分教学计划不存在");
        }

        // 校验每个计划的状态
        teachingPlanDrafts.forEach(this::checkPlanStatus);
        return teachingPlanDrafts;
    }

    /**
     * 批量获取直播间计划
     */
    private Map<Long, LiveRoomPlanDraft> batchGetLiveRoomPlans(
        List<TeachingPlanDraft> teachingPlanDrafts) {
        List<Long> liveRoomPlanIds =
            teachingPlanDrafts.stream().map(TeachingPlanDraft::getLiveRoomPlanId).toList();

        // 先查询在线版本的直播间计划ID
        List<LiveRoomPlanVersion> onlineVersions =
            liveRoomPlanVersionMapper.selectList(
                Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                    .in(LiveRoomPlanVersion::getPlanId, liveRoomPlanIds)
                    .eq(
                        LiveRoomPlanVersion::getOnlineVersion,
                        LiveRoomPlanVersionConstants.ONLINE_VERSION));

        if (CollectionUtils.isEmpty(onlineVersions)) {
            log.error("未找到在线版本的直播间计划");
            throw new BizException("未找到在线版本的直播间计划");
        }

        // 获取在线版本的计划ID列表
        List<Long> onlinePlanIds = onlineVersions.stream().map(LiveRoomPlanVersion::getPlanId)
            .toList();

        // 查询对应的直播间计划
        List<LiveRoomPlanDraft> liveRoomPlans =
            liveRoomPlanDraftMapper.selectList(
                Wrappers.lambdaQuery(LiveRoomPlanDraft.class)
                    .in(LiveRoomPlanDraft::getId, onlinePlanIds));

        return liveRoomPlans.stream()
            .collect(Collectors.toMap(LiveRoomPlanDraft::getId, plan -> plan));
    }

    /**
     * 处理单个教学计划
     *
     * @param teachingPlanDraft
     * @param liveRoomPlan
     * @return void
     * <AUTHOR>
     * @date 2024/12/7 11:47
     */
    private void processTeachingPlan(
        TeachingPlanDraft teachingPlanDraft, LiveRoomPlanDraft liveRoomPlan) {
        Long planId = teachingPlanDraft.getId();
        log.debug("开始处理教学计划, planId: {}", planId);

        try {
            // 1. 获取直播间排课明细
            List<LiveRoomPlanDetailDraft> planDetails = getPlanDetails(liveRoomPlan.getId());

            // 2. 更新计划状态
            updatePlanStatus(planId);

            // 3. 保存发布计划
            savePublishedPlan(teachingPlanDraft);

            // 4. 并行处理教学计划明细
            handleTeachingPlanDetailsAsync(planId, teachingPlanDraft, planDetails);

            // 保存版本记录
            handlePlanVersion(teachingPlanDraft.getId());

            // 匹配完成开始创建声网房间
            TransactionUtils.afterCommitSyncExecute(
                () ->
                    liveChannelManager.createRoomByTeachingPlan(
                        Collections.singletonList(teachingPlanDraft)));
            log.debug("教学计划处理完成, planId: {}", planId);
        } catch (Exception e) {
            e.getStackTrace();
            log.error("处理教学计划异常, planId: {}", planId, e);
            throw new BizException(
                String.format("处理教学计划失败[planId=%d]: %s", planId, e.getMessage()));
        }
    }

    /**
     * 获取直播间计划明细
     *
     * @param liveRoomPlanId
     * @return java.util.List<com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft>
     * <AUTHOR>
     * @date 2024/12/9 9:16
     */
    private List<LiveRoomPlanDetailDraft> getPlanDetails(Long liveRoomPlanId) {
        List<LiveRoomPlanDetailDraft> planDetails =
            liveRoomPlanDetailDraftMapper.selectList(
                Wrappers.lambdaQuery(LiveRoomPlanDetailDraft.class)
                    .eq(LiveRoomPlanDetailDraft::getPlanId, liveRoomPlanId)
                    .orderByAsc(LiveRoomPlanDetailDraft::getLessonOrder));

        if (CollectionUtils.isEmpty(planDetails)) {
            log.error("直播间计划下不存在有效的排课明细, liveRoomPlanId: {}", liveRoomPlanId);
            throw new BizException("直播间计划下不存在有效的排课明细");
        }
        return planDetails;
    }

    /**
     * 更新计划状态
     */
    private void updatePlanStatus(Long planId) {
        boolean updateSuccess =
            update(
                Wrappers.lambdaUpdate(TeachingPlanDraft.class)
                    .eq(TeachingPlanDraft::getId, planId)
                    .eq(TeachingPlanDraft::getPlanStatus, PlanStatusEnum.STATUS_ENUM_0.code) // 乐观锁
                    .set(TeachingPlanDraft::getUpdateBy, UserContextHolder.getUserName())
                    .set(TeachingPlanDraft::getUpdateTime, LocalDateTime.now())
                    .set(TeachingPlanDraft::getPlanStatus, PlanStatusEnum.STATUS_ENUM_1.code));

        if (!updateSuccess) {
            log.error("更新教学计划状态失败, planId: {}", planId);
            throw new BizException("更新教学计划状态失败");
        }
    }

    /**
     * 保存已发布计划
     *
     * @param teachingPlanDraft
     * @return void
     * <AUTHOR>
     * @date 2024/12/9 13:44
     */
    private void savePublishedPlan(TeachingPlanDraft teachingPlanDraft) {
        log.debug("开始保存/更新已发布教学计划, teachingPlanId: {}", teachingPlanDraft.getId());

        try {
            // 先查询是否已存在发布记录
            TeachingPlanPub existingPlan =
                teachingPlanPubService.getOne(
                    Wrappers.lambdaQuery(TeachingPlanPub.class)
                        .eq(TeachingPlanPub::getTeachingPlanId, teachingPlanDraft.getId())
                        .last("LIMIT 1"));

            boolean success;
            if (existingPlan != null) {
                log.debug("找到已存在的发布记录,进行更新操作, publishId: {}", existingPlan.getId());
                success =
                    teachingPlanPubService.update(
                        Wrappers.lambdaUpdate(TeachingPlanPub.class)
                            .eq(TeachingPlanPub::getId, existingPlan.getId())
                            .set(TeachingPlanPub::getLiveRoomPlanId,
                                teachingPlanDraft.getLiveRoomPlanId())
                            .set(TeachingPlanPub::getCourseId, teachingPlanDraft.getCourseId())
                            .set(TeachingPlanPub::getCourseName, teachingPlanDraft.getCourseName())
                            .set(TeachingPlanPub::getLectureId, teachingPlanDraft.getLectureId())
                            .set(TeachingPlanPub::getLectureName,
                                teachingPlanDraft.getLectureName())
                            .set(TeachingPlanPub::getStage, teachingPlanDraft.getStage())
                            .set(TeachingPlanPub::getUpdateTime, LocalDateTime.now())
                            .set(TeachingPlanPub::getUpdateBy, UserContextHolder.getUserName()));

                if (success) {
                    log.debug("更新已发布教学计划成功, publishId: {}", existingPlan.getId());
                } else {
                    log.error("更新已发布教学计划失败, publishId: {}", existingPlan.getId());
                    throw new BizException("更新已发布教学计划失败");
                }
            } else {
                log.debug("未找到发布记录,执行新增操作");
                // 创建新的发布记录
                TeachingPlanPub teachingPlanPub = new TeachingPlanPub();
                BeanUtils.copyProperties(teachingPlanDraft, teachingPlanPub);
                teachingPlanPub.setId(null);
                teachingPlanPub.setTeachingPlanId(teachingPlanDraft.getId());
                teachingPlanPub.setCreateTime(LocalDateTime.now());
                teachingPlanPub.setCreateBy(UserContextHolder.getUserName());
                teachingPlanPub.setUpdateTime(LocalDateTime.now());
                teachingPlanPub.setUpdateBy(UserContextHolder.getUserName());
                success = teachingPlanPubService.save(teachingPlanPub);
                if (success) {
                    log.debug("新增已发布教学计划成功, teachingPlanId: {}",
                        teachingPlanDraft.getId());

                    if (startCompatible) {
                        saveClassInfo(teachingPlanPub, teachingPlanDraft.getLiveRoomPlanId());
                    }

                } else {
                    log.error("新增已发布教学计划失败, teachingPlanId: {}",
                        teachingPlanDraft.getId());
                    throw new BizException("新增已发布教学计划失败");
                }
            }
        } catch (Exception e) {
            e.getStackTrace();
            log.error("保存/更新已发布教学计划异常, teachingPlanId: {}", teachingPlanDraft.getId(),
                e);
            throw new BizException("保存/更新已发布教学计划失败: " + e.getMessage());
        }
    }


    /**
     * 保存班级信息
     *
     * <AUTHOR>
     * @date 2025年01月14日 16时17分
     */
    private void saveClassInfo(TeachingPlanPub teachingPlanPub, Long liveRoomPlanId) {
        log.info("同步spring1班级信息, teachingPlanId: {},liveRoomPlanId:{}", teachingPlanPub,
            liveRoomPlanId);
        LiveRoomPlanVersion liveRoomPlanVersion = liveRoomPlanVersionMapper.selectOne(
            Wrappers.<LiveRoomPlanVersion>lambdaQuery()
                .eq(LiveRoomPlanVersion::getOnlineVersion,
                    LiveRoomPlanVersionConstants.ONLINE_VERSION)
                .eq(LiveRoomPlanVersion::getPlanId, liveRoomPlanId)
        );

        if (Objects.isNull(liveRoomPlanVersion)) {
            log.error("未找到线上版本直播间计划,liveRoomPlanId: {}", liveRoomPlanId);
            throw new BizException("未找到线上版本直播间计划");
        }

        SsClass ssClass = new SsClass();
        ssClass.setTeachingPlanId(teachingPlanPub.getTeachingPlanId());
        ssClass.setClassName(liveRoomPlanVersion.getPlanName());
        ssClass.setGrade(liveRoomPlanVersion.getStage());
        ssClass.setClassType(ClassTypeEnum.ATTENDCLASSTYPE_0.CODE);
        ssClass.setClassState(SsClassStateEnum.STATE_0.CODE);
        ssClass.setIsSyncXiaogj(IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE);
        ssClassMapper.insert(ssClass);
    }

    /**
     * 异步处理教学计划明细
     *
     * @param planId
     * @param teachingPlanDraft
     * @param planDetails
     * @return void
     * <AUTHOR>
     * @date 2024/12/7 11:46
     */
    private void handleTeachingPlanDetailsAsync(
        Long planId, TeachingPlanDraft teachingPlanDraft,
        List<LiveRoomPlanDetailDraft> planDetails) {

        log.debug("开始处理教学计划明细, planId: {}", planId);

        try {
            // 1. 获取教学计划明细
            List<TeachingPlanDetailDraft> detailDrafts = getTeachingPlanDetails(planId);

            // 2. 获取课节信息
            List<LessonVO> lessonList = getPublishLessonList(teachingPlanDraft.getCourseId());

            // 4. 并行构建发布数据
            processTeachingPlanRematch(teachingPlanDraft);
            List<TeachingPlanDetailVersion> versions =
                buildDetailVersions(
                    detailDrafts, planDetails, lessonList, teachingPlanDraft,
                    planId);
            log.debug("构建教学计划版本数据完成, planId: {}, size: {}", planId,
                versions.size());

            // 5. 等待所有任务完成并保存版本数据
            teachingPlanDetailVersionService.saveBatch(versions);
            log.debug("保存教学计划版本数据完成, planId: {}", planId);
            log.debug("教学计划明细处理完成, planId: {}", planId);
        } catch (Exception e) {
            e.getStackTrace();
            log.error("处理教学计划明细异常, planId: {}", planId, e);
            throw new BizException("处理教学计划明细失败: " + e.getMessage());
        }
    }

    /**
     * 校验计划状态
     */
    private void checkPlanStatus(TeachingPlanDraft draft) {
        if (PlanStatusEnum.STATUS_ENUM_1.code.equals(draft.getPlanStatus())) {
            log.warn("教学计划已发布，不能重复发布, planId: {}", draft.getId());
            throw new BizException("教学计划已发布，不能重复发布");
        }

        if (DelFlagEnum.DELFLAG_1.code.equals(draft.getDelFlag())) {
            log.warn("教学计划已删除，不能发布, planId: {}", draft.getId());
            throw new BizException("教学计划已删除，不能发布");
        }
    }

    /**
     * 构建教学计划明细版本数据
     *
     * @param detailDrafts
     * @param planDetails
     * @param lessonList
     * @param teachingPlanDraft
     * @param planId
     * @return java.util.List<com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailVersion>
     * <AUTHOR>
     * @date 2024/12/9 9:22
     */
    private List<TeachingPlanDetailVersion> buildDetailVersions(
        List<TeachingPlanDetailDraft> detailDrafts,
        List<LiveRoomPlanDetailDraft> planDetails,
        List<LessonVO> lessonList,
        TeachingPlanDraft teachingPlanDraft,
        Long planId) {

        log.info("开始构建教学计划明细版本数据, planId: {}, 明细数量: {}", planId,
            detailDrafts.size());

        try {
            return IntStream.range(0, detailDrafts.size())
                .mapToObj(
                    i -> {
                        // 检查索引是否越界
                        if (i >= lessonList.size()) {
                            return null;
                        }
                        TeachingPlanDetailDraft detailDraft = detailDrafts.get(i);
                        LiveRoomPlanDetailDraft planDetail = planDetails.get(i);
                        LessonVO lesson = lessonList.get(i);

                        TeachingPlanDetailVersion detailVersion = new TeachingPlanDetailVersion();
                        // 基本信息
                        detailVersion.setPlanId(planId);
                        detailVersion.setPeriod(detailDraft.getLessonOrder());

                        // 课程信息
                        detailVersion.setCourseId(teachingPlanDraft.getCourseId());
                        detailVersion.setCourseName(teachingPlanDraft.getCourseName());
                        detailVersion.setLessonId(lesson.getId().longValue());
                        detailVersion.setLessonName(lesson.getLessonName());

                        // 讲师信息
                        detailVersion.setLectureId(detailDraft.getLectureId());
                        detailVersion.setLectureName(detailDraft.getLectureName());

                        // 教材信息
                        detailVersion.setBookId(lesson.getBookId().longValue());
                        detailVersion.setBookName(lesson.getBookName());

                        // 时间信息
                        detailVersion.setTimeSlotId(detailDraft.getTimeSlotId());
                        detailVersion.setClassDate(planDetail.getClassDate());
                        detailVersion.setClassStartTime(planDetail.getClassStartTime());
                        detailVersion.setClassEndTime(planDetail.getClassEndTime());
                        detailVersion.setClassStartDateTime(planDetail.getClassStartDateTime());
                        detailVersion.setClassEndDateTime(planDetail.getClassEndDateTime());
                        return detailVersion;
                    })
                .filter(Objects::nonNull)
                .toList();

        } catch (Exception e) {
            e.getStackTrace();
            log.error("构建教学计划明细版本数据异常, planId: {}", planId, e);
            throw new BizException("构建教学计划明细版本数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取教学计划明细
     *
     * @param planId
     * @return java.util.List<com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailDraft>
     * <AUTHOR>
     * @date 2024/12/7 11:48
     */
    private List<TeachingPlanDetailDraft> getTeachingPlanDetails(Long planId) {
        log.info("开始获取教学计划明细, planId: {}", planId);

        try {
            List<TeachingPlanDetailDraft> detailDrafts = getTeachingPlanDetailDrafts(planId);

            if (CollectionUtils.isEmpty(detailDrafts)) {
                log.error("教学计划下不存在明细数据, planId: {}", planId);
                throw new BizException("教学计划下不存在明细数据");
            }

            log.info("获取教学计划明细成功, planId: {}, 明细数量: {}", planId, detailDrafts.size());
            return detailDrafts;

        } catch (Exception e) {
            e.getStackTrace();
            log.error("获取教学计划明细异常, planId: {}", planId, e);
            throw new BizException("获取教学计划明细失败: " + e.getMessage());
        }
    }

    /**
     * 根据教学计划id获取有效的教学计划明细列表
     *
     * @param planId
     * @return java.util.List<com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailDraft>
     * <AUTHOR>
     * @date 2024/12/6 20:00
     */
    private List<TeachingPlanDetailDraft> getTeachingPlanDetailDrafts(Long planId) {
        return teachingPlanDetailDraftService.list(
            Wrappers.lambdaQuery(TeachingPlanDetailDraft.class)
                .eq(TeachingPlanDetailDraft::getPlanId, planId)
                .orderByAsc(TeachingPlanDetailDraft::getLessonOrder));
    }

    /**
     * 获取课节信息列表
     */
    private List<LessonVO> getPublishLessonList(Long courseId) {
        log.info("开始获取课程课节信息, courseId: {}", courseId);

        try {
            CoursePublishQuery coursePublishQuery = new CoursePublishQuery();
            coursePublishQuery.setCourseId(courseId);

            // 调用远程服务获取课节信息
            List<LessonVO> lessonList = teachingPlanDraftManager.getPublishLessonList(
                coursePublishQuery);

            log.info("获取课程课节信息成功, courseId: {}, 课节数量: {}", courseId,
                lessonList.size());
            return lessonList;

        } catch (Exception e) {
            e.getStackTrace();
            log.error("获取课程课节信息异常, courseId: {}", courseId, e);
            throw new BizException("获取课程课节信息失败: " + e.getMessage());
        }
    }


    /**
     * 重新匹配课件与课程计划
     *
     * @param courseId
     * @return void
     * <AUTHOR>
     * @date 2024/12/7 9:53
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rematchCoursewareWithLessonPlans(Long liveRoomPlanId, Long courseId) {
        log.info("开始重新匹配课件与课程计划, liveRoomPlanId={}, courseId={}", liveRoomPlanId,
            courseId);
        try {
            if (Objects.nonNull(liveRoomPlanId)) {
                log.info("直播间计划重新发布，开始执行, liveRoomPlanId={}", liveRoomPlanId);
            }
            if (Objects.nonNull(courseId)) {
                log.info("课程计划重新发布，开始重新匹配课件与课程计划, courseId={}", courseId);
                // 查询最新课程计划，进行更新操作
                CourseVO courseVO = teachingPlanDraftManager.getCourseLatest(courseId);
                // 更新该课程id下为最新的信息
                if (Objects.nonNull(courseVO)) {
                    this.update(
                        Wrappers.lambdaUpdate(TeachingPlanDraft.class)
                            .eq(TeachingPlanDraft::getCourseId, courseId)
                            .set(TeachingPlanDraft::getCourseName, courseVO.getCourseName())
                            .set(TeachingPlanDraft::getStage, courseVO.getStageId()));
                    teachingPlanPubMapper.update(
                        Wrappers.lambdaUpdate(TeachingPlanPub.class)
                            .eq(TeachingPlanPub::getCourseId, courseId)
                            .set(TeachingPlanPub::getCourseName, courseVO.getCourseName())
                            .set(TeachingPlanPub::getStage, courseVO.getStageId()));
                }
            }
            // 1. 查询符合条件的教学计划
            List<TeachingPlanDraft> teachingPlans = getMatchingTeachingPlans(liveRoomPlanId,
                courseId);
            if (CollectionUtils.isEmpty(teachingPlans)) {
                log.warn("未找到符合条件的教学计划");
                return;
            }
            log.info("查询到符合条件的教学计划,数量：{}", teachingPlans.size());

            // 2. 并行处理每个教学计划
            for (TeachingPlanDraft plan : teachingPlans) {
                // 若是直播间计划重新发布了则需要重新更新教学计划草稿表
                if (Objects.nonNull(liveRoomPlanId)) {
                    TeachingPlanDraftQuery teachingPlanDraftQuery = new TeachingPlanDraftQuery();
                    teachingPlanDraftQuery.setId(plan.getId());
                    teachingPlanDraftQuery.setLiveRoomPlanId(plan.getLiveRoomPlanId());
                    teachingPlanDraftQuery.setLectureId(plan.getLectureId());
                    teachingPlanDraftQuery.setLectureName(plan.getLectureName());
                    teachingPlanDraftManager.handleTeachingDetailDraft(
                        teachingPlanDraftQuery, true);
                }
                processTeachingPlanRematch(plan);
            }
            log.info("重新匹配课件与课程计划完成");
            // 匹配完成开始创建声网房间
            TransactionUtils.afterCommitSyncExecute(
                () ->
                    liveChannelManager.createRoomByTeachingPlan(teachingPlans));
        } catch (Exception e) {
            e.getStackTrace();
            log.error("重新匹配课件与课程计划异常", e);
            throw new BizException("重新匹配课件与课程计划失败: " + e.getMessage());
        }
    }

    @Override
    public List<Long> listByLiveRoomPlanId(Long liveRoomPlanId) {
        return list(
            Wrappers.lambdaQuery(TeachingPlanDraft.class)
                .eq(TeachingPlanDraft::getLiveRoomPlanId, liveRoomPlanId))
            .stream()
            .map(TeachingPlanDraft::getId)
            .collect(Collectors.toList());
    }

    @Override
    public long countByLiveRoomPlanId(Long liveRoomPlanId) {
        if (Objects.isNull(liveRoomPlanId)) {
            return 0;
        }
        List<Long> teachingPlanIdList = this.listByLiveRoomPlanId(liveRoomPlanId);
        if (CollectionUtils.isEmpty(teachingPlanIdList)) {
            return 0;
        }
        return courseLiveService.count(
            Wrappers.lambdaQuery(CourseLive.class)
                .in(CourseLive::getTeachingPlanId, teachingPlanIdList));
    }

    /**
     * 处理教学计划匹配
     *
     * @param plan
     * @return void
     * <AUTHOR>
     * @date 2024/12/7 11:25
     */
    private void processTeachingPlanRematch(TeachingPlanDraft plan) {
        log.info("开始处理教学计划重新匹配, planId={}", plan.getId());
        try {
            // 1. 获取已发布的教学计划明细(按lesson_order排序)
            List<TeachingPlanDetailPub> publishedDetails = getPublishedDetails(plan.getId());

            // 2. 获取直播间计划明细(按lesson_order排序)
            List<LiveRoomPlanDetailDraft> liveRoomDetails = getPlanDetails(
                plan.getLiveRoomPlanId());

            // 3. 获取课节信息
            List<LessonVO> lessonList = getPublishLessonList(plan.getCourseId());

            // 4. 获取教学计划明细草稿数据
            List<TeachingPlanDetailDraft> teachingPlanDetails = getTeachingPlanDetailDrafts(
                plan.getId());
            Map<Integer, TeachingPlanDetailDraft> teachingPlanDetailMap =
                teachingPlanDetails.stream()
                    .collect(
                        Collectors.toMap(
                            TeachingPlanDetailDraft::getLessonOrder,
                            draft -> draft, // 当key冲突时，比较createTime，保留较新的记录
                            (existing, replacement) ->
                                replacement.getCreateTime().isAfter(existing.getCreateTime())
                                    ? replacement
                                    : existing));

            List<TeachingPlanDraft> teachingPlanDrafts = new ArrayList<>();
            teachingPlanDrafts.add(plan);
            Map<Long, LiveRoomPlanDraft> liveRoomPlanMap = batchGetLiveRoomPlans(
                teachingPlanDrafts);
            LiveRoomPlanDraft liveRoomPlanDraft = liveRoomPlanMap.get(plan.getLiveRoomPlanId());
            int operateType;
            // 4. 根据是否存在已发布明细选择处理方式
            if (CollectionUtils.isEmpty(publishedDetails)) {
                operateType = TeachingPlanOperateEnum.CREATE_PLAN.code;
                // 首次发布
                handleFirstTimePublish(
                    plan, liveRoomDetails, lessonList, liveRoomPlanDraft, teachingPlanDetailMap);
            } else {
                operateType = TeachingPlanOperateEnum.EDIT_PLAN.code;
                // 重新匹配
                updatePublishedDetailsContent(
                    plan,
                    publishedDetails,
                    liveRoomDetails,
                    lessonList,
                    liveRoomPlanDraft,
                    teachingPlanDetailMap);
            }
            // 校验是不是明细中老师进行修改了
            List<EditLessonDTO> editLessonDTOList = handleLectureChange(teachingPlanDetails);
            if (CollectionUtils.isNotEmpty(editLessonDTOList)) {
                operateType = TeachingPlanOperateEnum.EDIT_SCHEDULE.code;
            }
            // 发布时也需要调用队列去发布任务
            teachingPlanDraftManager.buildAndSendOperateMqMessage(
                plan,
                operateType,
                null,
                null,
                new ArrayList<>(),
                editLessonDTOList);

            log.info("教学计划处理完成, planId={}", plan.getId());
        } catch (Exception e) {
            e.getStackTrace();
            log.error("处理教学计划重新匹配异常, planId={}", plan.getId(), e);
            throw new BizException(
                String.format("处理教学计划[%d]重新匹配失败: %s", plan.getId(), e.getMessage()));
        }
    }

    /**
     * 校验是不是明细中老师进行修改了
     *
     * @param operateType
     * @param teachingPlanDetails
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO.EditLessonDTO>
     * <AUTHOR>
     * @date 2024/12/16 9:54
     */
    private List<EditLessonDTO> handleLectureChange(
        List<TeachingPlanDetailDraft> teachingPlanDetails) {
        log.info(
            "开始处理教学计划明细讲师变更, operateType={}, detailsSize={}",
            teachingPlanDetails.size());

        try {
            List<TeachingPlanDetailDraft> planDetailDrafts =
                teachingPlanDetails.stream().filter(e -> Objects.nonNull(e.getEditRemark()))
                    .toList();

            if (CollectionUtils.isEmpty(planDetailDrafts)) {
                log.info("未发现讲师变更记录");
                return Collections.emptyList();
            }
            ArrayList<EditLessonDTO> lessonDTOS = new ArrayList<>(
                CollectionUtils.size(planDetailDrafts));

            for (TeachingPlanDetailDraft draft : planDetailDrafts) {
                try {
                    EditLessonDTO editLessonDTO = JSONUtil.toBean(draft.getEditRemark(),
                        EditLessonDTO.class);
                    if (editLessonDTO != null) {
                        lessonDTOS.add(editLessonDTO);

                        // 单条更新 editRemark 为空
                        boolean updated =
                            teachingPlanDetailDraftService.update(
                                Wrappers.lambdaUpdate(TeachingPlanDetailDraft.class)
                                    .eq(TeachingPlanDetailDraft::getId, draft.getId())
                                    .set(TeachingPlanDetailDraft::getEditRemark, null));

                        if (updated) {
                            log.info("成功清空讲师变更记录, planDetailId={}", draft.getId());
                        } else {
                            log.error("清空讲师变更记录失败, planDetailId={}", draft.getId());
                        }
                    }
                } catch (Exception e) {
                    log.error(
                        "处理讲师变更记录异常, planDetailId={}, editRemark={}",
                        draft.getId(),
                        draft.getEditRemark(),
                        e);
                }
            }

            log.info("处理教学计划明细讲师变更完成, 变更数量={}", lessonDTOS.size());
            return lessonDTOS;

        } catch (Exception e) {
            log.error("处理教学计划明细讲师变更异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理首次发布
     *
     * @param plan
     * @param liveRoomDetails
     * @param lessonList
     * @param liveRoomPlan
     * @param teachingPlanDetailMap
     * @return void
     * <AUTHOR>
     * @date 2024/12/9 9:20
     */
    private void handleFirstTimePublish(
        TeachingPlanDraft plan,
        List<LiveRoomPlanDetailDraft> liveRoomDetails,
        List<LessonVO> lessonList,
        LiveRoomPlanDraft liveRoomPlan,
        Map<Integer, TeachingPlanDetailDraft> teachingPlanDetailMap) {

        log.info("处理首次发布教学计划, planId={}", plan.getId());

        try {
            List<TeachingPlanDetailPub> newDetails = new ArrayList<>();

            for (int i = 0; i < liveRoomDetails.size(); i++) {
                // 如果课节列表不足,则退出循环
                if (i >= lessonList.size()) {
                    log.warn(
                        "课节列表数量不足, planId={}, i={}, lessonList.size={}", plan.getId(), i,
                        lessonList.size());
                    break;
                }
                LiveRoomPlanDetailDraft liveRoomDetail = liveRoomDetails.get(i);
                LessonVO lesson = lessonList.get(i);

                TeachingPlanDetailPub pubDetail = new TeachingPlanDetailPub();
                // 设置基本信息
                pubDetail.setPlanId(plan.getId());
                pubDetail.setLessonOrder(liveRoomDetail.getLessonOrder());

                // 更新内容
                updateDetailContent(
                    pubDetail, plan, liveRoomDetail, lesson, liveRoomPlan, teachingPlanDetailMap);
                newDetails.add(pubDetail);
            }

            // 批量保存
            if (!newDetails.isEmpty()) {
                teachingPlanDetailPubService.saveBatch(newDetails);
                log.info("首次发布教学计划明细成功, planId={}, count={}", plan.getId(),
                    newDetails.size());
                if (startCompatible) {
                    saveClassTimeInfo(newDetails, plan.getId());
                }
            }

        } catch (Exception e) {
            e.getStackTrace();
            log.error("首次发布教学计划明细异常, planId={}", plan.getId(), e);
            throw new BizException("首次发布教学计划明细失败: " + e.getMessage());
        }
    }


    /**
     * 处理课次兼容
     *
     * <AUTHOR>
     * @date 2025年01月14日 11时10分
     */
    private void saveClassTimeInfo(List<TeachingPlanDetailPub> details, Long planId) {
        log.info("首次发布，同步spring1课次信息, planId={},details: {}", planId, details);
        SsClass ssClass = ssClassMapper.selectOne(
            Wrappers.<SsClass>lambdaQuery().eq(SsClass::getTeachingPlanId, planId));

        if (Objects.isNull(ssClass)) {
            throw new BizException("首次发布教学计划明细失败: 未找到班级信息");
        }

        List<SsDevice> ssDevices = ssDeviceMapper.selectList(
            Wrappers.<SsDevice>lambdaQuery()
                .in(SsDevice::getClassRoomId,
                    details.stream().map(s -> s.getLiveRoomId()).toList()));

        if (CollectionUtils.isEmpty(ssDevices)) {
            throw new BizException("首次发布教学计划明细失败: 未找到设备信息");
        }

        Map<Long, List<SsDevice>> devices = ssDevices.stream()
            .collect(Collectors.groupingBy(SsDevice::getClassRoomId));

        R<List<LecturerInfoVO>> r = remoteLecturerInfoService.getAllLecturerInfo();
        if (!r.isOk() || CollectionUtils.isEmpty(r.getData())) {
            throw new BizException("首次发布教学计划明细失败: 远程调用主讲老师服务失败");
        }

        List<LecturerInfoVO> ecturerInfoVOs = r.getData().stream()
            .filter(s -> StringUtils.isNotBlank(s.getXgjLecturerId()))
            .toList();

        if (CollectionUtils.isEmpty(ecturerInfoVOs)) {
            throw new BizException(
                "首次发布教学计划明细失败: 远程调用主讲老师服务失败,老师信息中未设置校管家信息！");
        }

        List<SsLecturer> ssLecturers = ssLecturerMapper.selectList(
            Wrappers.<SsLecturer>lambdaQuery()
                .in(SsLecturer::getXgjLecturerId,
                    ecturerInfoVOs.stream().map(s -> s.getXgjLecturerId()).toList()));

        if (CollectionUtils.isEmpty(ssLecturers)) {
            throw new BizException("首次发布教学计划明细失败:未查询到对应老师信息");
        }

        Map<String, List<SsLecturer>> lecturers = ssLecturers.stream()
            .collect(Collectors.groupingBy(SsLecturer::getXgjLecturerId));

        Map<Long, List<LecturerInfoVO>> lecturerXgjCache = ecturerInfoVOs.stream()
            .filter(s -> StringUtils.isNotBlank(s.getXgjLecturerId()))
            .collect(Collectors.groupingBy(LecturerInfoVO::getUserId));

        List<SsClassTime> classTimes = Lists.newArrayList();
        for (TeachingPlanDetailPub detail : details) {
            SsClassTime classTime = new SsClassTime();
            classTime.setClassId(ssClass.getId());
            classTime.setBooksName(detail.getBookName());
            classTime.setBooksId(detail.getBookId().toString());
            classTime.setAttendClassDate(detail.getClassDate());
            classTime.setAttendClassStartTime(detail.getClassStartTime());
            classTime.setAttendClassEndTime(detail.getClassEndTime());
            classTime.setIsSyncAgora(IsSyncAgoraEnum.ISSYNCAGORA_1.CODE());
            classTime.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_0.CODE);
            classTime.setAttendTimeStartTime(detail.getClassStartDateTime());
            classTime.setAttendTimeEndTime(detail.getClassEndDateTime());
            classTime.setTeachingPlanDetailsId(detail.getId());

            if (CollectionUtils.isNotEmpty(devices.get(detail.getLiveRoomId()))) {
                classTime.setClassRoomId(detail.getLiveRoomId());
                classTime.setDeviceId(devices.get(detail.getLiveRoomId()).get(0).getId());
            } else {
                throw new BizException("未找到设备信息");
            }

            if (CollectionUtils.isNotEmpty(lecturerXgjCache.get(detail.getLectureId()))) {
                if (CollectionUtils.isNotEmpty(lecturers.get(
                    lecturerXgjCache.get(detail.getLectureId()).get(0).getXgjLecturerId()))) {
                    classTime.setLecturerId(lecturers.get(
                            lecturerXgjCache.get(detail.getLectureId()).get(0).getXgjLecturerId())
                        .get(0).getId());
                } else {
                    throw new BizException("未找到老师信息");
                }

            } else {
                throw new BizException("未找远端服务返回老师信息");
            }

            classTimes.add(classTime);
        }

        classTimes.forEach(s -> ssClassTimeMapper.insert(s));
    }

    /**
     * 更新已发布明细内容
     *
     * @param plan
     * @param publishedDetails
     * @param liveRoomDetails
     * @param lessonList
     * @param liveRoomPlan
     * @param teachingPlanDetailMap
     * @return void
     * <AUTHOR>
     * @date 2024/12/9 9:20
     */
    private void updatePublishedDetailsContent(
        TeachingPlanDraft plan,
        List<TeachingPlanDetailPub> publishedDetails,
        List<LiveRoomPlanDetailDraft> liveRoomDetails,
        List<LessonVO> lessonList,
        LiveRoomPlanDraft liveRoomPlan,
        Map<Integer, TeachingPlanDetailDraft> teachingPlanDetailMap) {

        log.info(
            "开始更新已发布教学计划明细, planId={}, publishedDetails.size={}, liveRoomDetails.size={}, lessonList.size={}",
            plan.getId(),
            publishedDetails.size(),
            liveRoomDetails.size(),
            lessonList.size());

        try {
            List<TeachingPlanDetailPub> updatedDetails = new ArrayList<>();
            List<TeachingPlanDetailPub> newDetails = new ArrayList<>();

            // 1. 处理现有记录的更新
            for (int i = 0; i < Math.min(publishedDetails.size(), liveRoomDetails.size()); i++) {
                if (i >= lessonList.size()) {
                    break;
                }
                TeachingPlanDetailPub pubDetail = publishedDetails.get(i);
                LiveRoomPlanDetailDraft liveRoomDetail = liveRoomDetails.get(i);
                LessonVO lesson = lessonList.get(i);

                updateDetailContent(
                    pubDetail, plan, liveRoomDetail, lesson, liveRoomPlan, teachingPlanDetailMap);
                updatedDetails.add(pubDetail);
            }

            // 2. 处理新增的记录
            if (publishedDetails.size() < liveRoomDetails.size()) {
                for (int i = publishedDetails.size(); i < liveRoomDetails.size(); i++) {
                    if (i >= lessonList.size()) {
                        break;
                    }
                    LiveRoomPlanDetailDraft liveRoomDetail = liveRoomDetails.get(i);
                    LessonVO lesson = lessonList.get(i);

                    TeachingPlanDetailPub newDetail = new TeachingPlanDetailPub();
                    newDetail.setPlanId(plan.getId());
                    newDetail.setLessonOrder(liveRoomDetail.getLessonOrder());
                    updateDetailContent(
                        newDetail, plan, liveRoomDetail, lesson, liveRoomPlan,
                        teachingPlanDetailMap);
                    newDetails.add(newDetail);
                    log.info(
                        "新增教学计划明细, planId={}, lessonOrder={}", plan.getId(),
                        liveRoomDetail.getLessonOrder());
                }
            }

            // 3. 处理需要删除的记录
            if (publishedDetails.size() > liveRoomDetails.size()) {
                List<Long> deleteIds =
                    publishedDetails.subList(liveRoomDetails.size(), publishedDetails.size())
                        .stream()
                        .map(TeachingPlanDetailPub::getId)
                        .toList();

                if (!deleteIds.isEmpty()) {
                    teachingPlanDetailPubService.removeByIds(deleteIds);
                    log.info("删除多余的已发布明细记录, planId={}, count={}", plan.getId(),
                        deleteIds.size());
                    if (startCompatible) {
                        deleteUpdateClassTime(deleteIds);
                    }
                }
            }

            // 4. 批量更新和保存
            if (!updatedDetails.isEmpty()) {
                teachingPlanDetailPubService.updateBatchById(updatedDetails);
                log.info("更新已发布明细内容成功, planId={}, count={}", plan.getId(),
                    updatedDetails.size());
            }

            if (!newDetails.isEmpty()) {
                teachingPlanDetailPubService.saveBatch(newDetails);
                log.info("保存新增明细内容成功, planId={}, count={}", plan.getId(),
                    newDetails.size());
                if (startCompatible) {
                    saveNewClassTimeInfo(newDetails, plan.getId());
                }
            }
        } catch (Exception e) {
            e.getStackTrace();
            log.error("更新已发布教学计划明细内容异常, planId={}", plan.getId(), e);
            throw new BizException("更新已发布教学计划明细内容失败: " + e.getMessage());
        }
    }


    private void saveNewClassTimeInfo(List<TeachingPlanDetailPub> details, Long planId) {
        log.info("课次变化或者直播间计划变化: 同步spring1课次, planId={} ,details: {}", planId,
            details);
        SsClass ssClass = ssClassMapper.selectOne(
            Wrappers.<SsClass>lambdaQuery().eq(SsClass::getTeachingPlanId, planId));

        if (Objects.isNull(ssClass) || SsClassStateEnum.STATE_1.CODE.equals(
            ssClass.getClassState())) {
            log.error("课次新增: 未找到班级信息或已结业", planId);
            return;
            // throw new BizException("课次新增: 未找到班级信息");
        }

        List<SsDevice> ssDevices = ssDeviceMapper.selectList(
            Wrappers.<SsDevice>lambdaQuery()
                .in(SsDevice::getClassRoomId,
                    details.stream().map(s -> s.getLiveRoomId()).toList()));

        if (CollectionUtils.isEmpty(ssDevices)) {
            throw new BizException("课次新增: 未找到设备信息");
        }

        Map<Long, List<SsDevice>> devices = ssDevices.stream()
            .collect(Collectors.groupingBy(SsDevice::getClassRoomId));

        R<List<LecturerInfoVO>> r = remoteLecturerInfoService.getAllLecturerInfo();
        if (!r.isOk() || CollectionUtils.isEmpty(r.getData())) {
            throw new BizException("课次新增: 远程调用主讲老师服务失败");
        }

        List<LecturerInfoVO> ecturerInfoVOs = r.getData().stream()
            .filter(s -> StringUtils.isNotBlank(s.getXgjLecturerId()))
            .toList();

        if (CollectionUtils.isEmpty(ecturerInfoVOs)) {
            throw new BizException(
                "课次新增: 远程调用主讲老师服务失败,老师信息中未设置校管家信息！");
        }

        List<SsLecturer> ssLecturers = ssLecturerMapper.selectList(
            Wrappers.<SsLecturer>lambdaQuery()
                .in(SsLecturer::getXgjLecturerId,
                    ecturerInfoVOs.stream().map(s -> s.getXgjLecturerId()).toList()));

        if (CollectionUtils.isEmpty(ssLecturers)) {
            throw new BizException("课次新增:未查询到对应老师信息");
        }

        Map<String, List<SsLecturer>> lecturers = ssLecturers.stream()
            .collect(Collectors.groupingBy(SsLecturer::getXgjLecturerId));

        Map<Long, List<LecturerInfoVO>> lecturerXgjCache = ecturerInfoVOs.stream()
            .filter(s -> StringUtils.isNotBlank(s.getXgjLecturerId()))
            .collect(Collectors.groupingBy(LecturerInfoVO::getUserId));

        List<SsClassTimeBO> classTimes = Lists.newArrayList();
        for (TeachingPlanDetailPub detail : details) {
            if (LocalDateTime.now().isAfter(detail.getClassEndDateTime())) {
                continue;
            }
            SsClassTimeBO classTime = new SsClassTimeBO();
            classTime.setClassId(ssClass.getId());
            classTime.setBooksName(detail.getBookName());
            classTime.setBooksId(detail.getBookId().toString());
            classTime.setAttendClassDate(detail.getClassDate());
            classTime.setAttendClassStartTime(detail.getClassStartTime());
            classTime.setAttendClassEndTime(detail.getClassEndTime());
            classTime.setIsSyncAgora(IsSyncAgoraEnum.ISSYNCAGORA_1.CODE());
            classTime.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_0.CODE);
            classTime.setAttendTimeStartTime(detail.getClassStartDateTime());
            classTime.setAttendTimeEndTime(detail.getClassEndDateTime());
            classTime.setTeachingPlanDetailsId(detail.getId());

            if (CollectionUtils.isNotEmpty(devices.get(detail.getLiveRoomId()))) {
                classTime.setClassRoomId(detail.getLiveRoomId());
                classTime.setDeviceId(devices.get(detail.getLiveRoomId()).get(0).getId());
            } else {
                throw new BizException("课次新增：未找到设备信息");
            }

            if (CollectionUtils.isNotEmpty(lecturerXgjCache.get(detail.getLectureId()))) {
                if (CollectionUtils.isNotEmpty(lecturers.get(
                    lecturerXgjCache.get(detail.getLectureId()).get(0).getXgjLecturerId()))) {
                    classTime.setLecturerId(lecturers.get(
                            lecturerXgjCache.get(detail.getLectureId()).get(0).getXgjLecturerId())
                        .get(0).getId());
                    classTime.setXgjLecturerId(
                        lecturerXgjCache.get(detail.getLectureId()).get(0).getXgjLecturerId());
                } else {
                    throw new BizException("课次新增：未找到老师信息");
                }

            } else {
                throw new BizException("课次新增：未找远端服务返回老师信息");
            }

            classTimes.add(classTime);
        }

        if (CollectionUtils.isNotEmpty(classTimes)) {
            classTimes.forEach(s -> ssClassTimeMapper.insert(s));
            ssCourseScheduleManager.saveClassTimeStendsAndSyncXgj(ssClass, classTimes);
        }
    }


    /**
     * 更新明细内容
     *
     * @param pubDetail
     * @param plan
     * @param liveRoomDetail
     * @param lesson
     * @param liveRoomPlan
     * @param teachingPlanDetailMap
     * @return void
     * <AUTHOR>
     * @date 2024/12/9 8:52
     */
    private void updateDetailContent(
        TeachingPlanDetailPub pubDetail,
        TeachingPlanDraft plan,
        LiveRoomPlanDetailDraft liveRoomDetail,
        LessonVO lesson,
        LiveRoomPlanDraft liveRoomPlan,
        Map<Integer, TeachingPlanDetailDraft> teachingPlanDetailMap) {

        // 获取对应课节的教学计划明细
        TeachingPlanDetailDraft teachingPlanDetail =
            teachingPlanDetailMap.get(liveRoomDetail.getLessonOrder());
        if (teachingPlanDetail == null) {
            log.warn(
                "未找到对应课节的教学计划明细数据, planId={}, lessonOrder={}",
                plan.getId(),
                liveRoomDetail.getLessonOrder());
            // 如果找不到对应的明细数据,使用计划中的讲师信息
            pubDetail.setLectureId(plan.getLectureId());
            pubDetail.setLectureName(plan.getLectureName());
        } else {
            // 使用明细表中的讲师信息
            pubDetail.setLectureId(teachingPlanDetail.getLectureId());
            pubDetail.setLectureName(teachingPlanDetail.getLectureName());
        }

        // 更新课程相关信息
        pubDetail.setCourseId(plan.getCourseId());
        pubDetail.setCourseName(plan.getCourseName());
        pubDetail.setLessonId(lesson.getId().longValue());
        pubDetail.setLessonName(lesson.getLessonName());
        pubDetail.setLessonOrder(liveRoomDetail.getLessonOrder());
        pubDetail.setBookId(lesson.getBookId().longValue());
        pubDetail.setBookName(lesson.getBookName());

        // 更新时间相关信息
        pubDetail.setTimeSlotId(liveRoomDetail.getTimeSlotId());
        pubDetail.setClassDate(liveRoomDetail.getClassDate());
        pubDetail.setClassStartTime(liveRoomDetail.getClassStartTime());
        pubDetail.setClassEndTime(liveRoomDetail.getClassEndTime());
        pubDetail.setClassStartDateTime(liveRoomDetail.getClassStartDateTime());
        pubDetail.setClassEndDateTime(liveRoomDetail.getClassEndDateTime());

        // 更新直播间信息
        pubDetail.setLiveRoomId(liveRoomPlan.getLiveRoomId());
    }

    /**
     * 获取符合条件的教学计划
     */
    private List<TeachingPlanDraft> getMatchingTeachingPlans(Long liveRoomPlanId, Long courseId) {
        LambdaQueryWrapper<TeachingPlanDraft> wrapper =
            Wrappers.lambdaQuery(TeachingPlanDraft.class);

        if (courseId != null) {
            wrapper.eq(TeachingPlanDraft::getCourseId, courseId);
        }
        if (liveRoomPlanId != null) {
            wrapper.eq(TeachingPlanDraft::getLiveRoomPlanId, liveRoomPlanId);
        }

        return list(wrapper);
    }

    /**
     * 获取已发布的教学计划明细
     *
     * @param planId
     * @return java.util.List<com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub>
     * <AUTHOR>
     * @date 2024/12/9 9:15
     */
    private List<TeachingPlanDetailPub> getPublishedDetails(Long planId) {
        return teachingPlanDetailPubService.list(
            Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                .eq(TeachingPlanDetailPub::getPlanId, planId)
                .orderByAsc(TeachingPlanDetailPub::getLessonOrder));
    }
}
