package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanDraftQuery;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDraftVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDraftVO;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import jakarta.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * 教学计划草稿表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-29 09:24:03
 */
public interface TeachingPlanDraftService extends IService<TeachingPlanDraft> {

    /**
     * 通过条件查询教学计划
     *
     * @return List<TeachingPlanDraftVO>
     */
    List<TeachingPlanDraftVO> listByIds(List<Long> ids);

    /**
     * 分页查询
     *
     * @param page 分页条件
     * @param
     * @return page
     */
    Page<TeachingPlanDraftVO> getPage(Page page, TeachingPlanDraftQuery teachingPlanDraftQuery);

    /**
     * 教学计划详情
     *
     */
    TeachingPlanDraftVO getDetails(Integer id);

    /**
     * 新增教学计划草稿表
     * <AUTHOR>
     * @date 2024/12/6 9:45
     * @param teachingPlanDraftQuery
     * @return void
     */
    void savePlan(TeachingPlanDraftQuery teachingPlanDraftQuery);

    /**
     * 修改教学计划草稿表
     * <AUTHOR>
     * @date 2024/12/6 9:53
     * @param teachingPlanDraftQuery
     * @return void
     */
    void editPlan(TeachingPlanDraftQuery teachingPlanDraftQuery);

    /**
     * 删除计划
     * <AUTHOR>
     * @date 2024/12/6 10:54
     * @param list
     * @return void
     */
    void deltePlan(ArrayList<Long> list);

    /**
     * 发布教学计划
     * <AUTHOR>
     * @date 2024/12/6 14:49
     * @param ids
     * @return void
     */
    void publish(Long[] ids,Boolean forcePublish);

  /**
   * 重新匹配课件与课程计划
   *
   * <AUTHOR>
   * @date 2024/12/7 9:52
   * @param courseId
   * @return void
   */
  void rematchCoursewareWithLessonPlans(Long liveRoomPlanId, Long courseId);

    /**
     * 查询直播间关联的教学计划ID
     */
    List<Long> listByLiveRoomPlanId(Long liveRoomPlanId);

    /**
     * 查询直播间关联的教学计划是否已有约课
     * @param liveRoomPlanId 直播间计划ID
     */
    long countByLiveRoomPlanId(Long liveRoomPlanId);
}
