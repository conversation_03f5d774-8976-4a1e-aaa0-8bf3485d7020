package com.yuedu.ydsf.eduConnect.controller;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.dto.CourseVodDTO;
import com.yuedu.ydsf.eduConnect.api.query.CourseVodQuery;
import com.yuedu.ydsf.eduConnect.api.query.CourseVodVideoQuery;
import com.yuedu.ydsf.eduConnect.api.vo.CourseVodVO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseVodVideoVO;
import com.yuedu.ydsf.eduConnect.service.CourseVodService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 点播课程库 控制类
 *
 * <AUTHOR>
 * @date 2024-12-02 11:05:41
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/CourseVod" )
@Tag(description = "ea_course_vod" , name = "点播课程库管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseVodController {

    private final  CourseVodService courseVodService;
    @Operation(summary = "点播课程库列表" , description = "点播课程库列表")
    @GetMapping("/list")
    @HasPermission("edusystem_CourseVod_view")
    public R<List<CourseVodVO>> getCourseVodList(@ParameterObject CourseVodQuery courseVodQuery) {
        return R.ok(courseVodService.getCourseVodList(courseVodQuery));
    }

    @Operation(summary = "编辑点播课程状态",description = "编辑点播课程状态")
    @Idempotent(expireTime = 5)
    @PutMapping("/edit")
    @HasPermission("edusystem_CourseVod_edit")
    public R<Boolean> editCourseVodDisable(@Valid @RequestBody CourseVodDTO courseVodDTO){
        return R.ok(courseVodService.editCourseVodDisable(courseVodDTO.getId()));
    }

    @Operation(summary = "根据课节id获取点播课程视频列表", description = "根据课节id获取点播课程视频列表")
    @GetMapping("/infoList")
    @HasPermission("edusystem_CourseVod_view")
    public R<List<CourseVodVideoVO>> getCourseVodVideoList(@Valid @ParameterObject CourseVodVideoQuery courseVodVideoQuery){
        return R.ok(courseVodService.getCourseVodInfoList(courseVodVideoQuery.getCourseId(),courseVodVideoQuery.getLessonOrder()));
    }

    /**
     * 查询点播课课程库
     * @param courseVodDTO
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.api.vo.CourseVodVO>
     * <AUTHOR>
     * @date 2024/12/19 16:19
     */
    @PostMapping("/getCourseVodVideo")
    @Inner
    public R<List<CourseVodVO>> getCourseVodVideo(@RequestBody CourseVodDTO courseVodDTO) {
        return R.ok(courseVodService.courseVodService(courseVodDTO));
    }
}
