package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.query.SsDeviceAudioConfigQuery;
import com.yuedu.ydsf.eduConnect.api.dto.SsDeviceAudioConfigDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceAudioConfigVO;
import com.yuedu.ydsf.eduConnect.entity.SsDeviceAudioConfig;

import java.util.List;

/**
* 设备音频相关配置服务接口
*
* <AUTHOR>
* @date  2024/09/28
*/
public interface SsDeviceAudioConfigService extends IService<SsDeviceAudioConfig> {



    /**
    * 设备音频相关配置分页查询
    * @param page 分页对象
    * @param ssDeviceAudioConfigQuery 设备音频相关配置
    * @return IPage 分页结果
    */
    IPage page(Page page, SsDeviceAudioConfigQuery ssDeviceAudioConfigQuery);


    /**
    * 新增设备音频相关配置
    * @param ssDeviceAudioConfigDTO 设备音频相关配置
    * @return boolean 执行结果
    */
    boolean add(SsDeviceAudioConfigDTO ssDeviceAudioConfigDTO);


    /**
    * 修改设备音频相关配置
    * @param ssDeviceAudioConfigDTO 设备音频相关配置
    * @return boolean 执行结果
    */
    boolean edit(SsDeviceAudioConfigDTO ssDeviceAudioConfigDTO);


    /**
    * 导出excel 设备音频相关配置表格
    * @param ssDeviceAudioConfigQuery 查询条件
    * @param ids 导出指定ID
    * @return List<SsDeviceAudioConfigVO> 结果集合
    */
    List<SsDeviceAudioConfigVO> export(SsDeviceAudioConfigQuery ssDeviceAudioConfigQuery, Long[] ids);
}
