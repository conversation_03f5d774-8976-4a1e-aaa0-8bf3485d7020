package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 点播课程视频 实体类
 *
 * <AUTHOR>
 * @date 2024-12-02 11:08:51
 */
@Data
@TableName("ea_course_vod_video")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "点播课程视频实体类")
public class CourseVodVideo extends Model<CourseVodVideo> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 点播课ID
	*/
    @Schema(description="点播课ID")
    private Long courseVodId;

	/**
	* 阿里云视频点播ID
	*/
    @Schema(description="阿里云视频点播ID")
    private String aliyunVodId;

	/**
	* 阿里云视频播放地址
	*/
    @Schema(description="阿里云视频播放地址")
    private String aliyunPlayUrl;

	/**
	* mp4视频地址
	*/
    @Schema(description="mp4视频地址")
    private String mp4Url;

	/**
	* 录制任务ID
	*/
    @Schema(description="录制任务ID")
    private Long recordVideoTaskId;

    /**
     * 主讲录课ID
     */
    @Schema(description="主讲录课ID")
    private Long recordingId;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
