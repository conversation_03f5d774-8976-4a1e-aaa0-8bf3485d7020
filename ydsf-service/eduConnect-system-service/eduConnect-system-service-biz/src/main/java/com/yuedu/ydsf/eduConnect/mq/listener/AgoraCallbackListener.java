package com.yuedu.ydsf.eduConnect.mq.listener;

import com.alibaba.fastjson.JSON;
import com.dtflys.forest.utils.StringUtils;
import com.yuedu.ydsf.eduConnect.service.SsRecordingService;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.AgoraEventDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import java.nio.charset.StandardCharsets;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 声网回调监听listener
 * <AUTHOR>
 * @date 2025/1/10 10:02
 */
@Slf4j
@Service
@AllArgsConstructor
@RocketMQMessageListener(
    topic = "${rocketmq.topics.agora_record_event_topic}",
    consumerGroup = "${rocketmq.groups.agora_record_event_group}",
    tag = "*")
@ConditionalOnProperty(
    prefix = "rocketmq",
    name = "enabled",
    havingValue = "true",
    matchIfMissing = false)
public class AgoraCallbackListener implements RocketMQListener {

  private final SsRecordingService ssRecordingService;

  private final AgoraService agoraService;

  @Override
  public ConsumeResult consume(MessageView messageView) {
    log.info("接收声网回调消息: messageId={}, topic={}", messageView.getMessageId(), messageView.getTopic());

    try {
      // 1. 消息体校验, 消息体为空直接返回成功, 避免重试
      if (messageView.getBody() == null) {
        log.error("声网回调消息体为空");
        return ConsumeResult.SUCCESS;
      }

      // 2. 解析消息内容
        String message = "";
      try {
          message = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();

      } catch (Exception e) {
        log.error("声网回调消息解析失败: {}", e.getMessage(), e);
        // 解析失败直接返回成功,避免重试
        return ConsumeResult.SUCCESS;
      }

      if (StringUtils.isBlank(message)) {
        log.error("声网回调消息为空");
        return ConsumeResult.SUCCESS;
      }

      log.info("开始处理声网回调消息, message: {}", message);

      // 3. 处理声网回调相关业务处理
      try {

          if (!agoraService.handlerEvent(JSON.parseObject(message, AgoraEventDTO.class), dto -> {
              return ssRecordingService.recordUpload(dto.getRecordFileUpload());
          })) {
              return ConsumeResult.FAILURE;
          }

        log.info("声网回调消息处理成功, message: {}", message);
        return ConsumeResult.SUCCESS;
      } catch (Exception e) {
        log.error("声网回调消息处理失败, message: {}, error: {}", message, e.getMessage(), e);
          // 业务处理失败返回失败,触发重试机制
        return ConsumeResult.FAILURE;
      }

    } catch (Exception e) {
      log.error("声网回调消息消费异常: {}", e.getMessage(), e);
      return ConsumeResult.FAILURE;
    }
  }
}
