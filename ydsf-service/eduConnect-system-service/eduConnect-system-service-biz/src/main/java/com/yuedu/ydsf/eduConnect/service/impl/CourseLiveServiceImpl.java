package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.eduConnect.api.query.CourseLiveQuery;
import com.yuedu.ydsf.eduConnect.api.vo.CourseLiveVO;
import com.yuedu.ydsf.eduConnect.entity.CourseLive;
import com.yuedu.ydsf.eduConnect.manager.CourseLiveManager;
import com.yuedu.ydsf.eduConnect.mapper.CourseLiveMapper;
import com.yuedu.ydsf.eduConnect.service.CourseLiveService;

import java.sql.Wrapper;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 门店已约直播课 服务类
 *
 * <AUTHOR>
 * @date 2024-12-26 14:41:20
 */
@Slf4j
@Service
public class CourseLiveServiceImpl extends ServiceImpl<CourseLiveMapper, CourseLive> implements CourseLiveService {

    @Autowired
    private CourseLiveMapper courseLiveMapper;

    @Autowired
    private CourseLiveManager courseLiveManager;


    @Override
    public long countByTeachingPlanId(List<Long> teachingPlanIdList) {
        if (CollectionUtils.isEmpty(teachingPlanIdList)) {
            return 0;
        }
        return this.count(Wrappers.lambdaQuery(CourseLive.class).in(CourseLive::getTeachingPlanId, teachingPlanIdList));
    }

    @Override
    public IPage<CourseLiveVO> livePage(Page page, CourseLiveQuery query) {
        return courseLiveManager.fillDate(courseLiveMapper.page(page, query));
    }
}
