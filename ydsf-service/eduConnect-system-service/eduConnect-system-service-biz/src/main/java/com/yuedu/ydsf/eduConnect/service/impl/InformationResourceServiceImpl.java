package com.yuedu.ydsf.eduConnect.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.file.core.FileProperties;
import com.yuedu.ydsf.eduConnect.config.FileRuleProperties;
import com.yuedu.ydsf.eduConnect.config.FileRuleProperties.FileCategory;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.eduConnect.mapper.InformationResourceMapper;
import com.yuedu.ydsf.eduConnect.service.InformationResourceService;
import com.yuedu.ydsf.eduConnect.api.query.InformationResourceQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationResourceDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationResourceVO;
import com.yuedu.ydsf.eduConnect.entity.InformationResource;

import java.io.Serializable;
import java.util.Objects;
import java.util.Optional;
import java.util.List;
import java.util.Set;


/**
* 资料资源表服务层
*
* <AUTHOR>
* @date  2025/07/22
*/
@Service
@AllArgsConstructor
public class InformationResourceServiceImpl extends ServiceImpl<InformationResourceMapper,InformationResource>
    implements InformationResourceService{

    private final FileProperties fileProperties;
    private final FileRuleProperties fileRuleProperties;


    /**
     * 资料资源表分页查询
     *
     * @param page 分页对象
     * @param informationResourceQuery 资料资源表
     * @return IPage 分页结果
     */
    @Override
    public IPage<InformationResourceVO> page(Page page,InformationResourceQuery informationResourceQuery) {
        return page(page, Wrappers.<InformationResource>lambdaQuery()
                  .eq(InformationResource::getInformationId, informationResourceQuery.getInformationId())
                  .like(StringUtils.isNotBlank(informationResourceQuery.getResourceName()),InformationResource::getResourceName, informationResourceQuery.getResourceName())
                  .orderByDesc(InformationResource::getCreateTime)
                 )
                .convert(entity -> {
                    InformationResourceVO informationResourceVO = new InformationResourceVO();
                    BeanUtils.copyProperties(entity, informationResourceVO);
                    informationResourceVO.setResourceUrl(null);
                    if(StringUtils.isNotBlank(informationResourceVO.getCreateBy()) && informationResourceVO.getCreateBy().contains(":")){
                        informationResourceVO.setCreateBy(informationResourceVO.getCreateBy().substring(informationResourceVO.getCreateBy().lastIndexOf(":")).replace(":", ""));
                    }
                    if(StringUtils.isNotBlank(informationResourceVO.getUpdateBy()) && informationResourceVO.getUpdateBy().contains(":")){
                        informationResourceVO.setUpdateBy(informationResourceVO.getUpdateBy().substring(informationResourceVO.getUpdateBy().lastIndexOf(":")).replace(":", ""));
                    }
                    return informationResourceVO;
                });
    }


    /**
     * 根据ID获得资料资源表信息
     *
     * @param id id
     * @return InformationResourceVO 详细信息
     */
    @Override
    public InformationResourceVO getInfoById(Serializable id) {
        return Optional.of(getById(id))
                .map(entity -> {
                    InformationResourceVO informationResourceVO = new InformationResourceVO();
                    BeanUtils.copyProperties(entity, informationResourceVO);
                    informationResourceVO.setResourceUrl(String.format("%s/%s", fileProperties.getOss().getCustomDomain(), informationResourceVO.getResourceUrl()));
                    if(StringUtils.isNotBlank(informationResourceVO.getCreateBy()) && informationResourceVO.getCreateBy().contains(":")){
                        informationResourceVO.setCreateBy(informationResourceVO.getCreateBy().substring(informationResourceVO.getCreateBy().lastIndexOf(":")).replace(":", ""));
                    }
                    if(StringUtils.isNotBlank(informationResourceVO.getUpdateBy()) && informationResourceVO.getUpdateBy().contains(":")){
                        informationResourceVO.setUpdateBy(informationResourceVO.getUpdateBy().substring(informationResourceVO.getUpdateBy().lastIndexOf(":")).replace(":", ""));
                    }
                    return informationResourceVO;
                })
                .orElseThrow(()-> new CheckedException("查询结果为空"));
    }


    /**
     * 新增资料资源表
     *
     * @param informationResourceDTO 资料资源表
     * @return boolean 执行结果
     */
    @Override
    public boolean add(InformationResourceDTO informationResourceDTO) {
        InformationResource informationResource = new InformationResource();
        BeanUtils.copyProperties(informationResourceDTO, informationResource);
        return save(informationResource);
    }


    /**
     * 修改资料资源表
     *
     * @param informationResourceDTO 资料资源表
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(InformationResourceDTO informationResourceDTO) {
        InformationResource informationResource = new InformationResource();
        BeanUtils.copyProperties(informationResourceDTO, informationResource);
        return updateById(informationResource);
    }


    /**
     * 导出excel 资料资源表表格
     *
     * @param informationResourceQuery 查询条件
     * @param ids 导出指定ID
     * @return List<InformationResourceVO> 结果集合
     */
    @Override
    public List<InformationResourceVO> export(InformationResourceQuery informationResourceQuery, Long[] ids) {
        return list(Wrappers.<InformationResource>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), InformationResource::getId, ids))
            .stream()
            .map(entity -> {
                InformationResourceVO informationResourceVO = new InformationResourceVO();
                BeanUtils.copyProperties(entity, informationResourceVO);
                return informationResourceVO;
            }).toList();
    }

    @Override
    public boolean batchSave(List<InformationResourceDTO> informationResourceDTO) {

        Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
        for (InformationResourceDTO dto : informationResourceDTO) {
            Set<ConstraintViolation<InformationResourceDTO>> violations = validator.validate(dto, V_A.class);
            if (!violations.isEmpty()) {
                throw new ConstraintViolationException(violations);
            }
        }

        if(CollectionUtil.isEmpty(fileRuleProperties.getCategories())){
            throw new CheckedException("请先配置文件后缀规则");
        }

        List<InformationResource> list = informationResourceDTO.stream().map(dto -> {
            InformationResource entity = new InformationResource();
            BeanUtils.copyProperties(dto, entity);
            for(FileCategory fileCategory : fileRuleProperties.getCategories()){
                if(fileCategory.getExtensions().contains(FileUtil.extName(dto.getResourceUrl()))){
                    entity.setResourceType(fileCategory.getType());
                    break;
                }
            }
            if(Objects.isNull(entity.getResourceType())){
                throw new BizException(String.format("文件类型不支持: %s", FileUtil.extName(dto.getResourceUrl())));
            }
            return entity;
        }).toList();

        return saveBatch(list);

    }

}
