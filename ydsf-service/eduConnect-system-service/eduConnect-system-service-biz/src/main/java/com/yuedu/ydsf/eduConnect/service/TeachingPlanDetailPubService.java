package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanDetailPubDTO;
import com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub;
import java.util.List;

/**
 * 已发布的教学计划明细表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-29 15:14:17
 */
public interface TeachingPlanDetailPubService extends IService<TeachingPlanDetailPub> {

    /**
     * 查询老师是否有未结束的排课
     * @param lectureId 老师id
     * @return true:有未结束的排课 false:无未结束的排课
     */
    Boolean hasUnfinishedClass(Long lectureId);

    /**
     * 通过计划Id列表查询直播间计划名称
     *
     * @param planDetailPubIdList 计划Id列表
     * @return 结果
     */
    LiveRoomPlanVO getPlanVo(List<Long> planDetailPubIdList);

    /**
     * 通过课程, 主讲老师 查询对应时间最早的直播间计划/教学计划
     * @param teachingPlanDetailPubDTO
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO>
     * <AUTHOR>
     * @date 2024/12/11 14:54
     */
    List<AtTheEarliestAttendClassDetailVO> getAtTheEarliestCoursePlanList(TeachingPlanDetailPubDTO teachingPlanDetailPubDTO);

    /**
     * 专门为点播课场景：通过课程查询该课程下所有教学计划中每个课节的最早上课时间（不限制老师）
     *
     * 场景说明：
     * A课程有两个教学计划：A1计划(贾老师)和A2计划(易老师)
     * A1第一节：2020年1月1日12:00，A2第一节：2022年1月1日12:00
     * 当为A2教学计划排期第一节课时，校验会基于该课程第一节课的最早时间（2020年1月1日12:00）进行校验
     * 用户排期A2计划第一节课为2020年1月1日12:00是允许的，因为它不早于该课程第一节课的最早时间
     *
     * @param courseId 课程ID
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO>
     * <AUTHOR>
     * @date 2025/1/21 16:00
     */
    List<AtTheEarliestAttendClassDetailVO> getAtTheEarliestCoursePlanListForVod(Long courseId);



    /**
     * 通过教学任务Id查询教学任务详情
     *
     * @param planId 教学任务Id
     * @return 结果
     */
    List<LiveRoomPlanDetailVersionVO> getAllByPlanId(Long planId);

    /**
     * 通过教学计划ID查询教学计划明细,声网频道
     * @param teachingPlanIdList
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO>
     * <AUTHOR>
     * @date 2024/12/17 19:24
     */
    List<TeachingPlanDetailPubVO> getTeachingPlanDetailPubLiveChannel(List<Long> teachingPlanIdList);


    /**
     * 通过教学计划ID查询教学计划明细列表
     *
     * @param teachingPlanId 教学计划Id
     * @return 结果
     */
    List<TeachingPlanDetailPubVO> getTeachingPlanDetailPub(Long teachingPlanId);

    /**
     * 通过教学计划ID查询教学计划明细列表
     *
     * @param teachingPlanIdList 教学计划Id列表
     * @return 结果
     */
    List<TeachingPlanDetailPub> getTeachingPlanDetailPubDTOList(List<Long> teachingPlanIdList);
}
