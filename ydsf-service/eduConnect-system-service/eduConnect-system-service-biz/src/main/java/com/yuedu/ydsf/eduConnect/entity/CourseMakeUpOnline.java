package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店线上补课表
 * 
 * <AUTHOR>
 * @date 2025/04/28
 */
@TableName("b_course_make_up_online")
@Data
@EqualsAndHashCode(callSuper = true)
public class CourseMakeUpOnline extends Model<CourseMakeUpOnline> {
    /**
     * 主键ID(雪花id生成)
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 课表号
     */
    private Long lessonNo;

    /**
     * 教学计划ID
     */
    private Long teachingPlanId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 第几节课
     */
    private Integer lessonOrder;

    /**
     * 上课时段ID
     */
    private Long timeSlotId;

    /**
     * 主讲老师ID
     */
    private Long lectureId;

    /**
     * 上课教室ID
     */
    private Long classRoomId;

    /**
     * 班级ID
     */
    private Long classId;

    /**
     * 上课日期
     */
    private LocalDate classDate;

    /**
     * 上课开始时间
     */
    private LocalTime classStartTime;

    /**
     * 上课结束时间
     */
    private LocalTime classEndTime;

    /**
     * 补课有效期开始时间
     */
    private LocalDateTime validityStartTime;

    /**
     * 补课有效期结束时间
     */
    private LocalDateTime validityEndTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
}