package com.yuedu.ydsf.eduConnect.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableExportVO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.entity.Timetable;
import lombok.Data;
import java.util.List;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/03/11
 **/
public interface TimetableManager {


    /**
     *  填充分页数据
     *
     * <AUTHOR>
     * @date 2025年03月11日 15时04分
     */
    IPage<TimetableVO> fillData(IPage<TimetableVO> page);


    /**
     *  填充图片信息
     *
     * <AUTHOR>
     * @date 2025年03月12日 10时37分
     */
    TimetableVO fillInfoData(Timetable timetable);


    /**
     *  填充导出数据
     *
     * <AUTHOR>
     * @date 2025年07月08日 09时28分
     */
    List<TimetableExportVO> fillExportData(List<TimetableVO> export);
}
