package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.api.constant.AttendClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.valid.SsClassTimeValidGroup;
import com.yuedu.ydsf.eduConnect.api.valid.SsClassTimeValidGroup.GenRoomTimeCodeGroup;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsInRoomVo;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.job.SyncXiaogjMessageJobService;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 课次信息表 控制类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:12:52
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/ssClassTime")
@Tag(description = "ss_class_time", name = "课次信息表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SsClassTimeController {

    private final SsClassTimeService ssClassTimeService;

    private final SyncXiaogjMessageJobService syncXiaogjMessageJobService;

    /**
     * 直播课分页查询
     * @param ssClassTimeQuery 课次信息表
     * @return
     */
    @Operation(summary = "直播课分页查询", description = "直播课分页查询")
    @PostMapping("/direct/page")
    @HasPermission("edusystem_directClassTime_view")
    public R<IPage<SsClassTimeVO>> getDirectSsClassTimePage(@RequestBody SsClassTimeQuery ssClassTimeQuery) {
        ssClassTimeQuery.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_0.CODE);
        Page page = new Page(ssClassTimeQuery.getCurrent(), ssClassTimeQuery.getSize());
        return R.ok(ssClassTimeService.page(page, ssClassTimeQuery));
    }


    /**
     * 点播课分页查询
     * @param ssClassTimeQuery 课次信息表
     * @return
     */
    @Operation(summary = "点播课分页查询", description = "点播课分页查询")
    @PostMapping("/vod/page")
    @HasPermission("edusystem_vodClassTime_view")
    public R<IPage<SsClassTimeVO>> getSsClassTimePage(@RequestBody SsClassTimeQuery ssClassTimeQuery) {
        ssClassTimeQuery.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_1.CODE);
        Page page = new Page(ssClassTimeQuery.getCurrent(), ssClassTimeQuery.getSize());
        return R.ok(ssClassTimeService.page(page, ssClassTimeQuery));
    }


    /**
     * 获取直播授权校区列表
     *
     * @param id
     * <AUTHOR>
     * @date 2024年10月16日 13时33分
     */
    @Operation(summary = "获取直播授权校区列表", description = "获取直播授权校区列表")
    @GetMapping("/direct/authRoomList/{id}")
    @HasPermission("edusystem_directClassTime_authRoom_view")
    public R<List<SsDeviceVO>> getDirectAuthClassRoom(@PathVariable Long id) {
        return R.ok(ssClassTimeService.selectAuthClassRoom(id));
    }


    /**
     * 获取点播授权校区列表
     *
     * @param id
     * <AUTHOR>
     * @date 2024年10月16日 13时33分
     */
    @Operation(summary = "获取点播授权校区列表", description = "获取点播授权校区列表")
    @GetMapping("/vod/authRoomList/{id}")
    @HasPermission("edusystem_vodClassTime_authRoom_view")
    public R<List<SsDeviceVO>> getVodAuthClassRoom(@PathVariable Long id) {
        return R.ok(ssClassTimeService.selectAuthClassRoom(id));
    }


    /**
     * 直播课次授权校区
     *
     * @param ssClassTimeDTO
     * <AUTHOR>
     * @date 2024年10月16日 16时00分
     */
    @Operation(summary = "直播课次授权校区", description = "直播课次授权校区")
    @PutMapping("/direct/authClassTimeRoom")
    @HasPermission("edusystem_directClassTime_auth")
    public R directAuthClassTimeRoom(
        @Validated(SsClassTimeValidGroup.UpdateAuthClassTimeRoomGroup.class) @RequestBody SsClassTimeDTO ssClassTimeDTO) {
        log.debug("直播课次授权校区,ssClassTimeDTO:{}", ssClassTimeDTO);
        return R.ok(ssClassTimeService.updateAuthClassTimeRoom(ssClassTimeDTO));
    }

    /**
     * 点播课次授权校区
     *
     * @param ssClassTimeDTO
     * <AUTHOR>
     * @date 2024年10月16日 16时00分
     */
    @Operation(summary = "点播课次授权校区", description = "点播课次授权校区")
    @PutMapping("/vod/authClassTimeRoom")
    @HasPermission("edusystem_directClassTime_auth")
    public R vodAuthClassTimeRoom(
        @Validated(SsClassTimeValidGroup.UpdateAuthClassTimeRoomGroup.class) @RequestBody SsClassTimeDTO ssClassTimeDTO) {
        log.debug("点播课次授权校区,ssClassTimeDTO:{}", ssClassTimeDTO);
        return R.ok(ssClassTimeService.updateAuthClassTimeRoom(ssClassTimeDTO));
    }


    /**
     * 通过条件查询课次信息表
     *
     * @param ssClassTime 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询", description = "通过条件查询对象")
    @GetMapping("/details")
    @HasPermission("edusystem_ssClassTime_view")
    public R getDetails(@ParameterObject SsClassTime ssClassTime) {
        return R.ok(ssClassTimeService.list(Wrappers.query(ssClassTime)));
    }


    /**
     * 新增课次信息表
     *
     * @param ssClassTime 课次信息表
     * @return R
     */
    @Operation(summary = "新增课次信息表", description = "新增课次信息表")
    @PostMapping
    @HasPermission("edusystem_ssClassTime_add")
    public R save(@RequestBody SsClassTime ssClassTime) {
        log.info("新增课次信息表,ssClassTime:{}", ssClassTime);
        return R.ok(ssClassTimeService.save(ssClassTime));
    }

    /**
     * 修改课次信息表
     *
     * @param ssClassTime 课次信息表
     * @return R
     */
    @Operation(summary = "修改课次信息表", description = "修改课次信息表")
    @PutMapping
    @HasPermission("edusystem_ssClassTime_edit")
    public R updateById(@RequestBody SsClassTime ssClassTime) {
        log.info("修改课次信息表,ssClassTime:{}", ssClassTime);
        return R.ok(ssClassTimeService.updateById(ssClassTime));
    }

    /**
     * 通过id删除课次信息表
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除课次信息表", description = "通过id删除课次信息表")
    @DeleteMapping
    @HasPermission("edusystem_ssClassTime_del")
    public R removeById(@RequestBody Long[] ids) {
        log.info("通过id删除课次信息表,ids:{}", ids);
        return R.ok(ssClassTimeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @param ssClassTime 查询条件
     * @param ids         导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_ssClassTime_export")
    public List<SsClassTime> exportExcel(SsClassTime ssClassTime, Long[] ids) {
        return ssClassTimeService.list(Wrappers.lambdaQuery(ssClassTime)
            .in(ArrayUtil.isNotEmpty(ids), SsClassTime::getId, ids));
    }

    /**
     * 导入excel 表
     *
     * @param ssClassTimeList 对象实体列表
     * @param bindingResult   错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_ssClassTime_export")
    public R importExcel(@RequestExcel List<SsClassTime> ssClassTimeList,
        BindingResult bindingResult) {
        return R.ok(ssClassTimeService.saveBatch(ssClassTimeList));
    }

    /**
     * 监课通过房间id进入监课端
     *
     * @param classTimeQuery
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.api.vo.SsInRoomVo>
     * <AUTHOR>
     * @date 2024/10/14 10:11
     */
    @Operation(summary = "监课进入监课端", description = "监课进入监课端")
    @GetMapping("/inRoomByRoomUuid")
    @SysLog("监课进入监课端")
    public R<SsInRoomVo> inRoomByRoomUuid(@ParameterObject SsClassTimeQuery classTimeQuery) {
        SsInRoomVo ssInRoomVo = ssClassTimeService.inRoomByRoomUuid(classTimeQuery);
        return R.ok(ssInRoomVo);
    }


    /**
     * 生成主讲端/教室端上课码
     *
     * @param ssClassTimeDto
     * @return AjaxResult
     * <AUTHOR>
     * @date 2024/10/15 9:01
     */
    @PostMapping(value = "/genRoomTimeCode")
    @Operation(summary = "生成主讲端/教室端上课码", description = "生成主讲端/教室端上课码")
    @HasPermission("edusystem_ssClassTime_genRoomTimeCode")
    @SysLog("生成主讲端/教室端上课码")
    public R genRoomTimeCode(
        @Validated(GenRoomTimeCodeGroup.class) @RequestBody SsClassTimeDTO ssClassTimeDto) {
        log.info("生成主讲端/教室端上课码,ssClassTimeDto:{}", ssClassTimeDto);
        try {
            String classTimeCode = ssClassTimeService.genRoomTimeCode(ssClassTimeDto);
            return R.ok("获取成功", classTimeCode);
        } catch (Exception e) {
            return R.failed("获取失败:" + e.getMessage());
        }
    }


    /**
     * 获取课次对应的上课码
     *
     * @param id 课次ID
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/23 16:20
     */
    @GetMapping(value = "/getRoomTimeCode/{id}")
    @Operation(summary = "获取主讲端/教室端上课码", description = "获取主讲端/教室端上课码")
    @HasPermission("edusystem_ssClassTime_getRoomTimeCode")
    @SysLog("获取主讲端/教室端上课码")
    public R<SsClassTimeVO> getRoomTimeCode(@PathVariable Long id) {
        try {
            SsClassTimeVO classTimeVO = ssClassTimeService.getRoomTimeCode(id);
            return R.ok(classTimeVO);
        } catch (Exception e) {
            return R.failed("获取失败:" + e.getMessage());
        }
    }

    /**
     * 读书会新增课次
     *
     * @param ssClassTimeDto
     * @return
     * <AUTHOR>
     * @date 2024/10/16 11:15
     */
    @PostMapping(value = "/direct/addClassTime")
    @Operation(summary = "读书会新增课次", description = "读书会新增课次")
    @HasPermission("edusystem_ssClassTime_direct_addClassTime")
    public R directAddClassTime(
        @Validated(SsClassTimeValidGroup.AddClassTimeGroup.class) @RequestBody
        SsClassTimeDTO ssClassTimeDto) {
        log.info("读书会新增课次,ssClassTimeDto:{}", ssClassTimeDto);
        ssClassTimeService.addClassTime(ssClassTimeDto);
        return R.ok("新增成功");
    }

    /**
     * 点播课新增课次
     *
     * @param ssClassTimeDto
     * @return
     * <AUTHOR>
     * @date 2024/10/16 11:15
     */
    @PostMapping(value = "/vod/addClassTime")
    @Operation(summary = "点播课新增课次", description = "点播课新增课次")
    @HasPermission("edusystem_ssClassTime_vod_addClassTime")
    public R vodAddClassTime(
        @Validated(SsClassTimeValidGroup.AddClassTimeGroup.class) @RequestBody
        SsClassTimeDTO ssClassTimeDto) {
        log.info("点播课新增课次,ssClassTimeDto:{}", ssClassTimeDto);
        ssClassTimeService.addClassTime(ssClassTimeDto);
        return R.ok("新增成功");
    }

    /**
     * 读书会编辑课次
     *
     * @param ssClassTimeDto
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/17 10:47
     */
    @PutMapping(value = "/direct/updateClassTime")
    @Operation(summary = "读书会编辑课次", description = "读书会编辑课次")
    @HasPermission("edusystem_ssClassTime_direct_updateClassTime")
    public R directUpdateClassTime(
        @Validated(ValidGroup.Update.class) @RequestBody SsClassTimeDTO ssClassTimeDto) {
        log.info("读书会编辑课次,ssClassTimeDto:{}", ssClassTimeDto);
        ssClassTimeService.updateClassTime(ssClassTimeDto);
        return R.ok("编辑成功！");
    }

    /**
     * 点播课编辑课次
     *
     * @param ssClassTimeDto
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/17 10:47
     */
    @PutMapping(value = "/vod/updateClassTime")
    @Operation(summary = "点播课编辑课次", description = "点播课编辑课次")
    @HasPermission("edusystem_ssClassTime_vod_updateClassTime")
    @SysLog("点播课编辑课次")
    public R vodUpdateClassTime(
        @Validated(ValidGroup.Update.class) @RequestBody SsClassTimeDTO ssClassTimeDto) {
        log.info("点播课编辑课次,ssClassTimeDto:{}", ssClassTimeDto);
        ssClassTimeService.updateClassTime(ssClassTimeDto);
        return R.ok("编辑成功！");
    }

    /**
     * 读书会删除课次
     *
     * @param ids
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/18 13:52
     */
    @DeleteMapping(value = "/direct/deleteClassTime")
    @Operation(summary = "读书会删除课次", description = "读书会删除课次")
    @HasPermission("edusystem_ssClassTime_direct_deleteClassTime")
    @SysLog("读书会删除课次")
    public R directDeleteClassTime(@RequestBody @NotEmpty(message = "请选择删除数据！") Long[] ids) {
        log.info("读书会删除课次,ids:{}", ids);
        ssClassTimeService.deleteClassTime(ids);
        return R.ok("删除课次成功！");
    }

    /**
     * 点播课删除课次
     *
     * @param ids
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/18 13:52
     */
    @DeleteMapping(value = "/vod/deleteClassTime")
    @Operation(summary = "点播课删除课次", description = "点播课删除课次")
    @HasPermission("edusystem_ssClassTime_vod_deleteClassTime")
    @SysLog("点播课删除课次")
    public R vodDeleteClassTime(@RequestBody @NotEmpty(message = "请选择删除数据！") Long[] ids) {
        log.info("点播课删除课次,ids:{}", ids);
        ssClassTimeService.deleteClassTime(ids);
        return R.ok("删除课次成功！");
    }

    /**
     * 查看讲师空闲时间(读书会)
     *
     * @param ssClassTimeQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/18 9:01
     */
    @GetMapping(value = "/selectLiveLecturerFreeTime")
    @Operation(summary = "查看讲师空闲时间(读书会)", description = "查看讲师空闲时间(读书会)")
    public R<IPage<Map<String, Object>>> selectLiveLecturerFreeTime(@ParameterObject Page page,
        @ParameterObject SsClassTimeQuery ssClassTimeQuery) {
        ssClassTimeQuery.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_0.CODE);
        IPage lecturerFreeTimeVoList = ssClassTimeService.selectLecturerFreeTime(page,
            ssClassTimeQuery);
        return R.ok(lecturerFreeTimeVoList);
    }

    /**
     * 查看讲师空闲时间(点播课)
     *
     * @param ssClassTimeQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/18 9:01
     */
    @GetMapping(value = "/selectVodLecturerFreeTime")
    @Operation(summary = "查看讲师空闲时间(点播课)", description = "查看讲师空闲时间(点播课)")
    public R<IPage<Map<String, Object>>> selectVodLecturerFreeTime(@ParameterObject Page page,
        @ParameterObject SsClassTimeQuery ssClassTimeQuery) {
        ssClassTimeQuery.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_1.CODE);
        IPage lecturerFreeTimeVoList = ssClassTimeService.selectLecturerFreeTime(page,
            ssClassTimeQuery);
        return R.ok(lecturerFreeTimeVoList);
    }

    /**
     * 获取监课链接
     *
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/21 13:39
     */
    @GetMapping(value = "/getSupervisionUrl")
    @Operation(summary = "获取监课链接", description = "获取监课链接")
    @HasPermission("edusystem_ssClassTime_getSupervisionUrl")
    @SysLog("获取监课链接")
    public R<SsClassTimeVO> getSupervisionUrl(@ParameterObject SsClassTimeQuery classTimeQuery) {
        SsClassTimeVO ssClassTimeVO = ssClassTimeService.getSupervisionUrl(classTimeQuery);
        return R.ok(ssClassTimeVO);
    }

    /**
     * 获取课次详情
     *
     * @param id
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/21 14:04
     */
    @GetMapping(value = "/getClassTimeInfo/{id}")
    @Operation(summary = "获取课次详情", description = "获取课次详情")
    @SysLog("获取课次详情")
    public R<SsClassTimeVO> getClassTimeInfo(@PathVariable Long id) {
        SsClassTimeVO info = ssClassTimeService.getClassTimeInfo(id);
        return R.ok(info);
    }

    /**
     * 返回教室日历
     *
     * @param classTimeQuery
     * @return AjaxResult
     * <AUTHOR>
     * @date 2024/10/24 14:28
     */
    @PostMapping(value = "/classRoomFreeTimeCalendar")
    @Operation(summary = "主讲教室日历", description = "主讲教室日历")
    @SysLog("主讲教室日历")
    public R<IPage<SsClassRoomFreeTimeVO>> classRoomFreeTimeCalendar(
        @RequestBody SsClassTimeQuery classTimeQuery) {
        IPage timeCalendar = ssClassTimeService.classRoomFreeTimeCalendar(classTimeQuery);
        return R.ok(timeCalendar);
    }

    /**
     * 手动执行推送校管家任务
     */
    @Operation(summary = "手动执行推送校管家任务", description = "手动执行推送校管家任务")
    @GetMapping("/pushXiaogjMessage")
    @Idempotent(expireTime = 3)
    public R<Void> pushXiaogjMessage() {
        syncXiaogjMessageJobService.getResult(0);
        return R.ok("执行成功");
    }
}
