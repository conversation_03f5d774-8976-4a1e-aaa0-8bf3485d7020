package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanDetailDraftAddQuery;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanDetailDraftQuery;
import com.yuedu.ydsf.eduConnect.api.valid.LiveRoomPlanDetailDraftValidGroup.EditDetail;
import com.yuedu.ydsf.eduConnect.api.valid.LiveRoomPlanDetailDraftValidGroup.SaveDetail;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDetailDraftService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 直播间计划明细草稿表 控制类
 *
 * <AUTHOR>
 * @date 2024-12-03 09:46:17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/LiveRoomPlanDetailDraft" )
@Tag(description = "ea_live_room_plan_detail_draft" , name = "直播间计划明细草稿表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LiveRoomPlanDetailDraftController {

    private final  LiveRoomPlanDetailDraftService liveRoomPlanDetailDraftService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param liveRoomPlanDetailDraft 直播间计划明细草稿表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("edusystem_LiveRoomPlanDetailDraft_view")
    public R getLiveRoomPlanDetailDraftPage(@ParameterObject Page page, @ParameterObject LiveRoomPlanDetailDraft liveRoomPlanDetailDraft) {
        LambdaQueryWrapper<LiveRoomPlanDetailDraft> wrapper = Wrappers.lambdaQuery();
        return R.ok(liveRoomPlanDetailDraftService.page(page, wrapper));
    }

    /**
     * 查询全部直播间计划排期
     *
     * @return 查询全部直播间计划排期
     */
    @Operation(summary = "查询全部直播间计划排期", description = "查询全部直播间计划排期")
    @GetMapping("/list")
    public R list(@RequestParam(name = "plan_id") Integer planId) {
        return R.ok(liveRoomPlanDetailDraftService.listPlans(planId));
    }

    /**
     * 通过条件查询直播间计划明细草稿表
     * @param liveRoomPlanDetailDraft 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("edusystem_LiveRoomPlanDetailDraft_view")
    public R getDetails(@ParameterObject LiveRoomPlanDetailDraft liveRoomPlanDetailDraft) {
        return R.ok(liveRoomPlanDetailDraftService.list(Wrappers.query(liveRoomPlanDetailDraft)));
    }

  /**
   * 新增直播间计划明细草稿表
   *
   * @param detailDraftAddQuery 直播间计划明细草稿表
   * @return R
   */
  @Operation(summary = "新增直播间计划明细草稿表", description = "新增直播间计划明细草稿表")
  @PostMapping("/add")
  @HasPermission("edusystem_LiveRoomPlanDetailDraft_add")
  public R save(
      @Validated(SaveDetail.class) @RequestBody
          LiveRoomPlanDetailDraftAddQuery detailDraftAddQuery) {
    liveRoomPlanDetailDraftService.savePlanDetail(detailDraftAddQuery);
    return R.ok();
  }

    /**
     * 修改直播间计划明细草稿表
     * @param planDetailDraftQuery 直播间计划明细草稿表
     * @return R
     */
    @Operation(summary = "修改直播间计划明细草稿表" , description = "修改直播间计划明细草稿表" )
    @PutMapping("/edit")
    @HasPermission("edusystem_LiveRoomPlanDetailDraft_edit")
    public R updateById(@Validated(EditDetail.class) @RequestBody LiveRoomPlanDetailDraftQuery planDetailDraftQuery) {
        liveRoomPlanDetailDraftService.editPlanDetail(planDetailDraftQuery);
        return R.ok();
    }

    /**
     * 通过id删除直播间计划明细草稿表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除直播间计划明细草稿表" , description = "通过id删除直播间计划明细草稿表" )
    @DeleteMapping("/delete")
    @HasPermission("edusystem_LiveRoomPlanDetailDraft_del")
    public R removeById(@RequestBody Long[] ids) {
        liveRoomPlanDetailDraftService.removeDetail(CollUtil.toList(ids));
        return R.ok();
    }


    /**
     * 导出excel 表格
     * @param liveRoomPlanDetailDraft 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_LiveRoomPlanDetailDraft_export")
    public List<LiveRoomPlanDetailDraft> exportExcel(LiveRoomPlanDetailDraft liveRoomPlanDetailDraft,Long[] ids) {
        return liveRoomPlanDetailDraftService.list(Wrappers.lambdaQuery(liveRoomPlanDetailDraft).in(ArrayUtil.isNotEmpty(ids), LiveRoomPlanDetailDraft::getId, ids));
    }

    /**
     * 导入excel 表
     * @param liveRoomPlanDetailDraftList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_LiveRoomPlanDetailDraft_export")
    public R importExcel(@RequestExcel List<LiveRoomPlanDetailDraft> liveRoomPlanDetailDraftList, BindingResult bindingResult) {
        return R.ok(liveRoomPlanDetailDraftService.saveBatch(liveRoomPlanDetailDraftList));
    }
}
