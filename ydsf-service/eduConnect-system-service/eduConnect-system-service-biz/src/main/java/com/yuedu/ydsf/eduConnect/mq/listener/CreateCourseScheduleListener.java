package com.yuedu.ydsf.eduConnect.mq.listener;

import com.yuedu.ydsf.eduConnect.manager.SsCourseScheduleManager;
import com.yuedu.ydsf.eduConnect.mq.dto.CourseScheduleMqDTO;
import com.yuedu.ydsf.eduConnect.util.MqUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * @author: zhangchuanfu
 * @date: 2024/10/29
 **/
@Slf4j
@Service
@RocketMQMessageListener(topic = "${rocketmq.topics.create_course_schedule_topic}",
    consumerGroup = "${rocketmq.groups.create_course_schedule_group}", tag = "*")
@ConditionalOnProperty(
    prefix = "rocketmq",
    name = "enabled",
    havingValue = "true",
    matchIfMissing = false)
public class CreateCourseScheduleListener implements RocketMQListener {

    private final SsCourseScheduleManager ssCourseScheduleManager;

    public CreateCourseScheduleListener(SsCourseScheduleManager ssCourseScheduleManager) {
        this.ssCourseScheduleManager = ssCourseScheduleManager;
    }

    @Override
    public ConsumeResult consume(MessageView messageView) {
        //接收到的消息:MessageViewImpl{messageId=01FA783229666600010732155200000000, topic=edusystem_create_course_schedule_topic_dev, bornHost=educonnect-system-service-7bf9d5c86f-gqh24, bornTimestamp=1730181970990, endpoints=dns:ep-2zeia3ec2e9d74d6a357.epsrv-2ze9nhgmudbj2saxncjc.cn-beijing.privatelink.aliyuncs.com:8081, deliveryAttempt=1, tag=*, keys=[], messageGroup=null, deliveryTimestamp=null, properties={id=2a373b00-423a-bf7f-4a89-8c0e58f4ed0e, contentType=application/json, timestamp=1730181970578}}
        log.debug("接收到的消息:messageId:{},body:{}", messageView.getMessageId(),
            messageView.getBody());
        //{"payload":468,"headers":{"id":"be177389-6204-ffe8-432f-b35d548fece5","timestamp":1730181970488}}
        CourseScheduleMqDTO courseScheduleMqDTO = null;
        //如果解析消息内容失败，返回ConsumeResult.SUCCESS，避免消息重复消费阻塞其他正常消息消费，原因是消息重试还会是失败。
        try {
            //解析消息内容
            courseScheduleMqDTO = MqUtil.convertMessageBodyToDTO(messageView,
                CourseScheduleMqDTO.class);
            log.debug("解析后的消息内容:{}", courseScheduleMqDTO);
            if (courseScheduleMqDTO == null) {
                return ConsumeResult.SUCCESS;
            }
        } catch (Exception e) {
            log.error("解析消息内容失败", e);
            return ConsumeResult.SUCCESS;
        }
        Long courseScheduleId = courseScheduleMqDTO.getCourseScheduleId();
        //同步声网创建房间
        try {
            ssCourseScheduleManager.syncAgoraRooms(courseScheduleId);
        } catch (Exception e) {
            log.error("同步声网创建房间失败", e);
            return ConsumeResult.FAILURE;
        }
        //同步校管家
        try {
            ssCourseScheduleManager.syncXiaoGuanJia(courseScheduleId);
        } catch (Exception e) {
            log.error("同步校管家失败", e);
            return ConsumeResult.FAILURE;
        }
        return ConsumeResult.SUCCESS;
    }
}
