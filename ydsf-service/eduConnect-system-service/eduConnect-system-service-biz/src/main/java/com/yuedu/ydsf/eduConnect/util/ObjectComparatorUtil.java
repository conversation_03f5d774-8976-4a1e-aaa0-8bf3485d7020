package com.yuedu.ydsf.eduConnect.util;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * 数据对象字段比较类
 * <AUTHOR>
 * @date 2024/11/4 14:07
 */
public class ObjectComparatorUtil {

    public static String compareObjects(Object originalObject, Object newObject) {
        if (originalObject == null || newObject == null || !originalObject.getClass().equals(newObject.getClass())) {
            throw new IllegalArgumentException("Objects must be non-null and of the same class");
        }

        StringBuilder logBuilder = new StringBuilder();
        Class<?> clazz = originalObject.getClass();
        Field[] fields = clazz.getDeclaredFields();

        // 原内容/新内容, 修改比较参数封装
        printObjectFields(logBuilder, originalObject, newObject, fields);
        logBuilder.append("\n");

        return logBuilder.toString();
    }

    private static void printObjectFields(StringBuilder logBuilder, Object originalObject, Object newObject, Field[] fields) {
        for (Field field : fields) {

            // 允许访问私有字段
            field.setAccessible(true);

            try {
                String fieldName = field.getName();
                String getterMethodName = "get" + Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);
                Method getterMethod = originalObject.getClass().getMethod(getterMethodName);
                Object originalValue = getterMethod.invoke(originalObject);
                Object newValue = getterMethod.invoke(newObject);
                logBuilder.append("字段[")
                    .append(fieldName)
                    .append("]")
                    .append(":")
                    .append("修改前[")
                    .append(Objects.nonNull(originalValue) ? originalValue : "")
                    .append("]")
                    .append("修改后[")
                    .append(Objects.nonNull(newValue) ? newValue : "")
                    .append("]; ");
            } catch (IllegalAccessException | NoSuchMethodException | java.lang.reflect.InvocationTargetException e) {
                // 处理反射异常（这里简单抛出运行时异常，实际应用中可能需要日志记录或其他处理）
                throw new RuntimeException(e);
            }
        }
    }


}
