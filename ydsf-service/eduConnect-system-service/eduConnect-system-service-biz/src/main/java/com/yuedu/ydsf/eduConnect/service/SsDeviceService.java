package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.query.SsDeviceQuery;
import com.yuedu.ydsf.eduConnect.api.dto.SsDeviceDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceStatusVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsReadDeviceVo;
import com.yuedu.ydsf.eduConnect.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;

import java.io.Serializable;
import java.util.List;

/**
* 设备表服务接口
*
* <AUTHOR>
* @date  2024/09/26
*/
public interface SsDeviceService extends IService<SsDevice> {



    /**
    * 设备表分页查询
    * @param page 分页对象
    * @param ssDeviceQuery 设备表
    * @return IPage 分页结果
    */
    IPage page(Page page, SsDeviceQuery ssDeviceQuery);


    /**
    * 新增设备表
    * @param ssDeviceDTO 设备表
    * @return boolean 执行结果
    */
    boolean add(SsDeviceDTO ssDeviceDTO);


    /**
    * 修改设备表
    * @param ssDeviceDTO 设备表
    * @return boolean 执行结果
    */
    boolean edit(SsDeviceDTO ssDeviceDTO);


    /**
    * 导出excel 设备表表格
    * @param ssDeviceQuery 查询条件
    * @param ids 导出指定ID
    * @return List<SsDeviceVO> 结果集合
    */
    List<SsDeviceVO> export(SsDeviceQuery ssDeviceQuery, Long[] ids);

    /**
     * 通过设备ID获取设备信息
     *
     * @param id
     * @return com.yuedu.ydsf.eduConncet.api.vo.SsDeviceVO
     * <AUTHOR>
     * @date 2024/9/28 9:13
     */
    SsDeviceVO getInfoById(Serializable id);

    /**
     * 解绑设备
     *
     * @return void
     * <AUTHOR>
     * @date 2024/9/28 9:24
     */
    void unbind(Long[] ids);

    /**
     * 启用/禁用设备状态
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/9/28 9:46
     */
    void changeDeviceState(Long[] ids);

    /**
     * 通过id删除设备
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/9/28 10:04
     */
    void deleteDevice(Long[] ids);

    /**
     * 查询所有教室端有效设备
     * @param ssDeviceQuery
     * @return java.util.List<com.yuedu.ydsf.eduConncet.api.vo.SsDeviceVO>
     * <AUTHOR>
     * @date 2024/10/9 16:24
     */
    List<SsDeviceVO> getClassRoomDeviceList(SsDeviceQuery ssDeviceQuery);


    /**
     * 通过直播间ID获取设备信息
     * @param classRoomId 直播间ID
     * @return com.yuedu.ydsf.eduConncet.entity.SsDevice
     * @throws BizException 未查询到设备，抛出异常
     */
    SsDevice getDeviceByClassRoomId(Long classRoomId) throws BizException;

    /**
     * 获取设备列表
     * <AUTHOR>
     * @date 2025/2/10 14:35
     * @param page
     * @param pageSize
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsReadDeviceVo
     */
    SsReadDeviceVo pageRead(Integer page, Integer pageSize);

  /**
   * 结算更新设备信息
   *
   * <AUTHOR>
   * @date 2025/2/10 14:35
   * @param deviceDto
   */
  void settlementUpdateDeviceInfo(SsDeviceDTO deviceDto);
}
