package com.yuedu.ydsf.eduConnect.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineExportVO;
import com.yuedu.ydsf.eduConnect.manager.CourseMakeUpOnlineManager;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.eduConnect.mapper.CourseMakeUpOnlineMapper;
import com.yuedu.ydsf.eduConnect.service.CourseMakeUpOnlineService;
import com.yuedu.ydsf.eduConnect.api.query.CourseMakeUpOnlineQuery;
import com.yuedu.ydsf.eduConnect.api.dto.CourseMakeUpOnlineDTO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineVO;
import com.yuedu.ydsf.eduConnect.entity.CourseMakeUpOnline;

import java.io.Serializable;
import java.util.Objects;
import java.util.Optional;
import java.util.List;


/**
* 门店线上补课表服务层
*
* <AUTHOR>
* @date  2025/04/28
*/
@Service
@AllArgsConstructor
public class CourseMakeUpOnlineServiceImpl extends ServiceImpl<CourseMakeUpOnlineMapper,CourseMakeUpOnline>
    implements CourseMakeUpOnlineService{

    private final CourseMakeUpOnlineManager courseMakeUpOnlineManager;

    /**
     * 门店线上补课表分页查询
     *
     * @param page 分页对象
     * @param courseMakeUpOnlineQuery 门店线上补课表
     * @return IPage 分页结果
     */
    @Override
    public IPage<CourseMakeUpOnlineVO> page(Page page,CourseMakeUpOnlineQuery courseMakeUpOnlineQuery) {
       return courseMakeUpOnlineManager.fillData(baseMapper.page(page,  courseMakeUpOnlineQuery));
    }


    /**
     * 根据ID获得门店线上补课表信息
     *
     * @param id id
     * @return CourseMakeUpOnlineVO 详细信息
     */
    @Override
    public CourseMakeUpOnlineVO getInfoById(Serializable id) {
        CourseMakeUpOnline courseMakeUpOnline = getById(id);
        if(Objects.isNull(courseMakeUpOnline)){
            throw new BizException("未查询到对应的补课记录！");
        }

        return courseMakeUpOnlineManager.fillInfoData(courseMakeUpOnline);

    }


    /**
     * 新增门店线上补课表
     *
     * @param courseMakeUpOnlineDTO 门店线上补课表
     * @return boolean 执行结果
     */
    @Override
    public boolean add(CourseMakeUpOnlineDTO courseMakeUpOnlineDTO) {
        CourseMakeUpOnline courseMakeUpOnline = new CourseMakeUpOnline();
        BeanUtils.copyProperties(courseMakeUpOnlineDTO, courseMakeUpOnline);
        return save(courseMakeUpOnline);
    }


    /**
     * 修改门店线上补课表
     *
     * @param courseMakeUpOnlineDTO 门店线上补课表
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(CourseMakeUpOnlineDTO courseMakeUpOnlineDTO) {
        CourseMakeUpOnline courseMakeUpOnline = new CourseMakeUpOnline();
        BeanUtils.copyProperties(courseMakeUpOnlineDTO, courseMakeUpOnline);
        return updateById(courseMakeUpOnline);
    }



    @Override
    public List<CourseMakeUpOnlineExportVO> export(CourseMakeUpOnlineQuery courseMakeUpOnlineQuery) {
        return courseMakeUpOnlineManager.fillExportData(baseMapper.export( courseMakeUpOnlineQuery));
    }

}
