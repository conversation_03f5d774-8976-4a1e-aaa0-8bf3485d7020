package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.houbb.heaven.util.lang.BeanUtil;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteCourseVersionService;
import com.yuedu.teaching.api.feign.RemoteCoursewareService;
import com.yuedu.teaching.api.feign.RemoteCoursewareVersionService;
import com.yuedu.teaching.api.feign.RemoteLessonPubService;
import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.dto.CoursewareVersionDTO;
import com.yuedu.teaching.query.LessonPubQuery;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.CoursewareVersionVO;
import com.yuedu.teaching.vo.LessonPubVO;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import com.yuedu.ydsf.common.security.util.PcContextHolder;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.api.constant.AuditStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.CourseVodDisableEnum;
import com.yuedu.ydsf.eduConnect.api.constant.RecordingStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.TaskStatusEnum;
import com.yuedu.ydsf.eduConnect.api.dto.RecordVideoTaskDTO;
import com.yuedu.ydsf.eduConnect.api.query.RecordVideoTaskQuery;
import com.yuedu.ydsf.eduConnect.api.vo.RecordVideoTaskVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.constant.RecordVideoTaskConstant;
import com.yuedu.ydsf.eduConnect.entity.CourseVod;
import com.yuedu.ydsf.eduConnect.entity.CourseVodVideo;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.RecordVideoTask;
import com.yuedu.ydsf.eduConnect.entity.Recording;
import com.yuedu.ydsf.eduConnect.entity.SsRecording;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanPub;
import com.yuedu.ydsf.eduConnect.mapper.CourseVodMapper;
import com.yuedu.ydsf.eduConnect.mapper.CourseVodVideoMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.RecordVideoTaskMapper;
import com.yuedu.ydsf.eduConnect.mapper.RecordingMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsRecordingMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanPubMapper;
import com.yuedu.ydsf.eduConnect.service.RecordVideoTaskNewService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.BatchResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.yuedu.ydsf.eduConnect.constant.Constants.DEFAULT_DEVICE_ID;


/**
 * @ClassName RecordVideoTaskServiceImpl
 * @Description 录课任务控制类
 * <AUTHOR>
 * @Date 2024/11/29 16:48:01
 * @Version v0.0.1
 */
@Slf4j
@Service
public class RecordVideoTaskNewServiceImpl extends
    MPJBaseServiceImpl<RecordVideoTaskMapper, RecordVideoTask> implements
    RecordVideoTaskNewService {

    @Resource
    private TeachingPlanPubMapper planPubMapper;

    @Resource
    private LiveRoomPlanDraftMapper liveRoomPlanDraftMapper;

    @Resource
    private TeachingPlanDraftMapper teachingPlanDraftMapper;

    @Resource
    private RecordVideoTaskMapper recordVideoTaskMapper;

    @Resource
    private RemoteCourseService courseService;

    @Resource
    private RemoteLessonPubService lessonPubService;

    @Resource
    private RemoteCoursewareVersionService coursewareVersionService;

    @Resource
    private RecordingMapper eaRecordingMapper;

    @Resource
    private SsRecordingMapper ssRecordingMapper;

    @Resource
    private CourseVodMapper courseVodMapper;

    @Resource
    private CourseVodVideoMapper courseVodVideoMapper;

    /**
     * 通过讲师Id,课程Id,课节Id,最早课程开始时间去掉重复的教学计划
     *
     * @param detailList 教学计划
     */
    private static void duplicateRemovalDetail(List<TeachingPlanDetailPubVO> detailList) {
        //根据courseId、lessonOrde、coursewareVersion去重，保留classStartDateTime最早的
        if (detailList == null || detailList.isEmpty()) {
            return;
        }
        Map<String, TeachingPlanDetailPubVO> uniqueMap = new HashMap<>();
        for (TeachingPlanDetailPubVO detail : detailList) {
            String key =
                detail.getLectureId() + "_" + detail.getCourseId() + "_" + detail.getLessonOrder();
            TeachingPlanDetailPubVO exist = uniqueMap.get(key);
            if (exist == null || detail.getClassStartDateTime()
                .isBefore(exist.getClassStartDateTime())) {
                uniqueMap.put(key, detail);
            }
        }
        detailList.clear();
        detailList.addAll(uniqueMap.values());
    }

    /**
     * 设置录课任务部分字段信息
     *
     * @param detailPubVO     教学任务详情
     * @param recordVideoTask 录课任务
     */
    private static void setTaskPartInfo(TeachingPlanDetailPubVO detailPubVO,
        RecordVideoTask recordVideoTask) {
        recordVideoTask.setTeachingPlanDetailId(detailPubVO.getPlanId());
        recordVideoTask.setTeachingPlanId(detailPubVO.getPlanId());
        recordVideoTask.setCourseId(detailPubVO.getCourseId());
        recordVideoTask.setLessonOrder(detailPubVO.getLessonOrder());
        recordVideoTask.setEarliestStartDate(detailPubVO.getClassStartDateTime());
        recordVideoTask.setLectureId(detailPubVO.getLectureId());
        recordVideoTask.setLectureName(detailPubVO.getLectureName());
    }

    /**
     * 设置课程版本信息
     *
     * @param courseVersionList     课程版本列表
     * @param teachingPlanDetailPub 教学计划详情
     * @param recordVideoTask       录课任务
     */
    private static void setCourseVersionInfo(List<CourseVO> courseVersionList,
        TeachingPlanDetailPubVO teachingPlanDetailPub, RecordVideoTask recordVideoTask) {
        if (!courseVersionList.isEmpty()) {
            courseVersionList.stream().filter(courseVO ->
                    courseVO.getId().equals(teachingPlanDetailPub.getCourseId().intValue()))
                .findFirst()
                .ifPresent(courseVO -> {
                    recordVideoTask.setCourseVersion(courseVO.getVersion());
                });
        }
    }


    /**
     * 设置课节版本信息
     *
     * @param lessonVoList          课节发布列表
     * @param teachingPlanDetailPub 教学计划详情
     * @param recordVideoTask       录课任务
     */
    private static void setLessonVersionInfo(List<LessonPubVO> lessonVoList,
        TeachingPlanDetailPubVO teachingPlanDetailPub, RecordVideoTask recordVideoTask) {
        if (!lessonVoList.isEmpty()) {
            for (LessonPubVO lessonPubVO : lessonVoList) {
                if (Objects.equals(teachingPlanDetailPub.getCourseId(), lessonPubVO.getCourseId())
                    &&
                    teachingPlanDetailPub.getLessonOrder().equals(lessonPubVO.getLessonOrder())) {
                    recordVideoTask.setCoursewareId(lessonPubVO.getCoursewareId().longValue());
                    recordVideoTask.setCoursewareVersion(lessonPubVO.getCoursewareVersion());
                }
            }
        }
    }

    /**
     * 生成录课任务(新版)
     *
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveRecordVideoTask() {
        AtomicInteger result = new AtomicInteger();

        //删除未完成的录课任务
        recordVideoTaskMapper.update(Wrappers.lambdaUpdate(RecordVideoTask.class)
            .eq(RecordVideoTask::getTaskStatus, RecordVideoTaskConstant.NOT_FINISHED_TASK)
            .set(RecordVideoTask::getDelFlag, 1));

        //获取所有教学计划详情
        List<TeachingPlanDetailPubVO> planDetailPubList = getTeachingPlanDetailPubList();

        if (CollectionUtils.isNotEmpty(planDetailPubList)) {

            //获取课节列表
            List<LessonPubVO> lessonVoList = getLessonList(planDetailPubList);

            // 先将全部数据进行去重操作
            // 通过讲师Id,课程Id,课节Id,最早课程开始时间去掉重复的教学计划详情
            duplicateRemovalDetail(planDetailPubList);

            //对去重后的数据进行分片处理,每次处理25条
            List<List<TeachingPlanDetailPubVO>> splitList = ListUtil.partition(planDetailPubList,
                RecordVideoTaskConstant.PAGE_SIZE);
            for (List<TeachingPlanDetailPubVO> subList : splitList) {
                //批量添加录课任务
                int insertCount = batchInsertVideoTask(subList, getCourseList(subList),
                    lessonVoList);
                result.addAndGet(insertCount);
            }
            log.info("批量插入录课任务数量:{}", result.get());
            return result.get();
        } else {
            log.info("教学详情信息为空,无法生成录课任务!");
        }
        return 0;
    }

    /**
     * 获取教学计划详情列表
     *
     * @return 结果
     */
    private List<TeachingPlanDetailPubVO> getTeachingPlanDetailPubList() {
        List<TeachingPlanDetailPubVO> planDetailPubVoList = new ArrayList<>();
        //查询教学计划详情
        // 2025年5月9日10:25:11经大家一致决定去除这个教学计划关闭不生成录课任务的逻辑
        //.eq(TeachingPlanPub::getClosed, RecordVideoTaskConstant.TEACHING_PLAN_OPEN_STATUS)
        MPJLambdaWrapper<TeachingPlanPub> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(TeachingPlanPub::getTeachingPlanId, TeachingPlanPub::getCourseId)
            .selectAll(TeachingPlanDetailPub.class).selectAll(TeachingPlanPub.class)
            .leftJoin(TeachingPlanDetailPub.class, TeachingPlanDetailPub::getPlanId,
                TeachingPlanPub::getTeachingPlanId)
            .orderByAsc(TeachingPlanDetailPub::getClassStartDateTime);
        List<TeachingPlanDetailPub> planDetailPubList = planPubMapper.selectJoinList(
            TeachingPlanDetailPub.class, wrapper);
        if (!planDetailPubList.isEmpty()) {
            for (TeachingPlanDetailPub detailPub : planDetailPubList) {
                TeachingPlanDetailPubVO vo = new TeachingPlanDetailPubVO();
                BeanUtil.copyProperties(detailPub, vo);
                planDetailPubVoList.add(vo);
            }
        }
        log.info("去重前的教学计划详情数量:{},", JSON.toJSONString(planDetailPubVoList.size()));
        return planDetailPubVoList;
    }

    /**
     * 批量插入录课任务
     *
     * @param planDetailList    教学计划详情
     * @param courseVersionList 课程列表
     * @return 结果
     */
    private int batchInsertVideoTask(
        List<TeachingPlanDetailPubVO> planDetailList,
        List<CourseVO> courseVersionList,
        List<LessonPubVO> lessonPubVoList) {
        // 任务列表
        List<RecordVideoTask> taskList = new ArrayList<>();

        for (TeachingPlanDetailPubVO planDetailPub : planDetailList) {
            RecordVideoTask recordVideoTask = new RecordVideoTask();

            // 设置录课任务部分字段信息
            setTaskPartInfo(planDetailPub, recordVideoTask);

            // 设置课程版本信息
            setCourseVersionInfo(courseVersionList, planDetailPub, recordVideoTask);

            // 设置课节版本信息
            setLessonVersionInfo(lessonPubVoList, planDetailPub, recordVideoTask);

            // 查询已有的录课任务
            List<RecordVideoTask> oldTask = getRecordVideoTask(planDetailPub, recordVideoTask);
            if (CollectionUtils.isEmpty(oldTask)) {
                // 解决只发布了新课节，历史课节信息未改变情况下：
                // 1.已生产录课任务的课节，且完成录制的任务，无需再生产新的任务
                // 2.已生产录课任务课节，录课任务未完成，不生产新的任务，原任务保留
                // 判断教学计划的所关联的课程对应的课节有没有发生过改变
                Boolean isNewLesson = handleIsNewLesson(planDetailPub, lessonPubVoList);
                if (isNewLesson) {
                    taskList.add(recordVideoTask);
                    log.info(
                        "检测到新课节,已添加录课任务,跳过当前循环,教学计划ID={},课程名称={}, 主讲老师={}, 第几节课={}",
                        planDetailPub.getPlanId(), planDetailPub.getCourseName(),
                        planDetailPub.getLectureName(), planDetailPub.getLessonOrder());
                    continue;
                }

                // 不关联课件版本查询上一次录制完成的时间到截止当前时间之内课件的发布版本历史中是否存在不生成录课任务的课件发布版本
                RecordVideoTask oldTaskCourseWare =
                    getRecordVideoTaskCourseWareLatest(planDetailPub, recordVideoTask);
                // 根据查询出来的查看最后一次录课与当前时间段内课件的发布记录
                Boolean genRecordTask = getCourseWarePublishHisResult(oldTaskCourseWare);
                if (Objects.isNull(oldTaskCourseWare) || genRecordTask) {
                    // 添加未完成的录课任务
                    taskList.add(recordVideoTask);
                } else {
                    log.info(
                        "检测到课件发布历史记录,无需生成新的录课任务,教学计划ID={},课程名称={}, 主讲老师={}, 第几节课={}",
                        planDetailPub.getPlanId(), planDetailPub.getCourseName(),
                        planDetailPub.getLectureName(), planDetailPub.getLessonOrder());
                }
            }
        }

        // 批量插入录课任务
        List<BatchResult> inserted = recordVideoTaskMapper.insert(taskList);
        return inserted.size();
    }

    /**
     * 判断教学计划的所关联的课程对应的课节有没有发生过改变
     *
     * @param planDetailPub
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2025/3/14 13:56
     */
    private Boolean handleIsNewLesson(
        TeachingPlanDetailPubVO planDetailPub, List<LessonPubVO> lessonPubVoList) {
        // 判断课程以及lessonOrder是否已经完成录课任务，若没有则返回和true在创建一个
        RecordVideoTask oldTaskCourseLesson = getRecordVideoTaskCourseLatest(planDetailPub);
        if (Objects.isNull(oldTaskCourseLesson)) {
            return true;
        }
        // 若有则还需要判断课程以及lessonOrder对应的最后生成的日期与当前日期之间所关联的课件是否与当前课件是否一致，若不一致代表课节对应的课件发生改变，需要重新录课
        List<LessonPubVO> pubVOS =
            lessonPubVoList.stream()
                .filter(
                    e ->
                        Objects.equals(e.getCourseId(), planDetailPub.getCourseId())
                            && Objects.equals(e.getLessonOrder(), planDetailPub.getLessonOrder()))
                .toList();
        if (CollectionUtils.isEmpty(pubVOS)) {
            return false;
        }
        LessonPubVO lessonPubVO = pubVOS.get(0);
        boolean result =
            Objects.nonNull(lessonPubVO)
                && !Objects.equals(
                lessonPubVO.getCoursewareId().longValue(), oldTaskCourseLesson.getCoursewareId());
        // 将已经录完的课程的版本更新至最新版
        if (!result) {
            recordVideoTaskMapper.update(
                Wrappers.lambdaUpdate(RecordVideoTask.class)
                    .eq(RecordVideoTask::getCourseId, planDetailPub.getCourseId())
                    .eq(RecordVideoTask::getLessonOrder, planDetailPub.getLessonOrder())
                    .eq(RecordVideoTask::getLectureId, planDetailPub.getLectureId())
                    .eq(RecordVideoTask::getEarliestStartDate,
                        planDetailPub.getClassStartDateTime())
                    .in(
                        RecordVideoTask::getTaskStatus,
                        RecordVideoTaskConstant.FINISHED_TASK,
                        RecordVideoTaskConstant.RECORDING)
                    .set(RecordVideoTask::getCourseVersion, planDetailPub.getLessonVersion()));
        }
        return result;
    }

    /**
     * 只根据课程id课节顺序号查询已经录课任务
     *
     * @param planDetailPub
     * @return com.yuedu.ydsf.eduConnect.entity.RecordVideoTask
     * <AUTHOR>
     * @date 2025/3/14 14:36
     */
    private RecordVideoTask getRecordVideoTaskCourseLatest(TeachingPlanDetailPubVO planDetailPub) {

        List<RecordVideoTask> taskList =
            recordVideoTaskMapper.selectList(
                Wrappers.lambdaQuery(RecordVideoTask.class)
                    .eq(RecordVideoTask::getCourseId, planDetailPub.getCourseId())
                    .eq(RecordVideoTask::getLessonOrder, planDetailPub.getLessonOrder())
                    .eq(RecordVideoTask::getLectureId, planDetailPub.getLectureId())
                    .in(
                        RecordVideoTask::getTaskStatus,
                        RecordVideoTaskConstant.FINISHED_TASK,
                        RecordVideoTaskConstant.RECORDING)
                    .orderByDesc(RecordVideoTask::getCreateTime)
                    .last("limit 1") // 只取第一条记录
            );

        RecordVideoTask latestTask = CollectionUtils.isEmpty(taskList) ? null : taskList.get(0);
        log.info("查询最近完成的录课任务结果: {}", latestTask != null ? "成功" : "无记录");
        return latestTask;
    }

    /**
     * 查看最后一次录课与当前时间段内课件的发布记录
     *
     * @param oldTaskCourseWare
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2025/3/13 14:41
     */
    private Boolean getCourseWarePublishHisResult(RecordVideoTask oldTaskCourseWare) {
        log.info(
            "开始检查课件发布历史记录, 课件ID={}, 最近录制任务时间={}",
            oldTaskCourseWare != null ? oldTaskCourseWare.getCoursewareId() : "null",
            oldTaskCourseWare != null ? oldTaskCourseWare.getCreateTime() : "null");

        if (Objects.isNull(oldTaskCourseWare)) {
            log.info("无历史录制任务记录,需要生成新的录课任务");
            return true;
        }

        try {
            CoursewareVersionDTO coursewareVersionDTO = new CoursewareVersionDTO();
            coursewareVersionDTO.setCoursewareId(oldTaskCourseWare.getCoursewareId());
            coursewareVersionDTO.setLatestRecordTaskTime(oldTaskCourseWare.getCreateTime());

            // 获取时间段内的课件版本修改历史
            R<List<CoursewareVersionVO>> listCoursewareVersionHis =
                coursewareVersionService.listCoursewareVersionHis(coursewareVersionDTO);

            if (listCoursewareVersionHis == null || listCoursewareVersionHis.getCode() != 0) {
                log.error(
                    "获取课件版本历史记录失败, 错误信息: {}",
                    listCoursewareVersionHis != null
                        ? listCoursewareVersionHis.getMsg()
                        : "response is null");
                throw new BizException("获取课件版本历史记录失败");
            }

            List<CoursewareVersionVO> versionList = listCoursewareVersionHis.getData();
            if (CollectionUtils.isEmpty(versionList)) {
                log.info("课件ID={}在最近录制任务后无版本更新记录,无需生成新的录课任务",
                    oldTaskCourseWare.getCoursewareId());
                return false;
            }

            // 检查是否存在需要生成录课任务的版本(genRecordTask=1)
            boolean hasRecordTask =
                versionList.stream()
                    .anyMatch(
                        version ->
                            Objects.equals(
                                version.getGenRecordTask(),
                                Integer.parseInt(YesNoEnum.YES.getCode())));

            log.info(
                "课件ID={} 版本历史检查结果: {}",
                oldTaskCourseWare.getCoursewareId(),
                hasRecordTask ? "需要生成新的录课任务" : "无需生成新的录课任务");

            return hasRecordTask;

        } catch (Exception e) {
            log.error("检查课件发布历史记录异常", e);
            throw new BizException("检查课件发布历史记录失败");
        }
    }

    /**
     * 不关联课件版本查询上一次录制完成的时间到截止当前时间之内课件的发布版本历史中是否存在不生成录课任务的课件发布版本
     *
     * @param planDetailPub
     * @param recordVideoTask
     * @return com.yuedu.ydsf.eduConnect.entity.RecordVideoTask
     * <AUTHOR>
     * @date 2025/3/13 14:26
     */
    private RecordVideoTask getRecordVideoTaskCourseWareLatest(
        TeachingPlanDetailPubVO planDetailPub, RecordVideoTask recordVideoTask) {
        log.debug(
            "开始查询最近完成的录课任务, 课件ID={}, 课程ID={}, 课程版本={}, 讲师ID={}, 最早开始时间={}",
            planDetailPub.getCoursewareId(),
            planDetailPub.getCourseId(),
            recordVideoTask.getCourseVersion(),
            planDetailPub.getLectureId(),
            planDetailPub.getClassStartDateTime());

        List<RecordVideoTask> taskList =
            recordVideoTaskMapper.selectList(
                Wrappers.lambdaQuery(RecordVideoTask.class)
                    .eq(RecordVideoTask::getCoursewareId, planDetailPub.getCoursewareId())
                    .eq(RecordVideoTask::getCourseId, planDetailPub.getCourseId())
                    .eq(RecordVideoTask::getLectureId, planDetailPub.getLectureId())
                    .in(
                        RecordVideoTask::getTaskStatus,
                        RecordVideoTaskConstant.FINISHED_TASK,
                        RecordVideoTaskConstant.RECORDING)
                    .orderByDesc(RecordVideoTask::getCreateTime)
                    .last("limit 1") // 只取第一条记录
            );

        return CollectionUtils.isEmpty(taskList) ? null : taskList.get(0);
    }

    /**
     * 查询已有的录课任务
     *
     * @param planDetailPub   教学计划详情
     * @param recordVideoTask 录课任务
     * @return 结果
     */
    private List<RecordVideoTask> getRecordVideoTask(TeachingPlanDetailPubVO planDetailPub,
        RecordVideoTask recordVideoTask) {
        return recordVideoTaskMapper.selectList(Wrappers.lambdaQuery(RecordVideoTask.class)
            .eq(RecordVideoTask::getCoursewareId, planDetailPub.getCoursewareId())
            .eq(RecordVideoTask::getCoursewareVersion, planDetailPub.getCoursewareVersion())
            .eq(RecordVideoTask::getCourseId, planDetailPub.getCourseId())
            .eq(RecordVideoTask::getCourseVersion, recordVideoTask.getCourseVersion())
            .eq(RecordVideoTask::getLessonOrder, recordVideoTask.getLessonOrder())
            .eq(RecordVideoTask::getLectureId, planDetailPub.getLectureId())
            .in(RecordVideoTask::getTaskStatus, RecordVideoTaskConstant.FINISHED_TASK,
                RecordVideoTaskConstant.RECORDING)
        );
    }

    /**
     * 设置已完成的录课任务
     *
     * @param planDetailPub 教学计划详情
     * @param taskList      任务列表
     */
    private void setCompletedTask(List<RecordVideoTask> completedList,
        TeachingPlanDetailPubVO planDetailPub,
        List<RecordVideoTask> taskList) {
        if (!completedList.isEmpty()) {
            //设置已完成的录课任务
            completedList.forEach(completedTask -> {
                if ((completedTask.getCoursewareVersion() < planDetailPub.getCoursewareVersion() ||
                    !completedTask.getCourseVersion().equals(planDetailPub.getCourseVersion())) &&
                    completedTask.getCoursewareId()
                        .compareTo(planDetailPub.getCoursewareId().longValue()) == 0 &&
                    completedTask.getLectureId().compareTo(planDetailPub.getLectureId()) == 0
                ) {
                    //存在已完成的录课任务,且课件版本升级后重新生成录课任务
                    RecordVideoTask completedTaskNew = new RecordVideoTask();
                    BeanUtil.copyProperties(completedTask, completedTaskNew);
                    completedTaskNew.setTaskStatus(RecordVideoTaskConstant.NOT_FINISHED_TASK);
                    completedTaskNew.setId(null);
                    completedTaskNew.setCourseVersion(planDetailPub.getCourseVersion());
                    completedTaskNew.setCoursewareId(planDetailPub.getCoursewareId().longValue());
                    completedTaskNew.setCoursewareVersion(planDetailPub.getCoursewareVersion());
                    taskList.add(completedTaskNew);
                }
            });
        }
    }


    /**
     * 获取课节列表
     *
     * @param planDetailPubList 教学计划详情列表
     */
    private List<LessonPubVO> getLessonList(List<TeachingPlanDetailPubVO> planDetailPubList) {
        //课程Id列表
        List<Long> courseIdList = planDetailPubList.stream()
            .map(TeachingPlanDetailPubVO::getCourseId).toList();

        //课节顺序列表
        List<Integer> lessonOrderList = planDetailPubList.stream()
            .map(TeachingPlanDetailPubVO::getLessonOrder).toList();

        //获取课节信息
        LessonPubQuery lessonPubQuery = new LessonPubQuery();
        lessonPubQuery.setCourseIdList(courseIdList);
        lessonPubQuery.setLessonOrderList(lessonOrderList);
        log.debug("查询课节信息的入参:{},", JSON.toJSONString(lessonPubQuery));
        List<LessonPubVO> lessonPubVoList = getLessonPubVo(lessonPubQuery);
        log.debug("查询课节列表的结果:{}", JSON.toJSONString(lessonPubVoList));

        //获取到课节信息之后将课节版本,课件版本,课件Id进行赋值
        if (!lessonPubVoList.isEmpty()) {
            planDetailPubList.forEach(planDetailPub -> {
                for (LessonPubVO lessonPubVO : lessonPubVoList) {
                    if (planDetailPub.getCourseId().compareTo(lessonPubVO.getCourseId()) == 0 &&
                        planDetailPub.getLessonOrder().compareTo(lessonPubVO.getLessonOrder())
                            == 0) {
                        //课节版本信息
                        planDetailPub.setLessonVersion(lessonPubVO.getVersion());
                        //课件Id
                        planDetailPub.setCoursewareId(lessonPubVO.getCoursewareId());
                        //课件版本信息
                        planDetailPub.setCoursewareVersion(lessonPubVO.getCoursewareVersion());
                    }
                }
            });
        }
        log.info("组装完成的教学计划详情列表数量:{}", JSON.toJSONString(planDetailPubList.size()));
        return lessonPubVoList;
    }

    /**
     * 设置课节信息
     *
     * @param lessonPubQuery 课节Id列表
     */
    public List<LessonPubVO> getLessonPubVo(LessonPubQuery lessonPubQuery) {
        //获取课节信息
        R<List<LessonPubVO>> lessonNameListR = lessonPubService.getLessonByIds(lessonPubQuery);
        log.debug("课节信息:{}", JSON.toJSONString(lessonNameListR));
        List<LessonPubVO> lessonNameList;
        if (lessonNameListR != null && lessonNameListR.getCode() == 0) {
            lessonNameList = lessonNameListR.getData();
        } else {
            log.error("请求课节信息失败");
            throw new BizException("请求课节信息失败!");
        }
        return lessonNameList;
    }


    /**
     * 获取课程列表
     *
     * @param planDetailPubList 教学计划详情列表
     */
    private List<CourseVO> getCourseList(List<TeachingPlanDetailPubVO> planDetailPubList) {
        //课程Id列表
        List<Integer> courseIdList = planDetailPubList.stream()
            .map(teachingPlanDetailPubVO ->
                teachingPlanDetailPubVO.getCourseId().intValue())
            .distinct() // 去重
            .toList();

        //通过课程Id列表获取课程信息
        CourseDTO courseDTO = new CourseDTO();
        courseDTO.setCourseIdList(courseIdList);
        log.debug("请求课程信息的入参:{}", JSON.toJSONString(courseDTO));
        R<List<CourseVO>> courseResult = courseService.getCourseListByIds(courseDTO);
        log.debug("请求课程信息的结果:{}", JSON.toJSONString(courseResult));
        if (courseResult != null && courseResult.getCode() == 0) {
            return courseResult.getData();
        } else {
            log.error("请求课程信息失败");
            throw new BizException("请求课程信息失败!");
        }
    }

    /**
     * 录课任务管理-获取录课任务列表
     *
     * @param page                 分页参数
     * @param recordVideoTaskQuery 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<RecordVideoTaskVO>
     * <AUTHOR>
     * @date 2025/4/22 10:04
     */
    @Override
    public IPage<RecordVideoTaskVO> getRecordVideoTaskListPage(
        Page page, RecordVideoTaskQuery recordVideoTaskQuery) {
        // 处理任务状态查询条件
        if (Objects.nonNull(recordVideoTaskQuery.getTaskStatus())) {
            // 如果查询的是未完成状态，将已取消和录制中的状态也包含进来
            if (TaskStatusEnum.TASK_STATUS_ENUM_0.CODE.equals(
                recordVideoTaskQuery.getTaskStatus())) {
                recordVideoTaskQuery.setTaskStatusList(
                    Arrays.asList(
                        TaskStatusEnum.TASK_STATUS_ENUM_0.CODE, // 未完成
                        TaskStatusEnum.TASK_STATUS_ENUM_3.CODE, // 已取消
                        TaskStatusEnum.TASK_STATUS_ENUM_4.CODE // 录制中
                    ));
                // 清空单个状态查询条件
                recordVideoTaskQuery.setTaskStatus(null);
            }
        }
        // 如果需要按阶段筛选，先查询该阶段下的所有课程ID
        List<Integer> stageCoursesIds = null;
        if (Objects.nonNull(recordVideoTaskQuery.getStageId())) {
            CourseDTO courseDTO = new CourseDTO();
            courseDTO.setStageId(recordVideoTaskQuery.getStageId());
            R<List<CourseVO>> courseResult = courseService.getCourseListByStageId(courseDTO);
            if (courseResult.isOk() && CollectionUtils.isNotEmpty(courseResult.getData())) {
                stageCoursesIds = courseResult.getData().stream()
                    .map(CourseVO::getId)
                    .collect(Collectors.toList());
            }
            // 如果该阶段下没有课程，直接返回空结果
            if (CollectionUtils.isEmpty(stageCoursesIds)) {
                return new Page<RecordVideoTaskVO>().setRecords(Collections.emptyList());
            }
        }

        LambdaQueryWrapper<RecordVideoTask> wrapper =
            Wrappers.<RecordVideoTask>lambdaQuery()
                .eq(
                    Objects.nonNull(recordVideoTaskQuery.getId()),
                    RecordVideoTask::getId,
                    recordVideoTaskQuery.getId())
                .eq(
                    StringUtils.isNotBlank(recordVideoTaskQuery.getLectureName()),
                    RecordVideoTask::getLectureName,
                    recordVideoTaskQuery.getLectureName())
                .eq(
                    Objects.nonNull(recordVideoTaskQuery.getTaskStatus()),
                    RecordVideoTask::getTaskStatus,
                    recordVideoTaskQuery.getTaskStatus())
                .in(
                    CollectionUtil.isNotEmpty(recordVideoTaskQuery.getTaskStatusList()),
                    RecordVideoTask::getTaskStatus,
                    recordVideoTaskQuery.getTaskStatusList())
                .in(CollectionUtil.isNotEmpty(stageCoursesIds),  // 添加阶段对应的课程ID筛选
                    RecordVideoTask::getCourseId, stageCoursesIds)
                .eq(
                    Objects.nonNull(recordVideoTaskQuery.getCourseId()),
                    RecordVideoTask::getCourseId,
                    recordVideoTaskQuery.getCourseId())
                .eq(
                    Objects.nonNull(recordVideoTaskQuery.getLectureId()),
                    RecordVideoTask::getLectureId,
                    recordVideoTaskQuery.getLectureId())
                .ge(
                    Objects.nonNull(recordVideoTaskQuery.getStartTime()),
                    RecordVideoTask::getEarliestStartDate,
                    recordVideoTaskQuery.getStartTime())
                .le(
                    Objects.nonNull(recordVideoTaskQuery.getEndTime()),
                    RecordVideoTask::getEarliestStartDate,
                    recordVideoTaskQuery.getEndTime())
                .orderByAsc(RecordVideoTask::getEarliestStartDate); // 修改为按开始时间正序排列

        // 执行分页查询
        Page resultPage = recordVideoTaskMapper.selectPage(page, wrapper);

        // 如果查询结果为空，直接返回空的VO分页对象
        if (resultPage.getRecords().isEmpty()) {
            return new Page<RecordVideoTaskVO>().setRecords(Collections.emptyList());
        }

        List<RecordVideoTask> records = resultPage.getRecords();

        // 1. 收集所有需要的课程ID和课节顺序
        List<Long> courseIds =
            records.stream().map(RecordVideoTask::getCourseId).distinct()
                .collect(Collectors.toList());
        List<Integer> lessonOrders =
            records.stream()
                .map(RecordVideoTask::getLessonOrder)
                .distinct()
                .collect(Collectors.toList());

        // 2. 批量查询课节信息
        LessonPubQuery lessonPubQuery = new LessonPubQuery();
        lessonPubQuery.setCourseIdList(courseIds);
        lessonPubQuery.setLessonOrderList(lessonOrders);
        List<LessonPubVO> lessonList = getLessonPubVo(lessonPubQuery);

        // 构建课节信息Map (courseId_lessonOrder -> LessonPubVO)
        Map<String, LessonPubVO> lessonMap =
            lessonList.stream()
                .collect(
                    Collectors.toMap(
                        lesson -> lesson.getCourseId() + "_" + lesson.getLessonOrder(),
                        Function.identity()));

        // 3. 查询教学计划获取直播间计划信息
        List<Long> teachingPlanIds =
            records.stream()
                .map(RecordVideoTask::getTeachingPlanId)
                .distinct()
                .collect(Collectors.toList());

        List<TeachingPlanDraft> teachingPlans =
            teachingPlanDraftMapper.selectList(
                Wrappers.lambdaQuery(TeachingPlanDraft.class)
                    .in(TeachingPlanDraft::getId, teachingPlanIds));

        // 构建教学计划到直播间计划的映射
        Map<Long, Long> planToLiveRoomMap =
            teachingPlans.stream()
                .collect(
                    Collectors.toMap(TeachingPlanDraft::getId,
                        TeachingPlanDraft::getLiveRoomPlanId));

        // 4. 查询直播间计划信息
        List<Long> liveRoomPlanIds = new ArrayList<>(planToLiveRoomMap.values());
        List<LiveRoomPlanDraft> liveRoomPlans =
            liveRoomPlanDraftMapper.selectList(
                Wrappers.lambdaQuery(LiveRoomPlanDraft.class)
                    .in(LiveRoomPlanDraft::getId, liveRoomPlanIds));

        Map<Long, LiveRoomPlanDraft> liveRoomPlanMap =
            liveRoomPlans.stream()
                .collect(Collectors.toMap(LiveRoomPlanDraft::getId, Function.identity()));

        // 1. 收集所有需要的课程ID
        List<Integer> courseIdList =
            records.stream()
                .map(RecordVideoTask::getCourseId)
                .map(Long::intValue)
                .distinct()
                .collect(Collectors.toList());

        // 2. 批量查询课程信息
        CourseDTO courseDTO = new CourseDTO();
        courseDTO.setCourseIdList(courseIdList);
        R<List<CourseVO>> courseVoList = courseService.getCourseListByIds(courseDTO);

        // 构建课程信息Map
        Map<Integer, CourseVO> courseMap = new HashMap<>();
        if (courseVoList.isOk() && CollectionUtils.isNotEmpty(courseVoList.getData())) {
            courseMap =
                courseVoList.getData().stream()
                    .collect(Collectors.toMap(CourseVO::getId, Function.identity()));
        }

        // 转换为VO对象并填充信息
        // 对同一时间的任务按阶段ID排序
        // 首先按时间正序排序
        // 时间相同时，按阶段ID排序
        Map<Integer, CourseVO> finalCourseMap = courseMap;
        List<RecordVideoTaskVO> voList =
            records.stream()
                .map(
                    task -> {
                        RecordVideoTaskVO vo = new RecordVideoTaskVO();
                        BeanUtils.copyProperties(task, vo);

                        // 处理任务状态：将已取消和录制中的状态都转换为未完成状态
                        if (Objects.equals(task.getTaskStatus(),
                            TaskStatusEnum.TASK_STATUS_ENUM_3.CODE)
                            || Objects.equals(
                            task.getTaskStatus(), TaskStatusEnum.TASK_STATUS_ENUM_4.CODE)) {
                            vo.setTaskStatus(TaskStatusEnum.TASK_STATUS_ENUM_0.CODE);
                        }

                        // 填充课节信息
                        String lessonKey = task.getCourseId() + "_" + task.getLessonOrder();
                        LessonPubVO lessonPubVO = lessonMap.get(lessonKey);
                        if (lessonPubVO != null) {
                            vo.setLessonName(lessonPubVO.getLessonName());
                            vo.setBookName(lessonPubVO.getBookName());
                        }

                        // 从课程信息中获取阶段ID
                        CourseVO courseVO = finalCourseMap.get(task.getCourseId().intValue());
                        if (courseVO != null) {
                            vo.setStageId(courseVO.getStageId());
                        }

                        // 填充直播间信息
                        Long liveRoomPlanId = planToLiveRoomMap.get(task.getTeachingPlanId());
                        if (liveRoomPlanId != null) {
                            LiveRoomPlanDraft liveRoomPlan = liveRoomPlanMap.get(liveRoomPlanId);
                            if (liveRoomPlan != null) {
                                vo.setLivePlanName(liveRoomPlan.getPlanName());
                            }
                        }

                        // 格式化日期时间段
                        if (task.getEarliestStartDate() != null) {
                            getEarlierStartDate(task.getEarliestStartDate(), vo);
                        }

                        return vo;
                    })
                .sorted(
                    Comparator.comparing(RecordVideoTaskVO::getEarliestStartDate)
                        .thenComparingInt(
                            v -> Optional.ofNullable(v.getStageId()).orElse(Integer.MAX_VALUE)))
                .collect(Collectors.toList());

        // 构建返回的分页对象
        Page<RecordVideoTaskVO> voPage = new Page<>();
        voPage.setRecords(voList);
        voPage.setTotal(resultPage.getTotal());
        voPage.setCurrent(resultPage.getCurrent());
        voPage.setSize(resultPage.getSize());

        return voPage;
    }

    private static void getEarlierStartDate(
        LocalDateTime earliestStartDate, RecordVideoTaskVO recordVideoTaskVO) {
        String date = DateUtil.format(earliestStartDate, RecordVideoTaskConstant.YYYY_MM_DD);
        LocalTime time = earliestStartDate.toLocalTime();
        String week =
            switch (earliestStartDate.getDayOfWeek().getValue()) {
                case RecordVideoTaskConstant.ONE -> RecordVideoTaskConstant.MONDAY;
                case RecordVideoTaskConstant.TWO -> RecordVideoTaskConstant.TUESDAY;
                case RecordVideoTaskConstant.THREE -> RecordVideoTaskConstant.WEDNESDAY;
                case RecordVideoTaskConstant.FOUR -> RecordVideoTaskConstant.THURSDAY;
                case RecordVideoTaskConstant.FIVE -> RecordVideoTaskConstant.FRIDAY;
                case RecordVideoTaskConstant.SIX -> RecordVideoTaskConstant.SATURDAY;
                case RecordVideoTaskConstant.SEVEN -> RecordVideoTaskConstant.SUNDAY;
                default -> "";
            };
        String amOrPm = null;
        if (DateUtil.isAM(
            Date.from(earliestStartDate.atZone(ZoneId.systemDefault()).toInstant()))) {
            amOrPm = RecordVideoTaskConstant.MORNING;
        } else if (DateUtil.isPM(
            Date.from(earliestStartDate.atZone(ZoneId.systemDefault()).toInstant()))) {
            amOrPm = RecordVideoTaskConstant.AFTERNOON;
        }
        recordVideoTaskVO.setDateName(date + week + amOrPm + time);
    }

    /**
     * 录课任务管理-匹配已有录课资源
     *
     * @param recordVideoTaskDTO
     * @return void
     * <AUTHOR>
     * @date 2025/4/22 14:58
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void matchResources(RecordVideoTaskDTO recordVideoTaskDTO) {
        log.info(
            "开始匹配录课资源, taskId={}, resourceId={}",
            recordVideoTaskDTO.getId(),
            recordVideoTaskDTO.getResourcesId());

        try {
            long timeMillis = System.currentTimeMillis();
            String vodVideoId = RecordVideoTaskConstant.MATCH_PREFIX + timeMillis;
            // 1. 更新录课任务状态为已完成
            boolean updateResult =
                recordVideoTaskMapper.update(
                    null,
                    Wrappers.<RecordVideoTask>lambdaUpdate()
                        .eq(RecordVideoTask::getId, recordVideoTaskDTO.getId())
                        .set(RecordVideoTask::getTaskStatus,
                            TaskStatusEnum.TASK_STATUS_ENUM_1.CODE))
                    > 0;

            if (!updateResult) {
                log.error("更新录课任务状态失败, taskId={}", recordVideoTaskDTO.getId());
                throw new BizException("更新录课任务状态失败");
            }

            // 2. 查询现有录制记录
            Recording existingRecording =
                eaRecordingMapper.selectOne(
                    Wrappers.<Recording>lambdaQuery()
                        .eq(Recording::getRecordVideoTaskId, recordVideoTaskDTO.getId())
                        .orderByDesc(Recording::getCreateTime)
                        .last("LIMIT 1"));

            // 3. 获取源录制记录信息
            SsRecording sourceRecording =
                ssRecordingMapper.selectOne(
                    Wrappers.<SsRecording>lambdaQuery()
                        .eq(SsRecording::getId, recordVideoTaskDTO.getResourcesId()));

            if (sourceRecording == null) {
                log.error("源录制记录不存在, resourceId={}", recordVideoTaskDTO.getResourcesId());
                throw new BizException("源录制记录不存在");
            }

            Recording newRecording = null;
            if (existingRecording != null) {
                // 4a. 更新现有录制记录
                log.info("更新现有录制记录, recordingId={}", existingRecording.getId());
                boolean recordingUpdateResult =
                    eaRecordingMapper.update(
                        null,
                        Wrappers.<Recording>lambdaUpdate()
                            .eq(Recording::getId, existingRecording.getId())
                            .set(Recording::getAuditStatus, AuditStatusEnum.AUDIT_STATUS_2.code)
                            .set(
                                Recording::getAgoraCloudRecordId,
                                sourceRecording.getAgoraCloudRecordId())
                            .set(Recording::getAgoraRecordId, sourceRecording.getAgoraRecordId())
                            .set(
                                Recording::getRecordingStatus,
                                RecordingStatusEnum.RECORDING_STATUS_2.code)
                            .set(
                                Recording::getRecordingResources,
                                sourceRecording.getRecordingResources())
                            .set(Recording::getVodVideoId, vodVideoId))
                        > 0;

                if (!recordingUpdateResult) {
                    log.error("更新录制记录失败, recordingId={}", existingRecording.getId());
                    throw new BizException("更新录制记录失败");
                }
                newRecording = existingRecording;
            } else {
                // 4b. 创建新的录制记录
                log.info("创建新的录制记录, taskId={}", recordVideoTaskDTO.getId());
                newRecording = new Recording();
                BeanUtils.copyProperties(sourceRecording, newRecording, "id");
                newRecording.setRecordVideoTaskId(recordVideoTaskDTO.getId());
                newRecording.setVodVideoId(vodVideoId);
                newRecording.setAuditStatus(AuditStatusEnum.AUDIT_STATUS_2.code);
                newRecording.setRecordingStatus(RecordingStatusEnum.RECORDING_STATUS_2.code);
                if (Objects.isNull(newRecording.getDeviceId())) {
                    newRecording.setDeviceId(DEFAULT_DEVICE_ID);
                }

                if (eaRecordingMapper.insert(newRecording) <= 0) {
                    log.error("插入新录制记录失败");
                    throw new BizException("插入新录制记录失败");
                }
            }

            // 5. 获取录课任务信息
            RecordVideoTask recordVideoTask =
                recordVideoTaskMapper.selectById(recordVideoTaskDTO.getId());
            if (recordVideoTask == null) {
                throw new BizException("录课任务不存在");
            }

            // 6. 查询课程VOD信息
            CourseVod existingCourseVod =
                courseVodMapper.selectOne(
                    Wrappers.<CourseVod>lambdaQuery()
                        .eq(CourseVod::getCourseId, recordVideoTask.getCourseId())
                        .eq(CourseVod::getLessonOrder, recordVideoTask.getLessonOrder())
                        .eq(CourseVod::getLectureId, recordVideoTask.getLectureId())
                        .orderByDesc(CourseVod::getCreateTime)
                        .last("LIMIT 1"));

            // 7. 处理课程VOD信息
            CourseVod courseVod;
            if (existingCourseVod != null) {
                // 更新现有课程VOD
                boolean courseVodUpdateResult =
                    courseVodMapper.update(
                        null,
                        Wrappers.<CourseVod>lambdaUpdate()
                            .eq(CourseVod::getId, existingCourseVod.getId())
                            .set(CourseVod::getDisable, CourseVodDisableEnum.DISABLE_ENUM_0.code))
                        > 0;

                if (!courseVodUpdateResult) {
                    throw new BizException("更新课程VOD状态失败");
                }
                courseVod = existingCourseVod;
            } else {
                // 创建新的课程VOD
                CourseDTO courseDTO = new CourseDTO();
                courseDTO.setCourseIdList(
                    Collections.singletonList(recordVideoTask.getCourseId().intValue()));
                R<List<CourseVO>> courseVoList = courseService.getCourseListByIds(courseDTO);

                if (!courseVoList.isOk() || CollUtil.isEmpty(courseVoList.getData())) {
                    throw new BizException("课程不存在");
                }

                CourseVO course = courseVoList.getData().get(0);
                courseVod = new CourseVod();
                courseVod.setCourseId(recordVideoTask.getCourseId());
                courseVod.setLessonOrder(recordVideoTask.getLessonOrder());
                courseVod.setLectureId(recordVideoTask.getLectureId());
                courseVod.setStage(course.getStageId());
                courseVod.setDisable(CourseVodDisableEnum.DISABLE_ENUM_0.code);

                if (courseVodMapper.insert(courseVod) <= 0) {
                    throw new BizException("插入课程VOD失败");
                }
            }

            // 8. 处理课程VOD视频信息
            CourseVodVideo existingVodVideo =
                courseVodVideoMapper.selectOne(
                    Wrappers.<CourseVodVideo>lambdaQuery()
                        .eq(CourseVodVideo::getCourseVodId, courseVod.getId()));

            if (existingVodVideo != null) {
                // 更新现有VOD视频信息
                boolean vodVideoUpdateResult =
                    courseVodVideoMapper.update(
                        null,
                        Wrappers.<CourseVodVideo>lambdaUpdate()
                            .eq(CourseVodVideo::getId, existingVodVideo.getId())
                            .set(CourseVodVideo::getAliyunVodId, vodVideoId)
                            .set(
                                CourseVodVideo::getAliyunPlayUrl,
                                sourceRecording.getRecordingResources())
                            .set(CourseVodVideo::getMp4Url,
                                sourceRecording.getRecordingResources()))
                        > 0;

                if (!vodVideoUpdateResult) {
                    throw new BizException("更新课程VOD视频信息失败");
                }
            } else {
                // 创建新的VOD视频信息
                CourseVodVideo vodVideo = new CourseVodVideo();
                vodVideo.setCourseVodId(courseVod.getId());
                vodVideo.setAliyunVodId(vodVideoId);
                vodVideo.setAliyunPlayUrl(sourceRecording.getRecordingResources());
                vodVideo.setMp4Url(sourceRecording.getRecordingResources());
                vodVideo.setRecordVideoTaskId(recordVideoTaskDTO.getId());
                vodVideo.setRecordingId(newRecording.getId());

                if (courseVodVideoMapper.insert(vodVideo) <= 0) {
                    throw new BizException("插入课程VOD视频信息失败");
                }
            }

            log.info("匹配录课资源完成, taskId={}", recordVideoTaskDTO.getId());
        } catch (BizException e) {
            log.error("匹配录课资源失败, taskId={}, error={}", recordVideoTaskDTO.getId(),
                e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("匹配录课资源失败, taskId={}, error={}", recordVideoTaskDTO.getId(),
                e.getMessage(), e);
            throw new BizException("匹配录课资源失败: " + e.getMessage());
        }
    }
}
