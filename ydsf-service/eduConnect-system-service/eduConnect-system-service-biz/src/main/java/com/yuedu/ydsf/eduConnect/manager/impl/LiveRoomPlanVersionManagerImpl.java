package com.yuedu.ydsf.eduConnect.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDraftVO;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanVersionVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDraft;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import com.yuedu.ydsf.eduConnect.manager.LiveRoomPlanDraftManager;
import com.yuedu.ydsf.eduConnect.manager.LiveRoomPlanVersionManager;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanVersionMapper;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDetailDraftService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 班级信息表 公共服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-09 10:44:48
 */
@Slf4j
@Component
@AllArgsConstructor
public class LiveRoomPlanVersionManagerImpl implements LiveRoomPlanVersionManager {

    private final LiveRoomPlanVersionMapper liveRoomPlanVersionMapper;


    /**
     * 通过条件查询直播计划
     *
     * @return List<LiveRoomPlanDraftVO>
     */
    public List<LiveRoomPlanVersionVO> listByIds(List<Long> ids) {

        return liveRoomPlanVersionMapper.selectList(Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
                .eq(LiveRoomPlanVersion::getOnlineVersion, 1)
                .in(LiveRoomPlanVersion::getPlanId, ids)
            ).stream()
            .map(liveRoomPlanVersion -> {
                LiveRoomPlanVersionVO vo = new LiveRoomPlanVersionVO();
                BeanUtils.copyProperties(liveRoomPlanVersion, vo);
                return vo;
            })
            .toList();
    }


}
