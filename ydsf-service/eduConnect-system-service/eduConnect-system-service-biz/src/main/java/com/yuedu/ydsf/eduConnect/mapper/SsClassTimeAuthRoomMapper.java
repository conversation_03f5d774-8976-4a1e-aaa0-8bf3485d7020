package com.yuedu.ydsf.eduConnect.mapper;


import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeAuthRoomVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeAuthRoom;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 课次授权教室表 持久层
 *
 * <AUTHOR>
 * @date 2024-10-09 15:31:25
 */
@Mapper
public interface SsClassTimeAuthRoomMapper extends YdsfBaseMapper<SsClassTimeAuthRoom> {



    /**
     * 获取授权校区数量
     */
    List<SsClassTimeAuthRoomVO> selectAuthRoomCountByClassTimeIds(List<Long> ids);

    /**
     * 设备ID获取课次列表
     * <AUTHOR>
     * @date 2024/10/28 11:46
     * @param ssClassTimeDTO
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
     */
    List<SsClassTimeVO> getClassTimeListByDeviceId(SsClassTimeDTO ssClassTimeDTO);
}
