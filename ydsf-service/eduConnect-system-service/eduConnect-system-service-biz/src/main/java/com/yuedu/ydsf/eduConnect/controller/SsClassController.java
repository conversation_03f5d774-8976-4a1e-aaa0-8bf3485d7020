package com.yuedu.ydsf.eduConnect.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassDTO;
import com.yuedu.ydsf.eduConnect.api.excel.ClassExcel;
import com.yuedu.ydsf.eduConnect.api.query.SsClassAuthRoomQuery;
import com.yuedu.ydsf.eduConnect.api.query.SsClassQuery;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.valid.SsClassValidGroup;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassAuthRoomVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassVO;
import com.yuedu.ydsf.eduConnect.service.SsClassService;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 班级信息表 控制类
 *
 * <AUTHOR>
 * @date 2024-10-09 10:44:48
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/ssClass")
@Tag(description = "ss_class", name = "班级管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SsClassController {

    private final SsClassService ssClassService;

    private final SsClassTimeService ssClassTimeService;

    /**
     * 班级管理分页查询
     *
     * @param page         分页对象
     * @param ssClassQuery 班级信息表
     * @return
     */
    @Operation(summary = "班级管理分页查询", description = "班级管理分页查询")
    @GetMapping("/page")
    @HasPermission("edusystem_ssClass_view")
    public R<IPage<SsClassVO>> getSsClassPage(@ParameterObject Page page, @ParameterObject SsClassQuery ssClassQuery) {
        return R.ok(ssClassService.page(page, ssClassQuery));
    }

    /**
     * 班级管理查询全部
     * @param ssClassQuery
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.api.vo.SsClassVO>
     * <AUTHOR>
     * @date 2024/10/15 16:30
     */
    @Operation(summary = "班级管理查询全部", description = "班级管理查询全部")
    @GetMapping("/getSsClassList")
    public R<List<SsClassVO>> getSsClassList(@ParameterObject SsClassQuery ssClassQuery) {
        List<SsClassVO> ssClassList = ssClassService.getSsClassList(ssClassQuery);
        return R.ok(ssClassList);
    }

    /**
     * 通过id查询班级信息
     * @param id
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/14 9:45
     */
    @Operation(summary = "通过id查询班级信息", description = "通过id查询班级信息")
    @GetMapping("/{id}")
    @HasPermission("edusystem_ssClass_view")
    public R<SsClassVO> getById(@PathVariable Serializable id) {
        SsClassVO ssClassVO = ssClassService.selectById(id);
        return R.ok(ssClassVO);
    }

    /**
     * 新增班级信息表
     *
     * @param ssClassDTO 班级信息表
     * @return R
     */
    @Operation(summary = "新增班级信息表", description = "新增班级信息表")
    @PostMapping("/add")
    @HasPermission("edusystem_ssClass_add")
    public R add(@Validated(V_A.class) @RequestBody SsClassDTO ssClassDTO) {
        ssClassService.add(ssClassDTO);
        return R.ok("操作成功");
    }

    /**
     * 修改班级信息表
     *
     * @param ssClassDTO 班级信息表
     * @return R
     */
    @Operation(summary = "修改班级信息表", description = "修改班级信息表")
    @PutMapping("/edit")
    @HasPermission("edusystem_ssClass_edit")
    public R edit(@Validated(V_E.class) @RequestBody SsClassDTO ssClassDTO) {
        ssClassService.edit(ssClassDTO);
        return R.ok("操作成功");
    }

    /**
     * 班级删除
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "班级删除", description = "班级删除")
    @DeleteMapping("/delete")
    @HasPermission("edusystem_ssClass_del")
    public R delete(@RequestBody @NotEmpty(message = "请选择班级") Long[] ids) {
        ssClassService.delete(ids);
        return R.ok("操作成功");
    }

    /**
     * 导入班级
     *
     * @param classExcelList   对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @Operation(summary = "导入班级", description = "导入班级")
    @PostMapping("/import")
    @HasPermission("edusystem_ssClass_export")
    @Hidden
    public R importClass(@RequestExcel List<ClassExcel> classExcelList, BindingResult bindingResult) {
        return ssClassService.importClass(classExcelList, bindingResult);
    }

    /**
     * 班级结业
     *
     * @param ssClassDTO 班级信息表
     * @return R
     */
    @Operation(summary = "班级结业", description = "班级结业")
    @PutMapping("/complete")
    @HasPermission("edusystem_ssClass_complete")
    public R completeClass(@Validated(SsClassValidGroup.ClassCompleteGroup.class) @RequestBody SsClassDTO ssClassDTO) {
        ssClassService.completeClass(ssClassDTO);
        return R.ok("操作成功");
    }

    /**
     * 班级授权
     *
     * @param ssClassDTO 班级信息表
     * @return R
     */
    @Operation(summary = "班级授权", description = "班级授权")
    @PutMapping("/classAuthDevice")
    @HasPermission("edusystem_ssClass_auth_device")
    public R classAuthDevice(@Validated(V_E.class) @RequestBody SsClassDTO ssClassDTO) {
        log.debug("班级授权参数:{}", ssClassDTO);
        ssClassService.edit(ssClassDTO);
        return R.ok("操作成功");
    }

    /**
     * 查询班级已授权设备信息
     * @param ssClassAuthRoomQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/9 17:00
     */
    @Operation(summary = "查询班级已授权设备信息", description = "查询班级已授权设备信息")
    @GetMapping("/getAuthDeviceListByClassId")
    public R<List<SsClassAuthRoomVO>> getAuthDeviceListByClassId(@Validated(SsClassValidGroup.GetAuthDeviceListGroup.class) @ParameterObject SsClassAuthRoomQuery ssClassAuthRoomQuery) {
        List<SsClassAuthRoomVO> ssClassAuthRoomVOList = ssClassService.getAuthDeviceListByClassId(ssClassAuthRoomQuery);
        return R.ok(ssClassAuthRoomVOList, "操作成功");
    }

    /**
     * 查询班级课程安排
     * @param ssClassTimeQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/10 16:42
     */
    @Operation(summary = "查询班级课程安排", description = "查询班级课程安排")
    @GetMapping("/getPageClassTimeByClassId")
    @HasPermission("edusystem_ssClass_classTime")
    public R<IPage<SsClassTimeVO>> getPageClassTimeByClassId(@ParameterObject Page page, @ParameterObject SsClassTimeQuery ssClassTimeQuery) {
        return R.ok(ssClassTimeService.getPageClassTimeByClassId(page, ssClassTimeQuery));
    }


}
