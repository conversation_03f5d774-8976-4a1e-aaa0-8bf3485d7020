package com.yuedu.ydsf.eduConnect.mapper;


import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 直播间计划明细 持久层
 *
 * <AUTHOR>
 * @date 2024-12-03 09:49:55
 */
@Mapper
public interface LiveRoomPlanDetailVersionMapper extends YdsfBaseMapper<LiveRoomPlanDetailVersion> {

    /**
     * 通过计划Id查询直播间计划明细
     *
     * @param planIdList 计划Id列表
     * @return 结果
     */
    List<LiveRoomPlanDetailVersionVO> getLiveRoomPlanDetailVersionList(List<Long> planIdList);

    /**
     * 通过直播间计划Id获取直播间计划信息
     *
     * @param planId 计划Id
     * @return 结果
     */
    List<LiveRoomPlanDetailVersionVO> selectLiveRoomPlanDetailVersionList(Long planId);
}
