package com.yuedu.ydsf.eduConnect.mapper;

import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.yuedu.ydsf.eduConnect.entity.InformationAuthStoreHistory;
import java.util.List;

/**
* 资料授权历史持久层接口
*
* <AUTHOR>
* @date  2025/07/22
*/
@Mapper
public interface InformationAuthStoreHistoryMapper extends YdsfBaseMapper<InformationAuthStoreHistory> {


    /**
     *  批量保存
     *
     * <AUTHOR>
     * @date 2025年07月22日 14时38分
     */
    int batchSave(List<InformationAuthStoreHistory> informationAuthStoreHistories);
}




