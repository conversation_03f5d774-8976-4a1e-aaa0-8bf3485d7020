package com.yuedu.ydsf.eduConnect.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.api.feign.RemoteClassRoomService;
import com.yuedu.store.api.feign.RemoteLecturerService;
import com.yuedu.store.constant.enums.SchoolCampusEnum;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.dto.ClassRoomDTO;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.entity.CampusEntity;
import com.yuedu.store.entity.ClassRoomEntity;
import com.yuedu.store.entity.LecturerEntity;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.LecturerStateEnum;
import com.yuedu.ydsf.eduConnect.config.XiaogjApiConfig;
import com.yuedu.ydsf.eduConnect.service.InteriorsXiaogjService;
import com.yuedu.ydsf.eduConnect.util.DateUtils;
import com.yuedu.ydsf.eduConnect.util.interiorsXiaogj.InteriorsXiaogjApiCore;
import com.yuedu.ydsf.eduConnect.util.interiorsXiaogj.InteriorsXiaogjApiUrlConstant;
import com.yuedu.ydsf.eduConnect.util.interiorsXiaogj.resp.GetCampusResp;
import com.yuedu.ydsf.eduConnect.util.interiorsXiaogj.resp.GetClassRoomResp;
import com.yuedu.ydsf.eduConnect.util.interiorsXiaogj.resp.GetLecturerResp;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 对接内部校管家接口
 * <AUTHOR>
 * @date 2024年01月19日 09时12分
 */
@Slf4j
@Service
@AllArgsConstructor
public class InteriorsXiaogjServiceImpl implements InteriorsXiaogjService {

    private final XiaogjApiConfig xiaogjApiConfig;

    private final RemoteCampusService remoteCampusService;

    private final RemoteClassRoomService remoteClassRoomService;

    private final RemoteLecturerService remoteLecturerService;

    /**
     * 同步校管家校区数据
     * <AUTHOR>
     * @date 2024年01月19日 09时25分
     */
    @Override
    public void syncXgjCampusSchedule() throws IOException {

        // 获取所有校区数据
        String addCampusResult = InteriorsXiaogjApiCore.okHttpGetUtils(String.format("%s%s"
                ,xiaogjApiConfig.getApiHost()
                , InteriorsXiaogjApiUrlConstant.GET_CAMPUS
                )
        );

        List<GetCampusResp> campusRespList = JSONObject.parseArray(addCampusResult, GetCampusResp.class);

        // 过滤今天新增或者修改的校区信息
        campusRespList = campusRespList.stream()
                .filter(e -> DateUtils.getTodayDateString().equals(DateUtils.getYyyyMMddString(e.getCCreateTime()))
                            || DateUtils.getTodayDateString().equals(DateUtils.getYyyyMMddString(e.getCUpdateTime())))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(campusRespList)){
            return;
        }

        // 查询本系统所有校区数据
        CampusDTO campusDTO = new CampusDTO();
//        campusDTO.setCampusType(Integer.parseInt(SchoolCampusEnum.CAMPUS.getType()));
        R<List<CampusVO>> campusAll = remoteCampusService.getCampusAll(campusDTO);
        if (!campusAll.isOk()) {
            return;
        }

        // 新增/更新校区数据
        List<CampusEntity> campusList = new ArrayList<>();

        for (GetCampusResp campusResp : campusRespList) {

            // 校验本系统是否已存在该校区
            Optional<CampusVO> campusOptional = campusAll.getData().stream()
                    .filter(campus -> campusResp.getCID().equals(campus.getXgjCampusId()))
                    .findFirst();

            if(!campusOptional.isPresent()){

                CampusEntity ssCampus = new CampusEntity();
                ssCampus.setXgjCampusId(campusResp.getCID());
                ssCampus.setCampusName(campusResp.getCName());
                ssCampus.setCampusType(Integer.parseInt(SchoolCampusEnum.CAMPUS.getType()));
                ssCampus.setRegionName(campusResp.getCField1());
                ssCampus.setCampusNo(campusResp.getSchoolid());
                campusList.add(ssCampus);

            }else{

                CampusEntity ssCampus = new CampusEntity();
                ssCampus.setId(campusOptional.get().getId());
                ssCampus.setCampusName(campusResp.getCName());
                ssCampus.setRegionName(campusResp.getCField1());
                ssCampus.setCampusNo(campusResp.getSchoolid());
                ssCampus.setCampusType(Integer.parseInt(SchoolCampusEnum.CAMPUS.getType()));
                ssCampus.setCampusState(LecturerStateEnum.LECTURER_STATE_0.code);
                campusList.add(ssCampus);

            }

        }

        remoteCampusService.saveOrUpdateBatchCampus(campusList);

    }

    /**
     * 同步校管家教室数据
     * <AUTHOR>
     * @date 2024年01月19日 14时42分
     */
    @Override
    public void syncXgjClassRoomSchedule() throws IOException {

        // 获取所有教室数据
        String addClassRoomResult = InteriorsXiaogjApiCore.okHttpGetUtils(String.format("%s%s"
                ,xiaogjApiConfig.getApiHost()
                ,InteriorsXiaogjApiUrlConstant.GET_CLASS_ROOM
                )
        );

        List<GetClassRoomResp> classRoomRespList = JSONObject.parseArray(addClassRoomResult, GetClassRoomResp.class);

        // 过滤今天新增或者修改的教室信息
        classRoomRespList = classRoomRespList.stream()
                .filter(e -> DateUtils.getTodayDateString().equals(DateUtils.getYyyyMMddString(e.getCCreateTime()))
                        || DateUtils.getTodayDateString().equals(DateUtils.getYyyyMMddString(e.getCUpdateTime())))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(classRoomRespList)){
            return;
        }

        // 查询本系统所有校区数据
        CampusDTO campusDTO = new CampusDTO();
//        campusDTO.setCampusType(Integer.parseInt(SchoolCampusEnum.CAMPUS.getType()));
        R<List<CampusVO>> campusList = remoteCampusService.getCampusAll(campusDTO);
        if (!campusList.isOk()) {
            return;
        }

        // 查询本系统所有教室数据
        ClassRoomDTO classRoomDTO = new ClassRoomDTO();
        classRoomDTO.setClassRoomType(Integer.parseInt(SchoolCampusEnum.CAMPUS.getType()));
        R<List<ClassRoomVO>> classRoomVOList = remoteClassRoomService.getClassRoomAll(classRoomDTO);
        if (!classRoomVOList.isOk()) {
            return;
        }

        // 保存/更新教室数据
        List<ClassRoomEntity> classRoomList = new ArrayList<>();

        for (GetClassRoomResp classRoomResp : classRoomRespList) {

            // 校验本系统是否已存在对应校区
            Optional<CampusVO> campusOptional = campusList.getData().stream()
                    .filter(campus -> classRoomResp.getCCampusID().equals(campus.getXgjCampusId()))
                    .findFirst();

            // 校验本系统是否已存在该教室
            Optional<ClassRoomVO> classRoomOptional = classRoomVOList.getData().stream()
                    .filter(classRoom -> classRoomResp.getCID().equals(classRoom.getXgjClassRoomId()))
                    .findFirst();

            if(campusOptional.isPresent() && !classRoomOptional.isPresent()){

                ClassRoomEntity ssClassRoom = new ClassRoomEntity();
                ssClassRoom.setCampusId(campusOptional.get().getId());
                ssClassRoom.setXgjClassRoomId(classRoomResp.getCID());
                ssClassRoom.setClassRoomName(classRoomResp.getCName());
                ssClassRoom.setClassRoomType(Integer.parseInt(SchoolCampusEnum.CAMPUS.getType()));
                classRoomList.add(ssClassRoom);

            }else if(campusOptional.isPresent() && classRoomOptional.isPresent()){

                ClassRoomEntity ssClassRoom = new ClassRoomEntity();
                ssClassRoom.setId(classRoomOptional.get().getId());
                ssClassRoom.setClassRoomName(classRoomResp.getCName());
                classRoomList.add(ssClassRoom);

            }

        }

        remoteClassRoomService.saveOrUpdateBatchClassRoom(classRoomList);

    }

    /**
     * 同步校管家主讲老师数据
     * <AUTHOR>
     * @date 2024年01月19日 14时42分
     */
    @Override
    public void syncXgjLecturerSchedule() throws IOException {

        // 获取所有主讲老师数据
        String addLecturerResult = InteriorsXiaogjApiCore.okHttpGetUtils(String.format("%s%s?departID=%s"
                ,xiaogjApiConfig.getApiHost()
                ,InteriorsXiaogjApiUrlConstant.GET_LECTURER
                ,InteriorsXiaogjApiUrlConstant.HEAD_CAMPUS_ID
                )
        );

        List<GetLecturerResp> lecturerRespList = JSONObject.parseArray(addLecturerResult, GetLecturerResp.class);

        // 过滤今天新增或者修改的主讲信息
        lecturerRespList = lecturerRespList.stream()
                .filter(e -> DateUtils.getTodayDateString().equals(DateUtils.getYyyyMMddString(e.getCCreateTime()))
                        || DateUtils.getTodayDateString().equals(DateUtils.getYyyyMMddString(e.getCUpdateTime())))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(lecturerRespList)){
            return;
        }

        // 查询本系统所有主讲老师数据
        R<List<LecturerVO>> lecturerAllList = remoteLecturerService.getLecturerAll(new LecturerDTO());
        if (!lecturerAllList.isOk()) {
            return;
        }

        // 保存新增主讲老师数据
        List<LecturerEntity> lecturerList = new ArrayList<>();

        for (GetLecturerResp lecturerResp : lecturerRespList) {

            // 校验本系统是否已存在该主讲老师
            Optional<LecturerVO> lecturerOptional = lecturerAllList.getData().stream()
                    .filter(ssLecturer -> lecturerResp.getCUserID().equals(ssLecturer.getXgjLecturerId()))
                    .findFirst();

            if(!lecturerOptional.isPresent()){
                LecturerEntity ssLecturer = new LecturerEntity();
                ssLecturer.setXgjLecturerId(lecturerResp.getCUserID());
                ssLecturer.setLecturerName(lecturerResp.getCName());
                lecturerList.add(ssLecturer);

            }else{
                LecturerEntity ssLecturer = new LecturerEntity();
                ssLecturer.setId(lecturerOptional.get().getId());
                ssLecturer.setLecturerName(lecturerResp.getCName());
                lecturerList.add(ssLecturer);
            }

        }

        remoteLecturerService.saveOrUpdateBatchLecturer(lecturerList);

    }

}




