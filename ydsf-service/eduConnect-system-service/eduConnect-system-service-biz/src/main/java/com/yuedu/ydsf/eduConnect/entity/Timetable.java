package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 门店课表 实体类
 *
 * <AUTHOR>
 * @date 2025-01-10 14:27:17
 */
@Data
@TableName("b_timetable")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店课表实体类")
public class Timetable extends Model<Timetable> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 教学计划ID
	*/
    @Schema(description="教学计划ID")
    private Long teachingPlanId;

	/**
	* 这节课的编号，课程类型（1位）+ 第几节课（3位）+ 主表的ID
	*/
    @Schema(description="这节课的编号，课程类型（1位）+ 第几节课（3位）+ 主表的ID")
    private Long lessonNo;

	/**
	* 门店已约直播课ID、门店已约点播课排期表ID、门店补课表ID
	*/
    @Schema(description="门店已约直播课ID、门店已约点播课排期表ID、门店补课表ID")
    private Long coursePlanId;

	/**
	* 课程类型: 1-直播课; 2-点播课; 3-补课;
	*/
    @Schema(description="课程类型: 1-直播课; 2-点播课; 3-补课;")
    private Integer courseType;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 教室ID
	*/
    @Schema(description="教室ID")
    private Long classroomId;

	/**
	* 主讲老师ID
	*/
    @Schema(description="主讲老师ID")
    private Long lectureId;

	/**
	* 指导老师ID
	*/
    @Schema(description="指导老师ID")
    private Long teacherId;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    private Long courseId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 时段类型: 1-上午; 2-下午; 3-晚上;
	*/
    @Schema(description="时段类型: 1-上午; 2-下午; 3-晚上;")
    private Integer timeSlotType;

	/**
	* 上课日期
	*/
    @Schema(description="上课日期")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    private Object classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    private Object classEndTime;

	/**
	* 上课开始日期时间
	*/
    @Schema(description="上课开始日期时间")
    private LocalDateTime classStartDateTime;

	/**
	* 上课结束日期时间
	*/
    @Schema(description="上课结束日期时间")
    private LocalDateTime classEndDateTime;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 报警状态: 0-无照片; 1-检测不到摄像头; 2-未识别出人像; 3-实际出勤人数小于图片比对出勤数;
     */
    private Integer alarmStatus;

    /**
     * 识别人数
     */
    private Integer identifyNum;

    /**
     * 报警时间(yyyy-MM-dd HH:mm:ss)
     */
    private  LocalDateTime alarmTime;
}
