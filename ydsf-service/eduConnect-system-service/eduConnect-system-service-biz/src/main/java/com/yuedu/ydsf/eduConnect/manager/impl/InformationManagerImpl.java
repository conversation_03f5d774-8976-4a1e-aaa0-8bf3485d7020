package com.yuedu.ydsf.eduConnect.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Sets.SetView;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.constant.enums.SchoolCampusEnum;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.file.core.FileProperties;
import com.yuedu.ydsf.common.file.core.FileTemplate;
import com.yuedu.ydsf.common.file.domain.OssStsResult;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.api.constant.OptTypeEnum;
import com.yuedu.ydsf.eduConnect.api.dto.InformationDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationAuthStoreVO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationResourceVO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationVO;
import com.yuedu.ydsf.eduConnect.entity.Information;
import com.yuedu.ydsf.eduConnect.entity.InformationAuthStore;
import com.yuedu.ydsf.eduConnect.entity.InformationAuthStoreHistory;
import com.yuedu.ydsf.eduConnect.entity.InformationResource;
import com.yuedu.ydsf.eduConnect.manager.InformationManager;
import com.yuedu.ydsf.eduConnect.mapper.InformationAuthStoreHistoryMapper;
import com.yuedu.ydsf.eduConnect.mapper.InformationAuthStoreMapper;
import com.yuedu.ydsf.eduConnect.mapper.InformationMapper;
import com.yuedu.ydsf.eduConnect.mapper.InformationResourceMapper;
import com.yuedu.ydsf.eduConnect.service.impl.InformationServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.text.Collator;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/07/22
 **/
@Component
@AllArgsConstructor
public class InformationManagerImpl implements InformationManager {


    private final InformationAuthStoreMapper informationAuthStoreMapper;
    private final InformationAuthStoreHistoryMapper informationAuthStoreHistoryMapper;
    private final InformationResourceMapper informationResourceMapper;
    private final InformationMapper informationMapper;
    private final RemoteCampusService remoteCampusService;
    private final FileTemplate fileTemplate;
    private final FileProperties fileProperties;


    @Override
    public IPage<InformationVO> fillData(IPage<InformationVO> page) {
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }

        List<Long> collect = page.getRecords().stream().map(InformationVO::getId).distinct()
            .collect(Collectors.toList());

        Map<Long, Integer> cacheChild = informationMapper.getChildTotalByInformationIds(collect)
            .stream().collect(
                Collectors.toMap(InformationVO::getPid,
                    InformationVO::getChildCount));

        Map<Long, Integer> cacheInformation = informationAuthStoreMapper.getAuthStoreTotalByInformationIds(
            collect).stream().collect(
            Collectors.toMap(InformationAuthStoreVO::getInformationId,
                InformationAuthStoreVO::getAuthTotal));
        return page.convert(s -> {
            s.setAuthTotal(cacheInformation.getOrDefault(s.getId(), 0));
            s.setChildCount(cacheChild.getOrDefault(s.getId(), 0));
            s.setHasChild(s.getChildCount() > 0 ? 1 : 0);
            return s;
        });
    }

    @Override
    public boolean checkAuht(Information oldInformation) {
        return CollectionUtil.isNotEmpty(informationAuthStoreMapper
            .selectList(Wrappers.<InformationAuthStore>lambdaQuery()
                .eq(InformationAuthStore::getInformationId, oldInformation.getId())));
    }

    @Override
    public boolean checkResource(Information oldInformation) {
        return CollectionUtil.isNotEmpty(
            informationResourceMapper.selectList(Wrappers.<InformationResource>lambdaQuery()
                .eq(InformationResource::getInformationId, oldInformation.getId())));
    }

    @Override
    public OssStsResult getStsToken(Information oldInformation) {
        try {
            OssStsResult ossStsToken = fileTemplate.getOssStsToken(3600l);
            ossStsToken.setUploadPathPrefix(String.format("%s/information/%s/%s-%s",fileProperties.getOss().getPrefix(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")),oldInformation.getPid(),oldInformation.getId()));
            return ossStsToken;
        }catch (Exception e){
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "获取oss临时凭证失败："+ e.getMessage());
        }
    }




    @Override
    public void fillAuthData(InformationVO informationVO) {
        CampusDTO campusDTO = new CampusDTO();
        campusDTO.setCampusType(Integer.parseInt(SchoolCampusEnum.CAMPUS.getType()));
        R<List<CampusVO>> campusAll = remoteCampusService.getCampusAll(campusDTO);
        if (!campusAll.isOk()) {
            throw new RuntimeException("获取校区列表失败!");
        }
        List<CampusVO> campus = campusAll.getData();
        List<InformationAuthStore> informationAuthStores = informationAuthStoreMapper.selectList(
            Wrappers.<InformationAuthStore>lambdaQuery()
                .eq(InformationAuthStore::getInformationId, informationVO.getId()));

        Collator collator = Collator.getInstance(Locale.CHINA);

        informationVO.setAuthStoreList(campus.stream().filter(
                s -> informationAuthStores.stream().anyMatch(d -> d.getStoreId().equals(s.getId())))
            .sorted(Comparator.comparing(CampusVO::getCampusName, collator))
            .map(s -> {
                InformationAuthStoreVO informationAuthStoreVO = new InformationAuthStoreVO();
                informationAuthStoreVO.setStoreId(s.getId());
                informationAuthStoreVO.setStoreName(s.getCampusName());
                return informationAuthStoreVO;
            }).toList());
        informationVO.setUnAuthStoreList(campus.stream().filter(
                s -> informationAuthStores.stream().noneMatch(d -> d.getStoreId().equals(s.getId())))
            .sorted(Comparator.comparing(CampusVO::getCampusName, collator))
            .map(s -> {
                InformationAuthStoreVO informationAuthStoreVO = new InformationAuthStoreVO();
                informationAuthStoreVO.setStoreId(s.getId());
                informationAuthStoreVO.setStoreName(s.getCampusName());
                return informationAuthStoreVO;
            }).toList());
    }

    @Override
    public void fillResourceData(InformationVO informationVO) {
        informationVO.setResourceList(
            informationResourceMapper.selectList(Wrappers.<InformationResource>lambdaQuery()
                    .eq(InformationResource::getInformationId, informationVO.getId())).stream()
                .map(s -> {
                    InformationResourceVO informationResourceVO = new InformationResourceVO();
                    BeanUtils.copyProperties(s, informationResourceVO);
                    informationResourceVO.setResourceUrl(null);
                    if(StringUtils.isNotBlank(s.getCreateBy()) && s.getCreateBy().contains(":")){
                        s.setCreateBy(s.getCreateBy().substring(s.getCreateBy().lastIndexOf(":")).replace(":", ""));
                    }
                    if(StringUtils.isNotBlank(s.getUpdateBy()) && s.getUpdateBy().contains(":")){
                        s.setUpdateBy(s.getUpdateBy().substring(s.getUpdateBy().lastIndexOf(":")).replace(":", ""));
                    }
                    return informationResourceVO;
                }).toList());
    }



    @Override
    public void authStore(InformationDTO informationDTO) {
        List<InformationAuthStore> informationAuthStores = informationAuthStoreMapper.selectList(
            Wrappers.<InformationAuthStore>lambdaQuery()
                .eq(InformationAuthStore::getInformationId, informationDTO.getId())
        );
        Set<Long> newStoreIds = informationDTO.getStoreIds().stream().distinct().collect(Collectors.toSet());
        Set<Long> oldStoreIds = informationAuthStores.stream().map(InformationAuthStore::getStoreId).collect(Collectors.toSet());

        if(CollectionUtil.isEmpty(newStoreIds) && CollectionUtil.isEmpty(oldStoreIds)){
            return;
        }

        YdsfUser user = SecurityUtils.getUser();

        Set<Long> addStoreIds = Sets.difference(newStoreIds,oldStoreIds);
        Set<Long> deleteStoreIds = Sets.difference(oldStoreIds, newStoreIds);

        List<InformationAuthStoreHistory> informationAuthStoreHistories = Lists.newArrayList();
        if(CollectionUtil.isNotEmpty(deleteStoreIds)){
            informationAuthStoreMapper.batchDeleteByInformationIds(informationDTO.getId(),Lists.newArrayList(deleteStoreIds));
            deleteStoreIds.forEach(s -> {
                InformationAuthStoreHistory informationAuthStoreHistory = new InformationAuthStoreHistory();
                informationAuthStoreHistory.setStoreId(s);
                informationAuthStoreHistory.setInformationId(informationDTO.getId());
                informationAuthStoreHistory.setOptType(OptTypeEnum.OPT_TYPE_1.code);
                informationAuthStoreHistory.setCreateBy(StringUtils.isNotBlank(user.getUsername())?user.getUsername():"");
                informationAuthStoreHistory.setCreateTime(LocalDateTime.now());
                informationAuthStoreHistories.add(informationAuthStoreHistory);
            });
        }

        if(CollectionUtil.isNotEmpty(addStoreIds)){
            List<InformationAuthStore> addInformationAuthStores = Lists.newArrayList();
            addStoreIds.forEach(s -> {
                InformationAuthStore informationAuthStore = new InformationAuthStore();
                informationAuthStore.setStoreId(s);
                informationAuthStore.setInformationId(informationDTO.getId());
                informationAuthStore.setCreateBy(StringUtils.isNotBlank(user.getUsername())?user.getUsername():"");
                informationAuthStore.setCreateTime(LocalDateTime.now());
                informationAuthStore.setUpdateBy(informationAuthStore.getCreateBy());
                informationAuthStore.setUpdateTime(informationAuthStore.getCreateTime());
                addInformationAuthStores.add(informationAuthStore);

                InformationAuthStoreHistory informationAuthStoreHistory = new InformationAuthStoreHistory();
                BeanUtils.copyProperties(informationAuthStore, informationAuthStoreHistory);
                informationAuthStoreHistory.setOptType(OptTypeEnum.OPT_TYPE_0.code);
                informationAuthStoreHistories.add(informationAuthStoreHistory);
            });
            informationAuthStoreMapper.batchSave(addInformationAuthStores);
        }

        if(CollectionUtil.isNotEmpty(informationAuthStoreHistories)){
            informationAuthStoreHistoryMapper.batchSave(informationAuthStoreHistories);
        }

    }

    @Override
    public void deleteAuth(Information oldInformation) {
        informationAuthStoreMapper.deleteByInformationId(oldInformation.getId());
    }
}
