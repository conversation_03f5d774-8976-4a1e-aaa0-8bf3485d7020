package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuedu.ydsf.eduConnect.api.constant.IsOnLineEnum;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanVersionQuery;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanVersionVO;
import com.yuedu.ydsf.eduConnect.config.SsProperty;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanVersionMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanVersionService;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;


/**
 * 直播间计划版本记录表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-29 14:40:37
 */
@Slf4j
@Service
@AllArgsConstructor
public class LiveRoomPlanVersionServiceImpl extends
    ServiceImpl<LiveRoomPlanVersionMapper, LiveRoomPlanVersion> implements
    LiveRoomPlanVersionService {

    private final TeachingPlanDraftMapper teachingPlanDraftMapper;
    private final SsProperty ssProperty;

    /**
     * 查询全部已发布直播间计划信息
     *
     * @return List<LiveRoomPlanVersionVO>
     */
    @Override
    public List<LiveRoomPlanVersionVO> listCanCreateTeachingPlan(Integer onlineVersion) {
        //查询教学计划关联的所有直播间计划
        List<TeachingPlanDraft> planList = teachingPlanDraftMapper.selectList(
            Wrappers.lambdaQuery(TeachingPlanDraft.class));
        List<Long> ids = planList.stream().map(TeachingPlanDraft::getLiveRoomPlanId).toList();
        return this.listUnfinishedOnlineVersion().stream()
            .filter(liveRoomPlanVersion -> !ids.contains(liveRoomPlanVersion.getPlanId()))
            .map(liveRoomPlanVersion -> {
                LiveRoomPlanVersionVO liveRoomPlanVersionVo = new LiveRoomPlanVersionVO();
                BeanUtils.copyProperties(liveRoomPlanVersion, liveRoomPlanVersionVo);
                return liveRoomPlanVersionVo;
            }).toList();
    }

    @Override
    public List<LiveRoomPlanVersion> listUnfinishedOnlineVersion() {
        return baseMapper.selectJoinList(LiveRoomPlanVersion.class,
            new MPJLambdaWrapper<LiveRoomPlanVersion>()
                .selectAll(LiveRoomPlanVersion.class)
                .distinct()
                .innerJoin(LiveRoomPlanDetailVersion.class, LiveRoomPlanDetailVersion::getPlanId,
                    LiveRoomPlanVersion::getPlanId)
                .eq(LiveRoomPlanVersion::getOnlineVersion, IsOnLineEnum.ISONLINE_1.code)
                .eq(LiveRoomPlanDetailVersion::getVersion, LiveRoomPlanVersion::getVersion)
                .gt(ssProperty.getJwCheckPlanDateEnable(),
                    LiveRoomPlanDetailVersion::getClassEndDateTime, LocalDateTime.now()));
    }

    @Override
    public IPage<LiveRoomPlanVersionVO> pubList(Page page, LiveRoomPlanVersionQuery liveRoomPlanVersion) {
       return baseMapper. selectPage(page, Wrappers.lambdaQuery(LiveRoomPlanVersion.class)
            .eq(LiveRoomPlanVersion::getOnlineVersion, 1)
            .like(StringUtils.isNotBlank(liveRoomPlanVersion.getPlanName()),
                LiveRoomPlanVersion::getPlanName, liveRoomPlanVersion.getPlanName())
        ).convert(s->{
            LiveRoomPlanVersionVO liveRoomPlanVersionVO = new LiveRoomPlanVersionVO();
            BeanUtils.copyProperties(s, liveRoomPlanVersionVO);
            return liveRoomPlanVersionVO;
        });
    }
}
