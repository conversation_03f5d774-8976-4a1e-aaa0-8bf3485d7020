package com.yuedu.ydsf.eduConnect.mq.listener;

import com.yuedu.ydsf.eduConnect.service.TeachingPlanDraftService;
import com.yuedu.ydsf.eduConnect.util.MqUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 课程发布监听listener
 *
 * @date 2024/12/7 9:06
 * @project @Title: CoursePublishListener.java
 */
@Slf4j
@Service
@AllArgsConstructor
@RocketMQMessageListener(
    topic = "${rocketmq.topics.publish_course_topic}",
    consumerGroup = "${rocketmq.groups.publish_course_group}",
    tag = "*")
@ConditionalOnProperty(
    prefix = "rocketmq",
    name = "enabled",
    havingValue = "true",
    matchIfMissing = false)
public class CoursePublishListener implements RocketMQListener {

  private final TeachingPlanDraftService teachingPlanDraftService;

  @Override
  public ConsumeResult consume(MessageView messageView) {
    log.info(
        "接收到课程发布消息: messageId={}, topic={}", messageView.getMessageId(), messageView.getTopic());

    try {
      // 1. 消息体校验
      if (messageView.getBody() == null) {
        log.error("课程发布消息体为空");
        return ConsumeResult.SUCCESS; // 消息体为空直接返回成功,避免重试
      }

      // 2. 解析消息内容
      Long courseId = null;
      try {
        courseId = MqUtil.convertMessageBodyToDTO(messageView, Long.class);
      } catch (Exception e) {
        log.error("课程发布消息解析失败: {}", e.getMessage(), e);
        return ConsumeResult.SUCCESS; // 解析失败直接返回成功,避免重试
      }

      if (courseId == null) {
        log.error("课程ID为空");
        return ConsumeResult.SUCCESS;
      }

      log.info("开始处理课程发布消息, courseId: {}", courseId);

      // 3. 重新匹配课件与课程计划
      try {
        teachingPlanDraftService.rematchCoursewareWithLessonPlans(null, courseId);
        log.info("课程发布消息处理成功, courseId: {}", courseId);
        return ConsumeResult.SUCCESS;
      } catch (Exception e) {
        log.error("课程发布消息处理失败, courseId: {}, error: {}", courseId, e.getMessage(), e);
        return ConsumeResult.FAILURE; // 业务处理失败返回失败,触发重试机制
      }

    } catch (Exception e) {
      log.error("课程发布消息消费异常: {}", e.getMessage(), e);
      return ConsumeResult.FAILURE;
    }
  }
}
