package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.api.query.CourseMakeUpOnlineQuery;
import com.yuedu.ydsf.eduConnect.api.dto.CourseMakeUpOnlineDTO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineExportVO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineVO;
import com.yuedu.ydsf.eduConnect.entity.CourseMakeUpOnline;

import java.io.Serializable;
import java.util.List;

/**
* 门店线上补课表服务接口
*
* <AUTHOR>
* @date  2025/04/28
*/
public interface CourseMakeUpOnlineService extends IService<CourseMakeUpOnline> {



    /**
     * 门店线上补课表分页查询
     *
     * @param page 分页对象
     * @param courseMakeUpOnlineQuery 门店线上补课表
     * @return IPage 分页结果
     */
    IPage<CourseMakeUpOnlineVO> page(Page page, CourseMakeUpOnlineQuery courseMakeUpOnlineQuery);


    /**
     * 根据ID获得门店线上补课表信息
     *
     * @param id id
     * @return CourseMakeUpOnlineVO 详细信息
     */
    CourseMakeUpOnlineVO getInfoById(Serializable id);


    /**
     * 新增门店线上补课表
     *
     * @param courseMakeUpOnlineDTO 门店线上补课表
     * @return boolean 执行结果
     */
    boolean add(CourseMakeUpOnlineDTO courseMakeUpOnlineDTO);


    /**
     * 修改门店线上补课表
     *
     * @param courseMakeUpOnlineDTO 门店线上补课表
     * @return boolean 执行结果
     */
    boolean edit(CourseMakeUpOnlineDTO courseMakeUpOnlineDTO);


    /**
     *  导出门店线上补课
     *
     * <AUTHOR>
     * @date 2025年07月08日 13时47分
     */
    List<CourseMakeUpOnlineExportVO> export(CourseMakeUpOnlineQuery courseMakeUpOnlineQuery);
}
