package com.yuedu.ydsf.eduConnect.mapper;


import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.vo.PlanNameVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 直播间计划版本记录表 持久层
 *
 * <AUTHOR>
 * @date 2024-11-29 14:40:37
 */
@Mapper
public interface LiveRoomPlanVersionMapper extends YdsfBaseMapper<LiveRoomPlanVersion> {


    /**
     * 获取直播间计划名称
     *
     * @param planIdList 直播计划详情
     * @return 结果
     */
    List<PlanNameVO> selectPlanName(List<Long> planIdList);
}
