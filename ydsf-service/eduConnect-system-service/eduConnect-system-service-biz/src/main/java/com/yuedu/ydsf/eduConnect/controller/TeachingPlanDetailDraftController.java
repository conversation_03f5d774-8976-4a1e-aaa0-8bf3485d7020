package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.dto.TeachingPageTemplateDTO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanDetailDraftQuery;
import com.yuedu.ydsf.eduConnect.api.valid.SsClassValidGroup;
import com.yuedu.ydsf.eduConnect.api.valid.TeachingPlanDetailDraftValidGroup;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailDraft;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDetailDraftService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教学计划明细草稿表 控制类
 *
 * <AUTHOR>
 * @date 2024-12-03 09:52:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/TeachingPlanDetailDraft" )
@Tag(description = "ea_teaching_plan_detail_draft" , name = "教学计划明细草稿表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TeachingPlanDetailDraftController {

    private final  TeachingPlanDetailDraftService teachingPlanDetailDraftService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param teachingPlanDetailDraft 教学计划明细草稿表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("edusystem_TeachingPlanDetailDraft_view")
    public R getTeachingPlanDetailDraftPage(@ParameterObject Page page, @ParameterObject TeachingPlanDetailDraft teachingPlanDetailDraft) {
        LambdaQueryWrapper<TeachingPlanDetailDraft> wrapper = Wrappers.lambdaQuery();
        return R.ok(teachingPlanDetailDraftService.page(page, wrapper));
    }


    /**
     * 通过条件查询教学计划明细草稿表
     * @param teachingPlanDetailDraft 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("edusystem_TeachingPlanDetailDraft_detail")
    public R getDetails(@ParameterObject TeachingPlanDetailDraft teachingPlanDetailDraft) {
        return R.ok(teachingPlanDetailDraftService.list(Wrappers.query(teachingPlanDetailDraft)));
    }

    /**
     * 新增教学计划明细草稿表
     * @param teachingPlanDetailDraft 教学计划明细草稿表
     * @return R
     */
    @Operation(summary = "新增教学计划明细草稿表" , description = "新增教学计划明细草稿表" )
    @PostMapping("/add")
    @HasPermission("edusystem_TeachingPlanDetailDraft_add")
    public R save(@RequestBody TeachingPlanDetailDraft teachingPlanDetailDraft) {
        return R.ok(teachingPlanDetailDraftService.save(teachingPlanDetailDraft));
    }

    /**
     * 修改教学计划明细草稿表
     * @param
     * @return R
     */
    @Operation(summary = "修改教学计划明细草稿表" , description = "修改教学计划明细草稿表" )
    @PutMapping("/edit")
    @HasPermission("edusystem_TeachingPlanDetailDraft_edit")
    public R updateById(@Validated(TeachingPlanDetailDraftValidGroup.UpdateTeachingDetailGroup.class) @RequestBody TeachingPlanDetailDraftQuery teachingPlanDetailDraftQuery) {
        return R.ok(teachingPlanDetailDraftService.updateById(teachingPlanDetailDraftQuery));
    }


    /**
     * 通过id删除教学计划明细草稿表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除教学计划明细草稿表" , description = "通过id删除教学计划明细草稿表" )
    @DeleteMapping("/delete")
    @HasPermission("edusystem_TeachingPlanDetailDraft_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(teachingPlanDetailDraftService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param teachingPlanDetailDraft 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_TeachingPlanDetailDraft_export")
    public List<TeachingPlanDetailDraft> exportExcel(TeachingPlanDetailDraft teachingPlanDetailDraft,Long[] ids) {
        return teachingPlanDetailDraftService.list(Wrappers.lambdaQuery(teachingPlanDetailDraft).in(ArrayUtil.isNotEmpty(ids), TeachingPlanDetailDraft::getId, ids));
    }

    /**
     * 导入excel 表
     * @param teachingPlanDetailDraftList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_TeachingPlanDetailDraft_export")
    public R importExcel(@RequestExcel List<TeachingPlanDetailDraft> teachingPlanDetailDraftList, BindingResult bindingResult) {
        return R.ok(teachingPlanDetailDraftService.saveBatch(teachingPlanDetailDraftList));
    }
}
