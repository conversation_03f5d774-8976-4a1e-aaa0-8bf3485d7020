package com.yuedu.ydsf.eduConnect.manager.impl;

import static com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum.REQUEST_ERROR;

import com.aliyun.vod20170321.models.CreateUploadVideoResponseBody;
import com.aliyun.vod20170321.models.GetVideoPlayAuthResponseBody;
import com.aliyun.vod20170321.models.RefreshUploadVideoResponseBody;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.store.api.feign.RemoteLecturerService;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.ClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.RecordingStatusEnum;
import com.yuedu.ydsf.eduConnect.api.vo.PlayAuthVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsRecordingVO;
import com.yuedu.ydsf.eduConnect.api.vo.UploadAuthVO;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.SsRecording;
import com.yuedu.ydsf.eduConnect.manager.SsRecordingManager;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeMapper;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AgoraTokenGenerator;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AliTokenGenerator;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.RegisterMetaDataDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.service.VodService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 录制服务
 *
 * @author: KL
 * @date: 2024/09/29
 **/
@Slf4j
@Component
@AllArgsConstructor
public class SsRecordingManagerImpl implements SsRecordingManager {


    private AgoraTokenGenerator agoraTokenGenerator;

    private AliTokenGenerator aliTokenGenerator;

    private VodService vodService;

    private SsClassTimeMapper ssClassTimeMapper;

    private RemoteLecturerService remoteLecturerService;


    @Override
    public SsRecording fillResource(SsRecording ssRecording) {
        try {
            ssRecording.setRecordingResources(
                vodService.getMezzanineInfo(ssRecording.getVodVideoId()));
        } catch (Exception exception) {
            log.error("获取视频播放地址失败{}-{}", ssRecording.getVodVideoId(),
                exception.getMessage());
            throw new BizException(REQUEST_ERROR,"获取播放地址失败:" + exception.getMessage());
        }

        return ssRecording;
    }

    @Override
    public UploadAuthVO getUploadAuth(String fileName, String title) {
        try {
            UploadAuthVO uploadAuth = new UploadAuthVO();
            CreateUploadVideoResponseBody body = vodService.getUploadAuth(fileName, title);
            uploadAuth.setVideoId(body.getVideoId());
            uploadAuth.setUploadAuth(body.getUploadAuth());
            uploadAuth.setUploadAddress(body.getUploadAddress());
            uploadAuth.setRequestId(body.getRequestId());
            return uploadAuth;
        } catch (Exception e) {
            log.error("获得视频上传凭证{}-{}-{}", fileName, title, e.getMessage());
            throw new BizException(REQUEST_ERROR,"获取上传凭证失败:" + e.getMessage());
        }
    }

    @Override
    public PlayAuthVO getPlayAuth(String videoId, Long expiredTime) {
        try {
            PlayAuthVO playAuthVO = new PlayAuthVO();
            GetVideoPlayAuthResponseBody body = vodService.getVideoPlayAuth(videoId, expiredTime);
            playAuthVO.setPlayAuth(body.getPlayAuth());
            playAuthVO.setRequestId(body.getRequestId());
            return playAuthVO;
        } catch (Exception e) {
            log.error("获得视频播放凭证{}-{}", videoId, e.getMessage());
            throw new BizException(REQUEST_ERROR,"获取播放凭证失败:" + e.getMessage());
        }
    }

    @Override
    public UploadAuthVO refreshUploadAuth(String videoId) {
        try {
            UploadAuthVO uploadAuth = new UploadAuthVO();
            RefreshUploadVideoResponseBody body = vodService.refreshUploadAuth(videoId);
            uploadAuth.setVideoId(body.getVideoId());
            uploadAuth.setUploadAuth(body.getUploadAuth());
            uploadAuth.setUploadAddress(body.getUploadAddress());
            uploadAuth.setRequestId(body.getRequestId());
            return uploadAuth;
        } catch (Exception e) {
            log.error("刷新视频上传凭证{}-{}", videoId, e.getMessage());
            throw new BizException(REQUEST_ERROR,"刷新上传凭证失败:" + e.getMessage());
        }
    }

    @Override
    public List<SsRecordingVO> convert(List<SsRecording> ssRecordings) {

        Map<Long, LecturerVO> cacheLectureMap = new HashMap<>();
        LecturerDTO lecturerDTO = new LecturerDTO();
        lecturerDTO.setIds(ssRecordings.stream().map(s -> s.getLecturerId()).toList());
        R<List<LecturerVO>> lecturerList = remoteLecturerService.getLecturerList(lecturerDTO);
        if (lecturerList.isOk() && CollectionUtils.isNotEmpty(lecturerList.getData())) {
            lecturerList.getData().forEach(s -> {
                cacheLectureMap.put(s.getId(), s);
            });
        }

        return ssRecordings.stream().map(entity -> {
            SsRecordingVO ssRecordingVO = new SsRecordingVO();
            BeanUtils.copyProperties(entity, ssRecordingVO);
            if (cacheLectureMap.containsKey(entity.getLecturerId())) {
                ssRecordingVO.setLecturerName(
                    cacheLectureMap.get(entity.getLecturerId()).getLecturerName());
            }
            return ssRecordingVO;
        }).toList();
    }

    @Override
    public List<String> getPlayInfo(String videoId) {
        try {
            return vodService.getPlayInfo(videoId);
        } catch (Exception e) {
            throw new BizException(REQUEST_ERROR,"获取下载列表失败:" + e.getMessage());
        }
    }


    @Override
    public void submitTranscodeTask(SsRecording ssRecording) {
        try {
            vodService.submitTranscodeTask(ssRecording.getVodVideoId(),
                aliTokenGenerator.getTemplateGroupId());
            ssRecording.setRecordingStatus(RecordingStatusEnum.RECORDING_STATUS_7.code);
        } catch (Exception e) {
            log.error("提交转码任务失败：videoId:{} - templateGroupId:{} - exce:{}",
                ssRecording.getVodVideoId(), aliTokenGenerator.getTemplateGroupId(),
                e.getMessage());
            throw new BizException(REQUEST_ERROR,"提交转码任务失败:" + e.getMessage());
        }
    }


    @Override
    public boolean checkDeleteRecording(List<Long> ids) {
        return CollectionUtils.isEmpty(ssClassTimeMapper.selectList(Wrappers.<SsClassTime>lambdaQuery()
            .eq(SsClassTime::getAttendClassType, ClassTypeEnum.ATTENDCLASSTYPE_1.CODE)
            .in(SsClassTime::getRecordingId,ids)));
    }

    @Override
    public List<SsRecording> registerVideo(List<SsRecording> ssRecordings) {

        List<RegisterMetaDataDTO> registerMetaDatas = ssRecordings.stream().map(s -> {
            RegisterMetaDataDTO registerMetaDataDTO = new RegisterMetaDataDTO();
            registerMetaDataDTO.setFileUrl(
                String.format("%s/%s", agoraTokenGenerator.getAgoraStorageEndpoint(),
                    s.getRecordingResources()));
            registerMetaDataDTO.setTitle(s.getBooksName());
            return registerMetaDataDTO;
        }).toList();

       return vodService.registerMedia(registerMetaDatas,agoraTokenGenerator.getAgoraVodTemplateId())
            .stream().map(s->{
               SsRecording ssRecording = new SsRecording();
               ssRecording.setVodVideoId(s.getVideoId());
               ssRecording.setRecordingResources(vodService.getMezzanine(s.getVideoId()));
               return ssRecording;
            }).toList();
    }

}
