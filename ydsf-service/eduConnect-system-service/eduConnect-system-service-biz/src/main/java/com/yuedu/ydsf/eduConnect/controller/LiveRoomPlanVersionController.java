package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanVersionQuery;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanVersionService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 直播间计划版本记录表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-29 14:40:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/LiveRoomPlanVersion" )
@Tag(description = "ea_live_room_plan_version" , name = "直播间计划版本记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LiveRoomPlanVersionController {

    private final  LiveRoomPlanVersionService liveRoomPlanVersionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param liveRoomPlanVersion 直播间计划版本记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("admin_LiveRoomPlanVersion_view")
    public R getLiveRoomPlanVersionPage(@ParameterObject Page page, @ParameterObject LiveRoomPlanVersion liveRoomPlanVersion) {
        LambdaQueryWrapper<LiveRoomPlanVersion> wrapper = Wrappers.lambdaQuery();
        return R.ok(liveRoomPlanVersionService.page(page, wrapper));
    }

    /**
     * 查询全部已发布直播间列表
     *
     * @return 全部已发布直播间列表
     */
    @Operation(summary = "查询全部已发布直播间列表", description = "查询全部已发布直播间列表")
    @GetMapping("/list")
    public R list(@RequestParam(name = "online_version", required = false, defaultValue = "1") Integer onlineVersion) {
        return R.ok(liveRoomPlanVersionService.listCanCreateTeachingPlan(onlineVersion));
    }



    /**
     *  根据查询条件查询最新的直播间计划版本记录表
     *
     * <AUTHOR>
     * @date 2025年07月14日 10时09分
     */
    @Operation(summary = "根据查询条件查询最新的直播间计划版本记录表", description = "根据查询条件查询最新的直播间计划版本记录表")
    @GetMapping("/pub/list")
    public R list(@ParameterObject Page page, @ParameterObject LiveRoomPlanVersionQuery liveRoomPlanVersion) {
        return R.ok(liveRoomPlanVersionService.pubList(page,liveRoomPlanVersion));
    }


    /**
     * 通过条件查询直播间计划版本记录表
     * @param liveRoomPlanVersion 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("admin_LiveRoomPlanVersion_view")
    public R getDetails(@ParameterObject LiveRoomPlanVersion liveRoomPlanVersion) {
        return R.ok(liveRoomPlanVersionService.list(Wrappers.query(liveRoomPlanVersion)));
    }

    /**
     * 新增直播间计划版本记录表
     * @param liveRoomPlanVersion 直播间计划版本记录表
     * @return R
     */
    @Operation(summary = "新增直播间计划版本记录表" , description = "新增直播间计划版本记录表" )
    @PostMapping("/add")
    @HasPermission("admin_LiveRoomPlanVersion_add")
    public R save(@RequestBody LiveRoomPlanVersion liveRoomPlanVersion) {
        return R.ok(liveRoomPlanVersionService.save(liveRoomPlanVersion));
    }

    /**
     * 修改直播间计划版本记录表
     * @param liveRoomPlanVersion 直播间计划版本记录表
     * @return R
     */
    @Operation(summary = "修改直播间计划版本记录表" , description = "修改直播间计划版本记录表" )
    @PutMapping("/edit")
    @HasPermission("admin_LiveRoomPlanVersion_edit")
    public R updateById(@RequestBody LiveRoomPlanVersion liveRoomPlanVersion) {
        return R.ok(liveRoomPlanVersionService.updateById(liveRoomPlanVersion));
    }

    /**
     * 通过id删除直播间计划版本记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除直播间计划版本记录表" , description = "通过id删除直播间计划版本记录表" )
    @DeleteMapping("/delete")
    @HasPermission("admin_LiveRoomPlanVersion_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(liveRoomPlanVersionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param liveRoomPlanVersion 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("admin_LiveRoomPlanVersion_export")
    public List<LiveRoomPlanVersion> exportExcel(LiveRoomPlanVersion liveRoomPlanVersion,Long[] ids) {
        return liveRoomPlanVersionService.list(Wrappers.lambdaQuery(liveRoomPlanVersion).in(ArrayUtil.isNotEmpty(ids), LiveRoomPlanVersion::getId, ids));
    }

    /**
     * 导入excel 表
     * @param liveRoomPlanVersionList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("admin_LiveRoomPlanVersion_export")
    public R importExcel(@RequestExcel List<LiveRoomPlanVersion> liveRoomPlanVersionList, BindingResult bindingResult) {
        return R.ok(liveRoomPlanVersionService.saveBatch(liveRoomPlanVersionList));
    }
}
