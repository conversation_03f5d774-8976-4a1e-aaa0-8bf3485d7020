package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.api.constant.AuditStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DownloadStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.RecordingStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.StorageTypeEnum;
import com.yuedu.ydsf.eduConnect.api.dto.SsRecordingDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsRecordingQuery;
import com.yuedu.ydsf.eduConnect.api.vo.PlayAuthVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsRecordingVO;
import com.yuedu.ydsf.eduConnect.api.vo.UploadAuthVO;
import com.yuedu.ydsf.eduConnect.entity.SsRecording;
import com.yuedu.ydsf.eduConnect.service.SsRecordingService;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AliTokenGenerator;
import com.yuedu.ydsf.eduConnect.system.proxy.service.VodService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 点播库控制层
 *
 * <AUTHOR>
 * @date 2024/09/26
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/ssRecording")
@Tag(description = "ss_recording", name = "点播库")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SsRecordingController {


    private final SsRecordingService ssRecordingService;

    private final VodService vodService;

    private final AliTokenGenerator aliTokenGenerator;


    /**
     * 点播库分页查询
     *
     * @param page             分页对象
     * @param ssRecordingQuery 点播库
     * @return R
     */
    @GetMapping("/page")
    @HasPermission("edusystem_ssRecording_view")
    @Operation(summary = "分页查询", description = "点播库分页查询")
    public R<IPage<SsRecordingVO>> page(@ParameterObject Page page,
        @ParameterObject SsRecordingQuery ssRecordingQuery) {
        return R.ok(ssRecordingService.page(page, ssRecordingQuery));
    }

  /**
   * 条件查询返回List
   *
   * @param ssRecordingQuery
   * @return
   */
  @GetMapping("/list")
  @Operation(summary = "老双师点播库查询", description = "老双师点播库查询")
  public R<List<SsRecordingVO>> list(
      @ParameterObject SsRecordingQuery ssRecordingQuery) {
      List<SsRecordingVO> recordingVOList= ssRecordingService.recordTaskMatchList(ssRecordingQuery);
    return R.ok(recordingVOList);
  }

    /**
     * 通过id查询点播库
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询点播库")
    @GetMapping("/{id}")
    @HasPermission("edusystem_ssRecording_view")
    public R<SsRecordingVO> getById(@PathVariable Serializable id) {
        return R.ok(ssRecordingService.getRecordingById(id));
    }


    /**
     * 新增点播库
     *
     * @param ssRecordingDTO 点播库
     * @return R
     */
    @PostMapping
    @Idempotent
    @HasPermission("edusystem_ssRecording_add")
    @Operation(summary = "新增点播库", description = "新增点播库")
    public R add(@Validated(V_A.class) @RequestBody SsRecordingDTO ssRecordingDTO) {
        ssRecordingDTO.setRecordingStatus(RecordingStatusEnum.RECORDING_STATUS_0.code);
        ssRecordingDTO.setStorageType(StorageTypeEnum.STORAGE_TYPE_1.code);
        return R.ok(ssRecordingService.add(ssRecordingDTO));
    }


    /**
     * 修改点播库
     *
     * @param ssRecordingDTO 点播库
     * @return R
     */
    @PutMapping
    @Idempotent
    @HasPermission("edusystem_ssRecording_edit")
    @Operation(summary = "修改点播库", description = "修改点播库")
    public R edit(@Validated(V_E.class) @RequestBody SsRecordingDTO ssRecordingDTO) {
        return R.ok(ssRecordingService.edit(ssRecordingDTO));
    }


    /**
     * 通过id删除点播库
     *
     * @param ids id列表
     * @return R
     */
    @DeleteMapping
    @HasPermission("edusystem_ssRecording_del")
    @Operation(summary = "删除点播库", description = "删除点播库")
    public R delete(@RequestBody Long[] ids) {
        return R.ok(ssRecordingService.deleteByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 点播库表格
     *
     * @param ssRecordingQuery 查询条件
     * @param ids              导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_ssRecording_export")
    @Operation(summary = "导出点播库表格", description = "导出点播库表格")
    public List<SsRecordingVO> export(SsRecordingQuery ssRecordingQuery, Long[] ids) {
        return ssRecordingService.export(ssRecordingQuery, ids);
    }


    /**
     * 下载
     *
     * @param id 记录id
     * @return excel 文件流
     */
    @GetMapping("/download/{id}")
    @HasPermission("edusystem_ssRecording_download")
    @Operation(summary = "下载视频", description = "下载")
    public R<SsRecordingVO> download(@PathVariable @NotNull(message = "id不能为空") Long id) {
        return R.ok(ssRecordingService.download(id));
    }


    /**
     * 获取上传凭证
     *
     * @param
     * @return R
     */
    @GetMapping("/getUploadAuth")
    @Operation(summary = "获取上传凭证", description = "获取上传凭证")
    public R<UploadAuthVO> getUploadAuth(@NotBlank(message = "视频标题不能为空") String title,
        @NotBlank(message = "视频源文件名不能为空") String fileName) {
        return R.ok(ssRecordingService.getUploadAuth(fileName, title));
    }


    /**
     * 刷新上传凭证
     *
     * @param
     * @return R
     */
    @GetMapping("/refreshUploadAuth")
    @Operation(summary = "刷新上传凭证", description = "刷新上传凭证")
    public R<UploadAuthVO> refreshUploadAuth(
        @NotBlank(message = "videoId不能为空") String videoId) {
        return R.ok(ssRecordingService.refreshUploadAuth(videoId));
    }


    /**
     * 获取播放凭证
     *
     * @param
     * @return R
     */
    @GetMapping("/getPlayAuth")
    @Operation(summary = "获取播放凭证", description = "获取播放凭证")
    public R<PlayAuthVO> getPlayAuth(@NotBlank(message = "videoId不能为空") String videoId,
        Long expiredTime) {
        return R.ok(ssRecordingService.getPlayAuth(videoId, expiredTime));
    }

    /**
     * 修改审核状态
     *
     * <AUTHOR>
     * @date 2024年11月06日 11时10分
     */
    @PutMapping("/aduit/{id}/{status}")
    @HasPermission("edusystem_ssRecording_audit")
    @Operation(summary = "修改审核状态", description = "修改审核状态")
    public R auditStatus(
        @PathVariable("id") @Parameter(description = "资源ID") @NotNull(message = "id不能为空") Long id,
        @PathVariable("status") @Parameter(description = "审核状态") @NotNull(message = "status不能为空") Integer status) {
        SsRecording ssRecording = new SsRecording();
        ssRecording.setId(id);
        ssRecording.setAuditStatus(status);
        return R.ok(ssRecordingService.updateById(ssRecording));
    }

    /**
     * 上传视频到vod
     */
    @PostMapping("/uploadVideo")
    @HasPermission("edusystem_ssRecording_upload")
    @Operation(summary = "上传视频到vod", description = "上传视频到vod")
    public R uploadVideo(@RequestBody SsRecordingDTO ssRecordingDTO) {
        if (Objects.isNull(ssRecordingDTO) || StringUtils.isEmpty(ssRecordingDTO.getVodVideoId())) {
            return R.failed("参数不能为空");
        }
        String mezzanine = vodService.getMezzanine(ssRecordingDTO.getVodVideoId());
        if (mezzanine == null) {
            return R.failed("视频不存在");
        }
        SsRecording ssRecording = new SsRecording();
        ssRecording.setVodVideoId(ssRecordingDTO.getVodVideoId());
        ssRecording.setRecordingResources(mezzanine);
        ssRecording.setDownloadUrl(mezzanine);
        ssRecording.setDownloadStatus(DownloadStatusEnum.DOWNLOAD_STATUS_1.code);
        ssRecording.setRecordingStatus(RecordingStatusEnum.RECORDING_STATUS_2.code);
        ssRecording.setAuditStatus(AuditStatusEnum.AUDIT_STATUS_2.code);
        boolean saved = ssRecordingService.save(ssRecording);
        if (saved) {
            // 提交转码任务
            try {
                vodService.submitTranscodeTask(ssRecordingDTO.getVodVideoId(), aliTokenGenerator.getTemplateGroupId());
            } catch (Exception e) {
                return R.failed("提交转码任务失败");
            }
            return R.ok();
        } else {
            return R.failed("保存视频信息失败");
        }
    }
}
