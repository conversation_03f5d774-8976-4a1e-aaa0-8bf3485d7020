package com.yuedu.ydsf.eduConnect.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.config.AsyncConfiguration;
import com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.vo.ErrorMessage;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.api.constant.AppointmentEnum;
import com.yuedu.ydsf.eduConnect.api.constant.ClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceBindStateEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncXiaogjEnum;
import com.yuedu.ydsf.eduConnect.api.constant.SsClassStateEnum;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassDTO;
import com.yuedu.ydsf.eduConnect.api.excel.ClassExcel;
import com.yuedu.ydsf.eduConnect.api.query.SsClassAuthRoomQuery;
import com.yuedu.ydsf.eduConnect.api.query.SsClassQuery;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.vo.AttendedClassCountVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassAuthRoomVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassVO;
import com.yuedu.ydsf.eduConnect.entity.SsClass;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.manager.SsClassManager;
import com.yuedu.ydsf.eduConnect.manager.SsDeviceManager;
import com.yuedu.ydsf.eduConnect.manager.SsXiaogjLogManager;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomStudentMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsDeviceMapper;
import com.yuedu.ydsf.eduConnect.service.SsClassService;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.SsPushXiaogjEventReq;
import com.yuedu.ydsf.eduConnect.manager.XiaoGuanJiaService;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.BindingResult;

/**
 * 班级信息表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-09 10:44:48
 */
@Slf4j
@Service
@AllArgsConstructor
public class SsClassServiceImpl extends ServiceImpl<SsClassMapper, SsClass> implements SsClassService {

    private final SsClassMapper ssClassMapper;

    private final SsClassAuthRoomMapper ssClassAuthRoomMapper;

    private final SsClassTimeMapper ssClassTimeMapper;

    private final SsClassManager ssClassManager;

    private final SsClassAuthRoomStudentMapper ssClassAuthRoomStudentMapper;

    private final XiaoGuanJiaService xiaoGuanJiaService;

    private final SsDeviceMapper ssDeviceMapper;

    private final SsDeviceManager deviceManager;

    private final AsyncConfiguration asyncConfiguration;

    private final SsXiaogjLogManager xiaogjLogManager;


    /**
     * 班级管理分页查询
     * @param page
     * @param ssClassQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2024/10/9 10:52
     */
    @Override
    public IPage page(Page page, SsClassQuery ssClassQuery) {

        IPage pages = new Page();

        // 校验授权教室查询条件是否有值
        List<Long> classIdList = new ArrayList<>();
        if (Objects.nonNull(ssClassQuery.getClassRoomDeviceId())) {

            // 查询搜索设备已授权班级
            List<SsClassAuthRoom> ssClassAuthRoomList = ssClassAuthRoomMapper.selectList(Wrappers.lambdaQuery(SsClassAuthRoom.class)
                .eq(SsClassAuthRoom::getDeviceId, ssClassQuery.getClassRoomDeviceId())
                .eq(SsClassAuthRoom::getAppointmentStatus, AppointmentEnum.TYPE_1.CODE)
                .and(wrapper -> wrapper.isNull(SsClassAuthRoom::getClassTimeIds)
                    .or()
                    .eq(SsClassAuthRoom::getClassTimeIds, "")
                )
            );

            if (CollectionUtils.isEmpty(ssClassAuthRoomList)) {
                return pages;
            }

            classIdList = ssClassAuthRoomList.stream()
                .map(SsClassAuthRoom::getClassId)
                .distinct()
                .collect(Collectors.toList());

        }

        // 查询班级信息
        pages = page(page, Wrappers.<SsClass>lambdaQuery()
            .like(StringUtils.isNotBlank(ssClassQuery.getClassName()), SsClass::getClassName, ssClassQuery.getClassName())
            .eq(Objects.nonNull(ssClassQuery.getGrade()), SsClass::getGrade, ssClassQuery.getGrade())
            .eq(Objects.nonNull(ssClassQuery.getClassState()), SsClass::getClassState, ssClassQuery.getClassState())
            .eq(Objects.nonNull(ssClassQuery.getClassType()), SsClass::getClassType, ssClassQuery.getClassType())
            .in(CollectionUtils.isNotEmpty(classIdList), SsClass::getId, classIdList)
            .orderByAsc(SsClass::getClassState)
            .orderByDesc(SsClass::getId)
        );

        if(CollectionUtils.isEmpty(pages.getRecords())){
            return pages;
        }

        // 实体类转换VO类
        List<SsClassVO> ssClassVOList = ssClassManager.entityConvertVo(pages.getRecords());

        List<Long> classIds = ssClassVOList.stream()
            .map(SsClassVO::getId)
            .distinct()
            .collect(Collectors.toList());

        // 通过班级ID查询各状态课次数
        SsClassTimeQuery classTimeQuery = new SsClassTimeQuery();
        classTimeQuery.setClassIdList(classIds);
        classTimeQuery.setDelFlag(DelFlagEnum.DELFLAG_0.code);
        List<AttendedClassCountVO> attendedClassCountVoList = ssClassTimeMapper.selectAttendedClassCount(classTimeQuery);

        // 查询班级已授权设备数
        List<SsClassAuthRoom> classAuthRoomList = ssClassAuthRoomMapper.selectList(Wrappers.lambdaQuery(SsClassAuthRoom.class)
            .in(SsClassAuthRoom::getClassId, classIds)
            .eq(SsClassAuthRoom::getAppointmentStatus, AppointmentEnum.TYPE_1.CODE)
            .and(wrapper -> wrapper.isNull(SsClassAuthRoom::getClassTimeIds)
                .or()
                .eq(SsClassAuthRoom::getClassTimeIds, "")
            )
            .orderByDesc(SsClassAuthRoom::getId)
        );

        // 过滤已解绑授权设备
        if(CollectionUtils.isNotEmpty(classAuthRoomList)){

            List<Long> deviceIdList = classAuthRoomList.stream()
                .map(SsClassAuthRoom::getDeviceId)
                .distinct()
                .collect(Collectors.toList());

            // 查询班级已解绑授权设备
            List<SsDevice> ssDeviceList = ssDeviceMapper.selectList(Wrappers.lambdaQuery(SsDevice.class)
                .in(SsDevice::getId, deviceIdList)
                .eq(SsDevice::getClassRoomId, DeviceBindStateEnum.DEVICE_BIND_STATE_ENUM.code)
            );

            classAuthRoomList = classAuthRoomList.stream()
                .filter(room -> !ssDeviceList.stream()
                    .map(SsDevice::getId)
                    .collect(Collectors.toList())
                    .contains(room.getDeviceId()))
                .collect(Collectors.toList());

        }

        for (SsClassVO ssClassVO : ssClassVOList) {

            List<AttendedClassCountVO> attendedClassCountVOList = attendedClassCountVoList.stream()
                .filter(e -> e.getClassId().equals(ssClassVO.getId()))
                .collect(Collectors.toList());

            List<SsClassAuthRoom> ssClassAuthRoomList = classAuthRoomList.stream()
                .filter(e -> e.getClassId().equals(ssClassVO.getId()))
                .collect(Collectors.toList());

            // 已排课次数
            ssClassVO.setScheduledClassCount(CollectionUtils.isNotEmpty(attendedClassCountVOList) ? attendedClassCountVOList.get(0).getScheduledClassCount() : 0);

            // 已上课次数
            ssClassVO.setLessonsAttendedCount(CollectionUtils.isNotEmpty(attendedClassCountVOList) ? attendedClassCountVOList.get(0).getLessonsAttendedCount() : 0);

            // 班级授权数
            ssClassVO.setClassAuthCount(ssClassAuthRoomList.size());

        }

        pages.setRecords(ssClassVOList);

        return pages;

    }

    /**
     * 班级管理查询全部
     * @param ssClassQuery
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsClassVO
     * <AUTHOR>
     * @date 2024/10/15 16:30
     */
    @Override
    public List<SsClassVO> getSsClassList(SsClassQuery ssClassQuery) {

        // 查询班级信息
        List<SsClass> ssClassList = ssClassMapper.selectList(Wrappers.lambdaQuery(SsClass.class)
            .like(StringUtils.isNotBlank(ssClassQuery.getClassName()), SsClass::getClassName, ssClassQuery.getClassName())
            .eq(Objects.nonNull(ssClassQuery.getClassState()), SsClass::getClassState, ssClassQuery.getClassState())
            .eq(Objects.nonNull(ssClassQuery.getClassType()), SsClass::getClassType, ssClassQuery.getClassType())
            .orderByAsc(SsClass::getClassState)
            .orderByDesc(SsClass::getId)
        );

        // 实体类转换VO类
        List<SsClassVO> ssClassVOList = ssClassManager.entityConvertVo(ssClassList);

        return ssClassVOList;

    }

    /**
     * 通过id查询班级信息
     * @param id
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsClassVO
     * <AUTHOR>
     * @date 2024/10/14 10:29
     */
    @Override
    public SsClassVO selectById(Serializable id) {
        SsClass ssClass = ssClassMapper.selectById(id);
        SsClassVO ssClassVO = new SsClassVO();
        BeanUtils.copyProperties(ssClass, ssClassVO);
        return ssClassVO;
    }

    /**
     * 新增班级信息
     * @param ssClassDTO
     * <AUTHOR>
     * @date 2024/10/9 14:08
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(SsClassDTO ssClassDTO) {

        // 保存班级信息
        SsClass ssClass = new SsClass();
        BeanUtils.copyProperties(ssClassDTO, ssClass);
        save(ssClass);
        ssClassDTO.setId(ssClass.getId());

        // 处理班级授权校区并同步校管家
        ssClassManager.bindClassAuthRoom(ssClassDTO);

    }

    /**
     * 修改班级信息
     * @param ssClassDTO
     * <AUTHOR>
     * @date 2024/10/9 14:08
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(SsClassDTO ssClassDTO) {

        SsClass oldClass = ssClassMapper.selectById(ssClassDTO.getId());

        if (Objects.isNull(oldClass)) {
            throw new BizException("班级不存在!");
        }

        // 验证班级是否可操作
        ssClassManager.checkClassState(ssClassDTO.getId());

        // 修改班级信息
        SsClass ssClass = new SsClass();
        BeanUtils.copyProperties(ssClassDTO, ssClass);
        ssClassMapper.updateById(ssClass);

        // 处理班级授权校区并同步校管家
        ssClassManager.bindClassAuthRoom(ssClassDTO);

    }

    /**
     * 班级删除
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/10/10 16:14
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long[] ids) {

        // 校验班级下是否已有排课
        List<SsClassTime> ssClassTimeList = ssClassTimeMapper.selectList(Wrappers.lambdaQuery(SsClassTime.class)
            .in(SsClassTime::getClassId, ids)
        );

        if (CollectionUtils.isNotEmpty(ssClassTimeList)) {
            throw new BizException("选中的班级存在已排课次,无法删除!");
        }

        // 查询同步校管家班级信息
        List<SsClass> ssClassList = ssClassMapper.selectList(Wrappers.lambdaQuery(SsClass.class)
            .in(SsClass::getId, ids)
            .eq(SsClass::getIsSyncXiaogj, IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE)
        );

        List<SsClassAuthRoom> ssClassAuthRoomList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ssClassList)) {

            List<Long> syncXiaogjClassIdList = ssClassList.stream()
                .map(SsClass::getId)
                .distinct()
                .collect(Collectors.toList());

            // 查询同步校管家班级授权校区
            ssClassAuthRoomList = ssClassAuthRoomMapper.selectList(Wrappers.lambdaQuery(SsClassAuthRoom.class)
                .in(SsClassAuthRoom::getClassId, syncXiaogjClassIdList)
                .groupBy(SsClassAuthRoom::getXgjClassId)
            );

        }

        // 删除班级
        ssClassMapper.deleteByIds(Arrays.asList(ids));

        // 删除班级授权校区
        ssClassAuthRoomMapper.delete(Wrappers.lambdaQuery(SsClassAuthRoom.class)
            .in(SsClassAuthRoom::getClassId, ids)
        );

        // 删除班级学生信息
        ssClassAuthRoomStudentMapper.delete(Wrappers.lambdaQuery(SsClassAuthRoomStudent.class)
            .in(SsClassAuthRoomStudent::getClassId, ids)
        );


        // 异步同步校管家
        if (CollectionUtils.isNotEmpty(ssClassAuthRoomList)) {

            String userName = SecurityUtils.getUser().getUsername();

            List<SsClassAuthRoom> finalSsClassAuthRoomList = ssClassAuthRoomList;
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    asyncConfiguration.getAsyncExecutor().execute(() -> {
                        for (SsClassAuthRoom ssClassAuthRoom : finalSsClassAuthRoomList) {

                            // 同步校管家班级参数封装
                            ClassCourseReq classCourseReq = xiaoGuanJiaService.syncXiaogjClassParam(ClassCourseReq.CreateClassType.CREATE_CLASS_TYPE_2.CODE,
                                ssClassAuthRoom.getXgjClassId(),
                                null,
                                null,
                                null,
                                null,
                                null
                            );

                            // 双师排课推送校管家消息队列公共方法
                            SsPushXiaogjEventReq ssPushXiaogjEventReq = xiaoGuanJiaService.ssPushXiaogjMessage(classCourseReq);

                            // 保存排课校管家日志
                            xiaogjLogManager.saveXiaogjLog(ssPushXiaogjEventReq, classCourseReq, userName);

                        }

                    });
                }
            });

        }

    }

    /**
     * 班级结业
     * @param ssClassDTO
     * <AUTHOR>
     * @date 2024/10/9 14:08
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeClass(SsClassDTO ssClassDTO) {

        // 验证班级是否可操作
        SsClass ssClassInfo = ssClassManager.checkClassState(ssClassDTO.getId());

        // 通过班级ID查询各状态课次数
        SsClassTimeQuery classTimeQuery = new SsClassTimeQuery();
        classTimeQuery.setClassId(ssClassInfo.getId());
        List<AttendedClassCountVO> attendedClassCountVoList = ssClassTimeMapper.selectAttendedClassCount(classTimeQuery);
        if(CollectionUtils.isNotEmpty(attendedClassCountVoList)){

            if (attendedClassCountVoList.get(0).getNotLessonsAttendedCount() > 0
                || attendedClassCountVoList.get(0).getGoingLessonsAttendedCount() > 0) {

                throw new BizException("此班级有未上完课次, 无法设为结业!");
            }

        }

        // 更新班级为结业
        ssClassMapper.update(Wrappers.lambdaUpdate(SsClass.class)
            .eq(SsClass::getId, ssClassDTO.getId())
            .set(SsClass::getClassState, SsClassStateEnum.STATE_1.CODE)
        );


        // 异步同步校管家
        if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClassInfo.getIsSyncXiaogj())) {

            // 查询班级授权校区信息
            List<SsClassAuthRoom> classAuthRoomList = ssClassAuthRoomMapper.selectList(Wrappers.lambdaQuery(SsClassAuthRoom.class)
                .eq(SsClassAuthRoom::getClassId, ssClassInfo.getId())
                .groupBy(SsClassAuthRoom::getXgjClassId)
            );

            String userName = SecurityUtils.getUser().getUsername();
            
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                  @Override
                  public void afterCommit() {
                      asyncConfiguration.getAsyncExecutor().execute(() -> {
                          for (SsClassAuthRoom ssClassAuthRoom : classAuthRoomList) {

                              // 同步校管家班级参数封装
                              ClassCourseReq classCourseReq = xiaoGuanJiaService.syncXiaogjClassParam(ClassCourseReq.CreateClassType.CREATE_CLASS_TYPE_3.CODE,
                                  ssClassAuthRoom.getXgjClassId(),
                                  ssClassAuthRoom.getXgjCampusId(),
                                  null,
                                  null,
                                  null,
                                  null
                              );

                              // 双师排课推送校管家消息队列公共方法
                              SsPushXiaogjEventReq ssPushXiaogjEventReq = xiaoGuanJiaService.ssPushXiaogjMessage(classCourseReq);

                              // 保存排课校管家日志
                              xiaogjLogManager.saveXiaogjLog(ssPushXiaogjEventReq, classCourseReq, userName);

                          }

                      });
                  }
            });

        }

    }

    /**
     * 班级导入
     * @param classExcelList
     * @param bindingResult
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/10/10 16:49
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importClass(List<ClassExcel> classExcelList, BindingResult bindingResult) {
        // 通用校验获取失败的数据
        List<ErrorMessage> errorMessageList = (List<ErrorMessage>) bindingResult.getTarget();

        // 执行数据插入操作
        for (ClassExcel excel : classExcelList) {
            Set<String> errorMsg = new HashSet<>();

            // 数据合法情况
            if (CollUtil.isEmpty(errorMsg)) {
                insertExcelClass(excel);
            }
            else {
                // 数据不合法情况
                errorMessageList.add(new ErrorMessage(excel.getLineNum(), errorMsg));
            }
        }
        if (CollUtil.isNotEmpty(errorMessageList)) {
            return R.failed(errorMessageList);
        }
        return R.ok();
    }

    /**
     * 插入excel Class
     * @param excel
     * @return void
     * <AUTHOR>
     * @date 2024/10/10 17:27
     */
    private void insertExcelClass(ClassExcel excel) {
        SsClass ssClass = new SsClass();
        ssClass.setClassName(excel.getClassName());
        ssClass.setIsSyncXiaogj(IsSyncXiaogjEnum.getCodeByName(excel.getIsSyncXiaogj()));
        ssClass.setClassType(ClassTypeEnum.getCodeByName(excel.getClassType()));
        ssClassMapper.insert(ssClass);
    }

    /**
     * 查询班级已授权设备信息
     * @param ssClassAuthRoomQuery
     * @return java.util.List<com.yuedu.ydsf.eduConncet.api.vo.SsClassAuthRoomVO>
     * <AUTHOR>
     * @date 2024/10/9 17:19
     */
    @Override
    public List<SsClassAuthRoomVO> getAuthDeviceListByClassId(SsClassAuthRoomQuery ssClassAuthRoomQuery) {

        // 查询班级已授权信息
        List<SsClassAuthRoom> ssClassAuthRoomList = ssClassAuthRoomMapper.selectList(Wrappers.lambdaQuery(SsClassAuthRoom.class)
            .eq(SsClassAuthRoom::getClassId, ssClassAuthRoomQuery.getClassId())
            .eq(SsClassAuthRoom::getAppointmentStatus, AppointmentEnum.TYPE_1.CODE)
            .and(wrapper -> wrapper.isNull(SsClassAuthRoom::getClassTimeIds)
                .or()
                .eq(SsClassAuthRoom::getClassTimeIds, "")
            )
        );

        // 查询设备信息
        List<SsDevice> ssDeviceList = ssDeviceMapper.selectList(Wrappers.lambdaQuery(SsDevice.class)
            .eq(SsDevice::getDeviceType, DeviceTypeEnum.DEVICETYPE_2.code)
        );

        // 查询校区, 教室信息
        Map<String, Map<Long, String>> remoteData = deviceManager.fetchRemoteData(ssDeviceList);
        Map<Long, String> campusMap = remoteData.get("campus");
        Map<Long, String> classRoomMap = remoteData.get("classRoom");

        List<SsClassAuthRoomVO> ssClassAuthRoomVoList = new ArrayList<>();
        for (SsClassAuthRoom ssClassAuthRoom : ssClassAuthRoomList) {

            SsDevice ssDevice = new SsDevice();
            if (CollectionUtils.isNotEmpty(ssDeviceList)) {
                ssDevice = ssDeviceList.stream()
                    .filter(e -> e.getId().equals(ssClassAuthRoom.getDeviceId()))
                    .findFirst()
                    .orElse(new SsDevice());
            }

            SsClassAuthRoomVO ssClassAuthRoomVO = new SsClassAuthRoomVO();
            BeanUtils.copyProperties(ssClassAuthRoom, ssClassAuthRoomVO);
            ssClassAuthRoomVO.setCampusName(campusMap.get(ssClassAuthRoom.getCampusId()));
            ssClassAuthRoomVO.setClassRoomName(classRoomMap.get(ssClassAuthRoom.getClassRoomId()));
            ssClassAuthRoomVO.setDeviceNo(ssDevice.getDeviceNo());
            ssClassAuthRoomVO.setDeviceName(ssDevice.getDeviceName());

            ssClassAuthRoomVoList.add(ssClassAuthRoomVO);
        }

        return ssClassAuthRoomVoList;

    }

    @Override
    public SsClass getAvailableClass(Long classId) throws BizException {
        SsClass ssClass = ssClassMapper.selectById(classId);
        if (Objects.isNull(ssClass)) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "班级不存在");
        } else if (!ssClass.getClassState().equals(SsClassStateEnum.STATE_0.CODE)) {
            throw new BizException(BizErrorCodeEnum.REQUEST_ERROR, "班级已结业");
        }
        return ssClass;
    }
}
