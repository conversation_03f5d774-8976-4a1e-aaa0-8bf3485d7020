package com.yuedu.ydsf.eduConnect.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.yuedu.ydsf.eduConnect.api.dto.GenerateVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanPub;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 已发布的教学计划表 持久层
 *
 * <AUTHOR>
 * @date 2024-11-29 09:26:30
 */
@Mapper
public interface TeachingPlanPubMapper extends MPJBaseMapper<TeachingPlanPub> {


    /**
     * 通过指定条件查询教学计划列表
     *
     * @param teachingPlanQuery 查询条件
     * @return 结果
     */
    List<TeachingPlanPubVO> getTeachingPlanList(TeachingPlanQuery teachingPlanQuery);


    /**
     * 通过课程Id,讲师Id获取可能会出现重复录课任务的教学计划Id列表
     *
     * @param generateRecordTaskDto  录课任务信息
     * @return 结果
     */
    List<Long> getPlanIdList(GenerateVideoTaskDto generateRecordTaskDto);


    /**
     * 根据教学计划发布表的id集合批量获取对应的信息
     * <AUTHOR>
     * @date 2024/12/19 10:22
     * @param teachingPlanIds
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO>
     */
    List<TeachingPlanPubVO> getTeachingPlanPubByTeachingPlanIds(@Param("list") List<Long> teachingPlanIds);
}
