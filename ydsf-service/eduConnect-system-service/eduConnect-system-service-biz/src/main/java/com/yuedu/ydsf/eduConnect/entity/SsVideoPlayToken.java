package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 视频播放凭证 实体类
 *
 * <AUTHOR>
 * @date 2025-03-11 08:47:54
 */
@Data
@TableName("ss_video_play_token")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "视频播放凭证实体类")
public class SsVideoPlayToken extends Model<SsVideoPlayToken> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Integer id;

	/**
	* Token的有效使用次数
	*/
    @Schema(description="Token的有效使用次数")
    private Integer useCount;

	/**
	* token
	*/
    @Schema(description="token")
    private String token;

	/**
	* token过期时间，方便排查问题。
	*/
    @Schema(description="token过期时间，方便排查问题。")
    private LocalDateTime tokenExpire;

	/**
	* 视频播放链接
	*/
    @Schema(description="视频播放链接")
    private String playUrl;

	/**
	* 点播视频ID，阿里云点播视频ID
	*/
    @Schema(description="点播视频ID，阿里云点播视频ID")
    private String videoId;

	/**
	* 校区ID
	*/
    @Schema(description="校区ID")
    private Long campusId;

	/**
	* 校区名称
	*/
    @Schema(description="校区名称")
    private String campusName;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;
}
