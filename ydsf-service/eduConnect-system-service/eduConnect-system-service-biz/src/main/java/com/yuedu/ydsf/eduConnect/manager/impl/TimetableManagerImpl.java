package com.yuedu.ydsf.eduConnect.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.api.feign.RemoteClassService;
import com.yuedu.store.api.feign.RemoteEmployeeService;
import com.yuedu.store.api.feign.RemoteStoreCourseHoursLogService;
import com.yuedu.store.api.feign.RemoteStudentService;
import com.yuedu.store.constant.enums.CourseHoursOperationEnum;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.store.vo.StoreCourseHoursLogVO;
import com.yuedu.store.vo.StudentVO;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.dto.LessonOrderDTO;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.config.AsyncConfiguration;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.file.core.FileProperties;
import com.yuedu.ydsf.eduConnect.api.constant.AlarmStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.CourseTypeEnum;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanDetailPubService;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableExportVO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetablePictureVO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.constant.AdjustStatusEnum;
import com.yuedu.ydsf.eduConnect.entity.Timetable;
import com.yuedu.ydsf.eduConnect.entity.TimetableEventAlarmDeatils;
import com.yuedu.ydsf.eduConnect.entity.TimetablePicture;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteTimetableService;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BClassTimeStudentVO;
import com.yuedu.ydsf.eduConnect.manager.TimetableManager;
import com.yuedu.ydsf.eduConnect.mapper.TimetableEventAlarmDeatilsMapper;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import com.yuedu.ydsf.eduConnect.mapper.TimetablePictureMapper;
import org.javers.common.collections.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.yuedu.ydsf.eduConnect.api.constant.AttendClassEnum.ATTEND_CLASS_0;
import static com.yuedu.ydsf.eduConnect.api.constant.AttendClassEnum.ATTEND_CLASS_1;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/03/11
 **/
@Component
public class TimetableManagerImpl implements TimetableManager {

    @Autowired
    private RemoteClassService remoteClassService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteCampusService remoteCampusService;

    @Autowired
    private RemoteLessonService remoteLessonService;

    @Autowired
    private RemoteTeachingPlanDetailPubService remoteTeachingPlanDetailPubService;

    @Autowired
    private RemoteTimetableService remoteTimetableService;

    @Autowired
    private RemoteStoreCourseHoursLogService remoteStoreCourseHoursLogService;

    @Autowired
    private RemoteStudentService remoteStudentService;

    @Autowired
    private TimetableEventAlarmDeatilsMapper timetableEventAlarmDeatilsMapper;

    @Autowired
    private TimetablePictureMapper timetablePictureMapper;

    @Autowired
    private AsyncConfiguration asyncConfiguration;

    @Autowired
    private FileProperties fileProperties;



    @Autowired
    private RemoteCourseService remoteCourseService;


    @Override
    public IPage<TimetableVO> fillData(IPage<TimetableVO> page) {
        if(CollectionUtils.isEmpty(page.getRecords())){
            return page;
        }

        Map<Long, CampusVO> storeCache = new HashMap();
        Map<Long, ClassVO> classCache = new HashMap();
        Map<String, LessonVO> lessonCache = new HashMap();
        Map<String, TeachingPlanDetailPubVO> teachingPlanDetailPubCache = new HashMap();
        Map<Long,TimetableVO> classTimeStudentCache = new HashMap();
        Map<Long,TimetableVO> storeCourseHoursLogCache = new HashMap();
        Map<Long,TimetableVO> timetableEventAlarmDeatilsCache = new HashMap();

        CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
            CampusDTO campusDTO = new CampusDTO();
            campusDTO.setSchoolIdList(
                page.getRecords().stream().map(TimetableVO::getStoreId).distinct().toList());
            R<List<CampusVO>> campusList = remoteCampusService.getCampusList(campusDTO);
            if (campusList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(campusList.getData())) {
                campusList.getData().forEach(s -> {
                    storeCache.put(s.getId(), s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
            R<List<ClassVO>> classByIdList = remoteClassService
                .getClassByIdList(
                    page.getRecords().stream().map(TimetableVO::getClassId).distinct().toList());
            if (classByIdList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(classByIdList.getData())) {
                Map<Long, EmployeeVO> employeeCache = new HashMap();
                R<List<EmployeeVO>> employeeMapByIdList = remoteEmployeeService
                    .getEmployeeMapByIdList(classByIdList.getData().stream().filter(s->!Objects.isNull(s.getHeadTeacherId())).map(ClassVO::getHeadTeacherId).distinct().toList());
                if (employeeMapByIdList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(employeeMapByIdList.getData())){
                    employeeMapByIdList.getData().forEach(s->{
                        employeeCache.put(s.getUserId(),s);
                    });
                }

                classByIdList.getData().forEach(s -> {
                    classCache.put(Long.valueOf(s.getId()), s);
                    if(!Objects.isNull(employeeCache.get(s.getHeadTeacherId()))){
                        s.setHeadTeacherName(employeeCache.get(s.getHeadTeacherId()).getNickName());
                    }
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task4 = CompletableFuture.runAsync(() -> {
            R<List<LessonVO>> lessonListByOrder = remoteLessonService.getLessonListByOrder(
                page.getRecords().stream().map(s->s.getCourseId()).distinct().map(s -> {
                    LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
                   // lessonOrderDTO.setCourseId(s.getCourseId());
                    lessonOrderDTO.setCourseId(s);
                  //  lessonOrderDTO.setLessonOrderList(Lists.asList(s.getLessonOrder()));
                    return lessonOrderDTO;
                }).toList());
            if (lessonListByOrder.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(lessonListByOrder.getData())){
                lessonListByOrder.getData().forEach(s->{
                    lessonCache.put(String.format("%s#%s",s.getCourseId(),s.getLessonOrder()),s);
                });
            }

        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task5 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.getRecords().stream()
                .filter(s -> LocalDateTime.now().isAfter(s.getClassEndDateTime()))
                .map(TimetableVO::getTeachingPlanId).toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }
            R<List<TeachingPlanDetailPubVO>> teachingPlanDetailPubLiveChannel = remoteTeachingPlanDetailPubService.getTeachingPlanDetailPubLiveChannel(
                list);
            if(teachingPlanDetailPubLiveChannel.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(teachingPlanDetailPubLiveChannel.getData())){
                teachingPlanDetailPubLiveChannel.getData().forEach(s->{
                    teachingPlanDetailPubCache.put(String.format("%s#%s#%s",s.getPlanId(),s.getCourseId(),s.getLessonOrder()),s);
                });
            }
        });

        CompletableFuture<Void> task6 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.getRecords().stream().map(TimetableVO::getLessonNo).distinct()
                .toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }

            R<List<BClassTimeStudentVO>> bClassTimeStudentList = remoteTimetableService.getBClassTimeStudentList(
                list);
            if(bClassTimeStudentList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(bClassTimeStudentList.getData())){
                bClassTimeStudentList.getData().stream().collect(Collectors.groupingBy(BClassTimeStudentVO::getLessonNo)).forEach((k,v)->{
                    TimetableVO timetableVO = new TimetableVO();
                    timetableVO.setExpectedNumber(0);
                    timetableVO.setActualNumber(0);
                    v.forEach(s->{
                       if(AdjustStatusEnum.ADJUST_STATUS_0.code.equals(s.getAdjustStatus())){
                           timetableVO.setExpectedNumber(timetableVO.getExpectedNumber()+1);
                           if(CheckInStatusEnum.CHECK_IN_STATUS_1.code.equals(s.getCheckInStatus())){
                               timetableVO.setActualNumber(timetableVO.getActualNumber()+1);
                           }
                       }
                    });

                    classTimeStudentCache.put(k,timetableVO);
                });
            }

        });

        CompletableFuture<Void> task7 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.getRecords().stream().map(TimetableVO::getId).distinct()
                .toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }

            R<List<StoreCourseHoursLogVO>> storeCourseHoursLogs = remoteStoreCourseHoursLogService.getStudentConsumeListByIds(
                list);

            if(storeCourseHoursLogs.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(storeCourseHoursLogs.getData())){
                storeCourseHoursLogs.getData().stream().collect(Collectors.groupingBy(StoreCourseHoursLogVO::getTimetableId)).forEach((k,v)->{
                    TimetableVO timetableVO = new TimetableVO();
                    timetableVO.setGiftNumber(0);
                    timetableVO.setTrialNumber(0);
                    timetableVO.setConsumeNumber(0);
                    v.forEach(s->{
                        if(CourseHoursOperationEnum.GIFT.getDesc().equals(s.getOperationType())){
                           // timetableVO.setGiftNumber(timetableVO.getGiftNumber()+Math.abs(s.getCourseHours()));
                            timetableVO.setGiftNumber(timetableVO.getGiftNumber()+s.getCourseHours());
                        }
                        if(CourseHoursOperationEnum.TRIAL.getDesc().equals(s.getOperationType())){
                           // timetableVO.setTrialNumber(timetableVO.getTrialNumber()+Math.abs(s.getCourseHours()));
                            timetableVO.setTrialNumber(timetableVO.getTrialNumber()+s.getCourseHours());
                        }
                        if(CourseHoursOperationEnum.ENROLL.getDesc().equals(s.getOperationType())){
                            //timetableVO.setConsumeNumber(timetableVO.getConsumeNumber()+Math.abs(s.getCourseHours()));
                            timetableVO.setConsumeNumber(timetableVO.getConsumeNumber()+s.getCourseHours());
                        }

                    });
                    storeCourseHoursLogCache.put(k,timetableVO);
                });
            }
        });

        CompletableFuture<Void> task8 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.getRecords().stream().map(TimetableVO::getId).distinct()
                .toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }

            timetableEventAlarmDeatilsMapper.selectList(Wrappers.<TimetableEventAlarmDeatils>lambdaQuery()
                .in(TimetableEventAlarmDeatils::getTimeableId,list)
                .orderByAsc(TimetableEventAlarmDeatils::getEventTime))
                .stream().collect(Collectors.groupingBy(TimetableEventAlarmDeatils::getTimeableId)).forEach((k,v)->{
                    TimetableVO timetableVO = new TimetableVO();
                    v.forEach(s->{
                        if(StringUtils.isNotBlank(timetableVO.getReason())){
                            timetableVO.setReason(String.format("%s;%s%s",timetableVO.getReason(),DateTimeFormatter.ofPattern("HH:mm:ss").format(s.getEventTime()),s.getEventDescribe()));
                        }else {
                            timetableVO.setReason(String.format("%s%s", DateTimeFormatter.ofPattern("HH:mm:ss").format(s.getEventTime()),s.getEventDescribe()));
                        }
                    });
                    timetableEventAlarmDeatilsCache.put(k,timetableVO);
            });
        });


        CompletableFuture.allOf(task1,task2,task4,task5,task6,task7,task8).join();

        return page.convert(s->{
           TimetableVO timetableVO =  new TimetableVO();
            timetableVO.setExpectedNumber(0);
            timetableVO.setActualNumber(0);
            timetableVO.setGiftNumber(0);
            timetableVO.setTrialNumber(0);
            timetableVO.setConsumeNumber(0);
            s.setStoreName(storeCache.getOrDefault(s.getStoreId(),new CampusVO()).getCampusName());
            s.setClassName(classCache.getOrDefault(s.getClassId(),new ClassVO()).getCName());
            s.setTeacherName(classCache.getOrDefault(s.getClassId(),new ClassVO()).getHeadTeacherName());
           // s.setTeacherName(employeeCache.getOrDefault(s.getTeacherId(),new EmployeeVO()).getName());
            s.setExpectedNumber(classTimeStudentCache.getOrDefault(s.getLessonNo(),timetableVO).getExpectedNumber());
            s.setActualNumber(classTimeStudentCache.getOrDefault(s.getLessonNo(),timetableVO).getActualNumber());
            s.setGiftNumber(Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),timetableVO).getGiftNumber()));
            s.setTrialNumber(Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),timetableVO).getTrialNumber()));
            s.setConsumeNumber(Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),timetableVO).getConsumeNumber()));
            if(LocalDateTime.now().isBefore(s.getClassEndDateTime())){
                LessonVO orDefault = lessonCache.getOrDefault(String.format("%s#%s", s.getCourseId(), s.getLessonOrder()), new LessonVO());
                s.setLessonName(orDefault.getLessonName());
            }else {
                TeachingPlanDetailPubVO orDefault = teachingPlanDetailPubCache.getOrDefault(
                    String.format("%s#%s#%s", s.getTeachingPlanId(), s.getCourseId(),
                        s.getLessonOrder()), new TeachingPlanDetailPubVO());
                s.setLessonName(orDefault.getLessonName());
            }

            if(AlarmStatusEnum.ALARM_STATUS_0.code.equals(s.getAlarmStatus())){
                if(LocalDateTime.now().isAfter(s.getClassStartDateTime())){
                    s.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS_4.code);
                    s.setReason(AlarmStatusEnum.ALARM_STATUS_4.desc);
                }
            }else {
               s.setReason(timetableEventAlarmDeatilsCache.getOrDefault(s.getId(),timetableVO).getReason());
            }

            s.setClassStartTimeStr(String.format("%s-%s[%s]",
                LocalDateTime.of(s.getClassDate(),s.getClassStartTime()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                DateTimeFormatter.ofPattern("HH:mm").format(s.getClassEndTime()),
                s.getClassDate().getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINA)
            ));

            return s;
        });
    }

    @Override
    public TimetableVO fillInfoData(Timetable timetable) {
        TimetableVO timetableVO = new TimetableVO();
        timetableVO.setActualNames(new ArrayList<>());
        timetableVO.setExpectedNames(new ArrayList<>());
        timetableVO.setUnActualNames(new ArrayList<>());
        timetableVO.setPictureList(new ArrayList<>());

        timetablePictureMapper.selectList(Wrappers.<TimetablePicture>lambdaQuery().eq(TimetablePicture::getTimeableId,timetable.getId())).forEach(s->{
            TimetablePictureVO timetablePictureVO = new TimetablePictureVO();
            BeanUtils.copyProperties(s,timetablePictureVO);
            timetablePictureVO.setPhotoUrl(String.format("%s/%s",fileProperties.getOss().getCustomDomain(),s.getPhotoUrl()));
            timetableVO.getPictureList().add(timetablePictureVO);
        });


        R<List<BClassTimeStudentVO>> bClassTimeStudentList = remoteTimetableService.getBClassTimeStudentList(List.of(timetable.getLessonNo()));
        if(bClassTimeStudentList.isOk() && CollectionUtil.isNotEmpty(bClassTimeStudentList.getData())){
            Map<Long,String> studentCache = new HashMap<>();
            List<Long> list = bClassTimeStudentList.getData().stream()
                .map(BClassTimeStudentVO::getStudentId).distinct().toList();
            if(CollectionUtils.isNotEmpty(list)){
                R<List<StudentVO>> studentListByIds = remoteStudentService.getStudentListByIds(
                    list);
                if(studentListByIds.isOk() && CollectionUtil.isNotEmpty(studentListByIds.getData())){
                   studentListByIds.getData().forEach(s->{
                       studentCache.put(s.getUserId(),s.getName());
                   });
                }

            }
            bClassTimeStudentList.getData().forEach(s->{
                if(AdjustStatusEnum.ADJUST_STATUS_0.code.equals(s.getAdjustStatus())){
                    timetableVO.getExpectedNames().add(studentCache.getOrDefault(s.getStudentId(),"-"));
                    if(CheckInStatusEnum.CHECK_IN_STATUS_1.code.equals(s.getCheckInStatus())){
                        timetableVO.getActualNames().add(studentCache.getOrDefault(s.getStudentId(),"-"));
                    }
                    if(CheckInStatusEnum.CHECK_IN_STATUS_0.code.equals(s.getCheckInStatus())){
                        timetableVO.getUnActualNames().add(studentCache.getOrDefault(s.getStudentId(),"-"));
                    }
                }
            });

        }

        return timetableVO;
    }

    @Override
    public List<TimetableExportVO> fillExportData(List<TimetableVO> page) {
        if(CollectionUtils.isEmpty(page)){
            return new ArrayList<>();
        }

        Map<Long, CampusVO> storeCache = new HashMap();
        Map<Long, ClassVO> classCache = new HashMap();
        Map<String, LessonVO> lessonCache = new HashMap();
        Map<String, TeachingPlanDetailPubVO> teachingPlanDetailPubCache = new HashMap();
        Map<Long,TimetableVO> classTimeStudentCache = new HashMap();
        Map<Long,TimetableVO> storeCourseHoursLogCache = new HashMap();
        Map<String, List<TimetablePicture>> timetablePictureCache = new HashMap();
        Map<Long, CourseVO> courseCache = new HashMap();

        CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
            CampusDTO campusDTO = new CampusDTO();
            campusDTO.setSchoolIdList(
                page.stream().map(TimetableVO::getStoreId).distinct().toList());
            R<List<CampusVO>> campusList = remoteCampusService.getCampusList(campusDTO);
            if (campusList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(campusList.getData())) {
                campusList.getData().forEach(s -> {
                    storeCache.put(s.getId(), s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
            R<List<ClassVO>> classByIdList = remoteClassService
                .getClassByIdLists(
                    page.stream().map(TimetableVO::getClassId).distinct().toList());
            if (classByIdList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(classByIdList.getData())) {
                Map<Long, EmployeeVO> employeeCache = new HashMap();
                R<List<EmployeeVO>> employeeMapByIdList = remoteEmployeeService
                    .getEmployeeMapByIdList(classByIdList.getData().stream().filter(s->!Objects.isNull(s.getHeadTeacherId())).map(ClassVO::getHeadTeacherId).distinct().toList());
                if (employeeMapByIdList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(employeeMapByIdList.getData())){
                    employeeMapByIdList.getData().forEach(s->{
                        employeeCache.put(s.getUserId(),s);
                    });
                }

                classByIdList.getData().forEach(s -> {
                    classCache.put(Long.valueOf(s.getId()), s);
                    if(!Objects.isNull(employeeCache.get(s.getHeadTeacherId()))){
                        s.setHeadTeacherName(employeeCache.get(s.getHeadTeacherId()).getNickName());
                    }
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task4 = CompletableFuture.runAsync(() -> {
            R<List<LessonVO>> lessonListByOrder = remoteLessonService.getLessonListByOrder(
                page.stream().map(s -> s.getCourseId()).distinct().map(s -> {
                    LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
                    //lessonOrderDTO.setCourseId(s.getCourseId());
                    lessonOrderDTO.setCourseId(s);
                    //lessonOrderDTO.setLessonOrderList(Lists.asList(s.getLessonOrder()));
                    return lessonOrderDTO;
                }).toList());
            if (lessonListByOrder.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(lessonListByOrder.getData())){
                lessonListByOrder.getData().forEach(s->{
                    lessonCache.put(String.format("%s#%s",s.getCourseId(),s.getLessonOrder()),s);
                });
            }

        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task5 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.stream()
                .filter(s -> LocalDateTime.now().isAfter(s.getClassEndDateTime()))
                .map(TimetableVO::getTeachingPlanId).distinct().toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }
            R<List<TeachingPlanDetailPubVO>> teachingPlanDetailPubLiveChannel = remoteTeachingPlanDetailPubService.getTeachingPlanDetailPubLiveChannel(
                list);
            if(teachingPlanDetailPubLiveChannel.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(teachingPlanDetailPubLiveChannel.getData())){
                teachingPlanDetailPubLiveChannel.getData().forEach(s->{
                    teachingPlanDetailPubCache.put(String.format("%s#%s#%s",s.getPlanId(),s.getCourseId(),s.getLessonOrder()),s);
                });
            }
        },asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task6 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.stream().map(TimetableVO::getLessonNo).distinct()
                .toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }

            R<List<BClassTimeStudentVO>> bClassTimeStudentList = remoteTimetableService.getBClassTimeStudentList(
                list);
            if(bClassTimeStudentList.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(bClassTimeStudentList.getData())){
                bClassTimeStudentList.getData().stream().collect(Collectors.groupingBy(BClassTimeStudentVO::getLessonNo)).forEach((k,v)->{
                    TimetableVO timetableVO = new TimetableVO();
                    timetableVO.setExpectedNumber(0);
                    timetableVO.setActualNumber(0);
                    v.forEach(s->{
                        if(AdjustStatusEnum.ADJUST_STATUS_0.code.equals(s.getAdjustStatus())){
                            timetableVO.setExpectedNumber(timetableVO.getExpectedNumber()+1);
                            if(CheckInStatusEnum.CHECK_IN_STATUS_1.code.equals(s.getCheckInStatus())){
                                timetableVO.setActualNumber(timetableVO.getActualNumber()+1);
                            }
                        }
                    });

                    classTimeStudentCache.put(k,timetableVO);
                });
            }
        },asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task7 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.stream().map(TimetableVO::getId).distinct()
                .toList();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
                return;
            }

            R<List<StoreCourseHoursLogVO>> storeCourseHoursLogs = remoteStoreCourseHoursLogService.getStudentConsumeListByIds(
                list);

            if(storeCourseHoursLogs.isOk() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(storeCourseHoursLogs.getData())){
                storeCourseHoursLogs.getData().stream().collect(Collectors.groupingBy(StoreCourseHoursLogVO::getTimetableId)).forEach((k,v)->{
                    TimetableVO timetableVO = new TimetableVO();
                    timetableVO.setGiftNumber(0);
                    timetableVO.setTrialNumber(0);
                    timetableVO.setConsumeNumber(0);
                    v.forEach(s->{
                        if(CourseHoursOperationEnum.GIFT.getDesc().equals(s.getOperationType())){
                            // timetableVO.setGiftNumber(timetableVO.getGiftNumber()+Math.abs(s.getCourseHours()));
                            timetableVO.setGiftNumber(timetableVO.getGiftNumber()+s.getCourseHours());
                        }
                        if(CourseHoursOperationEnum.TRIAL.getDesc().equals(s.getOperationType())){
                            // timetableVO.setTrialNumber(timetableVO.getTrialNumber()+Math.abs(s.getCourseHours()));
                            timetableVO.setTrialNumber(timetableVO.getTrialNumber()+s.getCourseHours());
                        }
                        if(CourseHoursOperationEnum.ENROLL.getDesc().equals(s.getOperationType())){
                            //timetableVO.setConsumeNumber(timetableVO.getConsumeNumber()+Math.abs(s.getCourseHours()));
                            timetableVO.setConsumeNumber(timetableVO.getConsumeNumber()+s.getCourseHours());
                        }

                    });
                    storeCourseHoursLogCache.put(k,timetableVO);
                });
            }
        },asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task8 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.stream().map(TimetableVO::getCourseId).distinct()
                .toList();
            if(CollectionUtils.isEmpty(list)){
                return;
            }

            R<Map<Long, CourseVO>> courseMapByIdList = remoteCourseService.getCourseMapByIdList(
                list);

            if(courseMapByIdList.isOk() && CollectionUtils.isNotEmpty(courseMapByIdList.getData())){
                courseMapByIdList.getData().forEach((k,v)->{
                    courseCache.put(k,v);
                });
            }

        },asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task9 = CompletableFuture.runAsync(() -> {
            List<Long> timetableIdList = page.stream().map(TimetableVO::getId).distinct()
                .toList();
            List<Long> storeIdList = page.stream().map(TimetableVO::getStoreId).distinct().toList();
            if(CollectionUtils.isEmpty( timetableIdList) || CollectionUtils.isEmpty(storeIdList)){
                return;
            }

            timetablePictureMapper.selectList(Wrappers.<TimetablePicture>lambdaQuery()
                .in(TimetablePicture::getStoreId,storeIdList)
                .in(TimetablePicture::getTimeableId,timetableIdList)
            ).stream().collect(Collectors.groupingBy(s->String.format("%s#%s",s.getStoreId(),s.getTimeableId()))).forEach((k,v)->{
                timetablePictureCache.put(k,v);
            });

        },asyncConfiguration.getAsyncExecutor());


        CompletableFuture.allOf(task1,task2,task4,task5,task6,task7,task8,task9).join();

        return page.stream().map(s->{

            TimetableExportVO timetableExportVO = new TimetableExportVO();

            TimetableVO timetableVO =  new TimetableVO();
            timetableVO.setExpectedNumber(0);
            timetableVO.setActualNumber(0);
            timetableVO.setGiftNumber(0);
            timetableVO.setTrialNumber(0);
            timetableVO.setConsumeNumber(0);

            timetableExportVO.setStoreName(storeCache.getOrDefault(s.getStoreId(),new CampusVO()).getCampusName());
            timetableExportVO.setClassName(classCache.getOrDefault(s.getClassId(),new ClassVO()).getCName());
            timetableExportVO.setCourseName(courseCache.getOrDefault(s.getCourseId(),new CourseVO()).getCourseName());


            timetableExportVO.setExpectedNumber(classTimeStudentCache.getOrDefault(s.getLessonNo(),timetableVO).getExpectedNumber());
            timetableExportVO.setActualNumber(classTimeStudentCache.getOrDefault(s.getLessonNo(),timetableVO).getActualNumber());

            s.setGiftNumber(Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),timetableVO).getGiftNumber()));
            s.setTrialNumber(Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),timetableVO).getTrialNumber()));
            s.setConsumeNumber(Math.abs(storeCourseHoursLogCache.getOrDefault(s.getId(),timetableVO).getConsumeNumber()));

            if(s.getGiftNumber() + s.getTrialNumber() + s.getConsumeNumber() > 0){
                timetableExportVO.setIsAttendClass(ATTEND_CLASS_0.desc);
            }else {
                timetableExportVO.setIsAttendClass(ATTEND_CLASS_1.desc);
            }

            if(CourseTypeEnum.COURSE_TYPE_ENUM_1.code.equals(s.getCourseType())){
                timetableExportVO.setCourseTypeName(CourseTypeEnum.COURSE_TYPE_ENUM_1.desc);
            }else if(CourseTypeEnum.COURSE_TYPE_ENUM_2.code.equals(s.getCourseType())){
                timetableExportVO.setCourseTypeName(CourseTypeEnum.COURSE_TYPE_ENUM_2.desc);
            }else if(CourseTypeEnum.COURSE_TYPE_ENUM_3.code.equals(s.getCourseType())){
                timetableExportVO.setCourseTypeName(CourseTypeEnum.COURSE_TYPE_ENUM_3.desc);
            }

            if(LocalDateTime.now().isBefore(s.getClassEndDateTime())){
                LessonVO orDefault = lessonCache.getOrDefault(String.format("%s#%s", s.getCourseId(), s.getLessonOrder()), new LessonVO());
                timetableExportVO.setLessonName(orDefault.getLessonName());
            }else {
                TeachingPlanDetailPubVO orDefault = teachingPlanDetailPubCache.getOrDefault(
                    String.format("%s#%s#%s", s.getTeachingPlanId(), s.getCourseId(),
                        s.getLessonOrder()), new TeachingPlanDetailPubVO());
                timetableExportVO.setLessonName(orDefault.getLessonName());
            }

            timetableExportVO.setClassStartTimeStr(String.format("%s-%s[%s]",
                LocalDateTime.of(s.getClassDate(),s.getClassStartTime()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                DateTimeFormatter.ofPattern("HH:mm").format(s.getClassEndTime()),
                s.getClassDate().getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINA)
            ));

         timetablePictureCache.getOrDefault(String.format("%s#%s",s.getStoreId(),s.getId()),new ArrayList<>()).forEach(t->{
             if(Integer.valueOf(1).equals(t.getSort())){
                 timetableExportVO.setImage1Url(String.format("%s/%s",fileProperties.getOss().getCustomDomain(),t.getPhotoUrl()));
                 timetableExportVO.setImage1Number(t.getRecognitionHumanNum());
             }else if(Integer.valueOf(2).equals(t.getSort())){
                 timetableExportVO.setImage2Url(String.format("%s/%s",fileProperties.getOss().getCustomDomain(),t.getPhotoUrl()));
                 timetableExportVO.setImage2Number(t.getRecognitionHumanNum());
             }else if(Integer.valueOf(3).equals(t.getSort())){
                 timetableExportVO.setImage3Url(String.format("%s/%s",fileProperties.getOss().getCustomDomain(),t.getPhotoUrl()));
                 timetableExportVO.setImage3Number(t.getRecognitionHumanNum());
             }
         });


            return timetableExportVO;
        }).toList();
    }
}
