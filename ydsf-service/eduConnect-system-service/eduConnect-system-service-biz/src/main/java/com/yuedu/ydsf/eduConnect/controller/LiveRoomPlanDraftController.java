package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanDraftQuery;
import com.yuedu.ydsf.eduConnect.api.valid.LiveRoomPlanDraftValidGroup.EditPlan;
import com.yuedu.ydsf.eduConnect.api.valid.LiveRoomPlanDraftValidGroup.SavePlan;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDraft;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanDraftService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import jakarta.validation.constraints.NotEmpty;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 直播间计划草稿表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-29 08:28:00
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/LiveRoomPlanDraft" )
@Tag(description = "ea_live_room_plan_draft" , name = "直播间计划草稿表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Validated
public class LiveRoomPlanDraftController {

    private final  LiveRoomPlanDraftService liveRoomPlanDraftService;
    /**
     * 分页查询
     * @param page 分页对象
     * @param liveRoomId 直播间计划草稿表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("edusystem_LiveRoomPlanDraft_view")
    public R getLiveRoomPlanDraftPage(@ParameterObject Page page, @ParameterObject LiveRoomPlanDraftQuery liveRoomPlanDraftQuery) {

        return R.ok(liveRoomPlanDraftService.getPage(page, liveRoomPlanDraftQuery));
    }


    /**
     * 通过id查询直播间计划草稿表
     * @param id 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("edusystem_LiveRoomPlanDraft_detail")
    public R getDetails(@RequestParam(value = "id", defaultValue = "0") Integer id) {
        return R.ok(liveRoomPlanDraftService.getDetails(id));
    }

  /**
   * 新增直播间计划草稿表
   *
   * @param liveRoomPlanDraftQuery 直播间计划草稿表
   * @return R
   */
  @Operation(summary = "新增直播间计划草稿表", description = "新增直播间计划草稿表")
  @PostMapping("/add")
  @HasPermission("edusystem_LiveRoomPlanDraft_add")
  @Idempotent(expireTime = 5, info = "点击太快，休息一下吧！")
  public R save(
      @Validated(SavePlan.class) @RequestBody LiveRoomPlanDraftQuery liveRoomPlanDraftQuery) {
    liveRoomPlanDraftService.savePlan(liveRoomPlanDraftQuery);
    return R.ok();
  }

  /**
   * 修改直播间计划草稿表
   *
   * @param liveRoomPlanDraftQuery 直播间计划草稿表
   * @return R
   */
  @Operation(summary = "修改直播间计划草稿表", description = "修改直播间计划草稿表")
  @PutMapping("/edit")
  @HasPermission("edusystem_LiveRoomPlanDraft_edit")
  @Idempotent(expireTime = 5, info = "点击太快，休息一下吧！")
  public R updateById(
      @Validated(EditPlan.class) @RequestBody LiveRoomPlanDraftQuery liveRoomPlanDraftQuery) {
    liveRoomPlanDraftService.editPlan(liveRoomPlanDraftQuery);
    return R.ok();
  }

  /**
   * 通过id删除直播间计划草稿表
   *
   * @param ids id列表
   * @return R
   */
  @Operation(summary = "通过id删除直播间计划草稿表", description = "通过id删除直播间计划草稿表")
  @DeleteMapping("/delete")
  @HasPermission("edusystem_LiveRoomPlanDraft_del")
  @Idempotent(expireTime = 5, info = "点击太快，休息一下吧！")
  public R removeById(@NotEmpty(message = "请选择操作数据") @RequestBody Long[] ids) {
    liveRoomPlanDraftService.deletePlan(CollUtil.toList(ids));
    return R.ok();
  }

  /**
   * 发布直播间计划
   *
   * @param ids 计划ID列表
   * @param forcePublish 是否强制发布，默认false
   * @return R
   */
  @Operation(summary = "发布直播间计划", description = "发布直播间计划")
  @PutMapping("/publish")
  @HasPermission("edusystem_LiveRoomPlanDraft_publish")
  @Idempotent(expireTime = 5, info = "点击太快，休息一下吧！")
  public R publish(
      @NotEmpty(message = "请选择操作数据") @RequestBody Long[] ids,
      @RequestParam(defaultValue = "false") Boolean forcePublish) {
    liveRoomPlanDraftService.publish(CollUtil.toList(ids), forcePublish);
    return R.ok();
  }

    /**
     * 导出excel 表格
     * @param liveRoomPlanDraft 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("edusystem_LiveRoomPlanDraft_export")
    public List<LiveRoomPlanDraft> exportExcel(LiveRoomPlanDraft liveRoomPlanDraft,Long[] ids) {
        return liveRoomPlanDraftService.list(Wrappers.lambdaQuery(liveRoomPlanDraft).in(ArrayUtil.isNotEmpty(ids), LiveRoomPlanDraft::getId, ids));
    }

    /**
     * 导入excel 表
     * @param liveRoomPlanDraftList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("edusystem_LiveRoomPlanDraft_export")
    public R importExcel(@RequestExcel List<LiveRoomPlanDraft> liveRoomPlanDraftList, BindingResult bindingResult) {
        return R.ok(liveRoomPlanDraftService.saveBatch(liveRoomPlanDraftList));
    }
}
