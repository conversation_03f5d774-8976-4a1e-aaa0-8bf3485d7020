package com.yuedu.ydsf.eduConnect.constant;

import lombok.AllArgsConstructor;

/**
 * 识别状态枚举
 *
 * <AUTHOR>
 * @date 2025/02/13
 */
@AllArgsConstructor
public enum RecognitionStatusEnum {
    /**
     * 未处理
     */
    RECOGNITION_STATUS_0(0, "未处理"),
    /**
     * 处理成功
     */
    RECOGNITION_STATUS_1(1, "处理成功"),
    /**
     * 处理失败
     */
    RECOGNITION_STATUS_2(2, "处理失败");

    public final Integer code;

    public final String desc;
}