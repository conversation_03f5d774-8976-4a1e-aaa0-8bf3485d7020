package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.TeachingPageTemplateDTO;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanDetailDraftQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailDraftVO;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailDraft;
import java.util.List;
import java.util.Map;

/**
 * 教学计划明细草稿表 服务类
 *
 * <AUTHOR>
 * @date 2024-12-03 09:52:18
 */
public interface TeachingPlanDetailDraftService extends IService<TeachingPlanDetailDraft> {

    /**
     * 查询教学课节明细总数
     *
     * @return List<TeachingPlanDetailDraftVO>
     */
    Map<Long, Integer> countTeachs(List<Long> ids);

    /**
     * 查询全部教学计划明细信息
     *
     * @return List<TeachingPlanDetailDraftVO>
     */
    List<TeachingPlanDetailDraftVO> listPlans(Integer planId, Long courseId,Long id);

    /**
     * 修改主讲老师
     *
     * @return Boolean
     */
    Boolean updateById(TeachingPlanDetailDraftQuery teachingPlanDetailDraftQuery);
}
