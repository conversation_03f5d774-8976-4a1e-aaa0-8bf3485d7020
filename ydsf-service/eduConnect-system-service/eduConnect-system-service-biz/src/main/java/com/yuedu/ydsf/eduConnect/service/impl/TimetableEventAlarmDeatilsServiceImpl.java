package com.yuedu.ydsf.eduConnect.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.eduConnect.mapper.TimetableEventAlarmDeatilsMapper;
import com.yuedu.ydsf.eduConnect.service.TimetableEventAlarmDeatilsService;
import com.yuedu.ydsf.eduConnect.api.query.TimetableEventAlarmDeatilsQuery;
import com.yuedu.ydsf.eduConnect.api.dto.TimetableEventAlarmDeatilsDTO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableEventAlarmDeatilsVO;
import com.yuedu.ydsf.eduConnect.entity.TimetableEventAlarmDeatils;

import java.io.Serializable;
import java.util.Optional;
import java.util.List;


/**
* 课次报警明细表服务层
*
* <AUTHOR>
* @date  2025/03/10
*/
@Service
public class TimetableEventAlarmDeatilsServiceImpl extends ServiceImpl<TimetableEventAlarmDeatilsMapper,TimetableEventAlarmDeatils>
    implements TimetableEventAlarmDeatilsService{


    /**
     * 课次报警明细表分页查询
     *
     * @param page 分页对象
     * @param timetableEventAlarmDeatilsQuery 课次报警明细表
     * @return IPage 分页结果
     */
    @Override
    public IPage<TimetableEventAlarmDeatilsVO> page(Page page,TimetableEventAlarmDeatilsQuery timetableEventAlarmDeatilsQuery) {
        return page(page, Wrappers.<TimetableEventAlarmDeatils>lambdaQuery())
                .convert(entity -> {
                    TimetableEventAlarmDeatilsVO timetableEventAlarmDeatilsVO = new TimetableEventAlarmDeatilsVO();
                    BeanUtils.copyProperties(entity, timetableEventAlarmDeatilsVO);
                    return timetableEventAlarmDeatilsVO;
                });
    }


    /**
     * 根据ID获得课次报警明细表信息
     *
     * @param id id
     * @return TimetableEventAlarmDeatilsVO 详细信息
     */
    @Override
    public TimetableEventAlarmDeatilsVO getInfoById(Serializable id) {
        return Optional.of(getById(id))
                .map(entity -> {
                    TimetableEventAlarmDeatilsVO timetableEventAlarmDeatilsVO = new TimetableEventAlarmDeatilsVO();
                    BeanUtils.copyProperties(entity, timetableEventAlarmDeatilsVO);
                    return timetableEventAlarmDeatilsVO;
                })
                .orElseThrow(()-> new CheckedException("查询结果为空"));
    }


    /**
     * 新增课次报警明细表
     *
     * @param timetableEventAlarmDeatilsDTO 课次报警明细表
     * @return boolean 执行结果
     */
    @Override
    public boolean add(TimetableEventAlarmDeatilsDTO timetableEventAlarmDeatilsDTO) {
        TimetableEventAlarmDeatils timetableEventAlarmDeatils = new TimetableEventAlarmDeatils();
        BeanUtils.copyProperties(timetableEventAlarmDeatilsDTO, timetableEventAlarmDeatils);
        return save(timetableEventAlarmDeatils);
    }


    /**
     * 修改课次报警明细表
     *
     * @param timetableEventAlarmDeatilsDTO 课次报警明细表
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(TimetableEventAlarmDeatilsDTO timetableEventAlarmDeatilsDTO) {
        TimetableEventAlarmDeatils timetableEventAlarmDeatils = new TimetableEventAlarmDeatils();
        BeanUtils.copyProperties(timetableEventAlarmDeatilsDTO, timetableEventAlarmDeatils);
        return updateById(timetableEventAlarmDeatils);
    }


    /**
     * 导出excel 课次报警明细表表格
     *
     * @param timetableEventAlarmDeatilsQuery 查询条件
     * @param ids 导出指定ID
     * @return List<TimetableEventAlarmDeatilsVO> 结果集合
     */
    @Override
    public List<TimetableEventAlarmDeatilsVO> export(TimetableEventAlarmDeatilsQuery timetableEventAlarmDeatilsQuery, Long[] ids) {
        return list(Wrappers.<TimetableEventAlarmDeatils>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), TimetableEventAlarmDeatils::getId, ids))
            .stream()
            .map(entity -> {
                TimetableEventAlarmDeatilsVO timetableEventAlarmDeatilsVO = new TimetableEventAlarmDeatilsVO();
                BeanUtils.copyProperties(entity, timetableEventAlarmDeatilsVO);
                return timetableEventAlarmDeatilsVO;
            }).toList();
    }

}
