package com.yuedu.ydsf.eduConnect.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.api.query.TimetableQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.entity.Timetable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 门店课表 持久层
 *
 * <AUTHOR>
 * @date 2025-01-10 14:27:17
 */
@Mapper
public interface TimetableMapper extends YdsfBaseMapper<Timetable> {

    /**
     *  更新报警状态
     *
     * <AUTHOR>
     * @date 2025年03月11日 09时32分
     */
    void updateAlarmStatus(TimetableDTO timetableDTO);


    /**
     *  更新识别人数
     *
     * <AUTHOR>
     * @date 2025年03月11日 09时32分
     */
    void updateIdentifyNum(TimetableDTO timetableDTO);


    /**
     *  分页查询
     *
     * <AUTHOR>
     * @date 2025年03月11日 15时02分
     */
    IPage<TimetableVO> page(Page page,@Param("query") TimetableQuery timetableQuery);



    /**
     *  根据门店ID获得已约课的课程
     *
     * <AUTHOR>
     * @date 2025年06月05日 16时36分
     */
    List<Timetable> getCourseListByStoreId(Long storeId);


    /**
     *  课消导出列表
     *
     * <AUTHOR>
     * @date 2025年07月08日 09时27分
     */
    List<TimetableVO> export(@Param("query") TimetableQuery timetableQuery);
}
