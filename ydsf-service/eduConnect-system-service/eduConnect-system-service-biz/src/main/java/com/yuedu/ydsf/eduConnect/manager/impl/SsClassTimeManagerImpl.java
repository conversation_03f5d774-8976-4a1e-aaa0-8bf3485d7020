package com.yuedu.ydsf.eduConnect.manager.impl;

import static com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum.REQUEST_ERROR;
import static com.yuedu.ydsf.eduConnect.constant.Constants.YYYY_MM_DD_HH_MM_SS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.api.feign.RemoteClassRoomService;
import com.yuedu.store.api.feign.RemoteLecturerService;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.dto.ClassRoomDTO;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.admin.api.entity.SysDictItem;
import com.yuedu.ydsf.admin.api.feign.RemoteDictService;
import com.yuedu.ydsf.common.core.config.AsyncConfiguration;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.api.constant.ActiveEnum;
import com.yuedu.ydsf.eduConnect.api.constant.AppointmentEnum;
import com.yuedu.ydsf.eduConnect.api.constant.AttendClassStateEnum;
import com.yuedu.ydsf.eduConnect.api.constant.AuthRoomAppointmentTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.AuthRoomLogEditAuthTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.AuthState;
import com.yuedu.ydsf.eduConnect.api.constant.ClassTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DelFlagEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncAgoraEnum;
import com.yuedu.ydsf.eduConnect.api.constant.IsSyncXiaogjEnum;
import com.yuedu.ydsf.eduConnect.api.constant.SsClassStateEnum;
import com.yuedu.ydsf.eduConnect.api.dto.ClassAuthRoomDTO;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeAfternoonVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeClassTimeVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeEveningVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeFridayVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeMondayVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeMorningVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeSaturdayVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeSundayVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeThursdayVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeTuesdayVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.ClassRoomFreeTimeWednesdayVo;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassRoomFreeTimeVO.Weekday;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.config.SsProperty;
import com.yuedu.ydsf.eduConnect.entity.SsAuthRoomLog;
import com.yuedu.ydsf.eduConnect.entity.SsClass;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent;
import com.yuedu.ydsf.eduConnect.entity.SsClassTime;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeAuthRoom;
import com.yuedu.ydsf.eduConnect.entity.SsClassTimeStudent;
import com.yuedu.ydsf.eduConnect.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.entity.SsScreenshotDetail;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjPushTask;
import com.yuedu.ydsf.eduConnect.manager.SsClassTimeManager;
import com.yuedu.ydsf.eduConnect.manager.SsXiaogjLogManager;
import com.yuedu.ydsf.eduConnect.mapper.SsAuthRoomLogMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassAuthRoomStudentMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeAuthRoomMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsClassTimeMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsScreenshotDetailMapper;
import com.yuedu.ydsf.eduConnect.mapper.SsXiaogjPushTaskMapper;
import com.yuedu.ydsf.eduConnect.service.SsClassAuthRoomService;
import com.yuedu.ydsf.eduConnect.service.SsClassService;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeAuthRoomService;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeStudentService;
import com.yuedu.ydsf.eduConnect.service.SsDeviceService;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AgoraTokenGenerator;
import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.AgoraProperties;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.AgoraConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.SsPushXiaogjType;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.XiaoGuanJiaConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.ClassCourseReq.CreateClassType;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateAgoraClassRoomDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateCourseReq.CourseData;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.DeleteCourseReq;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.SsPushXiaogjEventReq;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import com.yuedu.ydsf.eduConnect.manager.XiaoGuanJiaService;
import com.yuedu.ydsf.eduConnect.util.DateUtil;
import com.yuedu.ydsf.eduConnect.util.DateUtils;
import com.yuedu.ydsf.eduConnect.util.StringUtil;
import java.lang.reflect.Field;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 课次处理层
 *
 * @author: KL
 * @date: 2024/10/15
 **/
@Slf4j
@Component
@AllArgsConstructor
public class SsClassTimeManagerImpl implements SsClassTimeManager {

    private final SsClassTimeMapper ssClassTimeMapper;
    private final SsXiaogjPushTaskMapper ssXiaogjPushTaskMapper;
    private final SsClassAuthRoomMapper ssClassAuthRoomMapper;

    private SsClassTimeAuthRoomMapper ssClassTimeAuthRoomMapper;

    private SsClassTimeAuthRoomService ssClassTimeAuthRoomService;

    private final AgoraTokenGenerator agoraTokenGenerator;

    private SsClassService ssClassService;

    private final AsyncConfiguration asyncConfiguration;

    private RemoteLecturerService remoteLecturerService;

    private SsDeviceService ssDeviceService;

    private RemoteCampusService remoteCampusService;

    private RemoteClassRoomService remoteClassRoomService;

    private final AgoraService agoraService;

    private final SsClassAuthRoomStudentMapper authRoomStudentMapper;

    private final SsClassAuthRoomService ssClassAuthRoomService;

    private final SsClassTimeAuthRoomService classTimeAuthRoomService;

    private final SsClassTimeStudentService classTimeStudentService;

    private final XiaoGuanJiaService xiaoGuanJiaService;

    private SsClassTimeStudentService ssClassTimeStudentService;

    private SsClassAuthRoomStudentMapper ssClassAuthRoomStudentMapper;

    private SsAuthRoomLogMapper ssAuthRoomLogMapper;

    private SsProperty ssProperty;

    private RemoteDictService remoteDictService;

    private AgoraProperties agoraProperties;

    private final SsXiaogjLogManager xiaogjLogManager;

    private final SsScreenshotDetailMapper ssScreenshotDetailMapper;

    /**
     * 获取RTM TOKEN
     *
     * @param roomUuid
     * @param s
     * @param aShort
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/16 10:34
     */
    @Override
    public String getAgoraRtmToken(String roomUuid, String s, Short aShort) {
        return agoraTokenGenerator.getAgoraRtmToken(roomUuid, s, aShort);
    }

    @Override
    public IPage<SsClassTimeVO> fillData(IPage<SsClassTimeVO> page) {
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }

        Map<Long, Long> cacheAuthRoomMap = new HashMap<>();
        Map<Long, SsClass> cacheClassMap = new HashMap<>();
        Map<Long, LecturerVO> cacheLectureMap = new HashMap<>();
        Map<Long, SsScreenshotDetail> screenshotDetailMap = new HashMap<>();

        // 异步查询并缓存教室授权数量
        CompletableFuture<Void> authRoomFuture = CompletableFuture.runAsync(() -> {
            List<Long> ids = page.getRecords().stream().map(SsClassTimeVO::getId).toList();
            ssClassTimeAuthRoomMapper.selectAuthRoomCountByClassTimeIds(ids)
                .forEach(s -> {
                    cacheAuthRoomMap.put(s.getClassTimeId(), s.getAuthRoomCount());
                });
        });
        // 异步查询并缓存课程信息
        CompletableFuture<Void> classFuture = CompletableFuture.runAsync(() -> {
            List<Long> ids = page.getRecords().stream().map(SsClassTimeVO::getClassId).toList();
            ssClassService.list(Wrappers.<SsClass>lambdaQuery().in(SsClass::getId, ids))
                .forEach(s -> cacheClassMap.put(s.getId(), s));
        });
        // 异步查询并缓存讲师信息
        CompletableFuture<Void> LectureFuture = CompletableFuture.runAsync(() -> {
            List<Long> ids = page.getRecords().stream().map(SsClassTimeVO::getLecturerId).toList();
            LecturerDTO lecturerDTO = new LecturerDTO();
            lecturerDTO.setIds(ids);
            R<List<LecturerVO>> lecturerList = remoteLecturerService.getLecturerList(lecturerDTO);
            if (lecturerList.isOk() && CollectionUtils.isNotEmpty(lecturerList.getData())) {
                lecturerList.getData().forEach(s -> {
                    cacheLectureMap.put(s.getId(), s);
                });
            }

        });

        // 异步查询并缓存是否截图信息
        CompletableFuture<Void> screenshotDetailFuture = CompletableFuture.runAsync(() -> {
            List<Long> classTimeIdList = page.getRecords().stream().map(SsClassTimeVO::getId).toList();
            List<SsScreenshotDetail> ssScreenshotDetailList = ssScreenshotDetailMapper.selectList(
                Wrappers.<SsScreenshotDetail>lambdaQuery()
                    .eq(SsScreenshotDetail::getDelFlag, DelFlagEnum.DELFLAG_0.code)
                    .in(SsScreenshotDetail::getClassTimeId, classTimeIdList)
                    .groupBy(SsScreenshotDetail::getClassTimeId)
            );

            ssScreenshotDetailList.forEach(s -> {
                screenshotDetailMap.put(s.getClassTimeId(), s);
            });

        });

        CompletableFuture.allOf(authRoomFuture, classFuture, LectureFuture, screenshotDetailFuture).join();
        // 返回处理后的页面数据
        return page.convert(ssClassTimeVO -> {

            ssClassTimeVO.setAuthRoomCount(
                cacheAuthRoomMap.getOrDefault(ssClassTimeVO.getId(), 0L));

            if (cacheClassMap.containsKey(ssClassTimeVO.getClassId())) {
                ssClassTimeVO.setClassName(
                    cacheClassMap.get(ssClassTimeVO.getClassId()).getClassName());
            }

            if (cacheLectureMap.containsKey(ssClassTimeVO.getLecturerId())) {
                ssClassTimeVO.setLecturerName(
                    cacheLectureMap.get(ssClassTimeVO.getLecturerId()).getLecturerName());
            }

            ssClassTimeVO.setIsScreenshot(screenshotDetailMap.containsKey(ssClassTimeVO.getId()) ? true : false);

            return ssClassTimeVO;
        });
    }


    @Override
    public List<SsDeviceVO> selectAuthClassRoom(SsClassTime ssClassTime) {

        //获取所有设备
        List<SsDevice> allDevices = ssDeviceService.list(Wrappers.<SsDevice>lambdaQuery()
            .eq(SsDevice::getDeviceType, DeviceTypeEnum.DEVICETYPE_2.code)
            .eq(SsDevice::getDeviceActive, ActiveEnum.DEVICEACTIVE_1.code)
            .ne(SsDevice::getClassRoomId, -1)
        );

        if (CollectionUtils.isEmpty(allDevices)) {
            return Lists.newArrayList();
        }

        Map<Long, CampusVO> cacheCampusMap = new HashMap<>();
        Map<Long, ClassRoomVO> cacheClassRoomMap = new HashMap<>();
        Map<Long, SsDevice> cacheDeviceMap = new HashMap<>();

        //查询校区
        CompletableFuture<Void> campusFuture = CompletableFuture.runAsync(() -> {
            CampusDTO campusDTO = new CampusDTO();
            campusDTO.setSchoolIdList(allDevices.stream().map(s -> s.getCampusId()).toList());
            R<List<CampusVO>> campusList = remoteCampusService.getCampusList(campusDTO);
            if (campusList.isOk() && CollectionUtils.isNotEmpty(campusList.getData())) {
                campusList.getData().forEach(s -> {
                    cacheCampusMap.put(s.getId(), s);
                });
            }

        });
        //查询教室
        CompletableFuture<Void> classRoomFuture = CompletableFuture.runAsync(() -> {
            ClassRoomDTO classRoomDTO = new ClassRoomDTO();
            classRoomDTO.setClassRoomIdList(
                allDevices.stream().map(s -> s.getClassRoomId()).toList());
            R<List<ClassRoomVO>> classRoomList = remoteClassRoomService.getList(classRoomDTO);
            if (classRoomList.isOk() && CollectionUtils.isNotEmpty(classRoomList.getData())) {
                classRoomList.getData().forEach(s -> {
                    cacheClassRoomMap.put(s.getId(), s);
                });
            }

        });

        //查询已授权设备
        CompletableFuture<Void> deviceFuture = CompletableFuture.runAsync(() -> {

            Optional.ofNullable(ssClassTimeAuthRoomService.list(
                    Wrappers.<SsClassTimeAuthRoom>lambdaQuery()
                        .eq(SsClassTimeAuthRoom::getClassTimeId, ssClassTime.getId())))
                .filter(s -> !s.isEmpty())
                .ifPresent(authRooms -> {
                    ssDeviceService.list(Wrappers.<SsDevice>lambdaQuery()
                        .in(SsDevice::getId, authRooms.stream().map(s -> s.getDeviceId()).toList())
                        .eq(SsDevice::getDeviceType, DeviceTypeEnum.DEVICETYPE_2.code)
                        .eq(SsDevice::getDeviceActive, ActiveEnum.DEVICEACTIVE_1.code)
                        .ne(SsDevice::getClassRoomId, -1)
                    ).stream().forEach(s -> {
                        cacheDeviceMap.put(s.getId(), s);
                    });
                });
        });

        CompletableFuture.allOf(campusFuture, classRoomFuture, deviceFuture).join();

        //合并处理
        return allDevices.stream().map(entity -> {
            SsDeviceVO ssDeviceVO = new SsDeviceVO();
            BeanUtils.copyProperties(entity, ssDeviceVO);
            if (cacheDeviceMap.containsKey(entity.getId())) {
                ssDeviceVO.setAuthState(AuthState.AUTH_STATE_1.code);
            } else {
                ssDeviceVO.setAuthState(AuthState.AUTH_STATE_0.code);
            }

            if (cacheCampusMap.containsKey(entity.getCampusId())) {
                ssDeviceVO.setCampusName(cacheCampusMap.get(entity.getCampusId()).getCampusName());
                ssDeviceVO.setXgjCampusId(
                    cacheCampusMap.get(entity.getCampusId()).getXgjCampusId());
            }

            if (cacheClassRoomMap.containsKey(entity.getClassRoomId())) {
                ssDeviceVO.setClassRoomName(
                    cacheClassRoomMap.get(entity.getClassRoomId()).getClassRoomName());
                ssDeviceVO.setXgjClassRoomId(
                    cacheClassRoomMap.get(entity.getClassRoomId()).getXgjClassRoomId());
            }

            return ssDeviceVO;
        }).toList();
    }

    /**
     * 返回项目中的线程池
     *
     * @return java.util.concurrent.Executor
     * <AUTHOR>
     * @date 2024/10/16 10:37
     */
    @Override
    public Executor getAsyncExecutor() {
        return asyncConfiguration.getAsyncExecutor();
    }

    /**
     * 查询班级信息
     *
     * @param classId
     * @return com.yuedu.ydsf.eduConnect.entity.SsClass
     * <AUTHOR>
     * @date 2024/10/16 11:30
     */
    @Override
    public SsClass getClassById(Long classId) {
        return ssClassService.getById(classId);
    }

    /**
     * 获取课程状态
     *
     * @param ssClassTimeVO
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:40
     */
    @Override
    public void setClassTimeAttendStateAndData(SsClassTimeVO ssClassTimeVO) {
        LocalDateTime localDateTime = LocalDateTime.now();

        // 校验课程状态
        LocalDateTime attendClassDateStartTime = ssClassTimeVO.getAttendClassDateStartTime();
        LocalDateTime attendClassDateEndTime = ssClassTimeVO.getAttendClassDateEndTime();

        // 当前时间戳
        long currentTime = System.currentTimeMillis();

        // 上课开始日期转换为时间戳
        Instant instant1 = attendClassDateStartTime.atZone(ZoneId.systemDefault()).toInstant();
        long attendClassDateStartTimeLong = instant1.toEpochMilli();

        // 进行中
        if (attendClassDateStartTime.isBefore(localDateTime) && attendClassDateEndTime.isAfter(
            localDateTime)) {
            ssClassTimeVO.setAttendClassState(AttendClassStateEnum.ATTEND_CLASS_STATE_ENUM_0.CODE);
            ssClassTimeVO.setHasStartTime(currentTime - attendClassDateStartTimeLong);
            return;
        }

        // 未开始
        if (attendClassDateStartTime.isAfter(localDateTime)) {
            ssClassTimeVO.setAttendClassState(AttendClassStateEnum.ATTEND_CLASS_STATE_ENUM_3.CODE);
            ssClassTimeVO.setDistanceStartTime(attendClassDateStartTimeLong - currentTime);
            return;
        }

        // 已结束
        if (attendClassDateEndTime.isBefore(localDateTime)) {
            ssClassTimeVO.setAttendClassState(AttendClassStateEnum.ATTEND_CLASS_STATE_ENUM_4.CODE);
        }

    }

    /**
     * 同步声网创建房间
     *
     * @param ssClassTimeDto
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:06
     */
    @Override
    public void syncAgoraClassRoom(SsClassTimeDTO ssClassTimeDto) {
        if (!ClassTypeEnum.ATTENDCLASSTYPE_0.CODE.equals(ssClassTimeDto.getAttendClassType())) {
            return;
        }
        LocalDateTime attendClassDateStartTime =
            LocalDateTime.of(
                ssClassTimeDto.getAttendClassDate(), ssClassTimeDto.getAttendClassStartTime());
        LocalDateTime attendClassDateEndTime =
            LocalDateTime.of(
                ssClassTimeDto.getAttendClassDate(), ssClassTimeDto.getAttendClassEndTime());
        Duration duration = Duration.between(LocalDateTime.now(), attendClassDateStartTime);
        if (duration.toMinutes() > AgoraConstant.DEFAULT_TIME_MINUTE) {
            ssClassTimeDto.setRoomUuid(null);
            ssClassTimeDto.setIsSyncAgora(IsSyncAgoraEnum.ISSYNCAGORA_0.CODE());
            return;
        }
        ssClassTimeDto.setRoomUuid(UUID.randomUUID().toString());
        CreateAgoraClassRoomDTO createAgoraClassRoomDTO =
            convertToCreateAgoraClassRoomDTO(
                ssClassTimeDto, attendClassDateStartTime, attendClassDateEndTime);
        String result = agoraService.syncAgoraClassRoom(createAgoraClassRoomDTO);
        if (StringUtils.isBlank(result)) {
            log.error("同步声网创建房间失败，返回结果: {}", result);
            throw new BizException("同步声网创建房间失败！");
        }
        ssClassTimeDto.setIsSyncAgora(IsSyncAgoraEnum.ISSYNCAGORA_1.CODE());
        ssClassTimeDto.setRoomUuid(result);
    }

    private CreateAgoraClassRoomDTO convertToCreateAgoraClassRoomDTO(SsClassTimeDTO ssClassTimeDto,
        LocalDateTime startTime, LocalDateTime endTime) {
        CreateAgoraClassRoomDTO dto = new CreateAgoraClassRoomDTO();
        dto.setRoomUuid(ssClassTimeDto.getRoomUuid());
        dto.setClassId(ssClassTimeDto.getClassId());
        dto.setCourseScheduleId(ssClassTimeDto.getCourseScheduleId());
        dto.setCourseScheduleBooksId(ssClassTimeDto.getCourseScheduleBooksId());
        dto.setCourseScheduleRuleId(ssClassTimeDto.getCourseScheduleRuleId());
        dto.setAttendClassDate(ssClassTimeDto.getAttendClassDate());
        dto.setAttendClassStartTime(ssClassTimeDto.getAttendClassStartTime());
        dto.setAttendClassEndTime(ssClassTimeDto.getAttendClassEndTime());
        dto.setIsSyncAgora(ssClassTimeDto.getIsSyncAgora());
        dto.setAttendClassType(ssClassTimeDto.getAttendClassType());
        dto.setSupervisionClassUrl(ssClassTimeDto.getSupervisionClassUrl());
        dto.setSupervisionClassStartTime(startTime);
        dto.setSupervisionClassEndTime(endTime);
        dto.setLecturerId(ssClassTimeDto.getLecturerId());
        dto.setDeviceId(ssClassTimeDto.getDeviceId());
        dto.setClassRoomId(ssClassTimeDto.getClassRoomId());
        dto.setBooksId(ssClassTimeDto.getBooksId());
        dto.setBooksName(ssClassTimeDto.getBooksName());
        dto.setRecordingId(ssClassTimeDto.getRecordingId());
        dto.setLecturerRoomCode(ssClassTimeDto.getLecturerRoomCode());
        dto.setClassRoomCode(ssClassTimeDto.getClassRoomCode());
        return dto;
    }

    /**
     * 创建课次
     *
     * @param newClassTime
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:08
     */
    @Override
    public void saveNewClassTime(SsClassTime newClassTime) {
        ssClassTimeMapper.insert(newClassTime);
    }

    /**
     * 查询主讲老师信息
     *
     * @param lecturerId
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:12
     */
    @Override
    public LecturerVO getLecturerInfoById(Long lecturerId) {
        try {
            R<LecturerVO> lecturer = remoteLecturerService.getLecture(lecturerId);
            if (lecturer != null && lecturer.isOk()) {
                return lecturer.getData();
            }
            log.warn("远程调用获取教师信息失败或返回数据为空");
            return new LecturerVO();
        } catch (Exception e) {
            log.error("远程调用获取教师信息异常", e);
            return new LecturerVO();
        }
    }

    /**
     * 查询班级下指定授权设备,指定状态的课次信息
     *
     * @param classTimeDto
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
     * <AUTHOR>
     * @date 2024/10/16 14:21
     */
    @Override
    public List<SsClassTimeVO> selectDeviceClassTimeByClassId(SsClassTimeDTO classTimeDto) {
        return ssClassTimeMapper.getDeviceClassTimeByClassId(classTimeDto);
    }


    /**
     * 查询班级已添加学生信息
     *
     * @param id
     * @param campusIdList
     * @return java.util.List<com.yuedu.ydsf.eduConnect.entity.SsClassAuthRoomStudent>
     * <AUTHOR>
     * @date 2024/10/16 14:27
     */
    @Override
    public List<SsClassAuthRoomStudent> ssClassAuthRoomStudentSelectList(Long id,
        List<Long> campusIdList) {
        return authRoomStudentMapper.selectList(Wrappers.lambdaQuery(SsClassAuthRoomStudent.class)
            .eq(SsClassAuthRoomStudent::getClassId, id)
            .in(SsClassAuthRoomStudent::getCampusId, campusIdList));
    }


    /**
     * 单次授权更新对应的字段
     *
     * @param ssClassAuthRoom
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:32
     */
    @Override
    public void ssClassAuthRoomupdateById(SsClassAuthRoom ssClassAuthRoom) {
        ssClassAuthRoomService.updateById(ssClassAuthRoom);
    }

    /**
     * 功能描述
     *
     * @param ssClassTimeAuthRoom
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:38
     */
    @Override
    public void ssClassTimeAuthRoomSave(SsClassTimeAuthRoom ssClassTimeAuthRoom) {
        classTimeAuthRoomService.save(ssClassTimeAuthRoom);
    }


    /**
     * 保存课次学生
     *
     * @param saveSsClassTimeStudentList
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 14:47
     */
    @Override
    public void ssClassTimeStudentSaveBatch(List<SsClassTimeStudent> saveSsClassTimeStudentList) {
        classTimeStudentService.saveBatch(saveSsClassTimeStudentList);
    }

    /**
     * 通过班级ID获取班级信息
     *
     * @param classId
     * @return com.yuedu.ydsf.eduConnect.entity.SsClass
     * <AUTHOR>
     * @date 2024/10/16 15:14
     */
    @Override
    public SsClass ClassGetById(Long classId) {
        return ssClassService.getById(classId);
    }

    /**
     * 删除课次授权教室
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/10/18 14:38
     */
    @Override
    public void deleteClassTimeAuthRoomByIds(Long[] ids) {
        ssClassTimeAuthRoomService.remove(
            Wrappers.lambdaQuery(SsClassTimeAuthRoom.class)
                .in(SsClassTimeAuthRoom::getClassTimeId, Arrays.asList(ids)));
    }

    /**
     * 删除课次授权教室下得学生
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2024/10/18 14:41
     */
    @Override
    public void deleteClassTimeStudent(Long[] ids) {
        classTimeStudentService.remove(
            Wrappers.lambdaQuery(SsClassTimeStudent.class)
                .in(SsClassTimeStudent::getClassTimeId, Arrays.asList(ids)));
    }

    /**
     * 根据id更新班级授权教室信息
     *
     * @param ssClassAuthRoom
     * @return void
     * <AUTHOR>
     * @date 2024/10/18 14:50
     */
    @Override
    public void classAuthRoomUpdateById(SsClassAuthRoom ssClassAuthRoom) {
        ssClassAuthRoomService.updateById(ssClassAuthRoom);
    }

    /**
     * 根据id获取班级信息
     *
     * @return java.util.Map<java.lang.Long, com.yuedu.ydsf.eduConnect.entity.SsClass>
     * <AUTHOR>
     * @date 2024/10/18 15:08
     */
    @Override
    public Map<Long, SsClass> getClassMapByIds(Set<Long> classIds) {
        if (CollUtil.isEmpty(classIds)) {
            return new HashMap<>();
        }
        List<SsClass> classList = ssClassService.listByIds(classIds);
        return classList.stream().collect(Collectors.toMap(SsClass::getId, Function.identity()));
    }

    /**
     * 根据id获取课次信息
     *
     * @param ids
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO>
     * <AUTHOR>
     * @date 2024/10/18 15:08
     */
    @Override
    public List<SsClassTimeVO> selectDeviceClassTimeByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return ssClassTimeMapper.selectDeviceClassTimeByIds(ids);
    }

    /**
     * 批量更新授权教室信息
     *
     * @param updateAuthRoomList
     * @return void
     * <AUTHOR>
     * @date 2024/10/18 15:13
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchClassAuthRoom(List<SsClassAuthRoom> updateAuthRoomList) {
        if (CollUtil.isEmpty(updateAuthRoomList)) {
            return;
        }
        // 使用批量更新来提高性能
        ssClassAuthRoomService.updateBatchById(updateAuthRoomList);

        // 处理需要取消预约的情况
        List<SsClassAuthRoom> cancelAppointmentRooms =
            updateAuthRoomList.stream()
                .filter(room -> CharSequenceUtil.isBlank(room.getClassTimeIds()))
                .toList();

        if (!cancelAppointmentRooms.isEmpty()) {
            List<Long> roomIds = cancelAppointmentRooms.stream().map(SsClassAuthRoom::getId)
                .toList();

            SsClassAuthRoom classAuthRoom = new SsClassAuthRoom();
            classAuthRoom.setAppointmentTime(null);
            classAuthRoom.setAppointmentStatus(AppointmentEnum.TYPE_0.CODE);
            // 取消相关的预约记录
            ssClassAuthRoomService.update(
                classAuthRoom,
                Wrappers.<SsClassAuthRoom>lambdaUpdate().in(SsClassAuthRoom::getId, roomIds));
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = "#classTime.id")
    public boolean updateAuthClassTimeRoom(SsClassTime classTime,
        List<ClassAuthRoomDTO> newAuthClassRoomList) {

        // 查询班级信息
        SsClass ssClass = ssClassService.getById(classTime.getClassId());

        // 校验当前班级是否为结业状态
        if (SsClassStateEnum.STATE_1.CODE.equals(ssClass.getClassState())) {
            throw new BizException(REQUEST_ERROR, "该班级已结业，操作失败！");
        }

        R<LecturerVO> lecturerById = remoteLecturerService.getLecture(classTime.getLecturerId());
        if (!lecturerById.isOk() || Objects.isNull(lecturerById.getData())) {
            throw new BizException(REQUEST_ERROR, "主讲老师信息不存在，操作失败！");
        }

        // 查询课次已授权教室信息
        List<SsClassTimeAuthRoom> oldClassAuthRoomList = ssClassTimeAuthRoomMapper.selectList(
            Wrappers.lambdaQuery(SsClassTimeAuthRoom.class)
                .eq(SsClassTimeAuthRoom::getClassTimeId, classTime.getId())
        );

        // 历史授权教室数据
        List<Long> oldDeviceIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(oldClassAuthRoomList)) {
            oldDeviceIdList = oldClassAuthRoomList.stream()
                .map(SsClassTimeAuthRoom::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        }

        // 本次授权班级信息
        List<Long> newDeviceIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(newAuthClassRoomList)) {

            newDeviceIdList = newAuthClassRoomList.stream()
                .map(s -> s.getDeviceId())
                .distinct()
                .collect(Collectors.toList());

        }

        if (CollectionUtils.isEmpty(oldDeviceIdList) && CollectionUtils.isEmpty(newDeviceIdList)) {
            return true;
        }

        oldDeviceIdList.sort(Comparator.comparing(Long::longValue));
        newDeviceIdList.sort(Comparator.comparing(Long::longValue));
        String oldJoin = StringUtils.join(oldDeviceIdList, ',');
        String newJoin = StringUtils.join(newDeviceIdList, ',');

        // ===================单课次授权处理==================
        // 寻找新增的校区
        List<Long> addDeviceIdList = new ArrayList<>(newDeviceIdList);
        addDeviceIdList.removeAll(oldDeviceIdList);
        List<ClassAuthRoomDTO> saveAuthDeviceList = newAuthClassRoomList.stream()
            .filter(room -> addDeviceIdList.contains(room.getDeviceId()))
            .collect(Collectors.toList());

        // 寻找删除的校区
        List<Long> deleteDeviceIdList = new ArrayList<>(oldDeviceIdList);
        deleteDeviceIdList.removeAll(newDeviceIdList);

        // 校管家班级同步参数
        List<ClassCourseReq> campusClassList = new ArrayList<>();

        deleteDeviceAuthRoom(deleteDeviceIdList, classTime, ssClass.getIsSyncXiaogj())
            .forEach(s -> campusClassList.add(s));

        addDeviceAuthRoom(saveAuthDeviceList, ssClass.getClassName(), classTime,
            ssClass.getIsSyncXiaogj(), lecturerById.getData().getXgjLecturerId())
            .forEach(s -> campusClassList.add(s));

        // 记录更改授权教室记录
        saveAuthRoomLog(classTime.getId(), oldJoin, newJoin);
        int update = 0;
        if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(ssClass.getIsSyncXiaogj())
            && CollectionUtils.isNotEmpty(campusClassList)) {
            //查询当前课次是有否有校管家待推送任务，如果有则删除
            update = getUpdateXiaogjPushTask(classTime, newAuthClassRoomList, ssClass);
        }

        //同步校管家
        syncXgj(update > 0 ? 0 : ssClass.getIsSyncXiaogj(), campusClassList, classTime.getId());
        return true;
    }

    @Override
    public int getUpdateXiaogjPushTask(SsClassTime classTime,
        List<ClassAuthRoomDTO> newAuthClassRoomList,
        SsClass ssClass) {
        int update;
        update = ssXiaogjPushTaskMapper.delete(
            Wrappers.lambdaQuery(SsXiaogjPushTask.class)
                .eq(SsXiaogjPushTask::getClassTimeId, classTime.getId())
                .eq(SsXiaogjPushTask::getTaskStatus, 0));
        log.info(
            "更新授权校区删除未同步校管家的课次任务,课次ID:{},班级名称:{},关联书籍:{},删除数量:{}",
            classTime.getId(), ssClass.getClassName(), classTime.getBooksName(), update);
        //删除任务条数大于0说明有未推送的任务，需要重新生成推送任务
        if (update > 0) {
            // 同步校管家
            newAuthClassRoomList.forEach(authClassRoom -> {
                SsClassAuthRoom ssClassAuthRoom = ssClassAuthRoomMapper.selectOne(
                    Wrappers.lambdaQuery(SsClassAuthRoom.class)
                        .eq(SsClassAuthRoom::getClassId, classTime.getClassId())
                        .eq(SsClassAuthRoom::getCampusId, authClassRoom.getCampusId())
                        .eq(SsClassAuthRoom::getClassRoomId, authClassRoom.getClassRoomId())
                        .eq(SsClassAuthRoom::getDeviceId, authClassRoom.getDeviceId()));
                if (Objects.isNull(ssClassAuthRoom)) {
                    throw new BizException("授权教室信息不存在");
                }

                List<SsXiaogjPushTask> xiaogjPushTaskEntityList = getClassCourseReqList(
                    classTime.getCourseScheduleId(), classTime.getClassId(),
                    classTime.getLecturerId(),
                    ssClassAuthRoom.getXgjClassId(), authClassRoom.getXgjClassRoomId())
                    .stream().map(classCourseReq -> {
                        try {
                            String eventId = java.util.UUID.randomUUID().toString();
                            Long timestamp = System.currentTimeMillis();
                            SsPushXiaogjEventReq<ClassCourseReq> ssPushXiaogjEventReq = new SsPushXiaogjEventReq<>();
                            // 双师排课推送校管家请求参数
                            ssPushXiaogjEventReq.setData(classCourseReq);
                            ssPushXiaogjEventReq.setEventId(eventId);
                            ssPushXiaogjEventReq.setEventKey(
                                SsPushXiaogjType.CLASS_COURSE.eventKey);
                            ssPushXiaogjEventReq.setEventTimestamp(timestamp);

                            String jsonStr = JSONUtil.toJsonStr(ssPushXiaogjEventReq);
                            SsXiaogjPushTask ssXiaogjPushTask = new SsXiaogjPushTask();
                            ssXiaogjPushTask.setCourseScheduleId(classTime.getCourseScheduleId());
                            ssXiaogjPushTask.setClassTimeId(classCourseReq.getClassTimeId());
                            String cStartTime = classCourseReq.getCreateCourseList().get(0)
                                .getCStartTime();
                            ssXiaogjPushTask.setAttendClassStartTime(
                                DateUtil.parse(cStartTime));
                            ssXiaogjPushTask.setXgjClassRoomId(
                                ssClassAuthRoom.getXgjClassRoomId());
                            ssXiaogjPushTask.setXgjEventId(eventId);
                            ssXiaogjPushTask.setXgjEventKey(
                                SsPushXiaogjType.CLASS_COURSE.eventKey);
                            ssXiaogjPushTask.setMessageBody(jsonStr);
                            return ssXiaogjPushTask;
                        } catch (Exception e) {
                            log.error("同步校管家失败", e);
                            return null;
                        }
                    }).filter(Objects::nonNull).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(xiaogjPushTaskEntityList)) {
                    return;
                }
                ssXiaogjPushTaskMapper.insert(xiaogjPushTaskEntityList);
            });
        }
        return update;
    }

    /**
     * 保存学生信息
     *
     * @param courseScheduleId 课程表ID
     * @param classId          班级ID
     * @param lecturerId       主讲老师ID
     * @param xgjClassId       校管家班级ID
     * @param xgjClassRoomId   校管家教室ID
     * @return List<ClassCourseReq>
     */
    private List<ClassCourseReq> getClassCourseReqList(Long courseScheduleId, Long classId,
        Long lecturerId,
        String xgjClassId,
        String xgjClassRoomId) {
        List<ClassCourseReq> classCourseReqList = new ArrayList<>();
        R<LecturerVO> lecture = remoteLecturerService.getLecture(lecturerId);
        if (lecture == null || lecture.getData() == null) {
            throw new BizException("主讲老师信息不存在");
        }
        //获取课次信息
        ssClassTimeMapper.selectList(
                Wrappers.lambdaQuery(SsClassTime.class)
                    .eq(SsClassTime::getCourseScheduleId, courseScheduleId))
            .forEach(classTime -> {//1次
                //获取课次授权教室信息
                List<SsClassTimeAuthRoom> allClassTimeAuthRoomList = ssClassTimeAuthRoomMapper.selectList(
                    Wrappers.lambdaQuery(SsClassTimeAuthRoom.class)
                        .eq(SsClassTimeAuthRoom::getClassTimeId, classTime.getId()));
                //保存学生信息

                Optional<SsClassTimeAuthRoom> optionalSsClassTimeAuthRoom = allClassTimeAuthRoomList.stream()
                    .filter(ssClassTimeAuthRoom -> ssClassTimeAuthRoom.getClassId().equals(classId)
                        && ssClassTimeAuthRoom.getXgjClassRoomId()
                        .equals(xgjClassRoomId)).findFirst();

                if (optionalSsClassTimeAuthRoom.isEmpty()) {
                    throw new BizException("课次授权教室信息不存在");
                }
                String xgjClassTimeId = optionalSsClassTimeAuthRoom.get().getXgjClassTimeId();
                ClassCourseReq classCourseReq = xiaogjClassCourseParam(
                    xgjClassId, xgjClassRoomId, xgjClassTimeId, classTime,
                    lecture.getData().getXgjLecturerId());
                classCourseReqList.add(classCourseReq);
            });
        return classCourseReqList;
    }

    /**
     * 封装同步校管家参数
     *
     * @param xgjClassId     校管家班级ID
     * @param xgjClassRoomId 校管家教室ID
     * @param xgjClassTimeId 校管家课次ID
     * @param classTime      课次信息
     * @param xgjLecturerId  校管家主讲老师ID
     * @return ClassCourseReq
     */
    private ClassCourseReq xiaogjClassCourseParam(String xgjClassId,
        String xgjClassRoomId,
        String xgjClassTimeId,
        SsClassTime classTime,
        String xgjLecturerId) {
        String startTime = LocalDateTime.of(classTime.getAttendClassDate(),
                classTime.getAttendClassStartTime())
            .format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
        String endTime = LocalDateTime.of(classTime.getAttendClassDate(),
                classTime.getAttendClassEndTime())
            .format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));

        ClassCourseReq classCourseReq = new ClassCourseReq();
        classCourseReq.setClassTimeId(classTime.getId());
        classCourseReq.setTripartiteId(xgjClassId);
        classCourseReq.setType(CreateClassType.CREATE_CLASS_TYPE.CODE);

        List<CreateCourseReq> createCourseReqList = com.google.common.collect.Lists.newArrayList();
        CreateCourseReq createCourseReq = new CreateCourseReq();
        createCourseReq.setTripartiteId(xgjClassId);
        createCourseReq.setCCourseTimes(1);
        createCourseReq.setCClassroomID(xgjClassRoomId);
        createCourseReq.setCCourseMode(1);
        createCourseReq.setCStartTime(startTime);
        createCourseReq.setCEndTime(endTime);

        int dayOfWeek = classTime.getAttendClassDate().getDayOfWeek().getValue();
        List<CourseData> courseDataList = new ArrayList<>();
        CourseData courseData = CourseData.builder()
            .cWeekday(dayOfWeek)
            .cWeekStartTime(startTime)
            .cWeekEndTime(endTime)
            .cDate(DateUtil.format(classTime.getAttendClassDate()))
            .cClassroomID(xgjClassRoomId)
            .cStartTime(startTime)
            .cEndTime(endTime)
            .threeId(xgjClassTimeId)
            .speaker(xgjLecturerId)
            .build();
        courseDataList.add(courseData);
        createCourseReq.setCCourseData(courseDataList);
        createCourseReqList.add(createCourseReq);
        classCourseReq.setCreateCourseList(createCourseReqList);
        return classCourseReq;
    }


    /**
     * 同步校管家
     *
     * <AUTHOR>
     * @date 2024年10月21日 20时46分
     */
    public void syncXgj(Integer syncXiaogj, List<ClassCourseReq> campusClassList,
        Long classTimeId) {
        // 同步校管家授权校区班级排课队列
        if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(syncXiaogj)
            && CollectionUtils.isNotEmpty(campusClassList)) {

            String userName = SecurityUtils.getUser().getUsername();

            TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        asyncConfiguration.getAsyncExecutor().execute(() -> {
                            for (ClassCourseReq classCourseReq : campusClassList) {

                                // 双师排课推送校管家消息队列公共方法
                                SsPushXiaogjEventReq ssPushXiaogjEventReq = xiaoGuanJiaService.ssPushXiaogjMessage(
                                    classCourseReq);

                                // 保存排课校管家日志
                                xiaogjLogManager.saveXiaogjLog(ssPushXiaogjEventReq, classCourseReq,
                                    userName);
                            }
                        });
                    }
                });

        }
    }


    /**
     * 添加日志
     *
     * <AUTHOR>
     * @date 2024年10月21日 20时04分
     */
    private void saveAuthRoomLog(Long classId, String oldJoin, String newJoin) {
        SsAuthRoomLog ssAuthRoomLog = new SsAuthRoomLog();
        ssAuthRoomLog.setBusinessId(classId);
        ssAuthRoomLog.setEditAuthType(AuthRoomLogEditAuthTypeEnum.EDITAUTHTYPE_1.CODE);
        ssAuthRoomLog.setOldValue(oldJoin);
        ssAuthRoomLog.setNewValue(newJoin);
        ssAuthRoomLogMapper.insert(ssAuthRoomLog);
    }


    /**
     * 删除校区相关处理 1.同步校管家取消授权校区删除本课次信息 2.删除本课次授权校区信息 3.删除班级下对应课次未开始课次学生信息 4.查询本课次是否存在单课次授权信息,存在则删除
     *
     * <AUTHOR>
     * @date 2024年10月21日 20时01分
     */
    public List<ClassCourseReq> deleteDeviceAuthRoom(List<Long> deleteDeviceIdList,
        SsClassTime classTime, Integer isSyncXiaogj) {

        List<ClassCourseReq> campusClassList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deleteDeviceIdList)) {

            // 是否同步校管家校验
            if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(isSyncXiaogj)) {

                // 1.同步校管家取消授权校区删除本课次信息
                SsClassTimeDTO classTimeDto = new SsClassTimeDTO();
                classTimeDto.setClassId(classTime.getClassId());
                classTimeDto.setId(classTime.getId());
                classTimeDto.setAuthDeviceIdList(deleteDeviceIdList);
                ssClassTimeMapper.selectDeviceInfosByClassId(classTimeDto)
                    .forEach(ssClassTimeVo -> {
                        // 校管家授权校区班级信息
                        ClassCourseReq classCourseReq = new ClassCourseReq();
                        classCourseReq.setTripartiteId(ssClassTimeVo.getXgjClassId());
                        classCourseReq.setType(
                            CreateClassType.CREATE_CLASS_TYPE.CODE);
                        List<DeleteCourseReq> deleteCourseInfoList = new ArrayList<>();
                        DeleteCourseReq deleteCourseReq = new DeleteCourseReq();
                        deleteCourseReq.setTripartiteId(ssClassTimeVo.getXgjClassId());
                        deleteCourseReq.setThreeId(ssClassTimeVo.getXgjClassTimeId());
                        deleteCourseInfoList.add(deleteCourseReq);
                        classCourseReq.setDeleteCourseList(deleteCourseInfoList);
                        campusClassList.add(classCourseReq);
                    });
            }

            // 2.删除本课次授权校区信息
            ssClassTimeAuthRoomMapper.delete(Wrappers.lambdaQuery(SsClassTimeAuthRoom.class)
                .eq(SsClassTimeAuthRoom::getClassId, classTime.getClassId())
                .eq(SsClassTimeAuthRoom::getClassTimeId, classTime.getId())
                .in(SsClassTimeAuthRoom::getDeviceId, deleteDeviceIdList)
            );

            // 3.删除班级下对应课次未开始课次学生信息
            ssClassTimeStudentService.remove(Wrappers.lambdaQuery(SsClassTimeStudent.class)
                .eq(SsClassTimeStudent::getClassId, classTime.getClassId())
                .eq(SsClassTimeStudent::getClassTimeId, classTime.getId())
                .in(SsClassTimeStudent::getDeviceId, deleteDeviceIdList)
            );

            // 4.查询本次删除设备是否存在单课次授权信息,存在则删除
            ssClassAuthRoomService.list(Wrappers.lambdaQuery(SsClassAuthRoom.class)
                    .eq(SsClassAuthRoom::getClassId, classTime.getClassId())
                    .in(SsClassAuthRoom::getDeviceId, deleteDeviceIdList)
                    .isNotNull(SsClassAuthRoom::getClassTimeIds)
                    .ne(SsClassAuthRoom::getClassTimeIds, Strings.EMPTY)
                //  .apply(" class_time_ids is not null and class_time_ids != '' ")
            ).forEach(ssClassAuthRoom -> {
                // 将逗号分隔字符串去除指定字符
                String newStr = StringUtil.removeStr(ssClassAuthRoom.getClassTimeIds(),
                    classTime.getId().toString());
                SsClassAuthRoom updateClassAuthRoom = new SsClassAuthRoom();
                updateClassAuthRoom.setId(ssClassAuthRoom.getId());
                updateClassAuthRoom.setClassTimeIds(newStr);
                if (StringUtils.isBlank(newStr)) {
                    updateClassAuthRoom.setAppointmentTime(null);
                    updateClassAuthRoom.setAppointmentStatus(
                        AuthRoomAppointmentTypeEnum.TYPE_0.CODE);
                }
                ssClassAuthRoomService.updateById(updateClassAuthRoom);

            });

        }
        return campusClassList;
    }

    /**
     * 新增相关处理 1.新增本课次授权信息 2.新增本课次对应班级授权信息 3.同步校管家班级信息 4.新增授权校区本课次信息 5.新增本课次班级学生
     *
     * <AUTHOR>
     * @date 2024年10月21日 20时01分
     */
    public List<ClassCourseReq> addDeviceAuthRoom(List<ClassAuthRoomDTO> saveAuthDeviceList,
        String className,
        SsClassTime classTime, Integer isSyncXiaogj, String xgjLecturerId
    ) {
        List<ClassCourseReq> campusClassList = new ArrayList<>();
        saveAuthDeviceList.forEach(classAuthRoomDto ->
            {

                // 1.新增本课次授权信息
                SsClassTimeAuthRoom ssClassTimeAuthRoom = new SsClassTimeAuthRoom();
                ssClassTimeAuthRoom.setClassId(classTime.getClassId());
                // 同步校管家生成校管家校区课次ID
                if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(isSyncXiaogj)) {
                    ssClassTimeAuthRoom.setXgjClassTimeId(UUID.randomUUID().toString());
                }
                ssClassTimeAuthRoom.setClassTimeId(classTime.getId());
                ssClassTimeAuthRoom.setCampusId(classAuthRoomDto.getCampusId());
                ssClassTimeAuthRoom.setClassRoomId(classAuthRoomDto.getClassRoomId());
                ssClassTimeAuthRoom.setDeviceId(classAuthRoomDto.getDeviceId());
                ssClassTimeAuthRoom.setXgjCampusId(classAuthRoomDto.getXgjCampusId());
                ssClassTimeAuthRoom.setXgjClassRoomId(classAuthRoomDto.getXgjClassRoomId());
                ssClassTimeAuthRoomService.save(ssClassTimeAuthRoom);

                // 2.新增本课次对应班级授权信息
                //查询本课次授权校区是否在班级授权中存在,存在则直接使用班级授权信息,不存在则创建班级授权对应课次信息
                List<SsClassAuthRoom> campusAllClassAuthRoomList = ssClassAuthRoomService.list(
                    Wrappers.lambdaQuery(SsClassAuthRoom.class)
                        .eq(SsClassAuthRoom::getClassId, classTime.getClassId())
                        .eq(SsClassAuthRoom::getCampusId, classAuthRoomDto.getCampusId())
                );

                List<SsClassAuthRoom> classAuthRoomList = campusAllClassAuthRoomList.stream()
                    .filter(e -> classAuthRoomDto.getDeviceId().equals(e.getDeviceId()))
                    .collect(Collectors.toList());

                SsClassAuthRoom ssClassAuthRoom = new SsClassAuthRoom();

                if (CollectionUtils.isEmpty(classAuthRoomList)) {

                    // 新增班级单课次授权信息
                    ssClassAuthRoom.setClassId(classTime.getClassId());
                    ssClassAuthRoom.setCampusId(classAuthRoomDto.getCampusId());
                    ssClassAuthRoom.setAppointmentTime(LocalDateTime.now());
                    ssClassAuthRoom.setXgjCampusId(classAuthRoomDto.getXgjCampusId());
                    ssClassAuthRoom.setClassTimeIds(classTime.getId().toString());
                    ssClassAuthRoom.setAppointmentStatus(AuthRoomAppointmentTypeEnum.TYPE_1.CODE);
                    ssClassAuthRoom.setClassRoomId(classAuthRoomDto.getClassRoomId());
                    ssClassAuthRoom.setDeviceId(classAuthRoomDto.getDeviceId());
                    ssClassAuthRoom.setXgjClassRoomId(classAuthRoomDto.getXgjClassRoomId());
                    // 同步校管家生成校管家校区班级ID
                    if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(isSyncXiaogj)) {
                        ssClassAuthRoom.setXgjClassId(
                            CollectionUtils.isNotEmpty(campusAllClassAuthRoomList)
                                ? campusAllClassAuthRoomList.get(0).getXgjClassId()
                                : UUID.randomUUID().toString());
                    }
                    ssClassAuthRoomService.save(ssClassAuthRoom);

                    // 是否同步校管家校验
                    if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(isSyncXiaogj)) {

                        // 3.同步校管家班级信息
                        // 4.新增授权校区本课次信息
                        List<CourseData> courseDataList = new ArrayList<>();
                        CourseData courseData = CourseData.builder()
                            .cWeekday(DateUtils.getWeek(classTime.getAttendClassDate()))
//                            .cWeekStartTime(DateUtils.timeSdfHms().format(classTime.getAttendClassStartTime()))
//                            .cWeekEndTime(DateUtils.timeSdfHms().format(classTime.getAttendClassEndTime()))
//                            .cDate(DateUtils.dateSdf().format(classTime.getAttendClassDate()))
                            .cWeekStartTime(DateUtils.timeSdfHms(classTime.getAttendClassStartTime()))
                            .cWeekEndTime(DateUtils.timeSdfHms(classTime.getAttendClassEndTime()))
                            .cDate(DateUtils.dateSdf(classTime.getAttendClassDate()))
                            .cClassroomID(classAuthRoomDto.getXgjClassRoomId())
                            .cStartTime(DateUtils.dateSdf(classTime.getAttendClassDate()) + " "
                                + DateUtils.timeSdfHms(classTime.getAttendClassStartTime()))
                            .cEndTime(DateUtils.dateSdf(classTime.getAttendClassDate()) + " "
                                + DateUtils.timeSdfHms(classTime.getAttendClassEndTime()))
                            .threeId(ssClassTimeAuthRoom.getXgjClassTimeId())
                            .speaker(xgjLecturerId)
                            .build();
                        courseDataList.add(courseData);

                        List<CreateCourseReq> createCourseInfoList = new ArrayList<>();
                        CreateCourseReq createCourseReq = new CreateCourseReq();
                        createCourseReq.setTripartiteId(ssClassAuthRoom.getXgjClassId());
                        createCourseReq.setCStartTime(
                            DateUtils.dateSdf(classTime.getAttendClassDate()));
                        createCourseReq.setCEndTime(DateUtils.dateSdf(classTime.getAttendClassDate()));
                        createCourseReq.setCCourseData(courseDataList);
                        createCourseReq.setCCourseMode(1);
                        createCourseInfoList.add(createCourseReq);

                        ClassCourseReq classCourseReq = new ClassCourseReq();
                        classCourseReq.setTripartiteId(ssClassAuthRoom.getXgjClassId());
                        classCourseReq.setCShiftID(XiaoGuanJiaConstant.DEFAULT_COURSE_ID);
                        classCourseReq.setCCampusID(classAuthRoomDto.getXgjCampusId());
                        classCourseReq.setCName(className);
                        classCourseReq.setType(CollectionUtils.isNotEmpty(campusAllClassAuthRoomList)
                            ? CreateClassType.CREATE_CLASS_TYPE.CODE
                            : CreateClassType.CREATE_CLASS_TYPE_0.CODE);
                        classCourseReq.setCreateCourseList(createCourseInfoList);
                        campusClassList.add(classCourseReq);
                    }

                } else {

                    ssClassAuthRoom.setId(classAuthRoomList.get(0).getId());
                    ssClassAuthRoom.setXgjClassId(classAuthRoomList.get(0).getXgjClassId());

                    if (AuthRoomAppointmentTypeEnum.TYPE_0.CODE.equals(
                        classAuthRoomList.get(0).getAppointmentStatus())) {
                        ssClassAuthRoom.setAppointmentTime(LocalDateTime.now());
                        ssClassAuthRoom.setClassTimeIds(classTime.getId().toString());
                        ssClassAuthRoom.setAppointmentStatus(AuthRoomAppointmentTypeEnum.TYPE_1.CODE);
                    } else {

                        if (StringUtils.isNotBlank(classAuthRoomList.get(0).getClassTimeIds())
                            && !classAuthRoomList.get(0).getClassTimeIds()
                            .contains(classTime.getId().toString())) {
                            ssClassAuthRoom.setClassTimeIds(
                                String.format("%s,%s", classAuthRoomList.get(0).getClassTimeIds(),
                                    classTime.getId()));
                        }

                    }

                    ssClassAuthRoomService.updateById(ssClassAuthRoom);

                    // 是否同步校管家校验
                    if (IsSyncXiaogjEnum.IS_SYNC_XIAOGJ_ENUM_1.CODE.equals(isSyncXiaogj)) {

                        // 4.新增授权校区本课次信息
                        List<CourseData> courseDataList = new ArrayList<>();
                        CourseData courseData = CourseData.builder()
                            .cWeekday(DateUtils.getWeek(classTime.getAttendClassDate()))
                            .cWeekStartTime(DateUtils.timeSdfHms(classTime.getAttendClassStartTime()))
                            .cWeekEndTime(DateUtils.timeSdfHms(classTime.getAttendClassEndTime()))
                            .cDate(DateUtils.dateSdf(classTime.getAttendClassDate()))
                            .cClassroomID(classAuthRoomDto.getXgjClassRoomId())
                            .cStartTime(DateUtils.dateSdf(classTime.getAttendClassDate()) + " "
                                + DateUtils.timeSdfHms(classTime.getAttendClassStartTime()))
                            .cEndTime(DateUtils.dateSdf(classTime.getAttendClassDate()) + " "
                                + DateUtils.timeSdfHms(classTime.getAttendClassEndTime()))
                            .threeId(ssClassTimeAuthRoom.getXgjClassTimeId())
                            .speaker(xgjLecturerId)
                            .build();
                        courseDataList.add(courseData);

                        List<CreateCourseReq> createCourseInfoList = new ArrayList<>();
                        CreateCourseReq createCourseReq = new CreateCourseReq();
                        createCourseReq.setTripartiteId(ssClassAuthRoom.getXgjClassId());
                        createCourseReq.setCStartTime(
                            DateUtils.dateSdf(classTime.getAttendClassDate()));
                        createCourseReq.setCEndTime(DateUtils.dateSdf(classTime.getAttendClassDate()));
                        createCourseReq.setCCourseData(courseDataList);
                        createCourseReq.setCCourseMode(1);
                        createCourseInfoList.add(createCourseReq);

                        ClassCourseReq classCourseReq = new ClassCourseReq();
                        classCourseReq.setTripartiteId(ssClassAuthRoom.getXgjClassId());
                        classCourseReq.setType(CreateClassType.CREATE_CLASS_TYPE.CODE);
                        classCourseReq.setCreateCourseList(createCourseInfoList);
                        campusClassList.add(classCourseReq);

                    }

                }

                // 查询授权校区班级学生
                ssClassAuthRoomStudentMapper.selectList(
                    Wrappers.lambdaQuery(SsClassAuthRoomStudent.class)
                        .eq(SsClassAuthRoomStudent::getClassId, classTime.getClassId())
                        .eq(SsClassAuthRoomStudent::getCampusId, classAuthRoomDto.getCampusId())
                ).forEach(classAuthRoomStudent -> {
                    // 新增课次学生信息
                    SsClassTimeStudent ssClassTimeStudent = new SsClassTimeStudent();
                    ssClassTimeStudent.setClassId(classTime.getClassId());
                    ssClassTimeStudent.setClassTimeAuthRoomId(ssClassTimeAuthRoom.getId());
                    ssClassTimeStudent.setClassTimeId(classTime.getId());
                    ssClassTimeStudent.setCampusId(classAuthRoomDto.getCampusId());
                    ssClassTimeStudent.setClassRoomId(classAuthRoomDto.getClassRoomId());
                    ssClassTimeStudent.setDeviceId(classAuthRoomDto.getDeviceId());
                    ssClassTimeStudent.setStudentId(classAuthRoomStudent.getStudentId());
                    ssClassTimeStudent.setStudentName(classAuthRoomStudent.getStudentName());
                    ssClassTimeStudent.setStudentMobile(classAuthRoomStudent.getStudentMobile());
                    ssClassTimeStudentService.save(ssClassTimeStudent);
                });
            }
        );

        return campusClassList;
    }


    /**
     * 获取监课链接
     *
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/21 13:45
     */
    @Override
    public SsClassTimeVO getSupervisionUrl(SsClassTimeQuery classTimeQuery) {
        SsClassTime ssClassTime = ssClassTimeMapper.selectById(classTimeQuery.getId());
        if (Objects.isNull(ssClassTime)) {
            throw new BizException("课次信息不存在");
        }
        if (Objects.isNull(ssClassTime.getRoomUuid())) {
            throw new BizException("当前课次房间不存在");
        }
        SsClassTimeVO ssClassTimeVO = new SsClassTimeVO();
        ssClassTimeVO.setSupervisionClassUrl(ssProperty.getObservationUrl());
        ssClassTimeVO.setRoomUuid(ssClassTime.getRoomUuid());
        return ssClassTimeVO;
    }

    /**
     * 通过ID 获取课次详情
     *
     * @param id
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO
     * <AUTHOR>
     * @date 2024/10/21 14:07
     */
    @Override
    public SsClassTimeVO getClassTimeInfo(Long id) {
        SsClassTime ssClassTime = ssClassTimeMapper.selectById(id);
        if (ssClassTime == null) {
            throw new BizException("课次信息不存在");
        }

        SsClassTimeVO classTimeVO = new SsClassTimeVO();
        BeanUtils.copyProperties(ssClassTime, classTimeVO);

        // 使用CompletableFuture并行获取讲师和班级信息
        CompletableFuture<LecturerVO> lecturerFuture =
            CompletableFuture.supplyAsync(() -> getLecturerInfoById(ssClassTime.getLecturerId()));
        CompletableFuture<SsClass> classFuture =
            CompletableFuture.supplyAsync(() -> ssClassService.getById(ssClassTime.getClassId()));

        try {
            CompletableFuture.allOf(lecturerFuture, classFuture).join();

            LecturerVO lecturerVO = lecturerFuture.get();
            SsClass ssClass = classFuture.get();

            if (lecturerVO != null) {
                classTimeVO.setLecturerName(lecturerVO.getLecturerName());
            }

            if (ssClass != null) {
                classTimeVO.setClassName(ssClass.getClassName());
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取课次详情信息失败", e);
            throw new BizException("获取课次详情信息失败");
        }

        return classTimeVO;
    }

    /**
     * 获取教室日历
     *
     * @param classTimeQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2024/10/24 14:34
     */
    @Override
    public IPage classRoomFreeTimeCalendar(SsClassTimeQuery classTimeQuery) {
        // 远程调用remoteDictService 根据live_room type 获取所有的教室
        R<List<SysDictItem>> liveRoom = remoteDictService.getDictByType("live_room");
        if (Objects.isNull(liveRoom) || !liveRoom.isOk()) {
            throw new BizException("获取直播教室信息失败!");
        }
        List<SysDictItem> liveRoomData = liveRoom.getData();
        if (StringUtils.isNotBlank(classTimeQuery.getClassRoomName())) {
            liveRoomData =
                liveRoomData.stream()
                    .filter(Objects::nonNull)
                    .filter(item -> StringUtils.isNotBlank(item.getLabel()))
                    .filter(item -> item.getLabel().contains(classTimeQuery.getClassRoomName()))
                    .toList();
        }

        // 获取所有直播教室的ID列表
        List<Long> classRoomIds =
            liveRoomData.stream()
                .map(item -> Long.parseLong(item.getItemValue()))
                .collect(Collectors.toList());

        // 获取指定时间范围内的课次信息
        List<SsClassTimeVO> classTimes =
            ssClassTimeMapper.getFreeTimeClassTimes(
                classRoomIds,
                classTimeQuery.getSelectAttendClassStartTime(),
                classTimeQuery.getSelectAttendClassEndTime());

        SsClassRoomFreeTimeVO roomFreeTimeVo = new SsClassRoomFreeTimeVO();
        List<ClassRoomFreeTimeVo> roomFreeTimeVos = new ArrayList<>();

        for (SysDictItem classRoom : liveRoomData) {
            ClassRoomFreeTimeVo classRoomFreeTimeVo =
                new ClassRoomFreeTimeVo();
            classRoomFreeTimeVo.setClassRoomId(Long.parseLong(classRoom.getItemValue()));
            classRoomFreeTimeVo.setClassRoomName(classRoom.getLabel());

            Map<Integer, Weekday> weekDayMap = initWeekDayMap();

            List<SsClassTimeVO> classRoomClassTimes =
                classTimes.stream()
                    .filter(e -> Objects.equals(e.getClassRoomId(),
                        classRoomFreeTimeVo.getClassRoomId()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(classRoomClassTimes)) {
                processClassTimes(classRoomClassTimes, weekDayMap, classRoomFreeTimeVo);
            }

            roomFreeTimeVos.add(classRoomFreeTimeVo);
        }

        roomFreeTimeVo.setClassRooms(roomFreeTimeVos);

        // 由于原方法返回类型是IPage，这里需要将结果包装成IPage对象
        Page<SsClassRoomFreeTimeVO> page = new Page<>();
        page.setRecords(Collections.singletonList(roomFreeTimeVo));
        page.setTotal(1);
        return page;
    }

    @Override
    public List<Long> getClassTimeIdsByCampulsId(String campusId) {
        return ssClassTimeAuthRoomService.list(Wrappers.<SsClassTimeAuthRoom>lambdaQuery()
                .eq(SsClassTimeAuthRoom::getCampusId, campusId))
            .stream()
            .map(s -> s.getClassTimeId())
            .distinct()
            .toList();

    }

    private Map<Integer, Weekday> initWeekDayMap() {
        Map<Integer, Weekday> weekDayMap = new HashMap<>();
        weekDayMap.put(1, new ClassRoomFreeTimeMondayVo());
        weekDayMap.put(2, new ClassRoomFreeTimeTuesdayVo());
        weekDayMap.put(3, new ClassRoomFreeTimeWednesdayVo());
        weekDayMap.put(4, new ClassRoomFreeTimeThursdayVo());
        weekDayMap.put(5, new ClassRoomFreeTimeFridayVo());
        weekDayMap.put(6, new ClassRoomFreeTimeSaturdayVo());
        weekDayMap.put(7, new ClassRoomFreeTimeSundayVo());
        return weekDayMap;
    }

    private void processClassTimes(
        List<SsClassTimeVO> classRoomClassTimes,
        Map<Integer, Weekday> weekDayMap,
        ClassRoomFreeTimeVo classRoomFreeTimeVo) {
        for (SsClassTimeVO classTimeVo : classRoomClassTimes) {
            classTimeVo.setWeekDay(classTimeVo.getAttendClassDate().getDayOfWeek().getValue());
            classTimeVo.setWeekDayDur(
                DateUtils.isTime(
                    classTimeVo
                        .getAttendClassStartTime()
                        .format(DateTimeFormatter.ofPattern(DatePattern.NORM_TIME_PATTERN))));
        }

        Map<Integer, List<SsClassTimeVO>> weekGroupMap =
            classRoomClassTimes.stream().collect(Collectors.groupingBy(SsClassTimeVO::getWeekDay));

        for (Entry<Integer, List<SsClassTimeVO>> entry : weekGroupMap.entrySet()) {
            Integer weekDay = entry.getKey();
            List<SsClassTimeVO> classTimeVosByWeekDay = entry.getValue();
            Weekday weekday = weekDayMap.get(weekDay);

            Map<String, List<SsClassTimeVO>> weekDayDurMap =
                classTimeVosByWeekDay.stream()
                    .collect(Collectors.groupingBy(SsClassTimeVO::getWeekDayDur));

            processWeekDayDur(weekDayDurMap, weekday);

            setWeekdayField(classRoomFreeTimeVo, weekday);
        }
    }

    private void processWeekDayDur(
        Map<String, List<SsClassTimeVO>> weekDayDurMap, Weekday weekday) {
        for (Entry<String, List<SsClassTimeVO>> durEntry : weekDayDurMap.entrySet()) {
            String weekDayDur = durEntry.getKey();
            List<SsClassTimeVO> classTimeVos = durEntry.getValue();

            List<ClassRoomFreeTimeClassTimeVo> classTimesVo =
                getFinalClassTimes(classTimeVos);

            switch (weekDayDur) {
                case "morning":
                    ClassRoomFreeTimeMorningVo morningVo =
                        new ClassRoomFreeTimeMorningVo();
                    morningVo.setClassTimes(classTimesVo);
                    weekday.setMorning(morningVo);
                    break;
                case "afternoon":
                    ClassRoomFreeTimeAfternoonVo afternoonVo =
                        new ClassRoomFreeTimeAfternoonVo();
                    afternoonVo.setClassTimes(classTimesVo);
                    weekday.setAfternoon(afternoonVo);
                    break;
                case "evening":
                    ClassRoomFreeTimeEveningVo eveningVo =
                        new ClassRoomFreeTimeEveningVo();
                    eveningVo.setClassTimes(classTimesVo);
                    weekday.setEvening(eveningVo);
                    break;
            }
        }
    }

    private void setWeekdayField(
        ClassRoomFreeTimeVo classRoomFreeTimeVo,
        Weekday weekday) {
        try {
            String fieldName = getFieldNameFromWeekday(weekday);
            if (!fieldName.isEmpty()) {
                Field field = ClassRoomFreeTimeVo.class.getDeclaredField(
                    fieldName);
                field.setAccessible(true);
                field.set(classRoomFreeTimeVo, weekday);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("获取教室日历解析失败：{}", e.getMessage());
        }
    }

    private List<ClassRoomFreeTimeClassTimeVo> getFinalClassTimes(
        List<SsClassTimeVO> classTimeVos) {
        return classTimeVos.stream()
            .map(
                classTime -> {
                    ClassRoomFreeTimeClassTimeVo classTimeVo =
                        new ClassRoomFreeTimeClassTimeVo();
                    classTimeVo.setClassTimeId(classTime.getId());
                    classTimeVo.setClassTimePeriod(
                        classTime
                            .getAttendClassStartTime()
                            .format(DateTimeFormatter.ofPattern(DateUtils.HH_MM))
                            + "-"
                            + classTime
                            .getAttendClassEndTime()
                            .format(DateTimeFormatter.ofPattern(DateUtils.HH_MM)));
                    return classTimeVo;
                })
            .collect(Collectors.toList());
    }

    /**
     * 返回Agora
     *
     * @return com.yuedu.ydsf.eduConnect.system.proxy.config.properties.AgoraProperties
     * <AUTHOR>
     * @date 2024/10/29 11:58
     */
    @Override
    public AgoraProperties getAgoraProperties() {
        return agoraProperties;
    }

    private String getFieldNameFromWeekday(Weekday weekday) {
        if (weekday instanceof ClassRoomFreeTimeMondayVo) {
            return "week1";
        }
        if (weekday instanceof ClassRoomFreeTimeTuesdayVo) {
            return "week2";
        }
        if (weekday instanceof ClassRoomFreeTimeWednesdayVo) {
            return "week3";
        }
        if (weekday instanceof ClassRoomFreeTimeThursdayVo) {
            return "week4";
        }
        if (weekday instanceof ClassRoomFreeTimeFridayVo) {
            return "week5";
        }
        if (weekday instanceof ClassRoomFreeTimeSaturdayVo) {
            return "week6";
        }
        if (weekday instanceof ClassRoomFreeTimeSundayVo) {
            return "week7";
        }
        return "";
    }

    /**
     * 根据选择的直播间教室查询对应的设备
     *
     * @param ssClassTimeDto
     * @return void
     * <AUTHOR>
     * @date 2024/10/30 9:26
     */
    @Override
    public void updateLiveDevice(SsClassTimeDTO ssClassTimeDto) {
        List<SsDevice> ssDevices =
            ssDeviceService.list(
                Wrappers.lambdaQuery(SsDevice.class)
                    .eq(SsDevice::getClassRoomId, ssClassTimeDto.getClassRoomId())
                    .eq(SsDevice::getDelFlag, DelFlagEnum.DELFLAG_0.code));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(ssDevices)) {
            ssClassTimeDto.setDeviceId(ssDevices.get(0).getId());
        }
    }
}
