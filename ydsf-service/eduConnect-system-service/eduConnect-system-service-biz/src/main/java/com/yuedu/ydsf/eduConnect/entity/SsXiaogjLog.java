package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 校管家日志表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-28 15:57:51
 */
@Data
@TableName("ss_xiaogj_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "校管家日志表实体类")
public class SsXiaogjLog extends Model<SsXiaogjLog> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 请求ID
	*/
    @Schema(description="请求ID")
    private String requestId;

	/**
	* 请求参数
	*/
    @Schema(description="请求参数")
    private String requestParam;

	/**
	* 返回参数
	*/
    @Schema(description="返回参数")
    private String responseParam;

	/**
	* 返回状态码: 200-响应成功; 400-响应失败;
	*/
    @Schema(description="返回状态码: 200-响应成功; 400-响应失败;")
    private Integer responseCode;

	/**
	* 事件类型: class_course-双师排课;
	*/
    @Schema(description="事件类型: class_course-双师排课;")
    private String eventKey;

	/**
	* 请求时间戳
	*/
    @Schema(description="请求时间戳")
    private Long timestamp;

	/**
	* 创建时间
	*/
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑者")
    private String modifer;
}
