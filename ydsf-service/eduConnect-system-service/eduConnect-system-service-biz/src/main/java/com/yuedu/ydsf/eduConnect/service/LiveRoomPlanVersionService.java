package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanVersionQuery;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanVersionVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import java.util.List;

/**
 * 直播间计划版本记录表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-29 14:40:37
 */
public interface LiveRoomPlanVersionService extends IService<LiveRoomPlanVersion> {

    /**
     * 查询可创建教学计划的直播间计划
     *
     * @return List<LiveRoomPlanVersionVO>
     */
    List<LiveRoomPlanVersionVO> listCanCreateTeachingPlan(Integer onlineVersion);

    /**
     * 查询全部未结束的线上版本直播间计划
     */
    List<LiveRoomPlanVersion> listUnfinishedOnlineVersion();

    /**
     *  查询发布最新的线上版本直播间计划
     *
     * <AUTHOR>
     * @date 2025年07月14日 09时56分
     */
    IPage<LiveRoomPlanVersionVO> pubList(Page page, LiveRoomPlanVersionQuery liveRoomPlanVersion);
}
