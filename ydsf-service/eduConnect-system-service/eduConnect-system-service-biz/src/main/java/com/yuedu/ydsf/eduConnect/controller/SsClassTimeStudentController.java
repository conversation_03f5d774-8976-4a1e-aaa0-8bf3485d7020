package com.yuedu.ydsf.eduConnect.controller;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeStudentDTO;
import com.yuedu.ydsf.eduConnect.service.SsClassTimeStudentService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

/**
 * 点播课程库 控制类
 *
 * <AUTHOR>
 * @date 2024-12-02 11:05:41
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/SsClassTimeStudent" )
@Tag(description = "SsClassTimeStudent" , name = "校区上课学生表" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SsClassTimeStudentController {

    private final SsClassTimeStudentService ssClassTimeStudentService;

    /**
     * 根据教室ID判断是否存在学生
     * @param classTimeStudentDTO DTO
     * @return R<Boolean>
     */
    @PostMapping("/existStudentByClassRoomId")
    @Inner
    public R<Boolean> existStudentByClassRoomId(@RequestBody SsClassTimeStudentDTO classTimeStudentDTO) {
        return R.ok(ssClassTimeStudentService.existStudentByClassRoomId(classTimeStudentDTO.getClassRoomId()));
    }

}
