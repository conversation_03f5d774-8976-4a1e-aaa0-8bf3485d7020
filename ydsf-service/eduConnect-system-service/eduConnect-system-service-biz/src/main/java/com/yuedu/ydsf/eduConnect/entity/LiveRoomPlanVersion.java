package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 直播间计划版本记录表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-29 14:40:37
 */
@Data
@TableName("ea_live_room_plan_version")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "直播间计划版本记录表实体类")
public class LiveRoomPlanVersion extends Model<LiveRoomPlanVersion> {


	/**
	* 主键id，ea_live_room_plan_draft.id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键id，ea_live_room_plan_draft.id")
    private Long id;

	/**
	* 直播间计划ID
	*/
    @Schema(description="直播间计划ID")
    private Long planId;

	/**
	* 计划名称
	*/
    @Schema(description="计划名称")
    private String planName;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    private Integer stage;

	/**
	* 直播间id
	*/
    @Schema(description="直播间id")
    private Long liveRoomId;

	/**
	* 版本号
	*/
    @Schema(description="版本号")
    private Integer version;

	/**
	* 线上使用版本:1线上使用版本，0历史版本
	*/
    @Schema(description="线上使用版本:1线上使用版本，0历史版本")
    private Integer onlineVersion;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
