package com.yuedu.ydsf.eduConnect.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.file.core.FileProperties;
import com.yuedu.ydsf.common.file.core.FileTemplate;
import com.yuedu.ydsf.eduConnect.api.constant.AlarmStatusEnum;
import com.yuedu.ydsf.eduConnect.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.api.dto.TimetablePictureDTO;
import com.yuedu.ydsf.eduConnect.api.query.TimetablePictureQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TimetablePictureVO;
import com.yuedu.ydsf.eduConnect.constant.CheckInStatusEnum;
import com.yuedu.ydsf.eduConnect.constant.RecognitionStatusEnum;
import com.yuedu.ydsf.eduConnect.entity.TimetableEventAlarmDeatils;
import com.yuedu.ydsf.eduConnect.entity.TimetablePicture;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteTimetableService;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BClassTimeStudentVO;
import com.yuedu.ydsf.eduConnect.mapper.TimetableEventAlarmDeatilsMapper;
import com.yuedu.ydsf.eduConnect.mapper.TimetableMapper;
import com.yuedu.ydsf.eduConnect.mapper.TimetablePictureMapper;
import com.yuedu.ydsf.eduConnect.service.TimetablePictureService;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.BaiduAttributeConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAttrReq;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAttrResp;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAttrResp.PersonAge;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAttrResp.PersonHuman;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.BaiduApi;
import com.yuedu.ydsf.eduConnect.system.proxy.util.Base64Util;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.Serializable;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.javers.common.collections.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;


/**
* 上课拍照记录服务层
*
* <AUTHOR>
* @date  2025/02/13
*/
@Slf4j
@Service
@AllArgsConstructor
@RefreshScope
public class TimetablePictureServiceImpl extends ServiceImpl<TimetablePictureMapper,TimetablePicture>
    implements TimetablePictureService{

    private final FileTemplate fileTemplate;

    private final FileProperties fileProperties;

    private final BaiduApi baiduApi;

    private final TimetableEventAlarmDeatilsMapper timetableEventAlarmDeatilsMapper;

    private final TimetableMapper timetableMapper;

    private final RemoteTimetableService remoteTimetableService;

    @Value("${image.cache.size:2097152}")
    private Integer imageCacheSize;


    /**
     * 上课拍照记录分页查询
     *
     * @param page 分页对象
     * @param timetablePictureQuery 上课拍照记录
     * @return IPage 分页结果
     */
    @Override
    public IPage<TimetablePictureVO> page(Page page,TimetablePictureQuery timetablePictureQuery) {
        return page(page, Wrappers.<TimetablePicture>lambdaQuery())
                .convert(entity -> {
                    TimetablePictureVO timetablePictureVO = new TimetablePictureVO();
                    BeanUtils.copyProperties(entity, timetablePictureVO);
                    return timetablePictureVO;
                });
    }


    /**
     * 根据ID获得上课拍照记录信息
     *
     * @param id id
     * @return TimetablePictureVO 详细信息
     */
    @Override
    public TimetablePictureVO getInfoById(Serializable id) {
        return Optional.of(getById(id))
                .map(entity -> {
                    TimetablePictureVO timetablePictureVO = new TimetablePictureVO();
                    BeanUtils.copyProperties(entity, timetablePictureVO);
                    return timetablePictureVO;
                })
                .orElseThrow(()-> new CheckedException("查询结果为空"));
    }


    /**
     * 新增上课拍照记录
     *
     * @param timetablePictureDTO 上课拍照记录
     * @return boolean 执行结果
     */
    @Override
    public boolean add(TimetablePictureDTO timetablePictureDTO) {
        TimetablePicture timetablePicture = new TimetablePicture();
        BeanUtils.copyProperties(timetablePictureDTO, timetablePicture);
        return save(timetablePicture);
    }


    /**
     * 修改上课拍照记录
     *
     * @param timetablePictureDTO 上课拍照记录
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(TimetablePictureDTO timetablePictureDTO) {
        TimetablePicture timetablePicture = new TimetablePicture();
        BeanUtils.copyProperties(timetablePictureDTO, timetablePicture);
        return updateById(timetablePicture);
    }


    /**
     * 导出excel 上课拍照记录表格
     *
     * @param timetablePictureQuery 查询条件
     * @param ids 导出指定ID
     * @return List<TimetablePictureVO> 结果集合
     */
    @Override
    public List<TimetablePictureVO> export(TimetablePictureQuery timetablePictureQuery, Long[] ids) {
        return list(Wrappers.<TimetablePicture>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), TimetablePicture::getId, ids))
            .stream()
            .map(entity -> {
                TimetablePictureVO timetablePictureVO = new TimetablePictureVO();
                BeanUtils.copyProperties(entity, timetablePictureVO);
                return timetablePictureVO;
            }).toList();
    }

    @Override
    public void pictureRecognitionHandle() {
        list(Wrappers.<TimetablePicture>lambdaQuery().ne(TimetablePicture::getRecognitionStatus,
                                            RecognitionStatusEnum.RECOGNITION_STATUS_1.code)).forEach(s->{

            if(StringUtils.isBlank(s.getPhotoUrl())){
                TimetablePicture timetablePicture = new TimetablePicture();
                timetablePicture.setId(s.getId());
                timetablePicture.setRecognitionStatus(RecognitionStatusEnum.RECOGNITION_STATUS_1.code);
                updateById(timetablePicture);
                return;
            }

//            String pathStr = UrlBuilder.of(s.getPhotoUrl()).getPathStr();
//            if(pathStr.startsWith("/")){
//                pathStr = pathStr.substring(1);
//            }

            try(InputStream inputStream = fileTemplate.getObject(fileProperties.getBucketName(),s.getPhotoUrl()).getObjectContent()){
                 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                 // 读取文件内容到字节数组。
                 byte[] readBuffer = new byte[imageCacheSize.intValue()];
                 int bytesRead;
                 while ((bytesRead = inputStream.read(readBuffer)) != -1) {
                     byteArrayOutputStream.write(readBuffer, 0, bytesRead);
                 }
                 BodyAttrResp bodyAttrResp = baiduApi.bodyAttr(BodyAttrReq.builder()
                     .image(URLEncoder.encode(Base64Util.encode(byteArrayOutputStream.toByteArray()), StandardCharsets.UTF_8))
                     .type(BaiduAttributeConstant.TYPE_AGE)
                     .build());

                 if(!bodyAttrResp.isSuccess()){
                     log.error("识别上课Id：{} 图片路径：{} 错误：{}",s.getId(),s.getPhotoUrl(),bodyAttrResp.getErrorMsg());
                     return;
                 }


                 TimetablePicture timetablePicture = new TimetablePicture();
                 timetablePicture.setId(s.getId());
                 timetablePicture.setRecognitionStatus(RecognitionStatusEnum.RECOGNITION_STATUS_1.code);
                 timetablePicture.setRecognitionTotalNum(bodyAttrResp.getPersonNum());
                 timetablePicture.setRecognitionChildrenNum(0);
                 timetablePicture.setRecognitionTeenagersNum(0);
                 timetablePicture.setRecognitionYouthNum(0);
                 timetablePicture.setRecognitionMiddleNum(0);
                 timetablePicture.setRecognitionElderlyNum(0);
                 if(!Objects.isNull(bodyAttrResp.getPersonInfo())){
                     bodyAttrResp.getPersonInfo().forEach(personInfo -> {
                         if(!Objects.isNull(personInfo.getAttributes()) && !Objects.isNull(personInfo.getAttributes().getAge())){
                             PersonAge age = personInfo.getAttributes().getAge();
                             switch (age.getName()){
                                 case BaiduAttributeConstant.TYPE_AGE_CHILDREN  -> timetablePicture.setRecognitionChildrenNum(timetablePicture.getRecognitionChildrenNum()+1);
                                 case BaiduAttributeConstant.TYPE_AGE_TEENAGERS -> timetablePicture.setRecognitionTeenagersNum(timetablePicture.getRecognitionTeenagersNum()+1);
                                 case BaiduAttributeConstant.TYPE_AGE_YOUTH  -> timetablePicture.setRecognitionYouthNum(timetablePicture.getRecognitionYouthNum()+1);
                                 case BaiduAttributeConstant.TYPE_AGE_MIDDLE  -> timetablePicture.setRecognitionMiddleNum(timetablePicture.getRecognitionMiddleNum()+1);
                                 case BaiduAttributeConstant.TYPE_AGE_ELDERLY  -> timetablePicture.setRecognitionElderlyNum(timetablePicture.getRecognitionElderlyNum()+1);
                                 default -> {
                                 }
                             }
                         }
                     });
                 }
                 updateById(timetablePicture);

             }catch (Exception e){
                 log.error("识别上课Id：{} 图片路径：{} 错误：{}",s.getId(),s.getPhotoUrl(),e.getMessage());
             }
        });

    }

    @Override
    public void handlerPicture(com.yuedu.ydsf.eduConnect.live.api.dto.TimetablePictureDTO eventDTO) {

//        TimetablePicture oldTimetablePicture = getById(id);
//        if(Objects.isNull(oldTimetablePicture)
//            || RecognitionStatusEnum.RECOGNITION_STATUS_1.code.equals(oldTimetablePicture.getRecognitionStatus())){
//            return;
//        }

        TimetablePicture timetablePicture = new TimetablePicture();
        timetablePicture.setId(eventDTO.getId());
        timetablePicture.setRecognitionChildrenNum(0);
        timetablePicture.setRecognitionTeenagersNum(0);
        timetablePicture.setRecognitionYouthNum(0);
        timetablePicture.setRecognitionMiddleNum(0);
        timetablePicture.setRecognitionElderlyNum(0);
        timetablePicture.setRecognitionHumanNum(0);

        TimetableEventAlarmDeatils timetableEventAlarmDeatils = new TimetableEventAlarmDeatils();
        timetableEventAlarmDeatils.setEventTime(eventDTO.getCreateTime());
        timetableEventAlarmDeatils.setStoreId(eventDTO.getStoreId());
        timetableEventAlarmDeatils.setTimeableId(eventDTO.getTimeableId());
        timetableEventAlarmDeatils.setLessionNo(eventDTO.getLessionNo());

        TimetableDTO timetableDTO = new TimetableDTO();
        timetableDTO.setId(eventDTO.getTimeableId());
        timetableDTO.setAlarmTime(eventDTO.getCreateTime());

        try(InputStream inputStream = fileTemplate.getObject(fileProperties.getBucketName(),eventDTO.getPhotoUrl()).getObjectContent()){
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            // 读取文件内容到字节数组。
            byte[] readBuffer = new byte[imageCacheSize.intValue()];
            int bytesRead;
            while ((bytesRead = inputStream.read(readBuffer)) != -1) {
                byteArrayOutputStream.write(readBuffer, 0, bytesRead);
            }
            BodyAttrResp bodyAttrResp = baiduApi.bodyAttr(BodyAttrReq.builder()
                .image(URLEncoder.encode(Base64Util.encode(byteArrayOutputStream.toByteArray()), StandardCharsets.UTF_8))
                .type(BaiduAttributeConstant.TYPE_AGE+","+BaiduAttributeConstant.TYPE_IS_HUMAN)
                .build());

            if(!bodyAttrResp.isSuccess()){
                timetablePicture.setRecognitionStatus(RecognitionStatusEnum.RECOGNITION_STATUS_2.code);
                timetablePicture.setRemark("百度接口响应失败");
            }else {
                timetablePicture.setRecognitionStatus(RecognitionStatusEnum.RECOGNITION_STATUS_1.code);
                timetablePicture.setRecognitionTotalNum(bodyAttrResp.getPersonNum());
                if(!Objects.isNull(bodyAttrResp.getPersonInfo())){
                    bodyAttrResp.getPersonInfo().forEach(personInfo -> {
                        if(!Objects.isNull(personInfo.getAttributes()) && !Objects.isNull(personInfo.getAttributes().getAge())){
                            PersonAge age = personInfo.getAttributes().getAge();
                            switch (age.getName()){
                                case BaiduAttributeConstant.TYPE_AGE_CHILDREN  -> timetablePicture.setRecognitionChildrenNum(timetablePicture.getRecognitionChildrenNum()+1);
                                case BaiduAttributeConstant.TYPE_AGE_TEENAGERS -> timetablePicture.setRecognitionTeenagersNum(timetablePicture.getRecognitionTeenagersNum()+1);
                                case BaiduAttributeConstant.TYPE_AGE_YOUTH  -> timetablePicture.setRecognitionYouthNum(timetablePicture.getRecognitionYouthNum()+1);
                                case BaiduAttributeConstant.TYPE_AGE_MIDDLE  -> timetablePicture.setRecognitionMiddleNum(timetablePicture.getRecognitionMiddleNum()+1);
                                case BaiduAttributeConstant.TYPE_AGE_ELDERLY  -> timetablePicture.setRecognitionElderlyNum(timetablePicture.getRecognitionElderlyNum()+1);
                                default -> {
                                }
                            }
                        }
                        if(!Objects.isNull(personInfo.getAttributes()) && !Objects.isNull(personInfo.getAttributes().getHuman())){
                            PersonHuman human = personInfo.getAttributes().getHuman();
                            if(BaiduAttributeConstant.TYPE_HUMAN_STANDARD.equals(human.getName())){
                                timetablePicture.setRecognitionHumanNum(timetablePicture.getRecognitionHumanNum()+1);
                            }
                        }
                    });
                }
            }

        }catch (Exception e){
            log.error("识别上课Id：{} 图片路径：{} 错误：{}",eventDTO.getId(),eventDTO.getPhotoUrl(),e.getMessage());
            timetablePicture.setRecognitionStatus(RecognitionStatusEnum.RECOGNITION_STATUS_2.code);
            String msg = e.getMessage();
            if(StringUtils.isNotBlank(msg) && msg.length() > 50){
                msg = msg.substring(0,50);
            }
            timetablePicture.setRemark("百度云接口分析错误：" + msg);
        }

        //updateById(timetablePicture);
       if(update(timetablePicture, Wrappers.<TimetablePicture>lambdaUpdate()
           .eq(TimetablePicture::getId,timetablePicture.getId())
           .eq(TimetablePicture::getRecognitionStatus,RecognitionStatusEnum.RECOGNITION_STATUS_0.code))){

        timetableDTO.setIdentifyNum(timetablePicture.getRecognitionHumanNum());
        timetableMapper.updateIdentifyNum(timetableDTO);

        if(RecognitionStatusEnum.RECOGNITION_STATUS_1.code.equals(timetablePicture.getRecognitionStatus())){
            if(Integer.valueOf(0).equals(timetablePicture.getRecognitionHumanNum())){
                timetableEventAlarmDeatils.setEventDescribe(AlarmStatusEnum.ALARM_STATUS_2.desc);
                timetableEventAlarmDeatils.setEventType(AlarmStatusEnum.ALARM_STATUS_2.code);
                timetableDTO.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS_2.code);
                timetableEventAlarmDeatilsMapper.insert(timetableEventAlarmDeatils);
                timetableMapper.updateAlarmStatus(timetableDTO);
            }else {

                R<List<BClassTimeStudentVO>> classTimeStudentList = remoteTimetableService.getBClassTimeStudentList(
                    Lists.asList(eventDTO.getLessionNo()));
                List<BClassTimeStudentVO> classTimeStudents = new ArrayList<>();
                if(classTimeStudentList.isOk() && CollectionUtil.isNotEmpty(classTimeStudentList.getData())){
                    classTimeStudents = classTimeStudentList.getData().stream()
                        .filter(item -> CheckInStatusEnum.CHECK_IN_STATUS_1.code.equals(item.getCheckInStatus())).toList();
                }


                if(Integer.valueOf(classTimeStudents.size()).compareTo(timetablePicture.getRecognitionHumanNum()) < 0){
                    timetableEventAlarmDeatils.setEventDescribe(AlarmStatusEnum.ALARM_STATUS_3.desc);
                    timetableEventAlarmDeatils.setEventType(AlarmStatusEnum.ALARM_STATUS_3.code);
                    timetableEventAlarmDeatils.setRemark("出勤数："+classTimeStudents.size()+" 识别数："+timetablePicture.getRecognitionHumanNum());
                    timetableDTO.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS_3.code);
                    timetableEventAlarmDeatilsMapper.insert(timetableEventAlarmDeatils);
                    timetableMapper.updateAlarmStatus(timetableDTO);
                }

            }
        }else {

            timetableEventAlarmDeatils.setEventDescribe(AlarmStatusEnum.ALARM_STATUS_5.desc);
            timetableEventAlarmDeatils.setEventType(AlarmStatusEnum.ALARM_STATUS_5.code);
            timetableDTO.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS_5.code);
            timetableEventAlarmDeatilsMapper.insert(timetableEventAlarmDeatils);
            timetableMapper.updateAlarmStatus(timetableDTO);
        }

    }
    }



}
