package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion;
import java.util.List;

/**
 * 直播间计划明细 服务类
 *
 * <AUTHOR>
 * @date 2024-12-03 09:49:55
 */
public interface LiveRoomPlanDetailVersionService extends IService<LiveRoomPlanDetailVersion> {


    /**
     * 通过直播间计划Id获取直播间计划信息
     *
     * @param planId 计划Id
     * @return 结果
     */
    List<LiveRoomPlanDetailVersionVO> selectLiveRoomPlanDetailVersionList(Long planId);
}
