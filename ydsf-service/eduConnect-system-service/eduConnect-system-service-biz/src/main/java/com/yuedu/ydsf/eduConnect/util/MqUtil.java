package com.yuedu.ydsf.eduConnect.util;

import com.jayway.jsonpath.JsonPath;
import java.nio.charset.StandardCharsets;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/10/29
 **/
public class MqUtil {

    private static final Logger log = LoggerFactory.getLogger(MqUtil.class);

    /**
     * @param messageView
     * @param clazz
     * @return
     */
    public static <T> T convertMessageBodyToDTO(MessageView messageView, Class<T> clazz) {
        String message = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("消息内容:{}", message);
        return JsonPath.parse(message).read("$.payload", clazz);
    }

}
