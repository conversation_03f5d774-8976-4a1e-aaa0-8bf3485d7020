package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 结算周期 实体类
 *
 * <AUTHOR>
 * @date 2025-04-29 09:00:54
 */
@Data
@TableName("ea_settlement_cycle")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "结算周期实体类")
public class SettlementCycle extends Model<SettlementCycle> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 周期类型，1:本周期；2:上周期及之前
	*/
    @Schema(description="周期类型，1:本周期；2:上周期及之前")
    private Integer cycleType;

	/**
	* 开始周几，取值：1～7
	*/
    @Schema(description="开始周几，取值：1～7")
    private Integer beginWeek;

	/**
	* 截止周几，取值：1～7
	*/
    @Schema(description="截止周几，取值：1～7")
    private Integer endWeek;

	/**
	* 周期天数
	*/
    @Schema(description="周期天数")
    private Integer cycleDays;

	/**
	* 考勤是否锁定,0:未锁定;1:已锁定
	*/
    @Schema(description="考勤是否锁定,0:未锁定;1:已锁定")
    private Integer checkinLocked;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;
}
