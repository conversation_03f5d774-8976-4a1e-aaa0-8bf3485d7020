package com.yuedu.ydsf.eduConnect.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanDraftQuery;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 教学计划草稿表 持久层
 *
 * <AUTHOR>
 * @date 2024-11-29 09:24:03
 */
@Mapper
public interface TeachingPlanDraftMapper extends YdsfBaseMapper<TeachingPlanDraft> {
    IPage<TeachingPlanDraft> selectCustomPage(Page<?> page, @Param("query") TeachingPlanDraftQuery teachingPlanDraftQuery);

}
