package com.yuedu.ydsf.eduConnect.util;

import com.alibaba.nacos.shaded.com.google.common.base.Strings;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDD = "yyyyMMdd";

    public static String YYYYMM = "yyyyMM";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";

    public static String HH_MM = "HH:mm";

    public static String HH_MM_SS = "HH:mm:ss";

    private static final String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate()
    {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * This method combines a date and a time into a single Date object.
     * It takes two parameters, a date and a time, and returns a new Date object that has the date from the first parameter and the time from the second parameter.
     *
     * @param date This is the date part to be used in the combined Date object. It should be a Date object where the time part does not matter as it will be replaced.
     * @param time This is the time part to be used in the combined Date object. It should be a Date object where the date part does not matter as it will be replaced.
     * @return Date This returns a new Date object that has the date from the first parameter and the time from the second parameter.
     */
    public static Date combineDateTime(Date date,
                                       Date time)
    {
        Calendar calendarDate = Calendar.getInstance();
        calendarDate.setTime(date);

        Calendar calendarTime = Calendar.getInstance();
        calendarTime.setTime(time);

        calendarDate.set(Calendar.HOUR_OF_DAY, calendarTime.get(Calendar.HOUR_OF_DAY));
        calendarDate.set(Calendar.MINUTE, calendarTime.get(Calendar.MINUTE));
        calendarDate.set(Calendar.SECOND, calendarTime.get(Calendar.SECOND));
        calendarDate.set(Calendar.MILLISECOND, calendarTime.get(Calendar.MILLISECOND));

        return calendarDate.getTime();
    }


    /**
     * 比较一个 HH:mm:ss 是否在一个时间段内
     * 如：14:33:00 是否在 09:30:00 和 12:00:00 内
     */
    public static boolean timeIsInRound(String str1, String start, String end) {
        SimpleDateFormat df = new SimpleDateFormat("HH:mm:ss");
        Date now = null;
        Date beginTime = null;
        Date endTime = null;

        try {
            now = df.parse(str1);
            beginTime = df.parse(start);
            endTime = df.parse(end);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return belongCalendar(now, beginTime, endTime);
    }

    /**
     * 判断时间是否在时间段内
     */
    public static boolean belongCalendar(Date nowTime, Date beginTime, Date endTime) {
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(beginTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        return date.after(begin) && date.before(end);
    }


    /**
     * 功能描述：返回星期 1:周一 2:周二 3:周三 4:周四 5:周五 6:周六 7:周日
     * @param date
     * @return
     */
    public static int getWeek(LocalDate date) {
        return date.getDayOfWeek().getValue();
    }

    /**
     * 时间转时间戳
     *
     * @param date
     * @return java.lang.Long
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/5/19 15:10
     */
    public static Long timeToTimestamp(Date date) {
        return date.getTime() / 1000L;
    }

    /**
     * 判断选择的日期是否是本周
     *
     * @param time
     * @return boolean
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/5/27 17:21
     */
    public static boolean isThisWeek(long time) {
        Date weekStartTimes = getCurrentWeekStartTimes();
        Date weekEndTimes = getCurrentWeekEndTimes();
        Long startTimeStamp = timeToTimestamp(weekStartTimes);
        Long endTimeStamp = timeToTimestamp(weekEndTimes);
        if (time >= startTimeStamp && time <= endTimeStamp) {
            return true;
        }
        return false;
    }

    // 获得本周一0点时间
    public static Date getCurrentWeekStartTimes() {
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(Calendar.MONDAY); // 设置一周的第一天为星期一
        cal.set(Calendar.HOUR_OF_DAY, 00);
        cal.set(Calendar.MINUTE, 00);
        cal.set(Calendar.SECOND, 00);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY); // 设置本周的星期一
        return cal.getTime();
    }

    // 获得本周日24点时间
    public static Date getCurrentWeekEndTimes() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getCurrentWeekStartTimes());
        cal.add(Calendar.DAY_OF_WEEK, 6);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTime();
    }

    // 获取上周一日期
    public static Date getLastWeekStartTimes() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取上周一
        LocalDate lastMonday = currentDate.minusWeeks(1).with(DayOfWeek.MONDAY);
        ZonedDateTime zonedDateTime = lastMonday.atStartOfDay(ZoneId.systemDefault());
        Date date = Date.from(zonedDateTime.toInstant());

        return date;
    }

    // 获取上周天日期
    public static Date getLastWeekStartEndTimes() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取上周天
        LocalDate lastSunday = currentDate.minusWeeks(1).with(DayOfWeek.SUNDAY);
        ZonedDateTime zonedDateTime = lastSunday.atStartOfDay(ZoneId.systemDefault());
        Date date = Date.from(zonedDateTime.toInstant());
        return date;
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return Date
     */
    public static Date getCurrentDate() throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat (YYYY_MM_DD);
        return formatter.parse(getDate());
    }

    /**
     * 获取指定日期集合间的yyyy-MM
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/6/1 17:17
      * @param dateStart
     * @param datEnd
     * @return java.util.List<java.util.Date>
     */
    public static List<Date> getMonths(Date dateStart, Date datEnd)
    {

        List<Date> list = new ArrayList<Date>(); //保存日期的集合
        Date date =dateStart;
        Calendar cd = Calendar.getInstance();//用Calendar 进行日期比较判断
        while (date.getTime() <= datEnd.getTime()){
            list.add(date);
            cd.setTime(date);
            cd.add(Calendar.MONTH, 1);//增加一月 放入集合
            date=cd.getTime();
        }
        return list;
    }

    /**
     * 获取指定日期集合间的yyyy-MM-dd
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/6/1 17:17
     * @param dateStart
     * @param datEnd
     * @return java.util.List<java.util.Date>
     */
    public static List<Date> getDates(Date dateStart, Date datEnd)
    {

        List<Date> list = new ArrayList<Date>(); //保存日期的集合
        Date date =dateStart;
        Calendar cd = Calendar.getInstance();//用Calendar 进行日期比较判断
        while (date.getTime() <= datEnd.getTime()){
            list.add(date);
            cd.setTime(date);
            cd.add(Calendar.DATE, 1);//增加一天 放入集合
            date=cd.getTime();
        }
        return list;
    }

    /**
     * 获取本月第一天
     *
     * @param
     * @return java.util.Date
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/6/10 15:47
     */
    public static Date monthFirstDay()
    {
        //获取当前月第一天：
        Calendar calstr = Calendar.getInstance();
        //本月
        calstr.add(Calendar.MONTH, 0);
        //设置为1号为本月第一天
        calstr.set(Calendar.DAY_OF_MONTH, 1);
        return calstr.getTime();
    }

    /**
     * 获取本月最后一天
     *
     * @param
     * @return java.util.Date
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/6/10 15:47
     */
    public static Date monthLastDay()
    {
        //获取当前月第一天：
        Calendar calstr = Calendar.getInstance();
        //本月
        calstr.add(Calendar.MONTH, 0);
        calstr.set(Calendar.DAY_OF_MONTH, calstr.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calstr.getTime();
    }

    /**
     * 指定日期判断是不是当天
     *
     * @param str
     * @param formatStr
     * @return boolean
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/6/15 10:57
     */
    public static boolean isToday(String str,
                                  String formatStr)
    throws Exception
    {
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        Date date = null;
        try {
            date = format.parse(str);
        }
        catch (ParseException e) {
        }
        Calendar c1 = Calendar.getInstance();
        c1.setTime(date);
        int year1 = c1.get(Calendar.YEAR);
        int month2 = c1.get(Calendar.MONTH) + 1;
        int day1 = c1.get(Calendar.DAY_OF_MONTH);
        Calendar c2 = Calendar.getInstance();
        c2.setTime(new Date());
        int year2 = c2.get(Calendar.YEAR);
        int month3 = c2.get(Calendar.MONTH) + 1;
        int day2 = c2.get(Calendar.DAY_OF_MONTH);
        if (year1 == year2 && month2 == month3 && day1 == day2) {
            return true;
        }
        return false;
    }

    /**
     * 判断两个时间相差的天数
     *
     * @param date1
     * @param date2
     * @return int
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/6/19 16:57
     */
    public static int differentDays(Date date1,
                                    Date date2)
    {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2)   //同一年
        {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0)    //闰年
                {
                    timeDistance += 366;
                }
                else    //不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2 - day1);
        }
        else    //不同年
        {
            System.out.println("判断day2 - day1 : " + (day2 - day1));
            return day2 - day1;
        }
    }

    /**
     * 指定月的第一天
     *
     * @param year
     * @param month
     * @return java.lang.String
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/7/1 16:53
     */
    public static String getFisrtDayOfMonth(int year,
                                            int month)
    {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    /**
     * 指定月最后一天
     *
     * @param year
     * @param month
     * @return java.lang.String
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/7/1 16:54
     */
    public static String getLastDayOfMonth(int year,
                                           int month)
    {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    /**
     * @Description: 获取指定日期 年 月
     * @Author: crr
     * @Param: date YYYY-MM
     * @return: Map<String,Integer>
     * @Date: 2022/10/8 17:36
     **/
    public static Map<String, Integer> getTheYearMonthDayOfTheSpecifiedDateObject(Date date) {

        Map<String,Integer> dateMap = new HashMap<>();

        // 获取当前分区的日历信息,默认当前时间，(这里可以使用参数指定时区)
        Calendar calendar = Calendar.getInstance();
        // 非当前时间在这里指定
        calendar.setTime(date);
        // 获取年月
        Integer year = calendar.get(Calendar.YEAR);
        Integer month = calendar.get(Calendar.MONTH);
//        Integer day = calendar.get(Calendar.DATE);

        dateMap.put("year",year);
        // 月份从0开始计算，所以这里需要加1
        dateMap.put("month",month+1);
//        dateMap.put("day",day);
        return dateMap;
    }

    /**
     * 获取当前时间到今天结束时间所剩余的毫秒数：
     *
     * @param
     * @return long
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2022/11/7 9:41
     */
    public static long getEndTime() {
        //获取当前时间的毫秒数
        long time = new Date().getTime();
        //获取到今天结束的毫秒数
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23); // Calendar.HOUR 12小时制。HOUR_OF_DAY 24小时制
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        long endTime = todayEnd.getTimeInMillis();
        //这里endTime-time获取的是到23：59：59：999的毫秒数。再加1才是到24点整的毫秒数
        return endTime - time + 1;
    }

    /**
     * 将秒转换成时分秒
     *
     * @param second
     * @return java.lang.String
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2023/2/3 16:12
     */
    public static String secondToTime(long second) {
        if (second < 0) {
            return "00时00分00秒";
        }
        // 计算小时
        long hours = second / 3600;
        // 计算分钟
        long minutes = (second % 3600) / 60;
        // 计算秒
        long seconds = second % 60;
        return hours + "时" + minutes + "分" + seconds + "秒";
    }

    // 获得当前时间七天前的日期
    public static String getCurrentSevenDays() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //获取七天前的日期
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 7);
        String startTime = sdf.format(calendar.getTime());
        return startTime;
    }

    /**
     * 接受这三个日期作为输入，并在方法中使用 isBefore 和 isAfter 方法进行比较。如果输入日期晚于或等于开始日期且早于或等于结束日期，则该方法将返回True，否则返回False。
     *
     * @param nowTime
     * @param beginTime
     * @param endTime
     * @return boolean
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2023/3/7 11:52
     */
    public static boolean isWithinRange(Date nowTime,
                                        Date beginTime,
                                        Date endTime)
    {
        LocalDate date = nowTime.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
        LocalDate startDate = beginTime.toInstant()
                                       .atZone(ZoneId.systemDefault())
                                       .toLocalDate();
        LocalDate endDate = endTime.toInstant()
                                   .atZone(ZoneId.systemDefault())
                                   .toLocalDate();
        return !date.isBefore(startDate) && !date.isAfter(endDate);
    }

    /**
     * 计算两个日期之间的相差天数
     *
     * @param currentDate
     * @param inputDate
     * @return int
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2023/10/25 16:07
     */
    public static int getYearDifference(Date currentDate,
                                        Date inputDate)
    {
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
        int year1 = Integer.parseInt(yearFormat.format(currentDate));
        int year2 = Integer.parseInt(yearFormat.format(inputDate));
        return year1 - year2;
    }

    public static SimpleDateFormat dateTimeSdf(){
        SimpleDateFormat dateTimeSdf = new SimpleDateFormat("yyy-MM-dd HH:mm:ss");
        return dateTimeSdf;
    }

    public static SimpleDateFormat dateSdf(){
        SimpleDateFormat dateSdf = new SimpleDateFormat("yyy-MM-dd");
        return dateSdf;
    }

    public static String dateSdf(LocalDate localDate){
        if(Objects.isNull(localDate)){
            return "";
        }
        return localDate.format(DateTimeFormatter.ofPattern("yyy-MM-dd"));
    }

    public static SimpleDateFormat timeSdfHms(){
        SimpleDateFormat timeSdf = new SimpleDateFormat("HH:mm:ss");
        return timeSdf;
    }

    public static String timeSdfHms(LocalTime localTime){
        if(Objects.isNull(localTime)){
            return "";
        }
        return localTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
    }




    public static SimpleDateFormat timeSdfHm(){
        SimpleDateFormat timeSdf = new SimpleDateFormat("HH:mm");
        return timeSdf;
    }

    /**
     * 获取昨天日期
     * <AUTHOR>
     * @date 2024年01月19日 13时45分
     * @return String
     */
    public static String getYesterdayDate(){
        // 创建 Calendar 对象并设置为当前时间
        Calendar calendar = Calendar.getInstance();
        // 将日期向前调整一天（即昨天）
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        // 格式化日期输出
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String yesterdayDate = dateFormat.format(calendar.getTime());
        return yesterdayDate;
    }

    /**
     * 获取昨天日期
     * <AUTHOR>
     * @date 2024年02月20日 16时19分
     * @param timeStringToCheck 校验时间
     * @return Boolean
     */
    public static String isTime(String timeStringToCheck){

        String timeType = "";

        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");

        String time1 = "00:00:00";
        String time2 = "12:00:00";
        String time3 = "12:00:01";
        String time4 = "18:00:00";
        String time5 = "18:00:01";
        String time6 = "23:59:59";

        // 将字符串转换为LocalTime对象
        LocalTime timeToCheck = LocalTime.parse(timeStringToCheck, formatter);

        // 检查时间是否在范围内
        if (timeToCheck.isAfter(LocalTime.parse(time1)) && timeToCheck.isBefore(LocalTime.parse(time2))) {
            timeType = "morning";
        }

        if (timeToCheck.isAfter(LocalTime.parse(time3)) && timeToCheck.isBefore(LocalTime.parse(time4))) {
            timeType = "afternoon";
        }

        if (timeToCheck.isAfter(LocalTime.parse(time5)) && timeToCheck.isBefore(LocalTime.parse(time6))) {
            timeType = "evening";
        }

        return timeType;
    }

    /**
     * 获取下周的周一
     *
     * @return java.time.LocalDate
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2024/2/21 15:58
     */
    public static Date nextWeekMonday() {
        LocalDate today = LocalDate.now();
        today = today.with(TemporalAdjusters.next(DayOfWeek.MONDAY));
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = today.atStartOfDay().atZone(zone).toInstant();
        Date date = Date.from(instant);
        return date;
    }

    /**
     * 获取下周的周日
     *
     * @return java.time.LocalDate
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2024/2/21 15:58
     */
    public static Date nextWeekSunday() {
        LocalDate today = LocalDate.now();
        today = today.with(TemporalAdjusters.next(DayOfWeek.MONDAY));
        LocalDate nextSunday = today.with(TemporalAdjusters.next(DayOfWeek.SUNDAY));
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = nextSunday.atStartOfDay().atZone(zone).toInstant();
        Date date = Date.from(instant);
        return date;
    }

    /**
     * 获取当前时间年月日字符串(yyyy-MM-dd)
     * <AUTHOR>
     * @date 2024年05月15日 11时47分
     * @return Date
     */
    public static String getTodayDateString() {
        // 获取当前日期字符串(yyyy-MM-dd)
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String todayDate = currentDate.format(formatter);
        return todayDate;
    }

    /**
     * 将指定yyyy-MM-dd HH:mm:ss.SSS字符串格式化为yyyy-MM-dd字符串
     * <AUTHOR>
     * @date 2024年05月15日 11时50分
     * @param date
     * @return String
     */
    public static String getYyyyMMddString(String date) {
        DateTimeFormatter fullFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        DateTimeFormatter dateOnlyFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime createTime = LocalDateTime.parse(date, fullFormatter);
        String dateString = createTime.format(dateOnlyFormatter);
        return dateString;
    }

  /**
   * 安全地将LocalDateTime转换为Date
   *
   * @param localDateTime 待转换的LocalDateTime
   * @return java.util.Date 转换后的Date,如果输入为null则返回null
   */
  public static Date toDate(LocalDateTime localDateTime) {
    if (localDateTime == null) {
      return null;
    }
    try {
      return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    } catch (Exception e) {
      return null;
    }
  }
}
