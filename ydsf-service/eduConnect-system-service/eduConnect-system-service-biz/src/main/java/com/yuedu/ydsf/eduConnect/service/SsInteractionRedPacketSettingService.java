package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.dto.SsInteractionRedPacketSettingDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsInteractionRedPacketSettingQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsInteractionRedPacketSettingInfoVO;
import com.yuedu.ydsf.eduConnect.entity.SsInteractionRedPacketSetting;

/**
 * 门店红包规则设置表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-04 08:41:26
 */
public interface SsInteractionRedPacketSettingService extends IService<SsInteractionRedPacketSetting> {

    /**
     * 约读店管家小程序-查询门店红包设置
     * @param ssInteractionRedPacketSettingQuery
     * @return com.yuedu.ydsf.eduConnect.api.vo.SsInteractionRedPacketSettingInfoVO
     * <AUTHOR>
     * @date 2024/11/4 9:42
     */
    SsInteractionRedPacketSettingInfoVO getInteractionRedPacketSettingBySource(SsInteractionRedPacketSettingQuery ssInteractionRedPacketSettingQuery);

    /**
     * 约读店管家小程序-门店红包规则设置
     * @param ssInteractionRedPacketSettingDTO
     * @return void
     * <AUTHOR>
     * @date 2024/11/4 9:42
     */
    void interactionRedPacketSetting(SsInteractionRedPacketSettingDTO ssInteractionRedPacketSettingDTO);

}
