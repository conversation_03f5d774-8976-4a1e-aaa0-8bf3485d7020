package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.eduConnect.api.constant.PlanStatusEnum;
import com.yuedu.ydsf.eduConnect.entity.LiveChannel;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft;
import com.yuedu.ydsf.eduConnect.manager.LiveChannelManager;
import com.yuedu.ydsf.eduConnect.mapper.LiveChannelMapper;
import com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDraftMapper;
import com.yuedu.ydsf.eduConnect.service.LiveChannelService;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanDraftService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 直播频道 服务类
 *
 * <AUTHOR>
 * @date 2024-11-28 16:56:34
 */
@Slf4j
@Service
public class LiveChannelServiceImpl extends ServiceImpl<LiveChannelMapper, LiveChannel>
    implements LiveChannelService {

  @Resource private LiveChannelManager liveChannelManager;

  @Resource private TeachingPlanDraftMapper planDraftMapper;

  /**
   * 自动创建直播频道
   *
   * <AUTHOR>
   * @date 2024/12/28 10:18
   * @return void
   */
  @Override
  public void autoCreateChannel() {
    // 查询出来教学计划为未关闭的
    List<TeachingPlanDraft> teachingPlanDrafts =
        planDraftMapper.selectList(
            Wrappers.lambdaQuery(TeachingPlanDraft.class)
                .eq(TeachingPlanDraft::getPlanStatus, PlanStatusEnum.STATUS_ENUM_1.code));
    log.debug("查询到{}个已发布的教学计划需要创建直播频道", CollectionUtils.size(teachingPlanDrafts));

    liveChannelManager.createRoomByTeachingPlan(teachingPlanDrafts);
  }
}
