package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.eduConnect.entity.TeachingPlanVersionNo;
import com.yuedu.ydsf.eduConnect.service.TeachingPlanVersionNoService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教学计划版本生成记录表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-29 14:42:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/TeachingPlanVersionNo" )
@Tag(description = "ea_teaching_plan_version_no" , name = "教学计划版本生成记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TeachingPlanVersionNoController {

    private final  TeachingPlanVersionNoService teachingPlanVersionNoService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param teachingPlanVersionNo 教学计划版本生成记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("admin_TeachingPlanVersionNo_view")
    public R getTeachingPlanVersionNoPage(@ParameterObject Page page, @ParameterObject TeachingPlanVersionNo teachingPlanVersionNo) {
        LambdaQueryWrapper<TeachingPlanVersionNo> wrapper = Wrappers.lambdaQuery();
        return R.ok(teachingPlanVersionNoService.page(page, wrapper));
    }


    /**
     * 通过条件查询教学计划版本生成记录表
     * @param teachingPlanVersionNo 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("admin_TeachingPlanVersionNo_view")
    public R getDetails(@ParameterObject TeachingPlanVersionNo teachingPlanVersionNo) {
        return R.ok(teachingPlanVersionNoService.list(Wrappers.query(teachingPlanVersionNo)));
    }

    /**
     * 新增教学计划版本生成记录表
     * @param teachingPlanVersionNo 教学计划版本生成记录表
     * @return R
     */
    @Operation(summary = "新增教学计划版本生成记录表" , description = "新增教学计划版本生成记录表" )
    @PostMapping("/add")
    @HasPermission("admin_TeachingPlanVersionNo_add")
    public R save(@RequestBody TeachingPlanVersionNo teachingPlanVersionNo) {
        return R.ok(teachingPlanVersionNoService.save(teachingPlanVersionNo));
    }

    /**
     * 修改教学计划版本生成记录表
     * @param teachingPlanVersionNo 教学计划版本生成记录表
     * @return R
     */
    @Operation(summary = "修改教学计划版本生成记录表" , description = "修改教学计划版本生成记录表" )
    @PutMapping("/edit")
    @HasPermission("admin_TeachingPlanVersionNo_edit")
    public R updateById(@RequestBody TeachingPlanVersionNo teachingPlanVersionNo) {
        return R.ok(teachingPlanVersionNoService.updateById(teachingPlanVersionNo));
    }

    /**
     * 通过id删除教学计划版本生成记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除教学计划版本生成记录表" , description = "通过id删除教学计划版本生成记录表" )
    @DeleteMapping("/delete")
    @HasPermission("admin_TeachingPlanVersionNo_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(teachingPlanVersionNoService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param teachingPlanVersionNo 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("admin_TeachingPlanVersionNo_export")
    public List<TeachingPlanVersionNo> exportExcel(TeachingPlanVersionNo teachingPlanVersionNo,Long[] ids) {
        return teachingPlanVersionNoService.list(Wrappers.lambdaQuery(teachingPlanVersionNo).in(ArrayUtil.isNotEmpty(ids), TeachingPlanVersionNo::getId, ids));
    }

    /**
     * 导入excel 表
     * @param teachingPlanVersionNoList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("admin_TeachingPlanVersionNo_export")
    public R importExcel(@RequestExcel List<TeachingPlanVersionNo> teachingPlanVersionNoList, BindingResult bindingResult) {
        return R.ok(teachingPlanVersionNoService.saveBatch(teachingPlanVersionNoList));
    }
}
