package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.eduConnect.entity.Recording;
import com.yuedu.ydsf.eduConnect.mapper.RecordingMapper;
import com.yuedu.ydsf.eduConnect.service.RecordingService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 主讲录课表 服务类
 *
 * <AUTHOR>
 * @date 2024-12-03 15:01:44
 */
@Slf4j
@Service
@AllArgsConstructor
public class RecordingServiceImpl extends ServiceImpl<RecordingMapper, Recording> implements RecordingService {


}
