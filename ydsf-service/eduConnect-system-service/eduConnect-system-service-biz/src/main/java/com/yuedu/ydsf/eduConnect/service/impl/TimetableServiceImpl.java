package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.constant.AlarmStatusEnum;
import com.yuedu.ydsf.eduConnect.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.api.query.TimetableQuery;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableExportVO;
import com.yuedu.ydsf.eduConnect.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.entity.Timetable;
import com.yuedu.ydsf.eduConnect.entity.TimetableEventAlarmDeatils;
import com.yuedu.ydsf.eduConnect.live.api.dto.TimetablePictureDTO;
import com.yuedu.ydsf.eduConnect.manager.TimetableManager;
import com.yuedu.ydsf.eduConnect.mapper.TimetableEventAlarmDeatilsMapper;
import com.yuedu.ydsf.eduConnect.mapper.TimetableMapper;
import com.yuedu.ydsf.eduConnect.service.TimetableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 门店课表 服务类
 *
 * <AUTHOR>
 * @date 2025-01-10 14:27:17
 */
@Slf4j
@Service
public class TimetableServiceImpl extends ServiceImpl<TimetableMapper, Timetable> implements TimetableService {

    @Autowired
    private TimetableEventAlarmDeatilsMapper timetableEventAlarmDeatilsMapper;

    @Autowired
    private TimetableManager timetableManager;

    @Override
    public void handlerEventAlarm(TimetablePictureDTO eventDTO) {

        Timetable timetable = getById(eventDTO.getTimeableId());
        if (Objects.isNull(timetable)){
            return;
        }

        TimetableEventAlarmDeatils timetableEventAlarmDeatils = new TimetableEventAlarmDeatils();
        timetableEventAlarmDeatils.setEventTime(eventDTO.getCreateTime());
        timetableEventAlarmDeatils.setEventDescribe(AlarmStatusEnum.ALARM_STATUS_1.desc);
        timetableEventAlarmDeatils.setEventType(AlarmStatusEnum.ALARM_STATUS_1.code);
        timetableEventAlarmDeatils.setStoreId(timetable.getStoreId());
        timetableEventAlarmDeatils.setTimeableId(timetable.getId());
        timetableEventAlarmDeatils.setLessionNo(timetable.getLessonNo());
        timetableEventAlarmDeatilsMapper.insert(timetableEventAlarmDeatils);

        TimetableDTO timetableDTO = new TimetableDTO();
        timetableDTO.setId(timetable.getId());
        timetableDTO.setAlarmTime(eventDTO.getCreateTime());
        timetableDTO.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS_1.code);
        baseMapper.updateAlarmStatus(timetableDTO);

    }

    @Override
    public IPage<TimetableVO> page(Page page, TimetableQuery timetableQuery) {
        return timetableManager.fillData(baseMapper.page(page,timetableQuery));
    }

    @Override
    public TimetableVO getInfoById(Long id) {
        Timetable timetable = getById(id);
        if(Objects.isNull(timetable)){
            throw new BizException("未查询到当前课次信息");
        }
        return timetableManager.fillInfoData(timetable);
    }


    @Override
    public List<Long> getCourseListByStoreId(Long storeId) {
        List<Timetable> courseListByStoreId = baseMapper.getCourseListByStoreId(storeId);

        if(CollectionUtils.isEmpty(courseListByStoreId)){
            return Collections.emptyList();
        }

        return courseListByStoreId
            .stream()
            .map(Timetable::getCourseId)
            .distinct()
            .toList();
    }

    @Override
    public List<TimetableExportVO> export(TimetableQuery timetableQuery) {
        return timetableManager.fillExportData(baseMapper.export(timetableQuery));
    }
}
