package com.yuedu.ydsf.eduConnect.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yuedu.ydsf.eduConnect.service.SsScreenshotDetailService;
import com.yuedu.ydsf.eduConnect.service.TimetablePictureService;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 拍照图片识别任务
 *
 * @author: KL
 * @date: 2025/02/17
 **/
@Slf4j
@Component
public class PcitureRecognitionTask {

    @Autowired
    private SsScreenshotDetailService ssScreenshotDetailService;

    @Autowired
    private TimetablePictureService timetablePictureService;

    @XxlJob("pictureRecognition")
    public void doTask() {
        log.info("拍照图片识别任务开始");
        CompletableFuture.runAsync(() -> {
            try{
                timetablePictureService.pictureRecognitionHandle();
                log.info("拍照图片识别任务结束");
            }catch (Exception e){
                log.error("拍照图片识别任务执行异常,error:{}", e.getMessage());
            }
        });

    }

    @XxlJob("pictureRecognitionTask")
    public void doPictureRecognitionTask() {
        log.info("拍照图片识别任务开始");
        CompletableFuture.runAsync(() -> {
            try{
                long start = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                ssScreenshotDetailService.pictureRecognitionHandle();
                long end = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                log.info("拍照图片识别任务结束,耗时: {}ms",(end-start));
            }catch (Exception e){
                log.error("拍照图片识别任务执行异常,error:{}", e.getMessage());
            }
        });

    }

}
