package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 门店预约班级记录表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-09 09:55:20
 */
@Data
@TableName("ss_appointment_class_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店预约班级记录表实体类")
public class SsAppointmentClassLog extends Model<SsAppointmentClassLog> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 所属门店
	*/
    @Schema(description="所属门店")
    private Long source;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 约课校区ID
	*/
    @Schema(description="约课校区ID")
    private Long campusId;

	/**
	* 约课教室ID
	*/
    @Schema(description="约课教室ID")
    private Long classRoomId;

	/**
	* 约课设备ID
	*/
    @Schema(description="约课设备ID")
    private Long deviceId;

	/**
	* 预约时间
	*/
    @Schema(description="预约时间")
    private LocalDateTime appointmentTime;

	/**
	* 取消预约时间
	*/
    @Schema(description="取消预约时间")
    private LocalDateTime cancelAppointmentTime;

	/**
	* 校管家校区ID
	*/
    @Schema(description="校管家校区ID")
    private String xgjCampusId;

	/**
	* 校管家教室ID
	*/
    @Schema(description="校管家教室ID")
    private String xgjClassRoomId;

	/**
	* 创建时间
	*/
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="编辑者")
    private String modifer;
}
