package com.yuedu.ydsf.eduConnect.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.yuedu.ydsf.eduConnect.api.dto.RecordVideoTaskDTO;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO.EditLessonDTO;
import com.yuedu.ydsf.eduConnect.api.query.RecordVideoTaskQuery;
import com.yuedu.ydsf.eduConnect.entity.RecordVideoTask;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 录课任务表 持久层
 *
 * <AUTHOR>
 * @date 2024-12-02 14:06:07
 */
@Mapper
public interface RecordVideoTaskMapper extends MPJBaseMapper<RecordVideoTask> {


    /**
     * 当前课节下的课件版本信息
     *
     * @param record 参数
     * @return 结果
     */
    RecordVideoTask selectByRecordVideoTask(RecordVideoTask record);

    /**
     * 获取当前计划中已完成的任务
     *
     * @param record 任务
     * @return 列表
     */
    List<RecordVideoTask> recordTaskOldList(RecordVideoTask record);


    /**
     * 查询录课任务列表
     *
     * @param recordVideoTaskQuery 查询类
     * @return 结果
     */
    List<RecordVideoTask> getRecordVideoTaskList(RecordVideoTaskQuery recordVideoTaskQuery);


    /**
     * 修改教学计划讲师后批量修改录课任务
     *
     * @param editLessonList 录课任务列表
     * @return 结果
     */
    int updateTaskByLectureId(EditLessonDTO editLessonList);


    /**
     * 课件版本修改后同步到录课任务
     *
     * @param recordVideoTaskDTO 录课任务列表
     * @return 结果
     */
    int updateCoursewareVersion(RecordVideoTaskDTO recordVideoTaskDTO);


    /**
     * 删除未完成的任务
     *
     * @param taskStatus 任务状态
     * @return 结果
     */
    int deleteNotCompletedTask(Integer taskStatus);
}
