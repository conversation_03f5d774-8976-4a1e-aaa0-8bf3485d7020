package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanDetailDraftAddQuery;
import com.yuedu.ydsf.eduConnect.api.query.LiveRoomPlanDetailDraftQuery;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailDraftVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailDraft;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 直播间计划明细草稿表 服务类
 *
 * <AUTHOR>
 * @date 2024-12-03 09:46:17
 */
public interface LiveRoomPlanDetailDraftService extends IService<LiveRoomPlanDetailDraft> {

    /**
     * 查询全部直播间计划明细信息
     *
     * @return List<LiveRoomPlanDetailDraftVO>
     */
    List<LiveRoomPlanDetailDraftVO> listPlans(Integer planId);

    /**
     * 查询直播间计划明细总数
     *
     * @return List<LiveRoomPlanDetailDraftVO>
     */
    Map<Long, Integer> countPlans(List<Long> ids);

    /**
     * 新增直播间计划明细
     * <AUTHOR>
     * @date 2024/12/4 11:50
     * @param detailDraftAddQuery
     * @return void
     */
    void savePlanDetail(LiveRoomPlanDetailDraftAddQuery detailDraftAddQuery);

    /**
     * 编辑直播间计划明细
     * <AUTHOR>
     * @date 2024/12/5 9:41
     * @param planDetailDraftQuery
     * @return void
     */
    void editPlanDetail(LiveRoomPlanDetailDraftQuery planDetailDraftQuery);

    /**
     * 删除直播间计划明细
     * <AUTHOR>
     * @date 2024/12/5 11:16
     * @param list
     * @return void
     */
    void removeDetail(ArrayList<Long> list);
}
