package com.yuedu.ydsf.eduConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 上课时段 实体类
 *
 * <AUTHOR>
 * @date 2024-11-28 16:44:02
 */
@Data
@TableName("ea_class_time")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "上课时段实体类")
public class ClassTime extends Model<ClassTime> {


	/**
	* 主键id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键id")
    private Long id;

	/**
	* 时段名称
	*/
    @Schema(description="时段名称")
    private String name;

	/**
	* 时段类型:1-上午;2-下午;3-晚上
	*/
    @Schema(description="时段类型:1-上午;2-下午;3-晚上")
    private Integer type;

	/**
	* 开始时间 (HH:mm:ss)
	*/
    @Schema(description="开始时间 (HH:mm:ss)")
    private LocalTime startTime;

	/**
	* 结束时间 (HH:mm:ss)
	*/
    @Schema(description="结束时间 (HH:mm:ss)")
    private LocalTime endTime;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
