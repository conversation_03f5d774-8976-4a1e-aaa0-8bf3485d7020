package com.yuedu.ydsf.eduConnect.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.admin.api.entity.SysAuditLog;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.api.dto.SsOperateLogDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsOperateLogQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsOperateLogVO;
import com.yuedu.ydsf.eduConnect.entity.SsOperateLog;
import com.yuedu.ydsf.eduConnect.service.SsOperateLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * 操作记录表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-04 10:00:15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/ssOperateLog" )
@Tag(description = "ss_operate_log" , name = "操作记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SsOperateLogController {

    private final  SsOperateLogService ssOperateLogService;

    /**
     * 分页查询操作日志
     * @param page
     * @param ssOperateLogQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/11/15 16:05
     */
    @Operation(summary = "分页查询操作日志" , description = "分页查询操作日志" )
    @GetMapping("/getSsOperateLogPage" )
    public R<IPage<SsOperateLogVO>> getSsOperateLogPage(@ParameterObject Page page, @ParameterObject SsOperateLogQuery ssOperateLogQuery) {
        return R.ok(ssOperateLogService.getSsOperateLogPage(page, ssOperateLogQuery));
    }

    /**
     * 新增操作记录
     * @param operateLogDTO
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/11/19 9:14
     */
    @Inner
    @PostMapping("/saveOperateLog")
    @Operation(summary = "新增操作记录", description = "新增操作记录")
    public R saveOperateLog(@RequestBody SsOperateLogDTO operateLogDTO) {
        SsOperateLog ssOperateLog = new SsOperateLog();
        BeanUtils.copyProperties(operateLogDTO, ssOperateLog);
        return R.ok(ssOperateLogService.save(ssOperateLog));
    }


}
