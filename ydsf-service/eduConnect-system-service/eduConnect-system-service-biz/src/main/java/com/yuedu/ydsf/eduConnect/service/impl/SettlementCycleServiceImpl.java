package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.data.annotation.ForceMaster;
import com.yuedu.ydsf.eduConnect.api.vo.SettlementCycleVO;
import com.yuedu.ydsf.eduConnect.constant.Constants;
import com.yuedu.ydsf.eduConnect.entity.SettlementCycle;
import com.yuedu.ydsf.eduConnect.mapper.SettlementCycleMapper;
import com.yuedu.ydsf.eduConnect.service.SettlementCycleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 结算周期 服务类
 *
 * <AUTHOR>
 * @date 2025-04-21 15:28:49
 */
@Slf4j
@Service
public class SettlementCycleServiceImpl extends
        ServiceImpl<SettlementCycleMapper, SettlementCycle> implements SettlementCycleService {

    @Override
    public List<SettlementCycleVO> listAll() {
        List<SettlementCycleVO> settlementCycleVOList = new ArrayList<>();
        List<SettlementCycle> settlementCycleList = this.list();
        Optional<SettlementCycleVO> first = settlementCycleList.stream()
                .filter(settlementCycle -> Objects.equals(settlementCycle.getCycleType(), Constants.CYCLE_TYPE_CURRENT))
                .map(result -> {
                    SettlementCycleVO settlementCycleVO = new SettlementCycleVO();
                    settlementCycleVO.setId(result.getId());
                    settlementCycleVO.setCycleType(result.getCycleType());
                    settlementCycleVO.setBeginWeek(result.getBeginWeek());
                    settlementCycleVO.setEndWeek(result.getEndWeek());
                    settlementCycleVO.setCycleDays(result.getCycleDays());
                    settlementCycleVO.setCheckinLocked(result.getCheckinLocked());
                    // 1:本周期；2:上周期及之前
                    setBeginDateAndEndDate(result, settlementCycleVO);
                    return settlementCycleVO;
                }).findFirst();
        if (first.isPresent()) {
            SettlementCycleVO settlementCycleVO = first.get();
            settlementCycleVOList.add(settlementCycleVO);

            Optional<SettlementCycleVO> optionalSettlementCycleVO = settlementCycleList.stream()
                    .filter(settlementCycle -> Objects.equals(settlementCycle.getCycleType(), Constants.CYCLE_TYPE_PREVIOUS))
                    .map(result -> {
                        SettlementCycleVO previousSettlementCycleVO = new SettlementCycleVO();
                        previousSettlementCycleVO.setId(result.getId());
                        previousSettlementCycleVO.setCycleType(result.getCycleType());
                        previousSettlementCycleVO.setBeginWeek(result.getBeginWeek());
                        previousSettlementCycleVO.setEndWeek(result.getEndWeek());
                        previousSettlementCycleVO.setCycleDays(result.getCycleDays());
                        previousSettlementCycleVO.setCheckinLocked(result.getCheckinLocked());
                        previousSettlementCycleVO.setBeginDate(LocalDate.of(2025, 1, 1));
                        previousSettlementCycleVO.setEndDate(settlementCycleVO.getBeginDate().plusDays(-1));
                        return previousSettlementCycleVO;
                    }).findFirst();

            settlementCycleVOList.add(optionalSettlementCycleVO.orElse(null));
        }
        return settlementCycleVOList;
    }

    @ForceMaster
    @Override
    public void lockOrUnlockCheckin(SettlementCycle settlementCycleQuery) {
        SettlementCycle settlementCycle = this.getById(settlementCycleQuery.getId());
        if (Objects.isNull(settlementCycle)) {
            return;
        } else if (settlementCycle.getCheckinLocked()
                .equals(settlementCycleQuery.getCheckinLocked())) {
            return;
        }
        settlementCycle.setCheckinLocked(settlementCycleQuery.getCheckinLocked());
        this.updateById(settlementCycle);

    }

    @Override
    public void updateById(SettlementCycleVO settlementCycleVO) {
        if (Objects.isNull(settlementCycleVO) || Objects.isNull(settlementCycleVO.getId())) {
            throw new BizException("ID不能为空");
        }
        if (Objects.isNull(settlementCycleVO.getBeginDate()) || Objects.isNull(
                settlementCycleVO.getEndDate())) {
            throw new BizException("开始日期和结束日期不能为空");
        }
        SettlementCycle settlementCycle = new SettlementCycle();
        settlementCycle.setId(settlementCycleVO.getId());
        settlementCycle.setBeginWeek(settlementCycleVO.getBeginDate().getDayOfWeek().getValue());
        settlementCycle.setEndWeek(settlementCycleVO.getEndDate().getDayOfWeek().getValue());
        long days = settlementCycleVO.getEndDate().toEpochDay() - settlementCycleVO.getBeginDate()
                .toEpochDay();
        if (days != 6) {
            throw new BizException("结算周期只能为7天");
        }
        settlementCycle.setCycleDays((int) days + 1);
        this.updateById(settlementCycle);
    }

    private static void setBeginDateAndEndDate(SettlementCycle result,
                                               SettlementCycleVO settlementCycleVO) {
        LocalDate beginDate;
        LocalDate endDate;
        if (Constants.CYCLE_TYPE_CURRENT.equals(result.getCycleType())) {
            //计算本周的结算周期开始日期和结束日期
            LocalDate now = LocalDate.now();
            int currentDayOfWeek = now.getDayOfWeek().getValue(); // 1-7，周一到周日


            int beginWeek = result.getBeginWeek();
            int endWeek = result.getEndWeek();

            if (currentDayOfWeek < beginWeek) {
                // 当前日期在开始日期之前，那么开始日期是上周的开始日
                beginDate = now.minusDays(currentDayOfWeek + result.getCycleDays() - beginWeek);
                // 结束日期是本周的结束日
                endDate = now.plusDays(endWeek - currentDayOfWeek);
            } else {
                // 当前日期在开始日期之后，那么开始日期是本周的开始日
                beginDate = now.minusDays(currentDayOfWeek - beginWeek);
                // 结束日期是下周的结束日
                endDate = now.plusDays(result.getCycleDays() + endWeek - currentDayOfWeek);
            }

            settlementCycleVO.setBeginDate(beginDate);
            settlementCycleVO.setEndDate(endDate);

        }
    }
}
