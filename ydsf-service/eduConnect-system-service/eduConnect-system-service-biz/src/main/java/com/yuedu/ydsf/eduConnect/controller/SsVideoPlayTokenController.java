package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.entity.SsVideoPlayToken;
import com.yuedu.ydsf.eduConnect.service.SsVideoPlayTokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 视频播放凭证 控制类
 *
 * <AUTHOR>
 * @date 2025-03-11 08:47:54
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/ssVideoPlayToken" )
@Tag(description = "ss_video_play_token" , name = "视频播放凭证管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SsVideoPlayTokenController {

    private final  SsVideoPlayTokenService ssVideoPlayTokenService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ssVideoPlayToken 视频播放凭证
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("admin_ssVideoPlayToken_view")
    public R getSsVideoPlayTokenPage(@ParameterObject Page page, @ParameterObject SsVideoPlayToken ssVideoPlayToken) {
        LambdaQueryWrapper<SsVideoPlayToken> wrapper = Wrappers.lambdaQuery();
        return R.ok(ssVideoPlayTokenService.page(page, wrapper));
    }


    /**
     * 通过条件查询视频播放凭证
     * @param ssVideoPlayToken 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("admin_ssVideoPlayToken_view")
    public R getDetails(@ParameterObject SsVideoPlayToken ssVideoPlayToken) {
        return R.ok(ssVideoPlayTokenService.list(Wrappers.query(ssVideoPlayToken)));
    }

    /**
     * 新增视频播放凭证
     * @param ssVideoPlayToken 视频播放凭证
     * @return R
     */
    @Operation(summary = "新增视频播放凭证" , description = "新增视频播放凭证" )
    @PostMapping("/add")
    @HasPermission("admin_ssVideoPlayToken_add")
    public R save(@RequestBody SsVideoPlayToken ssVideoPlayToken) {
        log.info("新增视频播放凭证,ssVideoPlayToken:{}", ssVideoPlayToken);
        return R.ok(ssVideoPlayTokenService.generateToken(ssVideoPlayToken));
    }

    /**
     * 修改视频播放凭证
     * @param ssVideoPlayToken 视频播放凭证
     * @return R
     */
    @Operation(summary = "修改视频播放凭证" , description = "修改视频播放凭证" )
    @PutMapping("/edit")
    @HasPermission("admin_ssVideoPlayToken_edit")
    public R updateById(@RequestBody SsVideoPlayToken ssVideoPlayToken) {
        log.info("修改视频播放凭证,ssVideoPlayToken:{}", ssVideoPlayToken);
        return R.ok(ssVideoPlayTokenService.updateById(ssVideoPlayToken));
    }

    /**
     * 通过id删除视频播放凭证
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除视频播放凭证" , description = "通过id删除视频播放凭证" )
    @DeleteMapping("/delete")
    @HasPermission("admin_ssVideoPlayToken_del")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(ssVideoPlayTokenService.removeBatchByIds(CollUtil.toList(ids)));
    }
}
