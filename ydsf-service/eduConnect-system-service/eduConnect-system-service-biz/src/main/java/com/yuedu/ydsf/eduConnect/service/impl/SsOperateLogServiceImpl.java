package com.yuedu.ydsf.eduConnect.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.query.SsOperateLogQuery;
import com.yuedu.ydsf.eduConnect.entity.SsOperateLog;
import com.yuedu.ydsf.eduConnect.mapper.SsOperateLogMapper;
import com.yuedu.ydsf.eduConnect.service.SsOperateLogService;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 操作记录表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-04 10:00:15
 */
@Slf4j
@Service
@AllArgsConstructor
public class SsOperateLogServiceImpl extends ServiceImpl<SsOperateLogMapper, SsOperateLog> implements SsOperateLogService {

    /**
     * 分页查询操作日志
     * @param page
     * @param ssOperateLogQuery
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2024/11/15 16:08
     */
    @Override
    public IPage getSsOperateLogPage(Page page, SsOperateLogQuery ssOperateLogQuery) {

        if (Objects.isNull(ssOperateLogQuery.getCategory())) {
            throw new BizException("类别不能为空");
        }

        if (Objects.isNull(ssOperateLogQuery.getObjectId())) {
            throw new BizException("业务ID不能为空");
        }

        IPage pages = page(page, Wrappers.<SsOperateLog>lambdaQuery()
            .eq(SsOperateLog::getCategory, ssOperateLogQuery.getCategory())
            .eq(SsOperateLog::getObjectId, ssOperateLogQuery.getObjectId())
            .orderByDesc(SsOperateLog::getCreateTime)
        );

        return pages;

    }


}
