package com.yuedu.ydsf.eduConnect.manager;


import com.yuedu.ydsf.eduConnect.api.vo.PlayAuthVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsRecordingVO;
import com.yuedu.ydsf.eduConnect.api.vo.UploadAuthVO;
import com.yuedu.ydsf.eduConnect.entity.SsRecording;

import java.util.List;

/**
 * @author: KL
 * @date: 2024/09/29
 **/
public interface SsRecordingManager {



    /**
     * 设置视频资源
     *
     * <AUTHOR>
     * @date 2024年09月29日 14时35分
     */
    SsRecording fillResource(SsRecording ssRecording);


    /**
     * 获得上传凭证
     *
     * <AUTHOR>
     * @date 2024年09月29日 14时35分
     */
    UploadAuthVO getUploadAuth(String fileName, String title);


    /**
     * 获得播放凭证
     *
     * <AUTHOR>
     * @date 2024年09月29日 14时35分
     */
    PlayAuthVO getPlayAuth(String videoId, Long expiredTime);

    /**
     * 刷新上传凭证
     *
     * <AUTHOR>
     * @date 2024年09月29日 14时35分
     */
    UploadAuthVO refreshUploadAuth(String videoId);


    /**
     * 填充转换
     *
     * <AUTHOR>
     * @date 2024年09月29日 22时20分
     */
    List<SsRecordingVO> convert(List<SsRecording> ssRecordings);

    /**
     * 获得视频列表
     *
     * @param videoId 视频ID
     * <AUTHOR>
     * @date 2024年10月10日 15时15分
     */
    List<String> getPlayInfo(String videoId);


    /**
     * 提交转码任务
     *
     * @param ssRecording
     * <AUTHOR>
     * @date 2024年10月12日 08时25分
     */
    void submitTranscodeTask(SsRecording ssRecording);


    /**
     * 删除是否可删除录播课
     *
     * <AUTHOR>
     * @date 2024年10月12日 08时56分
     * @param ids
     */
    boolean checkDeleteRecording(List<Long> ids);



    List<SsRecording> registerVideo(List<SsRecording> ssRecordings);
}
