package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.common.file.domain.OssStsResult;
import com.yuedu.ydsf.eduConnect.api.dto.InformationAuthStoreDTO;
import com.yuedu.ydsf.eduConnect.api.query.InformationQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationVO;
import com.yuedu.ydsf.eduConnect.entity.Information;

import java.io.Serializable;
import java.util.List;

/**
* 资料管理服务接口
*
* <AUTHOR>
* @date  2025/07/22
*/
public interface InformationService extends IService<Information> {



    /**
     * 资料管理分页查询
     *
     * @param page 分页对象
     * @param informationQuery 资料管理
     * @return IPage 分页结果
     */
    IPage<InformationVO> page(Page page, InformationQuery informationQuery);


    /**
     * 根据ID获得资料管理信息
     *
     * @param id id
     * @return InformationVO 详细信息
     */
    InformationVO getInfoById(Serializable id);


    /**
     * 新增资料管理
     *
     * @param informationDTO 资料管理
     * @return boolean 执行结果
     */
    InformationVO add(InformationDTO informationDTO);


    /**
     * 修改资料管理
     *
     * @param informationDTO 资料管理
     * @return boolean 执行结果
     */
    boolean edit(InformationDTO informationDTO);


    /**
     *  根据id删除
     *
     * <AUTHOR>
     * @date 2025年07月22日 10时02分
     */
    boolean delete(Serializable id);

    /**
     *  根据ID查询子目录
     *
     * <AUTHOR>
     * @date 2025年07月22日 10时17分
     */
    List<InformationVO> getChildById(Serializable id);

    /**
     *  根据id查询授权信息
     *
     * <AUTHOR>
     * @date 2025年07月22日 10时27分
     */
    InformationVO getAuthById(Serializable id);


    /**
     *  根据id查询资源信息
     *
     * <AUTHOR>
     * @date 2025年07月22日 10时27分
     */
    InformationVO getResourceById(Serializable id);


    /**
     *  根据目录获得上传临时sts
     *
     * <AUTHOR>
     * @date 2025年07月22日 11时32分
     */
    OssStsResult getInformationUploadStsToken(Long id);

    /**
     *  资料授权
     *
     * <AUTHOR>
     * @date 2025年07月22日 11时32分
     */
    void informationAuthStore(InformationDTO informationDTO);


    /**
     *  获得目录集合
     *
     * <AUTHOR>
     * @date 2025年07月29日 10时59分
     */
    List<InformationVO> getDirectory(InformationQuery informationQuery);
}
