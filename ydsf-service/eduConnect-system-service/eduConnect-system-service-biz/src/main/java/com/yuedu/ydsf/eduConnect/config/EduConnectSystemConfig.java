package com.yuedu.ydsf.eduConnect.config;

import com.yuedu.ydsf.eduConnect.system.proxy.config.AgoraConfig;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AliVodConfig;
import com.yuedu.ydsf.eduConnect.system.proxy.config.BaiduConfig;
import com.yuedu.ydsf.eduConnect.system.proxy.config.ForestConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration()
@Import(value = {AliVodConfig.class, AgoraConfig.class, ForestConfig.class, MessageUtils.class,BaiduConfig.class})
public class EduConnectSystemConfig {

}
