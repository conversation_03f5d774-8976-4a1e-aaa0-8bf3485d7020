package com.yuedu.ydsf.eduConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.entity.SsXiaogjLog;

/**
 * 校管家日志表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-28 15:57:51
 */
public interface SsXiaogjLogService extends IService<SsXiaogjLog> {

    /**
     * 同步校管家班级排课异常数据再次推送
     * @param
     * @return void
     * <AUTHOR>
     * @date 2025/2/24 14:42
     */
    void syncFailXiaogjClassCourseSchedule(String param);

}
