package com.yuedu.ydsf.eduConnect.constant;

import com.yuedu.ydsf.common.core.exception.ErrorCode;

/**
 * 直播间状态异常枚举类
 *
 * @date 2024/11/14 8:44
 * @project @Title: LiveRoomStatusTypeEnum.java
 */
public enum LiveRoomStatusTypeEnum implements ErrorCode {

  /** 直播间排课时间冲突 */
  CONFLICT_CODE("1504", "直播间排课时间冲突");

  public final String code;

  public final String desc;

  LiveRoomStatusTypeEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  @Override
  public String getCode() {
    return code;
  }

  @Override
  public String getDescription() {
    return desc;
  }
}
