<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.InformationResourceMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.InformationResource">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            <result property="resourceName" column="resource_name" jdbcType="VARCHAR"/>
            <result property="resourceUrl" column="resource_url" jdbcType="VARCHAR"/>
            <result property="isDownload" column="is_download" jdbcType="INTEGER"/>
            <result property="resourceType" column="resource_type" jdbcType="INTEGER"/>
            <result property="informationId" column="information_id" jdbcType="BIGINT"/>
    </resultMap>

</mapper>
