<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsOperateLogMapper">

  <resultMap id="ssOperateLogMap" type="com.yuedu.ydsf.eduConnect.entity.SsOperateLog">
        <id property="id" column="id"/>
        <result property="category" column="category"/>
        <result property="type" column="type"/>
        <result property="detail" column="detail"/>
        <result property="operateId" column="operate_id"/>
        <result property="operateName" column="operate_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>
