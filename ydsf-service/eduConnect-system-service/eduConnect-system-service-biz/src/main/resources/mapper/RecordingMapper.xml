<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.RecordingMapper">

  <resultMap id="recordingMap" type="com.yuedu.ydsf.eduConnect.entity.Recording">
        <id property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="recordVideoTaskId" column="record_video_task_id"/>
        <result property="agoraRecordId" column="agora_record_id"/>
        <result property="roomUuid" column="room_uuid"/>
        <result property="recordingStatus" column="recording_status"/>
        <result property="recordingResources" column="recording_resources"/>
        <result property="agoraCloudRecordId" column="agora_cloud_record_id"/>
        <result property="cloudRecordingResources" column="cloud_recording_resources"/>
        <result property="agoraCloudRecordIndividualResourceId" column="agora_cloud_record_individual_resource_id"/>
        <result property="agoraCloudRecordIndividualId" column="agora_cloud_record_individual_id"/>
        <result property="downloadUrl" column="download_url"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="recordingTime" column="recording_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>
