<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsLecturerMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.SsLecturer">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="xgjLecturerId" column="xgj_lecturer_id" jdbcType="VARCHAR"/>
            <result property="lecturerName" column="lecturer_name" jdbcType="VARCHAR"/>
            <result property="lecturerState" column="lecturer_state" jdbcType="INTEGER"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
            <result property="modifer" column="modifer" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>
