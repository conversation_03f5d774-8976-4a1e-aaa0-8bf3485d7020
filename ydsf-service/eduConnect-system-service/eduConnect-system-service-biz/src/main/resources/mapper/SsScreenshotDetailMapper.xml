<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsScreenshotDetailMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.SsScreenshotDetail">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="classTimeId" column="class_time_id" jdbcType="BIGINT"/>
            <result property="deviceId" column="device_id" jdbcType="BIGINT"/>
            <result property="resourcesName" column="resources_name" jdbcType="VARCHAR"/>
            <result property="resourcesPath" column="resources_path" jdbcType="VARCHAR"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
            <result property="modifer" column="modifer" jdbcType="VARCHAR"/>
            <result property="screenshotTime" column="screenshot_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="recognitionTotalNum" column="recognition_total_num" jdbcType="INTEGER"/>
            <result property="recognitionChildrenNum" column="recognition_children_num" jdbcType="INTEGER"/>
            <result property="recognitionTeenagersNum" column="recognition_teenagers_num" jdbcType="INTEGER"/>
            <result property="recognitionYouthNum" column="recognition_youth_num" jdbcType="INTEGER"/>
            <result property="recognitionMiddleNum" column="recognition_middle_num" jdbcType="INTEGER"/>
            <result property="recognitionElderlyNum" column="recognition_elderly_num" jdbcType="INTEGER"/>
            <result property="recognitionStatus" column="recognition_status" jdbcType="TINYINT"/>
    </resultMap>

</mapper>
