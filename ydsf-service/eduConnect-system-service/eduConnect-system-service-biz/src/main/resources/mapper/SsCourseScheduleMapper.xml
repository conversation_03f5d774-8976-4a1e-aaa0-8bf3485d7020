<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsCourseScheduleMapper">

  <resultMap id="ssCourseScheduleMap" type="com.yuedu.ydsf.eduConnect.entity.SsCourseSchedule">
        <id property="id" column="id"/>
        <result property="classId" column="class_id"/>
        <result property="lecturerId" column="lecturer_id"/>
        <result property="classTimeMethod" column="class_time_method"/>
        <result property="attendClassStartDate" column="attend_class_start_date"/>
        <result property="attendClassEndDate" column="attend_class_end_date"/>
        <result property="scheduleCap" column="schedule_cap"/>
        <result property="attendClassType" column="attend_class_type"/>
        <result property="ctime" column="ctime"/>
        <result property="creator" column="creator"/>
        <result property="mtime" column="mtime"/>
        <result property="modifer" column="modifer"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>

    <!--获取主讲端排课列表-->
    <select id="getCourseListByDeviceId" resultType="com.yuedu.ydsf.eduConnect.api.vo.SsCourseScheduleVO">
        select t2.id,
               t2.room_uuid,
               t2.class_id,
               t2.course_schedule_id,
               t2.course_schedule_books_id,
               t2.course_schedule_rule_id,
               t2.attend_class_date,
               t2.attend_class_start_time,
               t2.attend_class_end_time,
               t2.is_sync_agora,
               t2.attend_class_type,
               t2.supervision_class_url,
               t2.supervision_class_start_time,
               t2.supervision_class_end_time,
               t2.lecturer_id,
               t2.device_id,
               t2.class_room_id,
               t2.books_id,
               t2.books_name,
               t2.recording_id,
               t2.lecturer_room_code,
               t2.class_room_code
        from ss_device t1
                 left join ss_class_time t2 on t1.id = t2.device_id
                 left join ss_course_schedule t3 on t3.id = t2.course_schedule_id
        where t1.id = #{deviceId} AND CONCAT(t2.attend_class_date, ' ', t2.attend_class_end_time) >= #{attendTimeEndTime};
    </select>
</mapper>
