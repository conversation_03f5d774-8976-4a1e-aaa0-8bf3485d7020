<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.InformationAuthStoreHistoryMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.InformationAuthStoreHistory">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            <result property="storeId" column="store_id" jdbcType="BIGINT"/>
            <result property="informationId" column="information_id" jdbcType="BIGINT"/>
            <result property="optType" column="opt_type" jdbcType="INTEGER"/>
    </resultMap>
    <insert id="batchSave">
        insert into ss_information_auth_store_history (information_id, store_id,create_time,create_by,update_time,update_by,opt_type) values
        <foreach item="item" index="index" collection="list"
                 separator=",">
            (#{item.informationId},#{item.storeId},#{item.createTime},#{item.createBy},#{item.updateTime},#{item.updateBy},#{item.optType})
        </foreach>
    </insert>

</mapper>
