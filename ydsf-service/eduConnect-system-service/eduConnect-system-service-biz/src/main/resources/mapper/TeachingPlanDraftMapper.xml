<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDraftMapper">

  <resultMap id="teachingPlanDraftMap" type="com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft">
        <id property="id" column="id"/>
        <result property="liveRoomPlanId" column="live_room_plan_id"/>
        <result property="planStatus" column="plan_status"/>
        <result property="courseId" column="course_id"/>
        <result property="courseName" column="course_name"/>
        <result property="lectureId" column="lecture_id"/>
        <result property="lectureName" column="lecture_name"/>
        <result property="stage" column="stage"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
    <select id="selectCustomPage" resultType="com.yuedu.ydsf.eduConnect.entity.TeachingPlanDraft">
        SELECT
        t1.id,
        t1.live_room_plan_id,
        t1.plan_status,
        t1.course_id,
        t1.course_name,
        t1.lecture_id,
        t1.lecture_name,
        t1.stage,
        t1.create_by,
        t1.create_time,
        t1.update_by,
        t1.update_time,
        t1.del_flag
        FROM
        ea_teaching_plan_draft t1
        <if test="query.liveRoomId != null or query.liveRoomPlanId != null">
            left join ea_live_room_plan_version t2 on t1.live_room_plan_id = t2.plan_id and t2.online_version = 1
        </if>
        WHERE
        t1.del_flag = 0
        <if test="query.lectureId != null">
            AND t1.lecture_id = #{query.lectureId}
        </if>
        <if test="query.stage != null">
            AND t1.stage = #{query.stage}
        </if>
        <if test="query.liveRoomPlanId != null">
            AND t2.id = #{query.liveRoomPlanId}
        </if>
        <if test="query.courseName != null and query.courseName != ''">
            AND t1.course_name like concat('%',#{query.courseName},'%')
        </if>
        <if test="query.liveRoomId != null">
            AND t2.live_room_id = #{query.liveRoomId}
        </if>
        ORDER BY
        t1.plan_status ASC,
        CASE
        WHEN t1.plan_status = 0 THEN t1.create_time
        WHEN t1.plan_status = 1 THEN t1.update_time
        END DESC
    </select>
</mapper>
