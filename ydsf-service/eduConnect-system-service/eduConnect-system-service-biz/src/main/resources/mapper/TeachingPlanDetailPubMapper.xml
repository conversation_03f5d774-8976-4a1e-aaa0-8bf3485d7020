<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.TeachingPlanDetailPubMapper">

    <resultMap id="teachingPlanDetailPubMap"
               type="com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub">
        <id property="id" column="id"/>
        <result property="planId" column="plan_id"/>
        <result property="lessonOrder" column="lesson_order"/>
        <result property="courseId" column="course_id"/>
        <result property="courseName" column="course_name"/>
        <result property="lessonId" column="lesson_id"/>
        <result property="lessonName" column="lesson_name"/>
        <result property="bookId" column="book_id"/>
        <result property="bookName" column="book_name"/>
        <result property="classDate" column="class_date"/>
        <result property="classStartTime" column="class_start_time"/>
        <result property="classEndTime" column="class_end_time"/>
        <result property="classStartDateTime" column="class_start_date_time"/>
        <result property="classEndDateTime" column="class_end_date_time"/>
        <result property="lectureId" column="lecture_id"/>
        <result property="lectureName" column="lecture_name"/>
        <result property="liveRoomId" column="live_room_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <!--通过教学计划详情id获取课节顺序-->
    <select id="selectLessonOrder" parameterType="java.util.List"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.LessonOrderVO">
        SELECT
        recordTask.teaching_plan_detail_id as planDetailId,
        planDetailPub.lesson_order as lessonOrder
        FROM
        ea_teaching_plan_detail_pub planDetailPub
        LEFT JOIN ea_record_video_task recordTask ON recordTask.teaching_plan_detail_id =
        planDetailPub.id
        WHERE
        recordTask.teaching_plan_detail_id in
        <foreach collection="list" item="planDetailPubId" index="index" open="(" close=")"
                 separator=",">
            #{planDetailPubId}
        </foreach>
    </select>

    <!--通过教学任务Id查询已发布的教学任务详情-->
    <select id="getPlanDetailPubByIdList" parameterType="java.util.List"
            resultMap="teachingPlanDetailPubMap">
        SELECT DISTINCT
        planDetailPub.id,
        planDetailPub.plan_id,
        planDetailPub.lesson_order,
        planDetailPub.course_id,
        planDetailPub.course_name,
        planDetailPub.lesson_id,
        planDetailPub.lesson_name,
        planDetailPub.lecture_id,
        planDetailPub.lecture_name,
        planDetailPub.book_id,
        planDetailPub.book_name,
        planDetailPub.class_date,
        planDetailPub.class_start_time,
        planDetailPub.class_end_time,
        planDetailPub.class_start_date_time,
        planDetailPub.class_end_date_time,
        planDetailPub.live_room_id
        FROM ea_teaching_plan_pub planPub
        LEFT JOIN ea_teaching_plan_detail_pub planDetailPub ON planPub.teaching_plan_id =
        planDetailPub.plan_id
        WHERE
        planPub.del_flag = '0'
        AND planPub.closed = '0'
        AND planDetailPub.del_flag = '0'
        AND planPub.teaching_plan_id in
        <foreach collection="list" item="planId" index="index" open="(" close=")"
                 separator=",">
            #{planId}
        </foreach>
        ORDER BY class_start_date_time
    </select>

    <!--通过教学任务Id查询教学任务详情-->
    <select id="getAllByPlanId" parameterType="Long"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO">
        SELECT liveRoomDetailVersion.id,
               liveRoomDetailVersion.plan_id,
               liveRoomDetailVersion.course_id,
               liveRoomDetailVersion.lesson_id,
               liveRoomDetailVersion.lesson_name,
               liveRoomDetailVersion.lesson_order,
               liveRoomDetailVersion.lecture_id,
               liveRoomDetailVersion.lecture_name,
               liveRoomDetailVersion.class_date,
               liveRoomDetailVersion.class_start_time,
               liveRoomDetailVersion.class_end_time,
               liveRoomDetailVersion.class_start_date_time,
               liveRoomDetailVersion.class_end_date_time,
               liveRoomDetailVersion.time_slot_id
        FROM ea_teaching_plan_pub planPub
                 LEFT JOIN ea_live_room_plan_version liveRoomVersion
                           ON planPub.live_room_plan_id = liveRoomVersion.plan_id
                 LEFT JOIN ea_live_room_plan_detail_version liveRoomDetailVersion
                           ON liveRoomVersion.plan_id = liveRoomDetailVersion.plan_id
                               AND liveRoomVersion.version = liveRoomDetailVersion.version
        WHERE planPub.course_id = planDetailPub.course_id
          AND planPub.lecture_id = planDetailPub.lecture_id
          AND planPub.id = #{planId}
          AND planPub.del_flag = '0'
          AND planPub.closed = '0'
          AND planDetailPub.del_flag = '0';
    </select>


    <select id="getTeachingPlanDetail" parameterType="Long"
            resultType="com.yuedu.ydsf.eduConnect.entity.TeachingPlanDetailPub">
        SELECT planPub.id,
               planPub.lecture_id as lectureIdMain,
               planPub.course_id,
               planDetailPub.lecture_id,
               planDetailPub.lecture_name,
               planPub.live_room_plan_id as liveRoomId
        FROM ea_teaching_plan_pub planPub
                 LEFT JOIN ea_teaching_plan_detail_pub planDetailPub
                           ON planPub.teaching_plan_id = planDetailPub.plan_id
        where planPub.teaching_plan_id = #{planId}
    </select>


</mapper>
