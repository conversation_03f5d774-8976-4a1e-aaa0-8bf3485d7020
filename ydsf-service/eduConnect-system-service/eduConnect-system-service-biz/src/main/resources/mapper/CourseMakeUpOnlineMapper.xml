<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.CourseMakeUpOnlineMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.CourseMakeUpOnline">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="storeId" column="store_id" jdbcType="BIGINT"/>
            <result property="lessonNo" column="lesson_no" jdbcType="BIGINT"/>
            <result property="teachingPlanId" column="teaching_plan_id" jdbcType="BIGINT"/>
            <result property="courseId" column="course_id" jdbcType="BIGINT"/>
            <result property="lessonOrder" column="lesson_order" jdbcType="INTEGER"/>
            <result property="timeSlotId" column="time_slot_id" jdbcType="BIGINT"/>
            <result property="lectureId" column="lecture_id" jdbcType="BIGINT"/>
            <result property="classRoomId" column="class_room_id" jdbcType="BIGINT"/>
            <result property="classId" column="class_id" jdbcType="BIGINT"/>
            <result property="classDate" column="class_date" jdbcType="DATE"/>
            <result property="classStartTime" column="class_start_time" jdbcType="TIME"/>
            <result property="classEndTime" column="class_end_time" jdbcType="TIME"/>
            <result property="validityStartTime" column="validity_start_time" jdbcType="TIMESTAMP"/>
            <result property="validityEndTime" column="validity_end_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <select id="page" resultType="com.yuedu.ydsf.eduConnect.api.vo.CourseMakeUpOnlineVO">
        select
            t1.id,
            t1.store_id,
            t1.lesson_no,
            t1.teaching_plan_id,
            t1.course_id,
            t1.lesson_order,
            t1.time_slot_id,
            t1.lecture_id,
            t1.class_room_id,
            t1.class_id,
            t1.class_date,
            t1.class_start_time,
            t1.class_end_time,
            t1.validity_start_time,
            t1.validity_end_time,
            t1.create_by,
            t1.create_time,
            t1.update_by,
            t1.update_time,
            t1.del_flag
        from
            b_course_make_up_online t1
        <where>
            t1.del_flag = 0
        <if test="query.storeId != null">
            and t1.store_id = #{query.storeId}
        </if>
        <if test="query.validityStartDate != null and query.validityEndDate != null">
            and t1.validity_start_time between CONCAT(#{query.validityStartDate}, ' 00:00:00') and CONCAT(#{query.validityEndDate},' 23:59:59')
        </if>
        <if test="query.classStartDateTime != null and query.classEndDateTime != null">
            and t1.create_time between #{query.classStartDateTime} and #{query.classEndDateTime}
        </if>
        </where>
        order by t1.create_time desc

    </select>
    <select id="export" resultType="com.yuedu.ydsf.eduConnect.entity.CourseMakeUpOnline">
        select
        t1.id,
        t1.store_id,
        t1.lesson_no,
        t1.teaching_plan_id,
        t1.course_id,
        t1.lesson_order,
        t1.time_slot_id,
        t1.lecture_id,
        t1.class_room_id,
        t1.class_id,
        t1.class_date,
        t1.class_start_time,
        t1.class_end_time,
        t1.validity_start_time,
        t1.validity_end_time,
        t1.create_by,
        t1.create_time,
        t1.update_by,
        t1.update_time,
        t1.del_flag
        from
        b_course_make_up_online t1
        <where>
            t1.del_flag = 0
            <if test="query.storeId != null">
                and t1.store_id = #{query.storeId}
            </if>
            <if test="query.validityStartDate != null and query.validityEndDate != null">
                and t1.validity_start_time between CONCAT(#{query.validityStartDate}, ' 00:00:00') and CONCAT(#{query.validityEndDate},' 23:59:59')
            </if>
            <if test="query.classStartDateTime != null and query.classEndDateTime != null">
                and t1.create_time between #{query.classStartDateTime} and #{query.classEndDateTime}
            </if>
        </where>
        order by t1.create_time desc
    </select>

</mapper>
