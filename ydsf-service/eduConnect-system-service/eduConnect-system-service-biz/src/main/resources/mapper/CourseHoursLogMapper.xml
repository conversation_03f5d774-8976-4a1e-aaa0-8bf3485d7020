<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.CourseLiveMapper">

  <resultMap id="courseLiveMap" type="com.yuedu.ydsf.eduConnect.entity.CourseLive">
        <id property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="teachingPlanId" column="teaching_plan_id"/>
        <result property="timeSlotId" column="time_slot_id"/>
        <result property="stage" column="stage"/>
        <result property="classId" column="class_id"/>
        <result property="classroomId" column="classroom_id"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
    <select id="page" resultType="com.yuedu.ydsf.eduConnect.api.vo.CourseLiveVO">
      select
          <if test="query.courseId != null or (query.startTime != null and query.endTime != null) or query.lectureId != null">distinct </if>
          t1.id,
          t1.store_id,
          t1.teaching_plan_id,
          t1.time_slot_id,
          t1.stage,
          t1.class_id,
          t1.classroom_id,
          t1.teacher_id,
          t1.create_by,
          t1.create_time,
          t1.update_by,
          t1.update_time,
          t1.del_flag
      from
          b_course_live t1
        <if test="query.classId != null or query.courseId != null or (query.startTime != null and query.endTime != null)">
            left join b_timetable t2 on t1.id = t2.course_plan_id
        </if>
        <if test="query.lectureId != null">
            left join ea_teaching_plan_pub t3 on t3.teaching_plan_id = t1.teaching_plan_id
        </if>
      <where>
          t1.del_flag = 0
          <if test="query.classId != null or query.courseId != null or (query.startTime != null and query.endTime != null)">
             and t2.course_type = 1
          </if>
          <if test="query.storeId != null"> and t1.store_id = #{query.storeId} </if>
          <if test="query.stage != null"> and t1.stage = #{query.stage} </if>
          <if test="query.teacherId != null"> and t1.teacher_id = #{query.teacherId} </if>
          <if test="query.courseId != null">and t2.course_id = #{query.courseId} </if>
          <if test="query.lectureId != null">and t3.lecture_id = #{query.lectureId} </if>
          <if test="query.startTime != null and query.endTime != null">
              and t2.class_start_date_time between #{query.startTime} and #{query.endTime}
          </if>
      </where>
      order by t1.update_time desc
    </select>
</mapper>
