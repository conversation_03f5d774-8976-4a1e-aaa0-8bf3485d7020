<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanVersionMapper">

    <resultMap id="liveRoomPlanVersionMap"
               type="com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion">
        <id property="id" column="id"/>
        <result property="planId" column="plan_id"/>
        <result property="planName" column="plan_name"/>
        <result property="stage" column="stage"/>
        <result property="liveRoomId" column="live_room_id"/>
        <result property="version" column="version"/>
        <result property="onlineVersion" column="online_version"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <!--获取直播间计划名称-->
    <select id="selectPlanName" parameterType="java.util.List"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.PlanNameVO">
        SELECT DISTINCT
        liveRoomPlanVersion.plan_name AS planName,
        planDetailPub.plan_id AS planId
        FROM
        ea_teaching_plan_detail_pub planDetailPub
        LEFT JOIN ea_teaching_plan_pub planPub ON planDetailPub.plan_id = planPub.teaching_plan_id
        LEFT JOIN ea_live_room_plan_version liveRoomPlanVersion ON planPub.live_room_plan_id =
        liveRoomPlanVersion.plan_id
        WHERE
        planDetailPub.del_flag = 0
        AND liveRoomPlanVersion.del_flag = 0
        AND planPub.del_flag = '0'
        AND planPub.teaching_plan_id IN
        <foreach collection="list" item="planId" index="index" open="(" close=")"
                 separator=",">
            #{planId}
        </foreach>
    </select>
</mapper>
