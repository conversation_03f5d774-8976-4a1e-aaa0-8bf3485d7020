<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsClassTimeAuthRoomMapper">

  <resultMap id="ssClassTimeAuthRoomMap" type="com.yuedu.ydsf.eduConnect.entity.SsClassTimeAuthRoom">
        <id property="id" column="id"/>
        <result property="classId" column="class_id"/>
        <result property="classTimeId" column="class_time_id"/>
        <result property="xgjClassTimeId" column="xgj_class_time_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="classRoomId" column="class_room_id"/>
        <result property="deviceId" column="device_id"/>
        <result property="xgjCampusId" column="xgj_campus_id"/>
        <result property="xgjClassRoomId" column="xgj_class_room_id"/>
        <result property="xiaogjDeleteLog" column="xiaogj_delete_log"/>
        <result property="ctime" column="ctime"/>
        <result property="creator" column="creator"/>
        <result property="mtime" column="mtime"/>
        <result property="modifer" column="modifer"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
    <select id="selectAuthRoomCountByClassTimeIds"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeAuthRoomVO">

    select
        t1.class_time_id as classTimeId,
        count(1) as authRoomCount
    from ss_class_time_auth_room t1
    left join ss_device t2 on t1.device_id = t2.id
    where
        t2.id is not null
        and t2.del_flag = 0
        and t2.device_type = 2
        and t2.device_active = 1
        and t2.class_room_id != -1
        and class_time_id in
        <foreach collection="ids" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>
    group by
        t1.class_time_id
    </select>

    <!--设备ID 获取课次列表-->
    <select id="getClassTimeListByDeviceId"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO">
        select t3.id,
               t3.room_uuid,
               t3.class_id,
               t3.course_schedule_id,
               t3.course_schedule_books_id,
               t3.course_schedule_rule_id,
               t3.attend_class_date,
               t3.attend_class_start_time,
               t3.attend_class_end_time,
               t3.is_sync_agora,
               t3.attend_class_type,
               t3.supervision_class_url,
               t3.supervision_class_start_time,
               t3.supervision_class_end_time,
               t3.lecturer_id,
               t3.device_id,
               t3.class_room_id,
               t3.books_id,
               t3.books_name,
               t3.recording_id,
               t3.lecturer_room_code,
               t3.class_room_code
        from ss_class_time_auth_room t1
                 left join ss_class_time t3 on t1.class_time_id = t3.id
        where t1.device_id = #{deviceId}
              AND CONCAT(t3.attend_class_date, ' ', t3.attend_class_end_time) >= #{attendTimeEndTime};
    </select>
</mapper>
