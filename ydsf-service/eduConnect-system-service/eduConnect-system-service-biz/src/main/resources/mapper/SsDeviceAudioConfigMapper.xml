<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsDeviceAudioConfigMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.SsDeviceAudioConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="parameters" column="parameters" jdbcType="VARCHAR"/>
            <result property="inClassParameters" column="in_class_parameters" jdbcType="VARCHAR"/>
            <result property="adjustRecordingSignalVolume" column="adjust_recording_signal_volume" jdbcType="INTEGER"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
            <result property="modifer" column="modifer" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

</mapper>
