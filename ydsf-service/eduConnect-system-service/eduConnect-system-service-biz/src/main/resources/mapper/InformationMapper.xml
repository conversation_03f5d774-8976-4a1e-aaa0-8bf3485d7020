<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.InformationMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.Information">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            <result property="contentsName" column="contents_name" jdbcType="VARCHAR"/>
            <result property="pid" column="pid" jdbcType="BIGINT"/>
            <result property="isRoot" column="is_root" jdbcType="INTEGER"/>
    </resultMap>
    <select id="page" resultType="com.yuedu.ydsf.eduConnect.api.vo.InformationVO">
        select
            t.id,
            t.contents_name contentsName,
            t.pid,
            t.is_root isRoot,
            t.create_by createBy,
            t.create_time createTime,
            t.update_by updateBy,
            t.update_time updateTime,
            t.del_flag delFlag
        from
            ss_information t
        <where>
            t.del_flag = 0
            and t.is_root = 1
            <if test="query.contentsName != null">
                and t.contents_name like concat('%',#{query.contentsName},)
            </if>
        </where>

        order by t.create_time asc
    </select>
    <select id="getChildTotalByInformationIds"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.InformationVO">
        select
          pid as pid,
          count(1) as childCount
        from ss_information
        where
            del_flag = 0
        and pid in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        group by pid
    </select>


</mapper>
