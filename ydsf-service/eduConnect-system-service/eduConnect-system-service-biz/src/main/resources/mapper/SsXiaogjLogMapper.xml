<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsXiaogjLogMapper">

  <resultMap id="ssXiaogjLogMap" type="com.yuedu.ydsf.eduConnect.entity.SsXiaogjLog">
        <id property="id" column="id"/>
        <result property="requestId" column="request_id"/>
        <result property="requestParam" column="request_param"/>
        <result property="responseParam" column="response_param"/>
        <result property="responseCode" column="response_code"/>
        <result property="eventKey" column="event_key"/>
        <result property="timestamp" column="timestamp"/>
        <result property="ctime" column="ctime"/>
        <result property="creator" column="creator"/>
        <result property="mtime" column="mtime"/>
        <result property="modifer" column="modifer"/>
  </resultMap>

    <!--查询同步校管家班级排课异常数据-->
    <select id="xiaogjListPage" parameterType="com.yuedu.ydsf.eduConnect.api.dto.SsXiaogjLogDTO"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.SsXiaogjLogVO" >
        select
        t1.id,
        t1.request_id,
        t1.request_param,
        t1.response_param,
        t1.response_code,
        t1.event_key,
        t1.timestamp,
        t1.ctime,
        t1.creator,
        t1.mtime,
        t1.modifer
        from
        ss_xiaogj_log as t1
        <where>
            <if test="query.eventKey != null and query.eventKey != ''">and t1.event_key = #{ query.eventKey }</if>
            <if test="query.requestId != null and query.requestId != ''">and t1.request_id = #{ query.requestId }</if>
            <if test="query.responseCode == null ">
                and (
                t1.response_code = 400
                or t1.response_code is null
                )
            </if>
            <if test="query.responseCode != null and query.responseCode != 404">
                and t1.response_code = #{ query.responseCode }
            </if>
            <if test="query.responseCode != null and query.responseCode == 404">
                and t1.response_code is null
            </if>
        </where>
        order by t1.id
    </select>


</mapper>
