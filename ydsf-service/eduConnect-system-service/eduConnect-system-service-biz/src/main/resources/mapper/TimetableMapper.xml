<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.TimetableMapper">

  <resultMap id="timetableMap" type="com.yuedu.ydsf.eduConnect.entity.Timetable">
        <id property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="teachingPlanId" column="teaching_plan_id"/>
        <result property="lessonNo" column="lesson_no"/>
        <result property="coursePlanId" column="course_plan_id"/>
        <result property="courseType" column="course_type"/>
        <result property="classId" column="class_id"/>
        <result property="classroomId" column="classroom_id"/>
        <result property="lectureId" column="lecture_id"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="courseId" column="course_id"/>
        <result property="lessonOrder" column="lesson_order"/>
        <result property="timeSlotId" column="time_slot_id"/>
        <result property="timeSlotType" column="time_slot_type"/>
        <result property="classDate" column="class_date"/>
        <result property="classStartTime" column="class_start_time"/>
        <result property="classEndTime" column="class_end_time"/>
        <result property="classStartDateTime" column="class_start_date_time"/>
        <result property="classEndDateTime" column="class_end_date_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="alarmStatus" column="alarm_status"/>
        <result property="alarmTime" column="alarm_time"/>
        <result property="identifyNum" column="identify_num"/>
  </resultMap>

    <update id="updateAlarmStatus">
        update
            b_timetable
        set
            alarm_status = #{alarmStatus},
            alarm_time = #{alarmTime}
        where
            id = #{id} and #{alarmTime} &gt;= alarm_time
    </update>

    <update id="updateIdentifyNum">
        update
            b_timetable
        set
            identify_num = #{identifyNum}
        where
            id = #{id} and #{identifyNum} > identify_num
    </update>

    <select id="page" resultType="com.yuedu.ydsf.eduConnect.api.vo.TimetableVO">
        select
            t.id,
            t.store_id storeId,
            t.teaching_plan_id teachingPlanId,
            t.lesson_no lessonNo,
            t.course_plan_id coursePlanId,
            t.course_type courseType,
            t.class_id classId,
            t.classroom_id classroomId,
            t.lecture_id lectureId,
            t.teacher_id teacherId,
            t.course_id courseId,
            t.lesson_order lessonOrder,
            t.time_slot_id timeSlotId,
            t.time_slot_type timeSlotType,
            t.class_date classDate,
            t.class_start_time classStartTime,
            t.class_end_time classEndTime,
            t.class_start_date_time classStartDateTime,
            t.class_end_date_time classEndDateTime,
            t.create_by createBy,
            t.create_time createTime,
            t.update_by updateBy,
            t.update_time updateTime,
            t.update_by updateBy,
            t.del_flag delFlag,
            t.alarm_status alarmStatus,
            t.alarm_time alarmTime,
            t.identify_num identifyNum
        from
            b_timetable t
        <where>
            t.del_flag = 0
            <if test="query.storeId != null">
                and t.store_id = #{query.storeId}
            </if>
            <if test="query.classStartDateTime != null and query.classEndDateTime != null">
                and t.class_start_date_time between #{query.classStartDateTime} and #{query.classEndDateTime}
            </if>
        </where>
        order by t.class_start_date_time asc
    </select>

    <select id="getCourseListByStoreId"
            resultType="com.yuedu.ydsf.eduConnect.entity.Timetable">
        SELECT
        distinct
        t.course_id courseId
        from
        b_timetable t
        <where>
            t.del_flag = 0
            and t.store_id = #{storeId}
        </where>
    </select>
    <select id="export" resultType="com.yuedu.ydsf.eduConnect.api.vo.TimetableVO">
        select
        t.id,
        t.store_id storeId,
        t.teaching_plan_id teachingPlanId,
        t.lesson_no lessonNo,
        t.course_plan_id coursePlanId,
        t.course_type courseType,
        t.class_id classId,
        t.classroom_id classroomId,
        t.lecture_id lectureId,
        t.teacher_id teacherId,
        t.course_id courseId,
        t.lesson_order lessonOrder,
        t.time_slot_id timeSlotId,
        t.time_slot_type timeSlotType,
        t.class_date classDate,
        t.class_start_time classStartTime,
        t.class_end_time classEndTime,
        t.class_start_date_time classStartDateTime,
        t.class_end_date_time classEndDateTime,
        t.create_by createBy,
        t.create_time createTime,
        t.update_by updateBy,
        t.update_time updateTime,
        t.update_by updateBy,
        t.del_flag delFlag,
        t.alarm_status alarmStatus,
        t.alarm_time alarmTime,
        t.identify_num identifyNum
        from
        b_timetable t
        <where>
            t.del_flag = 0
            <if test="query.storeId != null">
                and t.store_id = #{query.storeId}
            </if>
            <if test="query.classStartDateTime != null and query.classEndDateTime != null">
                and t.class_start_date_time between #{query.classStartDateTime} and #{query.classEndDateTime}
            </if>
        </where>
        order by t.class_start_date_time asc
    </select>
</mapper>
