<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.InformationAuthStoreMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.InformationAuthStore">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            <result property="informationId" column="information_id" jdbcType="BIGINT"/>
            <result property="storeId" column="store_id" jdbcType="BIGINT"/>
    </resultMap>
    <insert id="batchSave">
        insert into ss_information_auth_store (information_id, store_id,create_time,create_by,update_time,update_by) values
        <foreach item="item" index="index" collection="list"
                 separator=",">
            (#{item.informationId},#{item.storeId},#{item.createTime},#{item.createBy},#{item.updateTime},#{item.updateBy})
        </foreach>
    </insert>

    <delete id="batchDeleteByInformationIds">
        delete from
           ss_information_auth_store
        where
           information_id = #{id}
           and  store_id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByInformationId">
        delete from
           ss_information_auth_store
        where
           information_id = #{id}
    </delete>

    <select id="getAuthStoreTotalByInformationIds"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.InformationAuthStoreVO">
        select
        information_id as informationId,
        count(1) as authTotal
        from ss_information_auth_store
        where del_flag = 0
        and information_id in
        <foreach item="item" index="index" collection="informationIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        group by information_id
    </select>

</mapper>
