<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsClassTimeMapper">

  <resultMap id="ssClassTimeMap" type="com.yuedu.ydsf.eduConnect.entity.SsClassTime">
        <id property="id" column="id"/>
        <result property="roomUuid" column="room_uuid"/>
        <result property="classId" column="class_id"/>
        <result property="courseScheduleId" column="course_schedule_id"/>
        <result property="courseScheduleBooksId" column="course_schedule_books_id"/>
        <result property="courseScheduleRuleId" column="course_schedule_rule_id"/>
        <result property="attendClassDate" column="attend_class_date"/>
        <result property="attendClassStartTime" column="attend_class_start_time"/>
        <result property="attendClassEndTime" column="attend_class_end_time"/>
        <result property="isSyncAgora" column="is_sync_agora"/>
        <result property="attendClassType" column="attend_class_type"/>
        <result property="supervisionClassUrl" column="supervision_class_url"/>
        <result property="supervisionClassStartTime" column="supervision_class_start_time"/>
        <result property="supervisionClassEndTime" column="supervision_class_end_time"/>
        <result property="lecturerId" column="lecturer_id"/>
        <result property="deviceId" column="device_id"/>
        <result property="classRoomId" column="class_room_id"/>
        <result property="booksId" column="books_id"/>
        <result property="booksName" column="books_name"/>
        <result property="recordingId" column="recording_id"/>
        <result property="lecturerRoomCode" column="lecturer_room_code"/>
        <result property="classRoomCode" column="class_room_code"/>
        <result property="ctime" column="ctime"/>
        <result property="creator" column="creator"/>
        <result property="mtime" column="mtime"/>
        <result property="modifer" column="modifer"/>
        <result property="delFlag" column="delFlag"/>
  </resultMap>

    <!--通过班级ID查询各状态课次数-->
    <select id="selectAttendedClassCount" parameterType="com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.AttendedClassCountVO">
        select
            t1.class_id,
            count(
                (
                    DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' )
                    and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) &lt;= DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i' )
                ) OR NULL
            ) AS goingLessonsAttendedCount,
            count(
                (
                    DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) > DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i' )
                ) OR NULL
            ) AS lessonsAttendedCount,
            count(
                (
                    DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) &lt; DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' )
                ) OR NULL
            ) AS notLessonsAttendedCount,
            count( 1 ) scheduledClassCount
        from
        ss_class_time as t1
        <where>
            <if test="delFlag != null ">and t1.del_flag = #{ delFlag }</if>
            <if test="classId != null ">and t1.class_id = #{ classId }</if>
            <if test="classIdList != null and classIdList.size > 0">
                and t1.class_id in
                <foreach collection="classIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by t1.class_id
    </select>

    <!--通过班级ID查询课程安排-->
    <select id="getClassTimeByClassId" parameterType="com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO">
        select
            t1.id,
            t1.room_uuid,
            t1.class_id,
            t1.course_schedule_id,
            t1.course_schedule_books_id,
            t1.course_schedule_rule_id,
            t1.attend_class_date,
            t1.attend_class_start_time,
            t1.attend_class_end_time,
            t1.is_sync_agora,
            t1.attend_class_type,
            t1.supervision_class_url,
            t1.supervision_class_start_time,
            t1.supervision_class_end_time,
            t1.ctime,
            t1.creator,
            t1.mtime,
            t1.modifer,
            t1.lecturer_id,
            t1.device_id,
            t1.class_room_id,
            t1.recording_id,
            t1.books_id,
            t1.books_name,
            t2.is_sync_xiaogj,
            t2.class_name,
            t3.device_name liveDeviceName,
            DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i:%s' ) attendClassDateStartTime,
            DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i:%s' ) attendClassDateEndTime
        from
        ss_class_time as t1
        left join ss_class t2 on t2.id = t1.class_id
        left join ss_device t3 on t3.id = t1.device_id
        <where>
            <if test="roomUuid != null and roomUuid != '' ">and t1.room_uuid = #{ roomUuid }</if>
            <if test="id != null ">and t1.id = #{ id }</if>
            <if test="classTimeIdList != null and classTimeIdList.size > 0">
                and t1.id in
                <foreach collection="classTimeIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="notClassTimeIdList != null and notClassTimeIdList.size > 0">
                and t1.id not in
                <foreach collection="notClassTimeIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="classId != null ">and t1.class_id = #{ classId }</if>
            <if test="courseScheduleId != null">and t1.course_schedule_id = #{ courseScheduleId }</if>
            <if test="courseScheduleBooksId != null ">and t1.course_schedule_books_id = #{ courseScheduleBooksId }</if>
            <if test="isSyncAgora != null">and t1.is_sync_agora = #{ isSyncAgora }</if>
            <if test="attendClassType != null">and t1.attend_class_type = #{ attendClassType }</if>
            <if test="supervisionClassStartTime != null">and t1.supervision_class_start_time = #{ supervisionClassStartTime }</if>
            <if test="supervisionClassEndTime != null">and t1.supervision_class_end_time = #{ supervisionClassEndTime }</if>
            <if test="ctime != null ">and t1.ctime = #{ ctime }</if>
            <if test="creator != null and creator != ''">and t1.creator like concat('%',#{ creator }, '%')</if>
            <if test="mtime != null ">and t1.mtime = #{ mtime }</if>
            <if test="modifer != null and modifer != ''">and t1.modifer like concat('%',#{ modifer }, '%')</if>
            <if test="selectAttendClassStartTime != null ">
                <![CDATA[ and DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT(#{selectAttendClassStartTime}, '%Y-%m-%d %H:%i') ]]>
            </if>
            <if test="selectAttendClassEndTime != null ">
                <![CDATA[ and DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i' ) <= DATE_FORMAT(#{selectAttendClassEndTime}, '%Y-%m-%d %H:%i') ]]>
            </if>
            <if test="booksId != null and booksId != '' ">and t1.books_id = #{ booksId }</if>
            <if test="booksName != null and booksName != '' ">and t1.books_name like concat('%', #{ booksName }, '%')</if>
            <if test="lecturerId != null">and t1.lecturer_id = #{ lecturerId }</if>
            <if test="deviceId != null">and t1.device_id = #{ deviceId }</if>
            <if test="classRoomId != null">and t1.class_room_id = #{ classRoomId }</if>
            <!-- 查询进行中-->
            <if test="attendClassState == 0 ">
                <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ) ]]>
                <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) &lt;= DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i' ) ]]>
            </if>
            <!-- 查询未开始-->
            <if test="attendClassState == 3 ">
                <![CDATA[ and DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( NOW() , '%Y-%m-%d %H:%i') ]]>
            </if>
            <!-- 查询已结束-->
            <if test="attendClassState == 4 ">
                <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) > DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i' ) ]]>
            </if>
            <!-- 查询进行中 + 未开始-->
            <if test="attendClassState == 5 ">
                and
                (
                    (
                        <![CDATA[ DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ) ]]>
                        <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) <= DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i' ) ]]>
                    )
                    or
                    (
                        <![CDATA[ DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( NOW() , '%Y-%m-%d %H:%i') ]]>
                    )
                )
            </if>
        </where>
        order by CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ) DESC

    </select>

    <!--通过班级ID查询课程安排(分页)-->
    <select id="getClassTimeByClassIdPage" parameterType="com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO">
        select
        t1.id,
        t1.room_uuid,
        t1.class_id,
        t1.course_schedule_id,
        t1.course_schedule_books_id,
        t1.course_schedule_rule_id,
        t1.attend_class_date,
        t1.attend_class_start_time,
        t1.attend_class_end_time,
        t1.is_sync_agora,
        t1.attend_class_type,
        t1.supervision_class_url,
        t1.supervision_class_start_time,
        t1.supervision_class_end_time,
        t1.ctime,
        t1.creator,
        t1.mtime,
        t1.modifer,
        t1.lecturer_id,
        t1.device_id,
        t1.class_room_id,
        t1.recording_id,
        t1.books_id,
        t1.books_name,
        t2.is_sync_xiaogj,
        t2.class_name,
        t3.device_name liveDeviceName,
        DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i:%s' ) attendClassDateStartTime,
        DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i:%s' ) attendClassDateEndTime
        from
        ss_class_time as t1
        left join ss_class t2 on t2.id = t1.class_id
        left join ss_device t3 on t3.id = t1.device_id
        <where>
            <if test="query.roomUuid != null and query.roomUuid != '' ">and t1.room_uuid = #{ query.roomUuid }</if>
            <if test="query.id != null ">and t1.id = #{ query.id }</if>
            <if test="query.classTimeIdList != null and query.classTimeIdList.size > 0">
                and t1.id in
                <foreach collection="query.classTimeIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.notClassTimeIdList != null and query.notClassTimeIdList.size > 0">
                and t1.id not in
                <foreach collection="query.notClassTimeIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.classId != null ">and t1.class_id = #{ query.classId }</if>
            <if test="query.courseScheduleId != null">and t1.course_schedule_id = #{ query.courseScheduleId }</if>
            <if test="query.courseScheduleBooksId != null ">and t1.course_schedule_books_id = #{ query.courseScheduleBooksId }</if>
            <if test="query.isSyncAgora != null">and t1.is_sync_agora = #{ query.isSyncAgora }</if>
            <if test="query.attendClassType != null">and t1.attend_class_type = #{ query.attendClassType }</if>
            <if test="query.supervisionClassStartTime != null">and t1.supervision_class_start_time = #{ query.supervisionClassStartTime }</if>
            <if test="query.supervisionClassEndTime != null">and t1.supervision_class_end_time = #{ query.supervisionClassEndTime }</if>
            <if test="query.ctime != null ">and t1.ctime = #{ query.ctime }</if>
            <if test="query.creator != null and query.creator != ''">and t1.creator like concat('%',#{ query.creator }, '%')</if>
            <if test="query.mtime != null ">and t1.mtime = #{ query.mtime }</if>
            <if test="query.modifer != null and query.modifer != ''">and t1.modifer like concat('%',#{ query.modifer }, '%')</if>
            <if test="query.selectAttendClassStartTime != null ">
                <![CDATA[ and DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT(#{query.selectAttendClassStartTime}, '%Y-%m-%d %H:%i') ]]>
            </if>
            <if test="query.selectAttendClassEndTime != null ">
                <![CDATA[ and DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i' ) <= DATE_FORMAT(#{query.selectAttendClassEndTime}, '%Y-%m-%d %H:%i') ]]>
            </if>
            <if test="query.booksId != null and query.booksId != '' ">and t1.books_id = #{ query.booksId }</if>
            <if test="query.booksName != null and query.booksName != '' ">and t1.books_name like concat('%', #{ query.booksName }, '%')</if>
            <if test="query.lecturerId != null">and t1.lecturer_id = #{ query.lecturerId }</if>
            <if test="query.deviceId != null">and t1.device_id = #{ query.deviceId }</if>
            <if test="query.classRoomId != null">and t1.class_room_id = #{ query.classRoomId }</if>
            <!-- 查询进行中-->
            <if test="query.attendClassState == 0 ">
                <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ) ]]>
                <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) &lt;= DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i' ) ]]>
            </if>
            <!-- 查询未开始-->
            <if test="query.attendClassState == 3 ">
                <![CDATA[ and DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( NOW() , '%Y-%m-%d %H:%i') ]]>
            </if>
            <!-- 查询已结束-->
            <if test="query.attendClassState == 4 ">
                <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) > DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i' ) ]]>
            </if>
            <!-- 查询进行中 + 未开始-->
            <if test="query.attendClassState == 5 ">
                and
                (
                (
                <![CDATA[ DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ) ]]>
                <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) <= DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i' ) ]]>
                )
                or
                (
                <![CDATA[ DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( NOW() , '%Y-%m-%d %H:%i') ]]>
                )
                )
            </if>
        </where>
        order by CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ) DESC

    </select>

    <select id="page"  resultType="com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO">
        select
            <if test="query.campusId != null">distinct </if>
            t1.id,
            t1.room_uuid,
            t1.class_id,
            t1.course_schedule_id,
            t1.course_schedule_books_id,
            t1.course_schedule_rule_id,
            t1.attend_class_date,
            t1.attend_class_start_time,
            t1.attend_class_end_time,
            t1.is_sync_agora,
            t1.attend_class_type,
            t1.supervision_class_url,
            t1.supervision_class_start_time,
            t1.supervision_class_end_time,
            t1.ctime,
            t1.creator,
            t1.mtime,
            t1.modifer,
            t1.lecturer_id,
            t1.device_id,
            t1.class_room_id,
            t1.recording_id,
            t1.books_id,
            t1.books_name,
            case
            WHEN DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i:%s' ) &lt; now() then 4
            WHEN DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i:%s' ) &lt; now() &amp;&amp; DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i:%s' ) &gt; now() then 0
            WHEN DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i:%s' ) > now() then 3
            else 1
            end  attendClassState,
            DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i:%s' ) attendClassStartTime
        from
           ss_class_time t1
         <if test="query.campusId != null">
             left join ss_class_time_auth_room t2 on t1.id = t2.class_time_id
         </if>
        <where>
          <if test="query.authDeviceId != null">
                t1.id in (select class_time_id from ss_class_time_auth_room where device_id = #{ query.authDeviceId })
          </if>
          <if test="query.campusId != null">  and t2.campus_id = #{ query.campusId }   </if>
          <if test="query.attendClassType != null">and t1.attend_class_type = #{ query.attendClassType }</if>
          <if test="query.classId != null">and t1.class_id = #{query.classId}</if>
          <if test="query.deviceId != null">and t1.device_id = #{query.deviceId}</if>
          <if test="query.lecturerId != null">and t1.lecturer_id = #{query.lecturerId}</if>
          <if test="query.classRoomId != null">and t1.class_room_id = #{ query.classRoomId }</if>
          <if test="query.booksName != null and query.booksName !=''">and t1.books_name like concat('%',#{query.booksName}, '%')</if>
          <if test="query.selectAttendClassStartTime != null "><!-- 开始时间检索 -->
            and date_format(CONCAT(t1.attend_class_date, ' ', t1.attend_class_start_time),'%Y-%m-%d %H:%i:%s')  &gt;= date_format(#{query.selectAttendClassStartTime},'%Y-%m-%d %H:%i:%s')
           </if>
           <if test="query.selectAttendClassEndTime != null "><!-- 结束时间检索 -->
            and date_format(CONCAT(t1.attend_class_date, ' ', t1.attend_class_end_time),'%Y-%m-%d %H:%i:%s')  &lt;= date_format(#{query.selectAttendClassEndTime},'%Y-%m-%d %H:%i:%s')
           </if>
            <if test="query.attendClassState != null">
                <!-- 进行中 -->
                <if test="query.attendClassState == '0'.toString()">
                    and now() between
                    date_format(CONCAT(t1.attend_class_date, ' ', t1.attend_class_start_time),'%Y-%m-%d %H:%i:%s') and
                    date_format(CONCAT(t1.attend_class_date, ' ', t1.attend_class_end_time),'%Y-%m-%d %H:%i:%s')
                </if>
                <!-- 未开始 -->
                <if test="query.attendClassState == '3'.toString()">
                    and now()   &lt;= date_format(CONCAT(t1.attend_class_date, ' ', t1.attend_class_start_time),'%Y-%m-%d %H:%i:%s')
                </if>
                <!-- 已结束 -->
                <if test="query.attendClassState == '4'.toString()">
                    and now() &gt;= date_format(CONCAT(t1.attend_class_date, ' ', t1.attend_class_end_time),'%Y-%m-%d %H:%i:%s')
                </if>
            </if>
            <if test="query.classIdList != null and query.classIdList.size > 0">
                and t1.class_id in
                <foreach collection="query.classIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by attendClassState,attendClassStartTime
    </select>

    <!--查询班级下指定授权设备进行中,未开始的课次信息-->
    <select id="getDeviceClassTimeByClassId" parameterType="com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO">
        select
            t2.id,
            t2.room_uuid,
            t2.class_id,
            t2.course_schedule_id,
            t2.course_schedule_books_id,
            t2.course_schedule_rule_id,
            t2.attend_class_date,
            t2.attend_class_start_time,
            t2.attend_class_end_time,
            t2.is_sync_agora,
            t2.attend_class_type,
            t2.supervision_class_url,
            t2.supervision_class_start_time,
            t2.supervision_class_end_time,
            t2.ctime,
            t2.creator,
            t2.mtime,
            t2.modifer,
            t2.lecturer_id,
            t2.device_id,
            t2.class_room_id,
            t2.books_id,
            t2.books_name,
            t1.id classTimeAuthRoomId,
            t1.xgj_class_time_id,
            t1.device_id authDeviceId,
            t1.campus_id authCampusId,
            t1.class_room_id authClassRoomId,
            t1.xgj_campus_id xgjCampusId,
            t1.xgj_class_room_id xgjClassRoomId,
            t3.id classAuthRoomId,
            t3.xgj_class_id,
            t3.class_time_ids,
            t4.class_name,
            t4.is_sync_xiaogj,
            DATE_FORMAT( CONCAT( t2.attend_class_date, ' ', t2.attend_class_start_time ), '%Y-%m-%d %H:%i:%s' ) attendClassDateStartTime,
            DATE_FORMAT( CONCAT( t2.attend_class_date, ' ', t2.attend_class_end_time ), '%Y-%m-%d %H:%i:%s' ) attendClassDateEndTime
        from
        ss_class_time_auth_room t1
        LEFT JOIN ss_class_time t2 ON t2.id = t1.class_time_id
        LEFT JOIN ss_class_auth_room t3 ON t3.class_id = t1.class_id AND t3.campus_id = t1.campus_id AND t3.device_id = t1.device_id
        LEFT JOIN ss_class t4 on t4.id = t2.class_id
        <where>
            <if test="id != null ">and t1.class_time_id = #{ id }</if>
            <if test="classTimeIdList != null and classTimeIdList.size > 0">
                and t1.class_time_id in
                <foreach collection="classTimeIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="notClassTimeIdList != null and notClassTimeIdList.size > 0">
                and t1.class_time_id not in
                <foreach collection="notClassTimeIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deviceId != null ">and t1.device_id = #{ deviceId }</if>
            <if test="authDeviceIdList != null and authDeviceIdList.size > 0">
                and t1.device_id in
                <foreach collection="authDeviceIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="classId != null ">and t1.class_id = #{ classId }</if>
            <if test="attendClassType != null">and t2.attend_class_type = #{ attendClassType }</if>
            <!-- 查询进行中-->
            <if test="attendClassState == 0 ">
                <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( CONCAT( t2.attend_class_date, ' ', t2.attend_class_start_time ), '%Y-%m-%d %H:%i' ) ]]>
                <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) &lt;= DATE_FORMAT( CONCAT( t2.attend_class_date, ' ', t2.attend_class_end_time ), '%Y-%m-%d %H:%i' ) ]]>
            </if>
            <!-- 查询未开始-->
            <if test="attendClassState == 3 ">
                <![CDATA[ and DATE_FORMAT( CONCAT( t2.attend_class_date, ' ', t2.attend_class_start_time ), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( NOW() , '%Y-%m-%d %H:%i') ]]>
            </if>
            <!-- 查询已结束-->
            <if test="attendClassState == 4 ">
                <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) > DATE_FORMAT( CONCAT( t2.attend_class_date, ' ', t2.attend_class_end_time ), '%Y-%m-%d %H:%i' ) ]]>
            </if>
            <!-- 查询进行中 + 未开始-->
            <if test="attendClassState == 5 ">
                and
                (
                    (
                        <![CDATA[ DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( CONCAT( t2.attend_class_date, ' ', t2.attend_class_start_time ), '%Y-%m-%d %H:%i' ) ]]>
                        <![CDATA[ and DATE_FORMAT( NOW(), '%Y-%m-%d %H:%i' ) <= DATE_FORMAT( CONCAT( t2.attend_class_date, ' ', t2.attend_class_end_time ), '%Y-%m-%d %H:%i' ) ]]>
                    )
                or
                    (
                        <![CDATA[ DATE_FORMAT( CONCAT( t2.attend_class_date, ' ', t2.attend_class_start_time ), '%Y-%m-%d %H:%i' ) >= DATE_FORMAT( NOW() , '%Y-%m-%d %H:%i') ]]>
                    )
                )
            </if>
        </where>
        group by t1.id
    </select>
    <select id="selectDeviceInfosByClassId"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO">

     select
        t1.xgj_class_time_id,
        t2.xgj_class_id
     from
         ss_class_time_auth_room t1
         left join ss_class_auth_room t2 ON t2.class_id = t1.class_id AND t2.campus_id = t1.campus_id AND t2.device_id = t1.device_id
     <where>
        <if test="id != null ">and t1.class_time_id = #{ id }</if>
        <if test="authDeviceIdList != null and authDeviceIdList.size > 0">
            and t1.device_id in
            <foreach collection="authDeviceIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="classId != null ">and t1.class_id = #{ classId }</if>
     </where>
    </select>

    <!--根据id获取课次信息-->
    <select id="selectDeviceClassTimeByIds"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO">
        select t2.id,
                t2.room_uuid,
                t2.class_id,
                t2.course_schedule_id,
                t2.course_schedule_books_id,
                t2.course_schedule_rule_id,
                t2.attend_class_date,
                t2.attend_class_start_time,
                t2.attend_class_end_time,
                t2.is_sync_agora,
                t2.attend_class_type,
                t2.supervision_class_url,
                t2.supervision_class_start_time,
                t2.supervision_class_end_time,
                t2.ctime,
                t2.creator,
                t2.mtime,
                t2.modifer,
                t2.lecturer_id,
                t2.device_id,
                t2.class_room_id,
                t2.books_id,
                t2.books_name,
                t1.id                            classTimeAuthRoomId,
                t1.xgj_class_time_id,
                t1.device_id                     authDeviceId,
                t1.campus_id                     authCampusId,
                t1.class_room_id                 authClassRoomId,
                t1.xgj_campus_id                 xgjCampusId,
                t1.xgj_class_room_id             xgjClassRoomId,
                t3.id                            classAuthRoomId,
                t3.xgj_class_id,
                t3.class_time_ids,
                t4.xgj_lecturer_id,
                t5.class_name,
                t5.is_sync_xiaogj,
                DATE_FORMAT(CONCAT(t2.attend_class_date, ' ', t2.attend_class_start_time),
                '%Y-%m-%d %H:%i:%s') attendClassDateStartTime,
                DATE_FORMAT(CONCAT(t2.attend_class_date, ' ', t2.attend_class_end_time),
                '%Y-%m-%d %H:%i:%s') attendClassDateEndTime
                from ss_class_time_auth_room t1
                LEFT JOIN ss_class_time t2 ON t2.id = t1.class_time_id
                LEFT JOIN ss_class_auth_room t3
                ON t3.class_id = t1.class_id AND t3.campus_id = t1.campus_id AND t3.device_id = t1.device_id
                LEFT JOIN ss_lecturer t4 on t4.id = t2.lecturer_id
                LEFT JOIN ss_class t5 on t5.id = t2.class_id
        WHERE
        t2.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getFreeTimeClassTimes"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO">
        select t1.id,
        room_uuid,
        class_id,
        course_schedule_id,
        course_schedule_books_id,
        course_schedule_rule_id,
        attend_class_date,
        attend_class_start_time,
        attend_class_end_time,
        is_sync_agora,
        attend_class_type,
        supervision_class_url,
        supervision_class_start_time,
        supervision_class_end_time,
        lecturer_id,
        device_id,
        class_room_id,
        books_id,
        books_name,
        recording_id,
        lecturer_room_code,
        class_room_code
        from ss_class_time t1
        <where>
            t1.attend_class_type = 0
            AND t1.class_room_id IN
            <foreach collection="classRoomIds" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
            <if test="selectAttendClassStartTime != null "><!-- 开始时间检索 -->
                and date_format(CONCAT(t1.attend_class_date, ' ', t1.attend_class_start_time),'%Y-%m-%d %H:%i:%s')  &gt;= date_format(#{selectAttendClassStartTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="selectAttendClassEndTime != null "><!-- 结束时间检索 -->
                and date_format(CONCAT(t1.attend_class_date, ' ', t1.attend_class_end_time),'%Y-%m-%d %H:%i:%s')  &lt;= date_format(#{selectAttendClassEndTime},'%Y-%m-%d %H:%i:%s')
            </if>
        </where>
        ORDER BY t1.attend_class_date, t1.attend_class_start_time
    </select>


    <select id="selectSyncAgora" resultType="com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO">
        select
        t1.id,
        t1.room_uuid,
        t1.class_id,
        t1.course_schedule_id,
        t1.course_schedule_books_id,
        t1.course_schedule_rule_id,
        t1.attend_class_date,
        t1.attend_class_start_time,
        t1.attend_class_end_time,
        t1.is_sync_agora,
        t1.attend_class_type,
        t1.supervision_class_url,
        t1.supervision_class_start_time,
        t1.supervision_class_end_time,
        t1.lecturer_id,
        t1.device_id,
        t1.class_room_id,
        t1.books_id,
        t1.books_name,
        t1.ctime,
        t1.creator,
        t1.mtime,
        t1.modifer,
        DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i:%s' ) attendClassDateStartTime,
        DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_end_time ), '%Y-%m-%d %H:%i:%s' ) attendClassDateEndTime
        from
        ss_class_time as t1
        <where>
            t1.attend_class_type = 0
            and t1.is_sync_agora = 0
            and DATE_SUB(DATE_FORMAT( CONCAT( t1.attend_class_date, ' ', t1.attend_class_start_time ), '%Y-%m-%d %H:%i' ), INTERVAL 24 hour) &lt; now()
        </where>
        order by t1.id desc
    </select>


</mapper>
