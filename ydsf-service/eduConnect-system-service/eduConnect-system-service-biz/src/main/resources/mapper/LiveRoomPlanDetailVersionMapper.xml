<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.LiveRoomPlanDetailVersionMapper">

    <resultMap id="liveRoomPlanDetailVersionMap"
               type="com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanDetailVersion">
        <id property="id" column="id"/>
        <result property="planId" column="plan_id"/>
        <result property="timeSlotId" column="time_slot_id"/>
        <result property="classDate" column="class_date"/>
        <result property="classStartTime" column="class_start_time"/>
        <result property="classEndTime" column="class_end_time"/>
        <result property="classStartDateTime" column="class_start_date_time"/>
        <result property="classEndDateTime" column="class_end_date_time"/>
        <result property="detailType" column="detail_type"/>
        <result property="lessonOrder" column="lesson_order"/>
        <result property="version" column="version"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <!--通过计划Id查询直播间计划明细-->
    <select id="getLiveRoomPlanDetailVersionList" parameterType="java.util.List"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO">
        SELECT  t1.plan_id,
                MIN(t1.class_date) classStartTime,
                MAX(t1.class_date) classEndTime
        FROM ea_live_room_plan_detail_version t1
             left join ea_live_room_plan_version t2 on t1.plan_id = t2.plan_id and t1.version = t2.version
        WHERE t2.online_version = 1 and t1.plan_id in
        <foreach collection="list" item="planId" index="index" open="(" close=")"
                 separator=",">
            #{planId}
        </foreach>
        GROUP BY t1.plan_id
        ORDER BY t1.plan_id
    </select>

    <!--通过直播间计划Id获取直播间计划信息-->
    <select id="selectLiveRoomPlanDetailVersionList" parameterType="java.util.List"
            resultMap="liveRoomPlanDetailVersionMap">
        SELECT liveRoomDetailVersion.id,
               liveRoomDetailVersion.plan_id,
               liveRoomDetailVersion.time_slot_id,
               liveRoomDetailVersion.class_date,
               liveRoomDetailVersion.class_start_time,
               liveRoomDetailVersion.class_end_time,
               liveRoomDetailVersion.class_start_date_time,
               liveRoomDetailVersion.class_end_date_time,
               liveRoomDetailVersion.lesson_order,
               liveRoomDetailVersion.version,
               liveRoomVersion.stage
        FROM ea_live_room_plan_version liveRoomVersion
                 LEFT JOIN ea_live_room_plan_detail_version liveRoomDetailVersion
                           ON liveRoomVersion.plan_id = liveRoomDetailVersion.plan_id
        WHERE liveRoomVersion.plan_id = #{planId}
          AND liveRoomVersion.del_flag = '0'
          AND liveRoomVersion.version = liveRoomDetailVersion.version
          AND liveRoomDetailVersion.del_flag = '0'
          AND liveRoomVersion.online_version = '1'
        ORDER BY liveRoomDetailVersion.class_start_date_time
    </select>
</mapper>
