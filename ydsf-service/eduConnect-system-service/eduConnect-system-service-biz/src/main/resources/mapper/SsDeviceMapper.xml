<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsDeviceMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.SsDevice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="campusId" column="campus_id" jdbcType="BIGINT"/>
            <result property="classRoomId" column="class_room_id" jdbcType="BIGINT"/>
            <result property="deviceName" column="device_name" jdbcType="VARCHAR"/>
            <result property="deviceNo" column="device_no" jdbcType="VARCHAR"/>
            <result property="deviceType" column="device_type" jdbcType="TINYINT"/>
            <result property="deviceState" column="device_state" jdbcType="TINYINT"/>
            <result property="deviceActive" column="device_active" jdbcType="INTEGER"/>
            <result property="deviceArrears" column="device_arrears" jdbcType="INTEGER"/>
            <result property="isOnLine" column="is_on_line" jdbcType="TINYINT"/>
            <result property="indateForever" column="indate_forever" jdbcType="INTEGER"/>
            <result property="indateStart" column="indate_start" jdbcType="TIMESTAMP"/>
            <result property="indateEnd" column="indate_end" jdbcType="TIMESTAMP"/>
            <result property="configId" column="config_id" jdbcType="BIGINT"/>
            <result property="audioConfigId" column="audio_config_id" jdbcType="BIGINT"/>
            <result property="agoraRecordingType" column="agora_recording_type" jdbcType="TINYINT"/>
            <result property="liveBackground" column="live_background" jdbcType="VARCHAR"/>
            <result property="sdkType" column="sdk_type" jdbcType="TINYINT"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
            <result property="modifer" column="modifer" jdbcType="VARCHAR"/>
            <result property="deviceUuid" column="device_uuid" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>


</mapper>
