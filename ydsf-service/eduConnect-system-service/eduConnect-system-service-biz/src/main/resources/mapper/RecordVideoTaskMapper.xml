<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.RecordVideoTaskMapper">

    <resultMap id="recordVideoTaskMap" type="com.yuedu.ydsf.eduConnect.entity.RecordVideoTask">
        <id property="id" column="id"/>
        <result property="teachingPlanId" column="teaching_plan_id"/>
        <result property="teachingPlanDetailId" column="teaching_plan_detail_id"/>
        <result property="lessonOrder" column="lesson_order"/>
        <result property="coursewareId" column="courseware_id"/>
        <result property="coursewareVersion" column="courseware_version"/>
        <result property="courseId" column="course_id"/>
        <result property="courseVersion" column="course_version"/>
        <result property="earliestStartDate" column="earliest_start_date"/>
        <result property="lectureId" column="lecture_id"/>
        <result property="lectureName" column="lecture_name"/>
        <result property="taskStatus" column="task_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <!--通过时间段查询录课任务列表-->
    <select id="getRecordVideoTaskList"
            parameterType="com.yuedu.ydsf.eduConnect.api.query.RecordVideoTaskQuery"
            resultMap="recordVideoTaskMap">
        SELECT id,
        teaching_plan_id,
        teaching_plan_detail_id,
        lesson_order,
        courseware_id,
        courseware_version,
        course_id,
        course_version,
        earliest_start_date,
        lecture_id,
        lecture_name,
        task_status
        FROM ea_record_video_task
        WHERE del_flag = 0
        <if test="lectureId!=null">
            AND lecture_id = #{lectureId}
        </if>
        <if test="startTime!=null and startTime!='' and endTime!=null and endTime!=''">
            AND earliest_start_date BETWEEN #{startTime} AND #{endTime}
        </if>
        ORDER BY earliest_start_date
    </select>


    <!--当前课节下的课件版本信息-->
    <select id="selectByRecordVideoTask"
            parameterType="com.yuedu.ydsf.eduConnect.entity.RecordVideoTask"
            resultMap="recordVideoTaskMap">
        <include refid="recordVideoTaskBaseSql"/>
        WHERE
        teaching_plan_id = #{teachingPlanId}
        AND courseware_id = #{coursewareId}
        <if test="coursewareVersion!=null">
            AND courseware_version = #{coursewareVersion}
        </if>
        AND course_id = #{courseId}
        <if test="lessonVersion!=null">
            AND lesson_version = #{lessonVersion}
        </if>
        AND earliest_start_date = #{earliestStartDate}
        AND lecture_id = #{lectureId}
        AND del_flag = 0
    </select>

    <sql id="recordVideoTaskBaseSql">
        SELECT teaching_plan_id,
               teaching_plan_detail_id,
               lesson_order,
               courseware_id,
               courseware_version,
               course_id,
               course_version,
               earliest_start_date,
               lecture_id,
               lecture_name
        FROM ea_record_video_task
    </sql>

    <!--通过教学计划与课件版本查询已完成的录课任务-->
    <select id="recordTaskOldList" parameterType="com.yuedu.ydsf.eduConnect.entity.RecordVideoTask"
            resultMap="recordVideoTaskMap">
        <include refid="recordVideoTaskBaseSql"/>
        where
        del_flag = 0
        AND course_id = #{courseId}

        AND teaching_plan_id = #{teachingPlanId}
        AND task_status = #{taskStatus}
        AND courseware_id = #{coursewareId}
        AND lesson_version = #{lessonVersion}
        AND courseware_version = #{coursewareVersion}

        AND lecture_id = #{lectureId}
    </select>

    <!--更新未完成的录课任务为新老师-->
    <update id="updateTaskByLectureId"
            parameterType="com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO">
        UPDATE ea_record_video_task
        <set>
            <if test="lectureId!= null ">
                lecture_id = #{lectureId},
            </if>
            <if test="lectureName != null and lectureName != ''">
                lecture_name = #{lectureName},
            </if>
            update_time = now()
        </set>
        WHERE teaching_plan_id = #{planId}
        AND lecture_id = #{lectureIdOld}
        AND course_id = #{courseIdOld}
        AND task_status = 0
    </update>

    <!--修改教学计划讲师后批量修改录课任务-->
    <update id="updateCoursewareVersion"
            parameterType="com.yuedu.ydsf.eduConnect.api.dto.LessonCoursewareEditDTO">
        UPDATE ea_record_video_task
        <set>
            <if test="coursewareVersion!=null">
                courseware_version = #{coursewareVersion},
            </if>
            <if test="lessonVersion!=null">
                lesson_version = #{lessonVersion},
            </if>
        </set>
        WHERE task_status = '0'
        <if test="coursewareId!=null">
            AND courseware_id = #{coursewareId}
        </if>
        <if test="coursewareVersion!=null">
            AND courseware_version <![CDATA[ < ]]> #{coursewareVersion}
        </if>
        <if test="lessonVersion!=null">
            AND lesson_version <![CDATA[ < ]]> #{lessonVersion}
        </if>
        AND del_flag = '0'
    </update>


    <!--删除未完成的任务-->
    <delete id="deleteNotCompletedTask" parameterType="integer">
        delete
        from ea_record_video_task
        where task_status = #{taskStatus}
    </delete>
</mapper>
