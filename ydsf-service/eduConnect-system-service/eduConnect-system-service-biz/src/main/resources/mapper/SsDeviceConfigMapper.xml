<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsDeviceConfigMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.SsDeviceConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tBw" column="t_bw" jdbcType="INTEGER"/>
            <result property="tBh" column="t_bh" jdbcType="INTEGER"/>
            <result property="tBb" column="t_bb" jdbcType="INTEGER"/>
            <result property="tBf" column="t_bf" jdbcType="INTEGER"/>
            <result property="tSw" column="t_sw" jdbcType="INTEGER"/>
            <result property="tSh" column="t_sh" jdbcType="INTEGER"/>
            <result property="tSb" column="t_sb" jdbcType="INTEGER"/>
            <result property="tSf" column="t_sf" jdbcType="INTEGER"/>
            <result property="sBw" column="s_bw" jdbcType="INTEGER"/>
            <result property="sBh" column="s_bh" jdbcType="INTEGER"/>
            <result property="sBb" column="s_bb" jdbcType="INTEGER"/>
            <result property="sBf" column="s_bf" jdbcType="INTEGER"/>
            <result property="sSw" column="s_sw" jdbcType="INTEGER"/>
            <result property="sSh" column="s_sh" jdbcType="INTEGER"/>
            <result property="sSb" column="s_sb" jdbcType="INTEGER"/>
            <result property="sSf" column="s_sf" jdbcType="INTEGER"/>
            <result property="tHd" column="t_hd" jdbcType="TINYINT"/>
            <result property="sHd" column="s_hd" jdbcType="TINYINT"/>
            <result property="sShowNumber" column="s_show_number" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
            <result property="modifer" column="modifer" jdbcType="VARCHAR"/>
            <result property="logEnable" column="log_enable" jdbcType="TINYINT"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

</mapper>
