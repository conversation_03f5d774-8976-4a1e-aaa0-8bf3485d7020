<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.TimetableEventAlarmDeatilsMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.TimetableEventAlarmDeatils">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="storeId" column="store_id" jdbcType="BIGINT"/>
            <result property="timeableId" column="timeable_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            <result property="eventDescribe" column="event_describe" jdbcType="VARCHAR"/>
            <result property="eventTime" column="event_time" jdbcType="TIMESTAMP"/>
            <result property="eventType" column="event_type" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="lessionNo" column="lession_no" jdbcType="BIGINT"/>
    </resultMap>

</mapper>
