<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.TeachingPlanPubMapper">

  <resultMap id="teachingPlanPubMap" type="com.yuedu.ydsf.eduConnect.entity.TeachingPlanPub">
        <id property="id" column="id"/>
        <result property="liveRoomPlanId" column="live_room_plan_id"/>
        <result property="courseId" column="course_id"/>
        <result property="courseName" column="course_name"/>
        <result property="lectureId" column="lecture_id"/>
        <result property="lectureName" column="lecture_name"/>
        <result property="closed" column="closed"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <!--通过课程Id,讲师Id获取可能会出现重复录课任务的教学计划Id列表-->
    <select id="getPlanIdList"
            parameterType="com.yuedu.ydsf.eduConnect.api.dto.GenerateVideoTaskDto"
            resultType="Long">
        SELECT teaching_plan_id as planId
        FROM ea_teaching_plan_pub
        WHERE course_id = #{courseId}
          AND lecture_id = #{lectureId}
        AND del_flag = '0'
        AND closed = '0'
        <if test="operateType==2">
            and id not in (#{planId})
        </if>
    </select>


    <!--通过指定条件查询教学计划列表-->
    <select id="getTeachingPlanList"
            parameterType="com.yuedu.ydsf.eduConnect.api.query.TeachingPlanQuery"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO">
        SELECT DISTINCT planPub.id,
        live_room_plan_id,
        lecture_id,
        lecture_name,
        planPub.stage,
        planPub.course_id,planPub.course_name,
        planPub.teaching_plan_id,
        liveRoomVersion.plan_id as liveRoomPlanId,
        liveRoomVersion.plan_name as liveRoomPlanName
        FROM ea_teaching_plan_pub planPub
        LEFT JOIN ea_live_room_plan_version liveRoomVersion
        ON planPub.live_room_plan_id = liveRoomVersion.plan_id
        WHERE closed = 0
        AND planPub.del_flag = 0
        <if test="stage!=null">
            AND planPub.stage= #{stage}
        </if>
        <if test="lectureId!=null">
            AND planPub.lecture_id= #{lectureId}
        </if>
        order by liveRoomVersion.plan_id
    </select>

    <!--根据教学计划发布表的id集合批量获取对应的信息-->
    <select id="getTeachingPlanPubByTeachingPlanIds"
            resultType="com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO">
        select  t1.course_name       courseName,
                t1.stage             stage,
                t1.lecture_name      lectureName,
                t2.plan_name         liveRoomPlanName,
                t1.live_room_plan_id liveRoomPlanId,
                t2.version           liveRoomPlanVersion,
                t1.teaching_plan_id     teachingPlanId,
                t1.closed                closed,
                t1.lecture_id            lectureId
        from ea_teaching_plan_pub t1
                 left join ea_live_room_plan_version t2 on t1.live_room_plan_id = t2.plan_id and t2.online_version = 1
        where t1.teaching_plan_id in
        <foreach item="item" index="index" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
