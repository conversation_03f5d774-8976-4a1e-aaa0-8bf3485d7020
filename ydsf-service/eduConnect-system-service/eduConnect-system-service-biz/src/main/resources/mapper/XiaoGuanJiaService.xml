<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.SsRecordingMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.SsRecording">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="recordingType" column="recording_type" jdbcType="INTEGER"/>
            <result property="deviceId" column="device_id" jdbcType="BIGINT"/>
            <result property="grade" column="grade" jdbcType="INTEGER"/>
            <result property="booksId" column="books_id" jdbcType="VARCHAR"/>
            <result property="booksName" column="books_name" jdbcType="VARCHAR"/>
            <result property="lecturerId" column="lecturer_id" jdbcType="BIGINT"/>
            <result property="lecturerName" column="lecturer_name" jdbcType="VARCHAR"/>
            <result property="originalCourseStartDate" column="original_course_start_date" jdbcType="TIMESTAMP"/>
            <result property="originalCourseEndDate" column="original_course_end_date" jdbcType="TIMESTAMP"/>
            <result property="agoraRecordId" column="agora_record_id" jdbcType="VARCHAR"/>
            <result property="roomUuid" column="room_uuid" jdbcType="VARCHAR"/>
            <result property="shelfStatus" column="shelf_status" jdbcType="INTEGER"/>
            <result property="recordingStatus" column="recording_status" jdbcType="INTEGER"/>
            <result property="storageType" column="storage_type" jdbcType="INTEGER"/>
            <result property="vodVideoId" column="vod_video_id" jdbcType="VARCHAR"/>
            <result property="recordingResources" column="recording_resources" jdbcType="VARCHAR"/>
            <result property="agoraCloudRecordId" column="agora_cloud_record_id" jdbcType="VARCHAR"/>
            <result property="agoraCloudRecordIndividualId" column="agora_cloud_record_individual_id" jdbcType="VARCHAR"/>
            <result property="cloudRecordingResources" column="cloud_recording_resources" jdbcType="VARCHAR"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
            <result property="modifer" column="modifer" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

</mapper>
