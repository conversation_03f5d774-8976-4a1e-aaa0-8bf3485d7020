<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.mapper.TimetablePictureMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.entity.TimetablePicture">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="timeableId" column="timeable_id" jdbcType="BIGINT"/>
            <result property="deviceId" column="device_id" jdbcType="BIGINT"/>
            <result property="photoUrl" column="photo_url" jdbcType="VARCHAR"/>
            <result property="recognitionTotalNum" column="recognition_total_num" jdbcType="INTEGER"/>
            <result property="recognitionChildrenNum" column="recognition_children_num" jdbcType="INTEGER"/>
            <result property="recognitionTeenagersNum" column="recognition_teenagers_num" jdbcType="INTEGER"/>
            <result property="recognitionYouthNum" column="recognition_youth_num" jdbcType="INTEGER"/>
            <result property="recognitionMiddleNum" column="recognition_middle_num" jdbcType="INTEGER"/>
            <result property="recognitionElderlyNum" column="recognition_elderly_num" jdbcType="INTEGER"/>
            <result property="recognitionStatus" column="recognition_status" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="duration" column="duration" jdbcType="INTEGER"/>
            <result property="lessionNo" column="lession_no" jdbcType="BIGINT"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>
