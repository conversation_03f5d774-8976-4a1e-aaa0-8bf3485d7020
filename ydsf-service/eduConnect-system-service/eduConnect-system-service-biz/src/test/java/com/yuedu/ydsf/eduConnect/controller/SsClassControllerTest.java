package com.yuedu.ydsf.eduConnect.controller;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.alibaba.fastjson.JSON;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassAuthInfoDTO;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassDTO;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * 班级管理 单元测试
 * <AUTHOR>
 * @date 2024/10/22 10:33
 */
@SpringBootTest(classes = EduConnectSystemApp.class)
class SsClassControllerTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mvc;

    @BeforeEach
    public void setup() {
        mvc = MockMvcBuilders.webAppContextSetup(context).apply(springSecurity()).build();
    }

    /**
     * mockUser 对象
     *
     * @return UserDetails
     */
    YdsfUser mockUser() {
        return new YdsfUser(1L,
            "admin",
            1L,
            "",
            "",
            "",
            "",
            "",
            1L,
            "",
            true,
            true,
            "0",
            true,
            true,
            AuthorityUtils.createAuthorityList("ROLE_ADMIN", "edusystem_ssClass_view")

        );
    }

    /**
     * 班级管理分页查询 单元测试
     * @param
     * @return void
     * <AUTHOR>
     * @date 2024/10/22 11:11
     */
    @Test
    void getSsClassPage() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/ssClass/page")
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .queryParam("current", "1")
                .queryParam("size", "10")
                .queryParam("className", "测试")
                .queryParam("grade", "1")
                .queryParam("classState", "1")
                .queryParam("classType", "1")
                .queryParam("classRoomDeviceId", "")
                .with(user(mockUser())))
                .andDo(print())
                .andExpect(status().isOk());
    }

    /**
     * 班级管理查询全部 单元测试
     * @param
     * @return void
     * <AUTHOR>
     * @date 2024/10/22 11:12
     */
    @Test
    void getSsClassList() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/ssClass/getSsClassList")
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .queryParam("className", "测试")
                .queryParam("grade", "")
                .queryParam("classState", "")
                .queryParam("classType", "")
                .queryParam("classRoomDeviceId", "")
                .with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk());
    }

    /**
     * 通过id查询班级信息
     * <AUTHOR>
     * @date 2024/10/23 11:42
     */
    @Test
    void getById() throws Exception {
        Long id = 342L;
        mvc.perform(MockMvcRequestBuilders.get("/ssClass/getSsClassList/{id}", id).with(user(mockUser())))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("code").value(0));

    }

    /**
     * 查询所有教室端有效设备
     * <AUTHOR>
     * @date 2024/10/23 11:42
     */
    @Test
    void getClassRoomDeviceList() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/ssDevice/getClassRoomDeviceList")
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .with(user(mockUser())))
                .andDo(print())
                .andExpect(status().isOk());
    }

    /**
     * 查询班级已授权设备信息
     * <AUTHOR>
     * @date 2024/10/23 11:42
     */
    @Test
    void getAuthDeviceListByClassId() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/ssClass/getAuthDeviceListByClassId")
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .queryParam("classId", "341")
                        .with(user(mockUser())))
                .andDo(print())
                .andExpect(status().isOk());
    }

    /**
     * 查询班级课程安排
     * <AUTHOR>
     * @date 2024/10/23 11:42
     */
    @Test
    void getPageClassTimeByClassId() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/ssClass/getPageClassTimeByClassId")
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .queryParam("classId", "105")
                        .with(user(mockUser())))
                .andDo(print())
                .andExpect(status().isOk());
    }

    /**
     * 新增班级信息表
     * <AUTHOR>
     * @date 2024/10/23 11:42
     */
    @Test
    public void add() throws Exception {
        SsClassDTO ssClassDTO = new SsClassDTO();
        ssClassDTO.setClassName("开发测试");
        ssClassDTO.setGrade(1);
        ssClassDTO.setIsSyncXiaogj(1);
        ssClassDTO.setClassType(1);

        List<SsClassAuthInfoDTO> ssClassAuthInfoDTOList = new ArrayList<>();
        SsClassAuthInfoDTO ssClassAuthInfoDTO = new SsClassAuthInfoDTO();
        ssClassAuthInfoDTO.setCampusId(311L);
        ssClassAuthInfoDTO.setClassRoomId(852L);
        ssClassAuthInfoDTO.setDeviceId(108L);
        ssClassAuthInfoDTO.setXgjCampusId("869305A0-62D1-4A7A-B0E9-0F303BAF9614");
        ssClassAuthInfoDTO.setXgjClassRoomId("CDD55B33-354A-4543-8859-45A552E9BCC6");
        ssClassAuthInfoDTOList.add(ssClassAuthInfoDTO);

        ssClassDTO.setSsClassAuthInfoDTOList(ssClassAuthInfoDTOList);

        mvc.perform(MockMvcRequestBuilders.post("/ssClass/add")
                .with(user(mockUser()))
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(ssClassDTO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("code").value(0));
    }

    /**
     * 修改班级信息表
     * <AUTHOR>
     * @date 2024/10/23 11:42
     */
    @Test
    public void edit() throws Exception {
        SsClassDTO ssClassDTO = new SsClassDTO();
        ssClassDTO.setId(342L);
        ssClassDTO.setClassName("开发测试");
        ssClassDTO.setGrade(1);
        ssClassDTO.setClassType(1);

        List<SsClassAuthInfoDTO> ssClassAuthInfoDTOList = new ArrayList<>();
        SsClassAuthInfoDTO ssClassAuthInfoDTO = new SsClassAuthInfoDTO();
        ssClassAuthInfoDTO.setCampusId(311L);
        ssClassAuthInfoDTO.setClassRoomId(852L);
        ssClassAuthInfoDTO.setDeviceId(108L);
        ssClassAuthInfoDTO.setXgjCampusId("869305A0-62D1-4A7A-B0E9-0F303BAF9614");
        ssClassAuthInfoDTO.setXgjClassRoomId("CDD55B33-354A-4543-8859-45A552E9BCC6");
        ssClassAuthInfoDTOList.add(ssClassAuthInfoDTO);

        ssClassDTO.setSsClassAuthInfoDTOList(ssClassAuthInfoDTOList);

        mvc.perform(MockMvcRequestBuilders.put("/ssClass/edit")
                        .with(user(mockUser()))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(ssClassDTO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("code").value(0));
    }

    /**
     * 班级授权
     * <AUTHOR>
     * @date 2024/10/23 11:42
     */
    @Test
    public void classAuthDevice() throws Exception {
        SsClassDTO ssClassDTO = new SsClassDTO();
        ssClassDTO.setId(342L);
        ssClassDTO.setClassName("开发测试");
        ssClassDTO.setGrade(1);
        ssClassDTO.setClassType(1);

        List<SsClassAuthInfoDTO> ssClassAuthInfoDTOList = new ArrayList<>();
        SsClassAuthInfoDTO ssClassAuthInfoDTO = new SsClassAuthInfoDTO();
        ssClassAuthInfoDTO.setCampusId(311L);
        ssClassAuthInfoDTO.setClassRoomId(852L);
        ssClassAuthInfoDTO.setDeviceId(108L);
        ssClassAuthInfoDTO.setXgjCampusId("869305A0-62D1-4A7A-B0E9-0F303BAF9614");
        ssClassAuthInfoDTO.setXgjClassRoomId("CDD55B33-354A-4543-8859-45A552E9BCC6");
        ssClassAuthInfoDTOList.add(ssClassAuthInfoDTO);

        ssClassDTO.setSsClassAuthInfoDTOList(ssClassAuthInfoDTOList);

        mvc.perform(MockMvcRequestBuilders.put("/ssClass/classAuthDevice")
                        .with(user(mockUser()))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(ssClassDTO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("code").value(0));

    }


}