package com.yuedu.ydsf.eduConnect.controller;

import com.alibaba.fastjson.JSON;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.api.dto.SsRecordingDTO;
import lombok.Data;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/10/23
 **/
@SpringBootTest(classes = EduConnectSystemApp.class)
public class SsRecordingPcControllerTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).apply(springSecurity()).build();
    }

    @Test
    public void testRecordingPcController() throws Exception{
        testStartRecording();
        try {
            Thread.sleep(1000 * 60);
        }catch (Exception e){

        }
        resetRecording();
        try {
            Thread.sleep(1000 * 60);
        }catch (Exception e){

        }

        testStopRecording();
    }

    @Test
    public void addRecording() throws Exception{
        SsRecordingDTO ssRecordingDTO = new SsRecordingDTO();
        ssRecordingDTO.setBooksName("test");
        ssRecordingDTO.setLecturerId(1l);
        ssRecordingDTO.setRecordingType(0);
        ssRecordingDTO.setGrade(1);
        ssRecordingDTO.setDeviceId(98l);
        ssRecordingDTO.setRecordingStatus(0);
        ssRecordingDTO.setStorageType(0);
        mockMvc.perform(MockMvcRequestBuilders.post("/ssRecording/add")
                .with(user(mockUser()))
                .contentType("application/json")
                .content(JSON.toJSONString(ssRecordingDTO))
            )
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }



    public void resetRecording() throws Exception{
        String roomId = "2ea5f8a4-a2f6-49ff-9b2c-f9d3fb21b645";
        mockMvc.perform(MockMvcRequestBuilders.put("/ssRecording/reset/{roomId}",roomId).with(user(mockUser())))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }

    public void testStartRecording() throws Exception{
        String roomId = "2ea5f8a4-a2f6-49ff-9b2c-f9d3fb21b645";
        mockMvc.perform(MockMvcRequestBuilders.put("/ssRecording/start/{roomId}",roomId).with(user(mockUser())))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }


    public void testStopRecording() throws Exception{
        String roomId = "2ea5f8a4-a2f6-49ff-9b2c-f9d3fb21b645";
        mockMvc.perform(MockMvcRequestBuilders.put("/ssRecording/stop/{roomId}",roomId).with(user(mockUser())))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }



    YdsfUser mockUser() {
        return new YdsfUser(1L, "admin", 1L, "", "", ""//
            , "", "", 1L, "", true, true//
            , "0", true, true,
            AuthorityUtils.NO_AUTHORITIES);
    }
}
