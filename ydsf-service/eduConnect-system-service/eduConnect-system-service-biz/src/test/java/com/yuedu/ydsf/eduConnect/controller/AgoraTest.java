package com.yuedu.ydsf.eduConnect.controller;

import com.alibaba.fastjson.JSON;
import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AgoraTokenGenerator;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.*;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.AgoraEducationApi;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.AgoraRecordApi;
import com.yuedu.ydsf.eduConnect.system.proxy.util.RtcTokenBuilder;
import org.javers.common.collections.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.UUID;

/**
 * 声网接口测试
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@SpringBootTest(classes = EduConnectSystemApp.class)
public class AgoraTest {


    @Autowired
    private AgoraRecordApi agoraRecordApi;

    @Autowired
    private AgoraTokenGenerator agoraTokenGenerator;

    @Autowired
    private AgoraEducationApi agoraEducationApi;


    public void test() {
        testRecord();
    }


    public void testToken() {
        String agoraRtcToken = agoraTokenGenerator.getAgoraRtmToken("f78944c8-5c50-4e76-8a1f-200e6719c334", String.valueOf(98), (short)1);
        System.out.println(agoraRtcToken);
    }

    @Test
    public void testRoom() {
        String lowerCase = UUID.randomUUID().toString().toLowerCase();
        System.out.println(lowerCase);
        RoomResp testRoom = agoraEducationApi.createRoom(lowerCase,
                RoomReq.builder().roomName("testRoom")
                        .roomProperties(RoomReq.RoomProperties.builder()
                                .schedule(RoomReq.RoomProperties.Schedule.builder()
                                        .startTime(System.currentTimeMillis())
                                        .duration(3600 * 2)
                                        .closeDelay(60 * 10)
                                        .build())
                                .build())
                        .build());

        System.out.printf(JSON.toJSONString(testRoom));

    }


    public void testRecord() {
        AcquireResp acquire = agoraRecordApi.acquire(AcquireReq.builder()
                .uid(String.valueOf(Integer.MAX_VALUE - 98 - 2))
                .cname("a1f6b6cd-de4d-44d8-addf-e1c8f693278d")
                .clientRequest(AcquireReq.ClientRequest.builder().build())
                .build());

        StreamInfoResp streamInfoResp = agoraEducationApi.streamInfo("a1f6b6cd-de4d-44d8-addf-e1c8f693278d", String.valueOf(98));

        StartResp startResp = agoraRecordApi.start(acquire.getResourceId(), "mix",
                StartReq.builder()
                        .uid(String.valueOf(acquire.getUid()))
                        .cname("a1f6b6cd-de4d-44d8-addf-e1c8f693278d")
                        .clientRequest(StartReq.ClientRequest.builder()
                                .token(agoraTokenGenerator.getAgoraRtcToken("a1f6b6cd-de4d-44d8-addf-e1c8f693278d", acquire.getUid(), RtcTokenBuilder.Role.Role_Subscriber))
                                .recordingConfig(StartReq.ClientRequest.RecordingConfig.builder()
                                        .subscribeVideoUids(Lists.asList(streamInfoResp.getData().getStreamUuid()))
                                        .subscribeAudioUids(Lists.asList(streamInfoResp.getData().getStreamUuid()))
                                        .transcodingConfig(StartReq.ClientRequest.RecordingConfig.TranscodingConfig.builder().build())
                                        .build())
                                .recordingFileConfig(StartReq.ClientRequest.RecordingFileConfig.builder().build())
                                .storageConfig(StartReq.ClientRequest.StorageConfig.builder()
                                        .accessKey("")
                                        .secretKey("")
                                        .bucket("ydsf-ss-vod")
                                        .fileNamePrefix(Lists.asList(agoraTokenGenerator.getAppId(), "202409", "a1f6b6cd-de4d-44d8-addf-e1c8f693278d".replace("-", "").toLowerCase()))
                                        .build())
                                .build())

                        .build());

        try {
            Thread.sleep(1000 * 60);
        }catch (Exception e){

        }

        StopResp stop = agoraRecordApi.stop(startResp.getResourceId(), startResp.getSid(), "mix",
                StopReq.builder()
                        .uid(String.valueOf(acquire.getUid()))
                        .cname("a1f6b6cd-de4d-44d8-addf-e1c8f693278d")
                        .clientRequest(StopReq.ClientRequest.builder().build())
                        .build());



    }
}
