package com.yuedu.ydsf.eduConnect.service.impl;

import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanVersionVO;
import com.yuedu.ydsf.eduConnect.entity.LiveRoomPlanVersion;
import com.yuedu.ydsf.eduConnect.service.LiveRoomPlanVersionService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = EduConnectSystemApp.class)
class LiveRoomPlanVersionServiceImplTest {
    @Autowired
    private LiveRoomPlanVersionService liveRoomPlanVersionService;

    @Test
    void listCanCreateTeachingPlan() {
        List<LiveRoomPlanVersionVO> liveRoomPlanVersions = liveRoomPlanVersionService.listCanCreateTeachingPlan(1);
        log.info("可以创建教学计划的直播间计划列表:{}", liveRoomPlanVersions);
    }

    @Test
    void listUnfinishedOnlineVersion() {
        List<LiveRoomPlanVersion> liveRoomPlanVersions = liveRoomPlanVersionService.listUnfinishedOnlineVersion();
        log.info("未结束在线版本的直播间计划列表:{}", liveRoomPlanVersions);
    }
}