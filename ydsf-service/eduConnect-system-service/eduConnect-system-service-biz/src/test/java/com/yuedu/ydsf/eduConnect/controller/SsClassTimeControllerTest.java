package com.yuedu.ydsf.eduConnect.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.api.constant.RoomCodeTypeEnum;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeDTO;
import com.yuedu.ydsf.eduConnect.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.PackageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Collections;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/10/21
 **/
@SpringBootTest(classes = EduConnectSystemApp.class)
public class SsClassTimeControllerTest {


    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).apply(springSecurity()).build();
    }

    @Test
    public void testController() throws Exception {
        getDirectSsClassTimePage_ValidRequest_ReturnsOk();
        getSsClassTimePage_ValidRequest_ReturnsOk();
        getDirectAuthClassRoom_ValidId_ReturnsOk();
        getVodAuthClassRoom_ValidId_ReturnsOk();
        directAuthClassTimeRoom_ValidRequest_ReturnsOk();
        vodAuthClassTimeRoom_ValidRequest_ReturnsOk();

    }

    @Test
    public void testClassTimeController() throws Exception {
        inRoomByRoomUuid_ValidRequest_ReturnsOk();
        getRoomTimeCode_ValidRequest_ReturnsOk();
        directAddClassTime_ValidRequest_ReturnsOk();
        vodAddClassTime_ValidRequest_ReturnsOk();
        directUpdateClassTime_ValidRequest_ReturnsOk();
        vodUpdateClassTime_ValidRequest_ReturnsOk();
        directDeleteClassTime_ValidRequest_ReturnsOk();
        vodDeleteClassTime_ValidRequest_ReturnsOk();
        getSupervisionUrl_ValidRequest_ReturnsOk();
        getClassTimeInfo_ValidRequest_ReturnsOk();
    }


    public void getDirectSsClassTimePage_ValidRequest_ReturnsOk() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/ssClassTime/direct/page")
                .with(user(mockUser()))
                .param("current", String.valueOf(1))
                .param("size", String.valueOf(10)))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("code").value(0));
    }

    public void getSsClassTimePage_ValidRequest_ReturnsOk() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/ssClassTime/vod/page")
                .with(user(mockUser()))
                .param("current", String.valueOf(1))
                .param("size", String.valueOf(10)))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("code").value(0));
    }

    public void getDirectAuthClassRoom_ValidId_ReturnsOk() throws Exception {
        Long id = 618L;
        mockMvc.perform(MockMvcRequestBuilders.get("/ssClassTime/direct/authRoomList/{id}", id).with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("code").value(0));
    }

    public void getVodAuthClassRoom_ValidId_ReturnsOk() throws Exception {
        Long id = 618L;
        mockMvc.perform(MockMvcRequestBuilders.get("/ssClassTime/vod/authRoomList/{id}", id).with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("code").value(0));
    }

    public void directAuthClassTimeRoom_ValidRequest_ReturnsOk() throws Exception {
        SsClassTimeDTO dto = new SsClassTimeDTO();
        dto.setId(718L);
        dto.setAuthDeviceList(Lists.list());

        mockMvc.perform(MockMvcRequestBuilders.put("/ssClassTime/direct/authClassTimeRoom")
                .with(user(mockUser()))
                .contentType("application/json")
                .content(JSON.toJSONString(dto)))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("code").value(0));
    }

    public void vodAuthClassTimeRoom_ValidRequest_ReturnsOk() throws Exception {
        SsClassTimeDTO dto = new SsClassTimeDTO();
        dto.setId(1L);
        dto.setAuthDeviceList(Lists.list());
        mockMvc.perform(MockMvcRequestBuilders.put("/ssClassTime/vod/authClassTimeRoom")
                .with(user(mockUser()))
                .contentType("application/json")
                .content(JSON.toJSONString(dto)))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("code").value(0));
    }

    public void inRoomByRoomUuid_ValidRequest_ReturnsOk() throws Exception {
        SsClassTimeQuery query = new SsClassTimeQuery();
        query.setRoomUuid("test-room-uuid");

        mockMvc.perform(
                MockMvcRequestBuilders.get("/ssClassTime/inRoomByRoomUuid").with(user(mockUser()))
                    .contentType("application/json").content(JSON.toJSONString(query))).andDo(print())
            .andExpect(status().isOk()).andExpect(jsonPath("code").value(0));
    }

    public void getRoomTimeCode_ValidRequest_ReturnsOk() throws Exception {
        SsClassTimeDTO dto = new SsClassTimeDTO();
        dto.setId(1L);
        dto.setRoomCodeType(RoomCodeTypeEnum.ROOM_CODE_TYPE_ENUM_1.CODE);

        mockMvc.perform(
                MockMvcRequestBuilders.post("/ssClassTime/getRoomTimeCode").with(user(mockUser()))
                    .contentType("application/json").content(JSON.toJSONString(dto))).andDo(print())
            .andExpect(status().isOk()).andExpect(jsonPath("code").value(0));
    }

    public void directAddClassTime_ValidRequest_ReturnsOk() throws Exception {
        SsClassTimeDTO dto = new SsClassTimeDTO();
        // 设置必要的属性

        mockMvc.perform(
                MockMvcRequestBuilders.post("/ssClassTime/direct/addClassTime").with(user(mockUser()))
                    .contentType("application/json").content(JSON.toJSONString(dto))).andDo(print())
            .andExpect(status().isOk()).andExpect(jsonPath("code").value(0));
    }

    public void vodAddClassTime_ValidRequest_ReturnsOk() throws Exception {
        SsClassTimeDTO dto = new SsClassTimeDTO();
        // 设置必要的属性

        mockMvc.perform(
                MockMvcRequestBuilders.post("/ssClassTime/vod/addClassTime").with(user(mockUser()))
                    .contentType("application/json").content(JSON.toJSONString(dto))).andDo(print())
            .andExpect(status().isOk()).andExpect(jsonPath("code").value(0));
    }

    public void directUpdateClassTime_ValidRequest_ReturnsOk() throws Exception {
        SsClassTimeDTO dto = new SsClassTimeDTO();
        dto.setId(1L);
        // 设置其他必要的属性

        mockMvc.perform(
                MockMvcRequestBuilders.put("/ssClassTime/direct/updateClassTime").with(user(mockUser()))
                    .contentType("application/json").content(JSON.toJSONString(dto))).andDo(print())
            .andExpect(status().isOk()).andExpect(jsonPath("code").value(0));
    }

    public void vodUpdateClassTime_ValidRequest_ReturnsOk() throws Exception {
        SsClassTimeDTO dto = new SsClassTimeDTO();
        dto.setId(1L);
        // 设置其他必要的属性

        mockMvc.perform(
                MockMvcRequestBuilders.put("/ssClassTime/vod/updateClassTime").with(user(mockUser()))
                    .contentType("application/json").content(JSON.toJSONString(dto))).andDo(print())
            .andExpect(status().isOk()).andExpect(jsonPath("code").value(0));
    }

    public void directDeleteClassTime_ValidRequest_ReturnsOk() throws Exception {
        Long[] ids = {1L, 2L};

        mockMvc.perform(MockMvcRequestBuilders.delete("/ssClassTime/direct/deleteClassTime")
                .with(user(mockUser())).contentType("application/json").content(JSON.toJSONString(ids)))
            .andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("code").value(0));
    }

    public void vodDeleteClassTime_ValidRequest_ReturnsOk() throws Exception {
        Long[] ids = {1L, 2L};

        mockMvc.perform(
                MockMvcRequestBuilders.delete("/ssClassTime/vod/deleteClassTime").with(user(mockUser()))
                    .contentType("application/json").content(JSON.toJSONString(ids))).andDo(print())
            .andExpect(status().isOk()).andExpect(jsonPath("code").value(0));
    }

    public void getSupervisionUrl_ValidRequest_ReturnsOk() throws Exception {
        SsClassTimeQuery query = new SsClassTimeQuery();
        query.setId(1L);

        mockMvc.perform(
                MockMvcRequestBuilders.get("/ssClassTime/getSupervisionUrl").with(user(mockUser()))
                    .contentType("application/json").content(JSON.toJSONString(query))).andDo(print())
            .andExpect(status().isOk()).andExpect(jsonPath("code").value(0));
    }

    public void getClassTimeInfo_ValidRequest_ReturnsOk() throws Exception {
        Long id = 1L;

        mockMvc.perform(MockMvcRequestBuilders.get("/ssClassTime/getClassTimeInfo/{id}", id)
                .with(user(mockUser()))).andDo(print()).andExpect(status().isOk())
            .andExpect(jsonPath("code").value(0));
    }


    YdsfUser mockUser() {
        return new YdsfUser(1L, "admin", 1L, "", "", ""//
            , "", "", 1L, "", true, true//
            , "0", true, true,
            AuthorityUtils.createAuthorityList("ROLE_ADMIN",
                "edusystem_directClassTime_view",
                "edusystem_vodClassTime_view",
                "edusystem_directClassTime_auth",
                "edusystem_directClassTime_auth",
                "edusystem_ssClassTime_direct_addClassTime",
                "edusystem_ssClassTime_vod_addClassTime",
                "edusystem_ssClassTime_direct_updateClassTime",
                "edusystem_ssClassTime_vod_updateClassTime",
                "edusystem_ssClassTime_direct_deleteClassTime",
                "edusystem_ssClassTime_vod_deleteClassTime",
                "edusystem_ssClassTime_getSupervisionUrl"
                ));
    }
}
