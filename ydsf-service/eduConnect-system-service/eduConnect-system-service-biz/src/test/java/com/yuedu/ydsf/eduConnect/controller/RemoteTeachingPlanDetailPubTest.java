package com.yuedu.ydsf.eduConnect.controller;

import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanDetailPubService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: 张浩宇
 * @date: 2024/12/03
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
//@EnableFeignClients("com.yuedu.ydsf.eduConnect.api.feign")
public class RemoteTeachingPlanDetailPubTest {

    @Autowired
    private RemoteTeachingPlanDetailPubService remoteTeachingPlanDetailPubService;

    @Test
    public void testHashUnfinishedClass() {
        Long lectureId = 1L;
        Boolean b = remoteTeachingPlanDetailPubService.hasUnfinishedClass(lectureId).getData();
        log.info("是否有未结束的排课: {}", b);
    }
}
