package com.yuedu.ydsf.eduConnect.controller;

import com.alibaba.fastjson.JSON;
import com.aliyun.vod20170321.models.SubmitTranscodeJobsResponseBody;
import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.system.proxy.service.VodService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/09/30
 **/
@SpringBootTest(classes = EduConnectSystemApp.class)
public class AliVodTest {

    @Autowired
    private VodService vodService;

    @Test
    public void test(){
        try{
//            String test = vodService.registerMedia(
//                    "https://ydsf-ss-vod.oss-cn-beijing.aliyuncs.com/606a2533a81443b38ceaccc131d6d7e0/20241006/44aab92beb684e57afd014d52dff9d4d/d58640a3aa4674ce3637d0ac7b220fae_44aab92b-eb68-4e57-afd0-14d52dff9d4d.m3u8"
//                    ,"test11",
//                    "1d3b8b5cc37281a4c1986708cd38aa65");
           // System.out.println(test);
//            String video = vodService.getMezzanineInfo(test);
//            System.out.println(video);
          //  vodService.getPlayInfo("004d634885ef71ef810735a6ecca0302");
            SubmitTranscodeJobsResponseBody body =
                    vodService.submitTranscodeTask("f019b73187ad71efa02b7fb2780c0102", "1d3b8b5cc37281a4c1986708cd38aa65");

            System.out.printf(JSON.toJSONString(body));
        }catch (Exception e){
            System.out.printf(e.getMessage());
        }



    }
}
