package com.yuedu.ydsf.eduConnect.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.api.dto.SsRecordingDTO;
import com.yuedu.ydsf.eduConnect.api.vo.SsRecordingVO;
import com.yuedu.ydsf.eduConnect.api.vo.UploadAuthVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/10/21
 **/
@SpringBootTest(classes = EduConnectSystemApp.class)
public class SsRecordingControllerTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).apply(springSecurity()).build();
    }

    @Test
    public void testRecordingController() throws Exception{
        testPage();
        getById_ValidId_ReturnsOk();
        add_ValidRequest_ReturnsOk();
        edit_ValidRequest_ReturnsOk();
        delete_ValidIds_ReturnsOk();
        download_ValidId_ReturnsVideo();
        getUploadAuth_ValidRequest_ReturnsOk();
        refreshUploadAuth_ValidVideoId_ReturnsOk();
        getPlayAuth_ValidRequest_ReturnsOk();
    }


    public void testPage() throws Exception{
        mockMvc.perform(MockMvcRequestBuilders.get("/ssRecording/page").with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }



    public void getById_ValidId_ReturnsOk() throws Exception {
        Long id = 1l;
        mockMvc.perform(MockMvcRequestBuilders.get("/ssRecording/{id}",id).with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }

    public void add_ValidRequest_ReturnsOk() throws Exception {
        SsRecordingDTO recordingDTO = new SsRecordingDTO();
        recordingDTO.setBooksName("Test Book");
        recordingDTO.setLecturerId(1L);
        recordingDTO.setRecordingStatus(2);
        mockMvc.perform(MockMvcRequestBuilders.post("/ssRecording")
                .with(user(mockUser()))
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(recordingDTO)))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }

    public void edit_ValidRequest_ReturnsOk() throws Exception {
        SsRecordingDTO recordingDTO = new SsRecordingDTO();
        recordingDTO.setId(1L);
        recordingDTO.setBooksName("Updated Book");
        recordingDTO.setLecturerId(2L);

        mockMvc.perform(MockMvcRequestBuilders.put("/ssRecording")
                .with(user(mockUser()))
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(recordingDTO)))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }

    public void delete_ValidIds_ReturnsOk() throws Exception {


        mockMvc.perform(MockMvcRequestBuilders.delete("/ssRecording")
                .with(user(mockUser()))
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(Lists.newArrayList(1,2))))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }


    public void download_ValidId_ReturnsVideo() throws Exception {
        SsRecordingVO recordingVO = new SsRecordingVO();
        recordingVO.setId(1L);
        recordingVO.setRecordingResources("video_url");


        mockMvc.perform(MockMvcRequestBuilders.get("/ssRecording/download/1").with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }

    public void getUploadAuth_ValidRequest_ReturnsOk() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders.get("/ssRecording/getUploadAuth")
                .with(user(mockUser()))
                .param("title", "Test Title")
                .param("fileName", "test.mp4"))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }

    public void refreshUploadAuth_ValidVideoId_ReturnsOk() throws Exception {
        UploadAuthVO uploadAuthVO = new UploadAuthVO();
        uploadAuthVO.setUploadAuth("refreshed_auth");
        mockMvc.perform(MockMvcRequestBuilders.get("/ssRecording/refreshUploadAuth")
                .with(user(mockUser()))
                .param("videoId", "test_video_id"))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }

    public void getPlayAuth_ValidRequest_ReturnsOk() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders.get("/ssRecording/getPlayAuth")
                .with(user(mockUser()))
                .param("videoId", "test_video_id")
                .param("expiredTime", "3600"))
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("code").value(0));
    }



    YdsfUser mockUser() {
        return new YdsfUser(1L, "admin", 1L, "", "", ""//
            , "", "", 1L, "", true, true//
            , "0", true, true,
            AuthorityUtils.createAuthorityList("ROLE_ADMIN",
                "edusystem_ssRecording_view",
                "edusystem_ssRecording_add",
                "edusystem_ssRecording_edit",
                "edusystem_ssRecording_download",
                "edusystem_ssRecording_del"
            ));
    }
}

