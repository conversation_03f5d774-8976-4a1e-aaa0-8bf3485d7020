package com.yuedu.ydsf.eduConnect.controller;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import com.yuedu.ydsf.eduConnect.api.constant.ScheduleEnum;
import com.yuedu.ydsf.eduConnect.api.vo.ClassCourseScheduleVO;
import com.yuedu.ydsf.eduConnect.api.vo.CreateReadingScheduleVO;
import com.yuedu.ydsf.eduConnect.api.vo.CreateVodScheduleVO;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Random;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * @author: zhangchuanfu
 * @date: 2024/10/21
 **/
@SpringBootTest
class SsCourseScheduleControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;
    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).apply(springSecurity())
            .build();

    }

    @Test
    void saveReading() throws Exception {
        String jsonByWeak = getReadingScheduleByWeak();
        mockMvc.perform(MockMvcRequestBuilders.post("/ssCourseSchedule/reading")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonByWeak)
                .with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk());

        String jsonByCalendar = getReadingScheduleByCalendar();
        mockMvc.perform(MockMvcRequestBuilders.post("/ssCourseSchedule/reading")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonByCalendar)
                .with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk());
    }

    @Test
    void saveVod() throws Exception {
        String jsonByWeak = getVodScheduleByWeak();
        mockMvc.perform(MockMvcRequestBuilders.post("/ssCourseSchedule/vod")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonByWeak)
                .with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk());

        String jsonByCalendar = getVodScheduleByCalendar();
        mockMvc.perform(MockMvcRequestBuilders.post("/ssCourseSchedule/vod")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonByCalendar)
                .with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk());
    }

    /**
     * 模拟按周排课
     *
     * @return json
     */
    private String getReadingScheduleByWeak() throws JsonProcessingException {
        LocalDate attendClassStartDate = LocalDate.now();
        CreateReadingScheduleVO createReadingScheduleVo = new CreateReadingScheduleVO();
        createReadingScheduleVo.setClassId(126L);
        createReadingScheduleVo.setLecturerId(5L);
        createReadingScheduleVo.setClassTimeMethod(ScheduleEnum.CLASSTIMEMETHOD_0.CODE);
        createReadingScheduleVo.setAttendClassStartDate(attendClassStartDate);
        createReadingScheduleVo.setAttendClassEndDate(attendClassStartDate.plusWeeks(1));
        List<ClassCourseScheduleVO> classCourseScheduleVOS = Lists.newArrayList();
        Random random = new Random();
        int randomNumber = random.nextInt(59) + 1;
        LocalTime localTime = LocalTime.of(0, randomNumber);
        for (int i = 0; i < 5; i++) {
            ClassCourseScheduleVO classCourseScheduleVo = new ClassCourseScheduleVO();
            classCourseScheduleVo.setAttendClassDate(attendClassStartDate.plusDays(i));
            classCourseScheduleVo.setAttendClassStartTime(localTime);
            classCourseScheduleVo.setAttendClassEndTime(localTime.plusMinutes(1));
            classCourseScheduleVo.setBooksId(i + 1 + "");
            classCourseScheduleVo.setBooksName("单元测试直播课按周排课");
            classCourseScheduleVo.setClassRoomId(930L);
            classCourseScheduleVOS.add(classCourseScheduleVo);
        }
        createReadingScheduleVo.setClassCourseScheduleVOList(classCourseScheduleVOS);

        return objectMapper.writeValueAsString(createReadingScheduleVo);
    }

    /**
     * 模拟按日历排课
     * @return  json
     */
    private String getReadingScheduleByCalendar() throws JsonProcessingException {
        LocalDate attendClassStartDate = LocalDate.now();
        CreateReadingScheduleVO createReadingScheduleVo = new CreateReadingScheduleVO();
        createReadingScheduleVo.setClassId(126L);
        createReadingScheduleVo.setLecturerId(5L);
        createReadingScheduleVo.setClassTimeMethod(ScheduleEnum.CLASSTIMEMETHOD_1.CODE);
        createReadingScheduleVo.setIsOutCollide(Boolean.TRUE);
        List<ClassCourseScheduleVO> classCourseScheduleVOS = Lists.newArrayList();
        Random random = new Random();
        int randomNumber = random.nextInt(59) + 1;
        LocalTime localTime = LocalTime.of(0, randomNumber);
        for (int i = 0; i < 2; i++) {
            ClassCourseScheduleVO classCourseScheduleVo = new ClassCourseScheduleVO();
            classCourseScheduleVo.setAttendClassDate(attendClassStartDate.plusDays(i));
            classCourseScheduleVo.setAttendClassStartTime(localTime);
            classCourseScheduleVo.setAttendClassEndTime(localTime.plusMinutes(1));
            classCourseScheduleVo.setBooksId(i + 1 + "");
            classCourseScheduleVo.setBooksName("单元测试直播课按日历排课");
            classCourseScheduleVo.setClassRoomId(930L);
            classCourseScheduleVOS.add(classCourseScheduleVo);
        }
        createReadingScheduleVo.setClassCourseScheduleVOList(classCourseScheduleVOS);

        return objectMapper.writeValueAsString(createReadingScheduleVo);
    }

    /**
     * 模拟按周排课
     *
     * @return json
     */
    private String getVodScheduleByWeak() throws JsonProcessingException {
        LocalDate attendClassStartDate = LocalDate.now();
        CreateVodScheduleVO createVodScheduleVo = new CreateVodScheduleVO();
        createVodScheduleVo.setClassId(126L);
        createVodScheduleVo.setLecturerId(5L);
        createVodScheduleVo.setClassTimeMethod(ScheduleEnum.CLASSTIMEMETHOD_0.CODE);
        createVodScheduleVo.setAttendClassStartDate(attendClassStartDate);
        createVodScheduleVo.setAttendClassEndDate(attendClassStartDate.plusWeeks(1));
        createVodScheduleVo.setIsOutCollide(Boolean.TRUE);
        createVodScheduleVo.setRecordingId(259L);
        List<ClassCourseScheduleVO> classCourseScheduleVOS = Lists.newArrayList();
        Random random = new Random();
        int randomNumber = random.nextInt(59) + 1;
        LocalTime localTime = LocalTime.of(0, randomNumber);
        for (int i = 0; i < 5; i++) {
            ClassCourseScheduleVO classCourseScheduleVo = new ClassCourseScheduleVO();
            classCourseScheduleVo.setAttendClassDate(attendClassStartDate.plusDays(i));
            classCourseScheduleVo.setAttendClassStartTime(localTime);
            classCourseScheduleVo.setAttendClassEndTime(localTime.plusMinutes(1));
            classCourseScheduleVo.setBooksId(i + 1 + "");
            classCourseScheduleVo.setBooksName("单元测试点播课按周排课");
            classCourseScheduleVOS.add(classCourseScheduleVo);
        }
        createVodScheduleVo.setClassCourseScheduleVOList(classCourseScheduleVOS);

        return objectMapper.writeValueAsString(createVodScheduleVo);
    }



    /**
     * 模拟按日历排课
     * @return  json
     */
    private String getVodScheduleByCalendar() throws JsonProcessingException {
        LocalDate attendClassStartDate = LocalDate.now();
        CreateVodScheduleVO createVodScheduleVo = new CreateVodScheduleVO();
        createVodScheduleVo.setClassId(126L);
        createVodScheduleVo.setLecturerId(5L);
        createVodScheduleVo.setClassTimeMethod(ScheduleEnum.CLASSTIMEMETHOD_1.CODE);
        createVodScheduleVo.setIsOutCollide(Boolean.TRUE);
        createVodScheduleVo.setRecordingId(259L);
        List<ClassCourseScheduleVO> classCourseScheduleVOS = Lists.newArrayList();
        Random random = new Random();
        int randomNumber = random.nextInt(59) + 1;
        LocalTime localTime = LocalTime.of(0, randomNumber);
        for (int i = 0; i < 2; i++) {
            ClassCourseScheduleVO classCourseScheduleVo = new ClassCourseScheduleVO();
            classCourseScheduleVo.setAttendClassDate(attendClassStartDate.plusDays(i));
            classCourseScheduleVo.setAttendClassStartTime(localTime);
            classCourseScheduleVo.setAttendClassEndTime(localTime.plusMinutes(1));
            classCourseScheduleVo.setBooksId(i + 1 + "");
            classCourseScheduleVo.setBooksName("单元测试点播课按日历排课");
            classCourseScheduleVOS.add(classCourseScheduleVo);
        }
        createVodScheduleVo.setClassCourseScheduleVOList(classCourseScheduleVOS);

        return objectMapper.writeValueAsString(createVodScheduleVo);
    }


    /**
     * mockUser 对象
     *
     * @return UserDetails
     */
    YdsfUser mockUser() {
        return new YdsfUser(1L, "admin", 4L, "", "", ""//
            , "", "", 1L, "", true, true//
            , "0", true, true,
            AuthorityUtils.createAuthorityList("ROLE_ADMIN", "course_schedule_reading_add", "edusystem_ssCourseSchedule_vod_add"));
    }
}