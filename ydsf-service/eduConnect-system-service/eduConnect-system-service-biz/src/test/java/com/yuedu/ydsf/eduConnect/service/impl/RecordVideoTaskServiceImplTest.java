package com.yuedu.ydsf.eduConnect.service.impl;

import com.alibaba.fastjson.JSON;
import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.api.dto.DeleteVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.EditVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.dto.GenerateVideoTaskDto;
import com.yuedu.ydsf.eduConnect.api.query.RecordVideoTaskQuery;
import com.yuedu.ydsf.eduConnect.api.vo.RecordVideoTaskVO;
import com.yuedu.ydsf.eduConnect.service.RecordVideoTaskService;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = EduConnectSystemApp.class)
class RecordVideoTaskServiceImplTest {

    @Resource
    private RecordVideoTaskService recordVideoTaskService;

    @Test
    void generateRecordVideoTask() {
        GenerateVideoTaskDto videoTaskDto = new GenerateVideoTaskDto();
        videoTaskDto.setPlanId(1L);
        videoTaskDto.setCourseId(75L);
        videoTaskDto.setLectureId(1840305514148294657L);
        videoTaskDto.setOperateType(0);
        recordVideoTaskService.generateRecordVideoTask(videoTaskDto);
    }

    @Test
    void editRecordVideoTask() {
        EditVideoTaskDto videoTaskDto = new EditVideoTaskDto();
        videoTaskDto.setPlanId(1L);
        videoTaskDto.setCourseId(78L);
        videoTaskDto.setLectureId(1840305467532800001L);
        videoTaskDto.setOperateType(1);
        videoTaskDto.setCourseIdOld(75L);
        videoTaskDto.setLectureIdOld(1840305514148294657L);
        recordVideoTaskService.editRecordVideoTask(videoTaskDto);
    }

//    @Test
//    void editTaskScheduling() {
//        List<EditVideoTaskDto> editVideoTaskList = new ArrayList<>();
//        EditVideoTaskDto videoTaskDto = new EditVideoTaskDto();
//        videoTaskDto.setPlanId(1L);
//        //修改后的课程Id
//        videoTaskDto.setCourseId(78L);
//        //修改后的讲师Id
//        videoTaskDto.setLectureId(1840305467532800001L);
//        //修改后的讲师名称
//        videoTaskDto.setLectureName("约读书房娟娟老师");
//        videoTaskDto.setOperateType(3);
//        //修改前的课程Id
//        videoTaskDto.setCourseIdOld(75L);
//        //修改前的讲师Id
//        videoTaskDto.setLectureIdOld(1840305514148294657L);
//        //修改后的课节Id
//        videoTaskDto.setLectureId();
//        editVideoTaskList.add(videoTaskDto);
//        recordVideoTaskService.editTaskScheduling(editVideoTaskList);
//    }

    @Test
    void deleteRecordVideoTask() {
        DeleteVideoTaskDto videoTaskDto = new DeleteVideoTaskDto();
        videoTaskDto.setPlanId(6L);
        videoTaskDto.setCourseId(78L);
        videoTaskDto.setLectureId(1840305467532800001L);
        videoTaskDto.setOperateType(2);
        recordVideoTaskService.deleteRecordVideoTask(videoTaskDto);
    }

    @Test
    void getRecordTaskList() {
        RecordVideoTaskQuery videoTaskQuery = new RecordVideoTaskQuery();
        videoTaskQuery.setLectureId(1840305514148294657L);
        List<RecordVideoTaskVO> list = recordVideoTaskService.getRecordTaskList(videoTaskQuery);
        log.info("录课任务列表:{}", JSON.toJSONString(list));
    }
}