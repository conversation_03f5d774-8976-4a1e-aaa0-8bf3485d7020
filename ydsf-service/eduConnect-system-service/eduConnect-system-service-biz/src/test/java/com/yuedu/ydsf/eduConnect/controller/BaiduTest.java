package com.yuedu.ydsf.eduConnect.controller;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.yuedu.ydsf.common.file.core.FileTemplate;
import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAnalysisReq;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAnalysisResp;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAttrReq;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.BodyAttrResp;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.DetectReq;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.DetectResp;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.BaiduApi;
import com.yuedu.ydsf.eduConnect.system.proxy.util.Base64Util;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/02/07
 **/
@SpringBootTest(classes = EduConnectSystemApp.class)
public class BaiduTest {

    @Autowired
    private BaiduApi baiduApi;

    //@Autowired
   // private OSS ossClient;

    @Autowired
    private FileTemplate fileTemplate;


    @Test
    public void testBodyAttr(){
       // OSSObject ossObject = fileTemplate.getObject("ydsf-prod", "resources/ssScreenshot/20241101/一只黑猩猩的宇宙史/LV3-1&一只黑猩猩的宇宙史&L013-2邹平市-邹平书店&33392.jpg");
        try(InputStream inputStream = fileTemplate.getObject("ydsf-prod", "resources/ssScreenshot/20241101/一只黑猩猩的宇宙史/LV3-1&一只黑猩猩的宇宙史&L013-2邹平市-邹平书店&33392.jpg").getObjectContent();){
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            // 读取文件内容到字节数组。
            byte[] readBuffer = new byte[1024*1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(readBuffer)) != -1) {
                byteArrayOutputStream.write(readBuffer, 0, bytesRead);
            }
            // 获取最终的字节数组。
            byte[] fileBytes = byteArrayOutputStream.toByteArray();
            BodyAttrResp gender = baiduApi.bodyAttr(BodyAttrReq.builder()
                .image(URLEncoder.encode(Base64Util.encode(fileBytes),"UTF-8"))
                .build());
            System.out.println(JSON.toJSON(gender));
        }catch (Exception e){
            e.printStackTrace();
        }


    }

    @Test
    public void testDetect(){
        try(InputStream inputStream = fileTemplate.getObject("ydsf-prod", "resources/ssScreenshot/20241101/一只黑猩猩的宇宙史/LV3-1&一只黑猩猩的宇宙史&L013-2邹平市-邹平书店&33392.jpg").getObjectContent()){
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            // 读取文件内容到字节数组。
            byte[] readBuffer = new byte[1024*1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(readBuffer)) != -1) {
                byteArrayOutputStream.write(readBuffer, 0, bytesRead);
            }
            // 获取最终的字节数组。
            byte[] fileBytes = byteArrayOutputStream.toByteArray();
            DetectResp resp = baiduApi.detect(DetectReq.builder()
                    .image(URLEncoder.encode(Base64Util.encode(fileBytes),"UTF-8"))
                    .faceField("age")
                    .build());
            System.out.println(JSON.toJSON(resp));
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    @Test
    public void testBodyAnalysis(){
        try(InputStream inputStream = fileTemplate.getObject("ydsf-prod", "resources/ssScreenshot/20241101/一只黑猩猩的宇宙史/LV3-1&一只黑猩猩的宇宙史&L013-2邹平市-邹平书店&33392.jpg").getObjectContent()){
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            // 读取文件内容到字节数组。
            byte[] readBuffer = new byte[1024*1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(readBuffer)) != -1) {
                byteArrayOutputStream.write(readBuffer, 0, bytesRead);
            }
            // 获取最终的字节数组。
            byte[] fileBytes = byteArrayOutputStream.toByteArray();
            BodyAnalysisResp resp = baiduApi.bodyAnalysis(BodyAnalysisReq.builder()
                .image(URLEncoder.encode(Base64Util.encode(fileBytes),"UTF-8"))
                .build());
            System.out.println(JSON.toJSON(resp));
        }catch (Exception e){
            e.printStackTrace();
        }

    }

}
