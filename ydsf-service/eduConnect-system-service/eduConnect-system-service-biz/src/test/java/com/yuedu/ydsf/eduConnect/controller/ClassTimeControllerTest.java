package com.yuedu.ydsf.eduConnect.controller;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.yuedu.ydsf.eduConnect.service.ClassTimeService;
import java.util.ArrayList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

/**
 * @ClassName ClassTimeControllerTest
 * @Description 上课时段测试
 * <AUTHOR>
 * @Date 2024/11/29 13:51
 * @Version v0.0.1
 */


class ClassTimeControllerTest {
    private MockMvc mockMvc;

    @Mock
    private ClassTimeService classTimeService;

    @InjectMocks
    private ClassTimeController classTimeController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(classTimeController).build();
    }

    @Test
    void getClassTimeList() throws Exception {
        when(classTimeService.getClassTimeList()).thenReturn(new ArrayList<>());

        mockMvc.perform(get("/ClassTime/list"))
            .andExpect(status().isOk());
    }

}