package com.yuedu.ydsf.eduConnect.controller;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanVersionService;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanVersionVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: 张浩宇
 * @date: 2024/12/11
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class RemoteTeachingPlanVersionServiceTest {

    @Autowired
    private RemoteTeachingPlanVersionService remoteTeachingPlanVersionService;

    @Test
    public void testHashUnfinishedClass() {
        ArrayList<Long> ids = new ArrayList<>();
        ids.add(17L);
        ids.add(17L);
        R<List<TeachingPlanVersionVO>> infoByList = remoteTeachingPlanVersionService.getInfoByList(ids);
        log.info("infoByList:{}", infoByList);
    }
}
