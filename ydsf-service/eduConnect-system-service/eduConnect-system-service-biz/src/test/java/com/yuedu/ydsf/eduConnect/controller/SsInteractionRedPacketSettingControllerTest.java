package com.yuedu.ydsf.eduConnect.controller;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.alibaba.fastjson.JSON;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.api.dto.SsInteractionRedPacketSettingDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * 门店红包规则设置表 单元测试
 * <AUTHOR>
 * @date 2024/11/11 11:13
 */
@SpringBootTest(classes = EduConnectSystemApp.class)
class SsInteractionRedPacketSettingControllerTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mvc;

    @BeforeEach
    public void setup() {
        mvc = MockMvcBuilders.webAppContextSetup(context).apply(springSecurity()).build();
    }

    /**
     * mockUser 对象
     *
     * @return UserDetails
     */
    YdsfUser mockUser() {
        return new YdsfUser(1L,
            "admin",
            1L,
            "",
            "",
            "",
            "",
            "",
            1L,
            "",
            true,
            true,
            "0",
            true,
            true,
            AuthorityUtils.createAuthorityList("ROLE_ADMIN", "edusystem_ssClass_view")

        );
    }

    /**
     * 查询门店红包设置
     * <AUTHOR>
     * @date 2024/11/11 11:18
     */
    @Test
    void getInteractionRedPacketSettingBySource() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/ssInteractionRedPacketSetting/getInteractionRedPacketSettingBySource")
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .queryParam("source", "2766")
                .queryParam("xgjCampusId", "869305A0-62D1-4A7A-B0E9-0F303BAF9614")
                .with(user(mockUser())))
            .andDo(print())
            .andExpect(status().isOk());
    }

    /**
     * 门店红包规则设置
     * <AUTHOR>
     * @date 2024/11/11 11:18
     */
    @Test
    void interactionRedPacketSetting() throws Exception {

        SsInteractionRedPacketSettingDTO ssInteractionRedPacketSettingDTO = new SsInteractionRedPacketSettingDTO();
        ssInteractionRedPacketSettingDTO.setSource(2766L);
        ssInteractionRedPacketSettingDTO.setXgjCampusId("869305A0-62D1-4A7A-B0E9-0F303BAF9614");
        ssInteractionRedPacketSettingDTO.setRedPacketNumber(700);
        ssInteractionRedPacketSettingDTO.setRedPacketUpperLimit(300);
        ssInteractionRedPacketSettingDTO.setRedPacketLowerLimit(30);
        ssInteractionRedPacketSettingDTO.setManagerMobile("17606405210");
        ssInteractionRedPacketSettingDTO.setManagerName("双师姜超加盟商");
        ssInteractionRedPacketSettingDTO.setRulesSetting(1);

        mvc.perform(MockMvcRequestBuilders.post("/ssInteractionRedPacketSetting/interactionRedPacketSetting")
                        .with(user(mockUser()))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(ssInteractionRedPacketSettingDTO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("code").value(0));

    }



}