package com.yuedu.ydsf.eduConnect.service.impl;

import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.api.query.SettlementCycleQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SettlementCycleVO;
import com.yuedu.ydsf.eduConnect.entity.SettlementCycle;
import com.yuedu.ydsf.eduConnect.service.SettlementCycleService;
import java.time.LocalDate;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = EduConnectSystemApp.class)
class SettlementCycleServiceImplTest {
    @Autowired
    private SettlementCycleService settlementCycleService;

    @Test
    void listAll() {
        List<SettlementCycleVO> settlementCycleVOS = settlementCycleService.listAll();
        log.info(settlementCycleVOS.toString());
    }

    @Test
    void updateById(){
        SettlementCycleVO settlementCycleVO = new SettlementCycleVO();
        settlementCycleVO.setId(1L);
        settlementCycleVO.setBeginDate(LocalDate.now());
        settlementCycleVO.setEndDate(LocalDate.now().plusDays(6));
        log.info(settlementCycleVO.toString());
        settlementCycleService.updateById(settlementCycleVO);
    }

    @Test
    void lockOrUnlockCheckin() {
        SettlementCycle settlementCycle = new SettlementCycle();
        settlementCycle.setId(1L);
        settlementCycle.setCheckinLocked(1);
        settlementCycleService.lockOrUnlockCheckin(settlementCycle);
    }
}