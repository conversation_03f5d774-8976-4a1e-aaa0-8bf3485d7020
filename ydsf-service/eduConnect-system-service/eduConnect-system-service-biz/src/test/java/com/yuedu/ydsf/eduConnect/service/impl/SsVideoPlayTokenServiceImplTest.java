package com.yuedu.ydsf.eduConnect.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.yuedu.ydsf.eduConnect.EduConnectSystemApp;
import com.yuedu.ydsf.eduConnect.entity.SsVideoPlayToken;
import com.yuedu.ydsf.eduConnect.service.SsVideoPlayTokenService;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@Slf4j
@SpringBootTest(classes = EduConnectSystemApp.class)
class SsVideoPlayTokenServiceImplTest {

    @Autowired
    private SsVideoPlayTokenService ssVideoPlayTokenService;


    @Test
    void generateToken() {
        // Arrange
        SsVideoPlayToken videoPlayToken = new SsVideoPlayToken();
        videoPlayToken.setVideoId("40c8e4cdcbfa71efa060752281ec0302");
        videoPlayToken.setCampusId(680L);
        videoPlayToken.setCampusName("长安区-占位书叶");
        videoPlayToken.setPlayUrl(
            "https://alivod.yuedushufang.com/606a2533a81443b38ceaccc131d6d7e0/20250106/bd06a624c48a4c5e992d249cb5ad598f/b5c06685b84461321a61b5a924c69e9d_bd06a624-c48a-4c5e-992d-249cb5ad598f.m3u8");
        videoPlayToken.setTokenExpire(LocalDateTime.now().plusDays(30));
        // Act
        String actualToken = ssVideoPlayTokenService.generateToken(videoPlayToken);

        String playUrl = ssVideoPlayTokenService.getPlayUrl(actualToken);
        // Assert
        assertEquals(playUrl, videoPlayToken.getPlayUrl());
    }

    @Test
    void getPlayUrl() {

    }

    @Test
    void updatePlayCount() {
    }
}