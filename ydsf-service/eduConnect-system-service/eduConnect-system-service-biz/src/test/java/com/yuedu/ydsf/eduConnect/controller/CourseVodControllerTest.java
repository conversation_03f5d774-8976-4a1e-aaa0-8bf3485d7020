
package com.yuedu.ydsf.eduConnect.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import cn.hutool.json.JSONUtil;
import com.yuedu.ydsf.eduConnect.api.dto.CourseVodDTO;
import com.yuedu.ydsf.eduConnect.api.query.CourseVodQuery;
import com.yuedu.ydsf.eduConnect.api.vo.CourseVodVO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseVodVideoVO;
import com.yuedu.ydsf.eduConnect.service.CourseVodService;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

/**
 * @ClassName CourseVodControllerTest
 * @Description 点播课程测试类
 * <AUTHOR>
 * @Date 2024/12/5 11:50
 * @Version v0.0.1
 */
class CourseVodControllerTest {

    private MockMvc mockMvc;

    @Mock
    private CourseVodService courseVodService;

    @InjectMocks
    private CourseVodController courseVodController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(courseVodController).build();
    }

    @Test
    void getCourseVodList() throws Exception {
        // 创建一个模拟的课程列表
        List<CourseVodVO> courseVodVOList = new ArrayList<>();
        courseVodVOList.add(new CourseVodVO());
        courseVodVOList.add(new CourseVodVO());

        // 模拟 courseVodService.getCourseVodList 方法的返回值
        when(courseVodService.getCourseVodList(any(CourseVodQuery.class))).thenReturn(courseVodVOList);

        // 执行请求并进行断言
        mockMvc.perform(get("/CourseVod/list")
                .param("courseName", "测试课程")
                .param("lessonName", "测试课节"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data").exists())
            .andExpect(jsonPath("$.data").isArray())
            .andExpect(jsonPath("$.data.length()").value(2));

        // 验证 courseVodService.getCourseVodList 方法被调用了一次
        verify(courseVodService, times(1)).getCourseVodList(any(CourseVodQuery.class));
    }

    @Test
    void editCourseVodDisable() throws Exception {
        CourseVodDTO courseVodDTO = new CourseVodDTO();
        courseVodDTO.setId(1L);

        // 模拟 courseVodService.editCourseVodDisable 方法的返回值
        when(courseVodService.editCourseVodDisable(1L)).thenReturn(true);

        // 执行请求并进行断言
        mockMvc.perform(put("/CourseVod/edit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSONUtil.toJsonStr(courseVodDTO)))
            .andExpect(status().isOk())
            .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":true,\"ok\":true}"));

        // 验证 courseVodService.editCourseVodDisable 方法被调用了一次
        verify(courseVodService, times(1)).editCourseVodDisable(1L);
    }

    @Test
    void getCourseVodVideoList() throws Exception {
        // 创建一个模拟的视频列表
        List<CourseVodVideoVO> courseVodVideoVOList = new ArrayList<>();
        courseVodVideoVOList.add(new CourseVodVideoVO());
        courseVodVideoVOList.add(new CourseVodVideoVO());

        // 模拟 courseVodService.getCourseVodInfoList 方法的返回值
        when(courseVodService.getCourseVodInfoList(1L, 1)).thenReturn(courseVodVideoVOList);

        // 执行请求并进行断言
        mockMvc.perform(get("/CourseVod/infoList")
                .param("lessonId", "1"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data").exists())
            .andExpect(jsonPath("$.data").isArray())
            .andExpect(jsonPath("$.data.length()").value(2));

        // 验证 courseVodService.getCourseVodInfoList 方法被调用了一次
        verify(courseVodService, times(1)).getCourseVodInfoList(1L, 1);
    }
}