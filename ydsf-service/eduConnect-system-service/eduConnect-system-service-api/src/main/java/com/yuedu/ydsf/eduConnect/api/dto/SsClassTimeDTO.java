package com.yuedu.ydsf.eduConnect.api.dto;

import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.eduConnect.api.valid.SsClassTimeValidGroup;
import com.yuedu.ydsf.eduConnect.api.valid.SsClassTimeValidGroup.GenRoomTimeCodeGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 课次信息表 传输类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:12:52
 */
@Data
@Schema(description = "课次信息表传输类")
public class SsClassTimeDTO implements Serializable {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @NotNull(groups = {ValidGroup.Update.class, GenRoomTimeCodeGroup.class,
        SsClassTimeValidGroup.AddClassTimeGroup.class,
        SsClassTimeValidGroup.UpdateAuthClassTimeRoomGroup.class}, message = "id不能为空")
    private Long id;

  /** 声网UUID */
  @Schema(description = "声网UUID")
  @NotBlank(
      groups = {SsClassTimeValidGroup.GetSupervisionUrlGroup.class},
      message = "房间id不能为空")
  private String roomUuid;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 排课ID
	*/
    @Schema(description="排课ID")
    private Long courseScheduleId;

	/**
	* 排课书籍ID
	*/
    @Schema(description="排课书籍ID")
    private Long courseScheduleBooksId;

	/**
	* 排课规则ID
	*/
    @Schema(description="排课规则ID")
    private Long courseScheduleRuleId;

	/**
	* 上课日期（yyyy-MM-dd）
	*/
    @Schema(description="上课日期（yyyy-MM-dd）")
    @NotNull(groups = {SsClassTimeValidGroup.AddClassTimeGroup.class}, message = "上课日期不能为空")
    private LocalDate attendClassDate;

	/**
	* 上课开始时间（HH:mm）
	*/
    @Schema(description="上课开始时间（HH:mm）")
    @NotNull(groups = {
        SsClassTimeValidGroup.AddClassTimeGroup.class}, message = "上课开始时间不能为空")
    private LocalTime attendClassStartTime;

	/**
	* 上课结束时间（HH:mm）
	*/
    @Schema(description="上课结束时间（HH:mm）")
    @NotNull(groups = {
        SsClassTimeValidGroup.AddClassTimeGroup.class}, message = "上课结束时间不能为空")
    private LocalTime attendClassEndTime;

	/**
	* 是否已同步声网创建课堂: 0-否; 1-是;
	*/
    @Schema(description="是否已同步声网创建课堂: 0-否; 1-是;")
    private Integer isSyncAgora;

	/**
	* 上课类型: 0-直播课; 1-点播课;
	*/
    @Schema(description="上课类型: 0-直播课; 1-点播课;")
    private Integer attendClassType;

	/**
	* 监课链接url路径
	*/
    @Schema(description="监课链接url路径")
    private String supervisionClassUrl;

	/**
	* 监课开始时间(yyyy-MM-dd HH:mm:ss）
	*/
    @Schema(description="监课开始时间(yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime supervisionClassStartTime;

	/**
	* 监课结束时间(yyyy-MM-dd HH:mm:ss）
	*/
    @Schema(description="监课结束时间(yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime supervisionClassEndTime;

	/**
	* 主讲老师ID(ss_lecturer主键ID)
	*/
    @Schema(description="主讲老师ID(ss_lecturer主键ID)")
    private Long lecturerId;

	/**
	* 主讲设备ID
	*/
    @Schema(description="主讲设备ID")
    private Long deviceId;

	/**
	* 主讲教室ID
	*/
    @Schema(description="主讲教室ID")
    private Long classRoomId;

	/**
	* 书籍ID
	*/
    @Schema(description="书籍ID")
    @NotNull(groups = {SsClassTimeValidGroup.AddClassTimeGroup.class}, message = "书籍ID不能为空")
    private String booksId;

	/**
	* 书籍名称
	*/
    @Schema(description = "书籍名称")
    @NotBlank(groups = {
        SsClassTimeValidGroup.AddClassTimeGroup.class}, message = "书籍名称不能为空")
    @Length(min = 1, max = 64, groups = {
        SsClassTimeValidGroup.AddClassTimeGroup.class}, message = "书籍名称最小1个字符,最大不能超过64个字符")
    private String booksName;

	/**
	* 课程库ID(录播课资源ID)
	*/
    @Schema(description="课程库ID(录播课资源ID)")
    private Long recordingId;

	/**
	* 主讲端上课码(上课端标识1 + 5位随机数  例:115329)
	*/
    @Schema(description="主讲端上课码(上课端标识1 + 5位随机数  例:115329)")
    private String lecturerRoomCode;

	/**
	* 教室端上课码(教室端标识2 + 5位随机数  例:235329)
	*/
    @Schema(description="教室端上课码(教室端标识2 + 5位随机数  例:235329)")
    private String classRoomCode;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime ctime;

	/**
	* 创建者
	*/
    @Schema(description="创建者")
    private String creator;

	/**
	* 编辑时间
	*/
    @Schema(description="编辑时间")
    private LocalDateTime mtime;

	/**
	* 编辑者
	*/
    @Schema(description="编辑者")
    private String modifer;

	/**
	* 删除标记
	*/
    @Schema(description="删除标记")
    private Integer delFlag;

    /**
     * 上课码类型: 1-主讲端; 2-教室端;
     */
    @NotBlank(groups = {
        GenRoomTimeCodeGroup.class}, message = "上课码类型不能为空")
    @Schema(description = "上课码类型")
    private String roomCodeType;

    /**
     * 授权设备ID集合
     */
    @Schema(description = "授权设备列表Id集合")
    private List<Long> authDeviceIdList;

    /**
     * 授权设备集合
     */
    @NotNull(groups = {SsClassTimeValidGroup.UpdateAuthClassTimeRoomGroup.class}, message = "授权设备集合不能为空")
    @Schema(description = "授权设备集合")
    private List<ClassAuthRoomDTO> authDeviceList;

	/**
	 * 在课次ID集合
	 */
	private List<Long> classTimeIdList;

    /**
     * 不在课次ID集合
     */
    private List<Long> notClassTimeIdList;

    /**
     * 课程状态: 0-进行中; 1-即将开始; 2-本周内; 3-未开始; 4-已结束; 5-进行中 + 未开始
     */
    private Integer attendClassState;

    /**
     * 是否同步校管家: 0-否; 1-是;
     */
    private Integer isSyncXiaogj;

    /**
     * 是否跳过冲突: 0-否; 1-是;
     */
    private Integer isOutCollide = 0;

    /**
     * 课次开始时间
     */
    private LocalDateTime attendTimeStartTime;

    /**
     * 课次结束时间
     */
    private LocalDateTime attendTimeEndTime;

}
