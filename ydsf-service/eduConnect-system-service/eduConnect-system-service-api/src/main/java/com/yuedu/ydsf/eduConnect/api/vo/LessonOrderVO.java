package com.yuedu.ydsf.eduConnect.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName LessonOrderVO
 * @Description 课节顺序视图类
 * <AUTHOR>
 * @Date 2024/12/03 10:08:01
 * @Version v0.0.1
 */
@Data
@Schema(description = "已发布的直播间计划视图类")
public class LessonOrderVO {


    /**
     * 计划名称
     */
    @Schema(description = "直播间计划id")
    private Long planDetailId;

    /**
     * 课节顺序
     */
    @Schema(description = "课节顺序")
    private Integer lessonOrder;
}