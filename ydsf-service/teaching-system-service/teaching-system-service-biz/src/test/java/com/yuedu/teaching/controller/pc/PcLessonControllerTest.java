package com.yuedu.teaching.controller.pc;

import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.service.LessonService;
import com.yuedu.teaching.vo.LessonPracticeVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @ClassName PcLessonControllerTest
 * @Description Pc端课节管理测试类
 *
 * <AUTHOR>
 * @Date 2025/1/6 10:49
 * @Version v0.0.1
 */

class PcLessonControllerTest {
    private MockMvc mockMvc;

    @Mock
    private LessonService lessonService;

    @InjectMocks
    private PcLessonController pcLessonController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(pcLessonController).build();
    }

    @Test
    void getLessonPracticeList() throws Exception {
        CoursePublishQuery coursePublishQuery = new CoursePublishQuery();
        coursePublishQuery.setCourseId(1L);
        coursePublishQuery.setVersion(1);

        List<LessonPracticeVO> list = new ArrayList<>();


        when(lessonService.getLessonPracticeList(coursePublishQuery.getCourseId(),coursePublishQuery.getVersion())).thenReturn(list);

        mockMvc.perform(get("/pcLesson/lessonPracticeList"))
                .andExpect(status().isOk());
    }
}