package com.yuedu.teaching.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static com.yuedu.teaching.service.impl.CoursewareDataStepDetailsServiceImpl.addPathField;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(TeachingPageTemplateController.class)
public class TeachingPageTemplateControllerTest {

    private MockMvc mockMvc;

    @InjectMocks
    private TeachingPageTemplateController teachingPageTemplateController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        this.mockMvc = MockMvcBuilders.standaloneSetup(teachingPageTemplateController).build();
    }

    @Test
    public void testList() throws Exception {
        // 模拟教学模板列表
        mockMvc.perform(get("/teachingPageTemplate/list")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testSave() throws Exception {
        mockMvc.perform(post("/teachingPageTemplate")
                        .contentType("application/json")
                        .content("{\"templateName\": \"教学结束页\", \"type\": 1, \"url\": \"/img1.jpg\", \"remark\": \"1\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    public void testUpdateById() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders.put("/teachingPageTemplate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"id\": 3, \"templateName\": \"教学结束页\", \"type\": 1, \"url\": \"/img1.jpg\", \"remark\": \"1\"}"))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }


    @Test
    public void testAddPathField() throws Exception {

        JSONObject details = JSONUtil.parseObj("{\"title\": {\"type\": \"text\", \"content\": \"这是主标题\"}, \"answer\": [{\"url\": \"\", \"type\": \"image\", \"option\": \"A\", \"select\": false, \"content\": \"\"}, {\"url\": \"\", \"type\": \"image\", \"option\": \"B\", \"select\": false, \"content\": \"\"}, {\"url\": \"\", \"type\": \"image\", \"option\": \"C\", \"select\": false, \"content\": \"\"}, {\"url\": \"\", \"type\": \"image\", \"option\": \"D\", \"select\": false, \"content\": \"\"}, {\"url\": \"\", \"type\": \"image\", \"option\": \"E\", \"select\": false, \"content\": \"\"}, {\"url\": \"\", \"type\": \"image\", \"option\": \"F\", \"select\": false, \"content\": \"\"}], \"question\": {\"type\": \"text\", \"content\": \"这是问题?\"}, \"answerType\": \"image\", \"background\": {\"url\": \"teaching/pageTemplate/2024/11/25/4c4004371f5a4e4d8dd214593142c8ff.jpg\", \"type\": \"image\"}}");

        addPathField(details);
        System.out.println(details);
    }
}
