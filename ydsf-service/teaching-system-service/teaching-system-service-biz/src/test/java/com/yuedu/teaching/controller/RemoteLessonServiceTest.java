package com.yuedu.teaching.controller;

import com.yuedu.teaching.api.feign.RemoteCoursewareVersionService;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.dto.LessonNameDTO;
import com.yuedu.teaching.dto.LessonOrderDTO;
import com.yuedu.teaching.query.CoursePublishQuery;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @ClassName RemoteLessonServiceTest
 * @Description lessonService远程调用测试类
 * <AUTHOR>
 * @Date 2024/11/26 09:26
 * @Version v0.0.1
 */

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@EnableFeignClients("com.yuedu.teaching.api.feign")
class RemoteLessonServiceTest {

    @Resource
    private RemoteLessonService remoteLessonService;

    @Resource
    private RemoteCoursewareVersionService remoteCoursewareVersionService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void listLessonNameByIds() {
        List<Long> ids = Arrays.asList(1833426982L, 1833426976L, 1833426979L);
        log.info("通过id列表查询课件名列表:{}", remoteLessonService.listLessonNameByIds(ids));
    }

    @Test
    void listCoursewareByIds() {
        List<Long> ids = Arrays.asList(1833426993L, 1833426956L, 1833426986L);
        log.info("通过Id列表查询课件Id和课件名称:{}", remoteCoursewareVersionService.listCoursewareByIds(ids));
    }

    @Test
    void getPublishLessonList() {
        Long courseId = 68L;
        CoursePublishQuery coursePublishQuery = new CoursePublishQuery();
        coursePublishQuery.setCourseId(courseId);
        log.info("根据课程id查询所有已发布课节:{}", remoteLessonService.getPublishLessonList(coursePublishQuery));
    }

    @Test
    void getLessonByName(){
        CoursePublishQuery coursePublishQuery = new CoursePublishQuery();
        coursePublishQuery.setLessonName("第一节");
        log.info("根据课节名称查询课节信息:{}", remoteLessonService.getLessonByName(coursePublishQuery));
    }

    @Test
    void getPublishLessonListById(){
        CoursePublishQuery coursePublishQuery = new CoursePublishQuery();
        coursePublishQuery.setLessonIdList(List.of(9));
        log.info("根据id列表查询已发布课节:{}", remoteLessonService.getPublishLessonListById(coursePublishQuery));
    }

    @Test
    void getLessonListByOrder(){
        List<LessonOrderDTO> lessonOrderDTOList = new ArrayList<>();
        LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
        lessonOrderDTO.setCourseId(4L);
        lessonOrderDTO.setLessonOrderList(List.of(1,2));
        LessonOrderDTO lessonOrderDTO2 = new LessonOrderDTO();
        lessonOrderDTO2.setCourseId(6L);
        lessonOrderDTO2.setLessonOrderList(List.of(1,3,5));
        lessonOrderDTOList.add(lessonOrderDTO);
        log.info("根据课程id和课节排序获取课节列表:{}", remoteLessonService.getLessonListByOrder(lessonOrderDTOList));
    }

    @Test
    void getLessonNameList(){
        List<Integer> versionList = Arrays.asList(60,154,60,208,210);
        LessonNameDTO lessonNameDTO = new LessonNameDTO();
        lessonNameDTO.setVersionList(versionList);
        log.info("根据版本号列表查询课节名称列表:{}", remoteLessonService.getLessonNameList(lessonNameDTO));
    }
}