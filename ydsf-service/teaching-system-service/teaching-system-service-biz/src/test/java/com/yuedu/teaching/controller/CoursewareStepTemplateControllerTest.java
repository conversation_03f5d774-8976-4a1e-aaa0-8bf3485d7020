package com.yuedu.teaching.controller;


import com.alibaba.nacos.common.http.param.MediaType;
import com.yuedu.teaching.dto.CoursewareStepTemplateAddDTO;
import com.yuedu.teaching.service.CoursewareStepTemplateService;
import com.yuedu.teaching.vo.CoursewareStepTemplateVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2024/11/08
 **/
class CoursewareStepTemplateControllerTest {

    private MockMvc mockMvc;

    @Mock
    private CoursewareStepTemplateService coursewareStepTemplateService;

    @InjectMocks
    private CoursewareStepTemplateController coursewareStepTemplateController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(coursewareStepTemplateController).build();
    }

    /**
     * 查询所有教学环节模版测试
     */
    @Test
    void test_getCoursewareStepTemplatePage() throws Exception {
        List<CoursewareStepTemplateVO> coursewareStepTemplateVOList = Collections.emptyList();
        when(coursewareStepTemplateService.listCoursewareStepTemplate()).thenReturn(coursewareStepTemplateVOList);

        mockMvc.perform(get("/coursewareStepTemplate/list")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());
    }

    /**
     * 通过id查询测试
     */
    @Test
    void test_getDetails() throws Exception {
        List<CoursewareStepTemplateVO> coursewareStepTemplateVOList = Collections.emptyList();
        when(coursewareStepTemplateService.listCoursewareStepTemplate()).thenReturn(coursewareStepTemplateVOList);

        mockMvc.perform(get("/coursewareStepTemplate/details")
                        .contentType(MediaType.APPLICATION_JSON)
                        .param("id", "10"))
                .andDo(print())
                .andExpect(status().isOk());
    }


    /**
     * 新增教学环节模版测试
     */
    @Test
    void test_save() throws Exception {
        when(coursewareStepTemplateService.addCoursewareStepTemplate(any(CoursewareStepTemplateAddDTO.class))).thenReturn(true);

        mockMvc.perform(post("/coursewareStepTemplate/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {
                                    "templateName": "测试保存模板",
                                    "coursewareId": 23,
                                    "coursewareDataId": 1
                                }
                                """))
                .andDo(print())
                .andExpect(status().isOk());
    }

}