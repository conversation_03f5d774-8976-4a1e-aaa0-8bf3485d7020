package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.LessonAddDTO;
import com.yuedu.teaching.dto.LessonUpdateDTO;
import com.yuedu.teaching.dto.RemoveLessonDto;
import com.yuedu.teaching.dto.UpdateBatchLessonDto;
import com.yuedu.teaching.entity.LessonEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@SpringBootTest
class LessonControllerTest {

    @Resource
    private LessonController lessonController;

    @Test
    void getLessonList() {
        log.info("通过课程Id查询所有课节:{}", lessonController.getLessonList(2));
    }


    @Test
    void getById() {
        log.info("通过id查询:{}", lessonController.getById(2));
    }


    @Test
    void save() {
        LessonAddDTO addDTO = new LessonAddDTO();
        addDTO.setCourseId(1665032218);
        addDTO.setCourseId(1);
        addDTO.setCoursewareId(1);
        addDTO.setType(1);
        addDTO.setLessonName("test");
        lessonController.save(addDTO);
    }

    @Test
    void updateById() {
        LessonUpdateDTO updateDTO = new LessonUpdateDTO();
        updateDTO.setId(1665032215);
        updateDTO.setLessonOrder(1);
        lessonController.updateById(updateDTO);
    }

    @Test
    void updateBatchByList() {
        UpdateBatchLessonDto updateBatchLessonDto = new UpdateBatchLessonDto();
        updateBatchLessonDto.setCourseId(1);

        List<LessonEntity> entityList = new ArrayList<>();
        LessonEntity lesson = new LessonEntity();
        lesson.setId(1665032218L);
        lesson.setLessonName("2024秋季读书会LV7");
        lesson.setLessonOrder(34);
        entityList.add(lesson);

        updateBatchLessonDto.setEntityList(entityList);
        lessonController.updateBatchByList(updateBatchLessonDto);
    }


    @Test
    void removeLesson() {
        RemoveLessonDto removeLessonDto = new RemoveLessonDto();
        removeLessonDto.setId(1665032218);
        lessonController.removeLesson(removeLessonDto);
    }
}