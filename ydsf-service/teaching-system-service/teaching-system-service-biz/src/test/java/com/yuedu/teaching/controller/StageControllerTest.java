package com.yuedu.teaching.controller;

import com.yuedu.teaching.service.StageService;
import com.yuedu.teaching.vo.StageVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(StageController.class)
public class StageControllerTest {

    private MockMvc mockMvc;

    @Mock
    private StageService stageService;
    @InjectMocks
    private StageController stageController;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        this.mockMvc = MockMvcBuilders.standaloneSetup(stageController).build();
    }

    @Test
    public void testList() throws Exception {
        // 模拟查询到的书单列表
        List<StageVO> stageVOList = new ArrayList<>();
        when(stageService.listBooks(1)).thenReturn(stageVOList);
        // 发送 GET 请求，参数 product_id=1
        mockMvc.perform(get("/stage/list?product_id=1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void getStagePage() throws Exception {
        mockMvc.perform(get("/stage/page")
                        .param("current", "1")
                        .param("size", "10")
                        .param("product_id", "1")
                        .param("stageName", "TestName")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());
    }

}
