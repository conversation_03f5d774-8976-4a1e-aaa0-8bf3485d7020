package com.yuedu.teaching.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.yuedu.teaching.api.feign.RemoteCoursewareDataService;
import com.yuedu.teaching.api.feign.RemoteCoursewareService;
import com.yuedu.teaching.dto.CoursewareInfoDTO;
import com.yuedu.teaching.dto.CoursewareVersionDTO;
import com.yuedu.teaching.query.CoursewareDataStepDetailsQuery;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;
import com.yuedu.ydsf.common.core.util.R;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
*@ClassName RemoteCoursewareDataServiceTest
*@Description 课节远程调用测试类
*<AUTHOR>
*@Date 2024/12/5 14:24
*@Version v0.0.1 
*/

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@EnableFeignClients("com.yuedu.teaching.api.feign")
class RemoteCoursewareDataServiceTest {
    @Resource
    private RemoteCoursewareDataService remoteCoursewareDataService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testInnerGetDetails(){
        CoursewareDataStepDetailsQuery coursewareDataStepDetailsQuery = new CoursewareDataStepDetailsQuery();
        coursewareDataStepDetailsQuery.setCoursewareId(34);
        coursewareDataStepDetailsQuery.setStepId(617);
        R<CoursewareDataStepDetailsVO> details = remoteCoursewareDataService.getDetails(coursewareDataStepDetailsQuery);
        log.info("details:{}",details);
    }
}