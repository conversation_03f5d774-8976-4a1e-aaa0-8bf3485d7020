package com.yuedu.teaching.controller;

import com.yuedu.teaching.api.feign.RemoteCoursewareService;
import com.yuedu.teaching.dto.CoursewareInfoDTO;
import com.yuedu.teaching.dto.CoursewareVersionDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
*@ClassName RemoteCoursewareServiceTest
*@Description 课节远程调用测试类
*<AUTHOR>
*@Date 2024/12/5 14:24
*@Version v0.0.1 
*/

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@EnableFeignClients("com.yuedu.teaching.api.feign")
class RemoteCoursewareServiceTest {
    @Resource
    private RemoteCoursewareService remoteCoursewareService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void getCoursewareVersionList(){
        CoursewareVersionDTO coursewareVersionDTO = new CoursewareVersionDTO();
//        coursewareVersionDTO.setVersionList(List.of(19,20,21));
        coursewareVersionDTO.setCoursewareIdList(List.of(3L,10L,28L));
        log.info("调用课件服务返回结果：{}",remoteCoursewareService.getCoursewareVersionList(coursewareVersionDTO));
    }

    @Test
    void getCoursewareInfoList(){
        CoursewareInfoDTO coursewareInfoDTO = new CoursewareInfoDTO();
        coursewareInfoDTO.setVersionList(List.of(19,20,21));
        log.info("根据课件版本查询课件信息：{}",remoteCoursewareService.getCoursewareInfoList(coursewareInfoDTO));
    }

    @Test
    void getById(){
        log.info("根据id查询课件：{}",remoteCoursewareService.getById(33L));
    }
}