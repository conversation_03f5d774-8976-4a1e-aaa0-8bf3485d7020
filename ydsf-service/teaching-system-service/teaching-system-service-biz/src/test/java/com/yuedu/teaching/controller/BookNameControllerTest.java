package com.yuedu.teaching.controller;

import com.alibaba.nacos.common.http.param.MediaType;
import com.yuedu.teaching.dto.BookNameDTO;
import com.yuedu.teaching.entity.BookName;
import com.yuedu.teaching.service.BookNameService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @author: 张浩宇
 * @date: 2024/10/25
 **/
@SpringBootTest
public class BookNameControllerTest {

    private MockMvc mockMvc;

    @Mock
    private BookNameService bookNameService;

    @InjectMocks
    private BookNameController bookNameController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        this.mockMvc = MockMvcBuilders.standaloneSetup(bookNameController).build();
    }

    @Test
    public void testInsert() throws Exception {
        BookNameDTO bookName = new BookNameDTO();
        bookName.setAuthor("吴承恩");
        bookName.setTitle("西游记");
        bookName.setStageId(2);

        when(bookNameService.insert(bookName)).thenReturn(null);

        mockMvc.perform(post("/bookName/add")
                        .content("{\"author\":\"吴承恩\",\"title\":\"西游记\",\"stageId\":2}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":null,\"ok\":true}"));

    }

    @Test
    public void testUpdate() throws Exception {
        BookName bookName = new BookName();
        bookName.setId(4);
        bookName.setAuthor("吴承恩");
        bookName.setTitle("三国演义");

        when(bookNameService.updateBook(bookName)).thenReturn(false);

        mockMvc.perform(put("/bookName/edit")
                        .content("{\"id\":4,\"title\":\"三国演义\",\"author\":\"吴承恩\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":false,\"ok\":true}"));

    }

    @Test
    public void testRemove() throws Exception {
        BookNameDTO bookNameDTO = new BookNameDTO();
        bookNameDTO.setBookId(4);
        bookNameDTO.setStageId(1);

        when(bookNameService.removeBook(bookNameDTO)).thenReturn(true);

        mockMvc.perform(delete("/bookName/delete")
                        .content("{\"bookId\":4,\"stageId\":1}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":true,\"ok\":true}"));
    }
}
