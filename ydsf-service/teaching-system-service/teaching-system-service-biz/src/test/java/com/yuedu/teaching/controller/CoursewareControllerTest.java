package com.yuedu.teaching.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.http.param.MediaType;
import com.yuedu.teaching.dto.CoursewareDTO;
import com.yuedu.teaching.service.CoursewareService;
import com.yuedu.teaching.vo.CoursewareVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;


/**
 * @ClassName CoursewareControllerTest
 * @Description 课件测试类
 * <AUTHOR>
 * @Date 2024/11/6 09:33
 * @Version v0.0.1
 */

class CoursewareControllerTest {

    private MockMvc mockMvc;

    @Mock
    private CoursewareService coursewareService;

    @InjectMocks
    private CoursewareController coursewareController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(coursewareController).build();
    }

    @Test
    void getCoursewarePage() throws Exception {
        // 创建一个模拟的课程列表
        List<CoursewareVO> coursewareVOList = new ArrayList<>();

        // 创建一个 Courseware 对象并设置属性
        CoursewareVO coursewareVO = new CoursewareVO();
        coursewareVO.setId(1);
        coursewareVO.setCoursewareName("test");
        coursewareVO.setBookId(4);
        coursewareVO.setBookVersionId(11);
        coursewareVO.setPublishStatus(0);
        coursewareVO.setPublishTime(null);
        coursewareVO.setCreateBy("622144324");

        coursewareVOList.add(coursewareVO);

        // 模拟 coursewareService.list 方法的返回值
        when(coursewareService.getCoursewareListByBookId(coursewareVO.getBookId())).thenReturn(coursewareVOList);

        // 进行断言
        mockMvc.perform(get("/courseware/page/{bookId}", coursewareVO.getBookId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data", hasSize(1))) // 确保 data 是一个包含一个元素的数组
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].coursewareName").value("test"))
                .andExpect(jsonPath("$.data[0].bookId").value(4))
                .andExpect(jsonPath("$.data[0].bookVersionId").value(11))
                .andExpect(jsonPath("$.data[0].publishStatus").value(0));
    }

    @Test
    void save() throws Exception {
        // 创建一个 CoursewareDTO 对象
        CoursewareDTO coursewareDTO = new CoursewareDTO();
        coursewareDTO.setCoursewareName("测试课件");
        coursewareDTO.setBookVersionId(1);
        coursewareDTO.setBookId(1);

        // 模拟 coursewareService.addCoursewareSeries 方法的返回值
        when(coursewareService.addCoursewareSeries(any(CoursewareDTO.class))).thenReturn(true);

        // 执行请求并进行断言
        mockMvc.perform(post("/courseware/add")
                        .content(JSONUtil.toJsonStr(coursewareDTO))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":true,\"ok\":true}"));

        // 验证 coursewareService.addCoursewareSeries 方法被调用了一次
        verify(coursewareService, times(1)).addCoursewareSeries(any(CoursewareDTO.class));
    }

    @Test
    void getCoursewareListByName() throws Exception {
        // 创建一个 CoursewareDTO 对象
        CoursewareDTO coursewareDTO = new CoursewareDTO();
        coursewareDTO.setBookId(10);
        coursewareDTO.setCoursewareName("测试");

        // 模拟 coursewareService.getCoursewareList 方法的返回值
        List<CoursewareVO> coursewareList = Collections.singletonList(new CoursewareVO());
        coursewareList.get(0).setId(2); // 设置 id
        coursewareList.get(0).setCoursewareName("测试"); // 设置 coursewareName
        coursewareList.get(0).setBookId(10); // 设置 bookId
        coursewareList.get(0).setBookVersionId(14); // 设置 bookVersionId
        coursewareList.get(0).setPublishStatus(1); // 设置 publishStatus
        coursewareList.get(0).setPress("ewq"); // 设置 press
        coursewareList.get(0).setCreateBy("622144324"); // 设置 createBy

        when(coursewareService.getCoursewareList(10, "测试")).thenReturn(coursewareList);

        // 执行请求并进行断言
        mockMvc.perform(post("/courseware/listByName")
                        .content(JSONUtil.toJsonStr(coursewareDTO))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isNotEmpty())
                .andExpect(jsonPath("$.data[0].id").value(2))
                .andExpect(jsonPath("$.data[0].coursewareName").value("测试"))
                .andExpect(jsonPath("$.data[0].bookId").value(10))
                .andExpect(jsonPath("$.data[0].bookVersionId").value(14))
                .andExpect(jsonPath("$.data[0].publishStatus").value(1))
                .andExpect(jsonPath("$.data[0].press").value("ewq"))
                .andExpect(jsonPath("$.data[0].createBy").value("622144324"))
                .andReturn();
    }

    @Test
    void updateById() throws Exception {
        // 创建一个 CoursewareDTO 对象
        CoursewareDTO coursewareDTO = new CoursewareDTO();
        coursewareDTO.setId(1);
        coursewareDTO.setCoursewareName("更新后的课件名称");

        // 模拟 coursewareService.updateCourseware 方法的返回值
        when(coursewareService.updateCourseware(1, "更新后的课件名称")).thenReturn(true);

        // 执行请求并进行断言
        mockMvc.perform(MockMvcRequestBuilders.put("/courseware/edit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSONUtil.toJsonStr(coursewareDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true))
                .andExpect(jsonPath("$.ok").value(true));

        // 验证 coursewareService.updateCourseware 方法被调用了一次
        verify(coursewareService, times(1)).updateCourseware(1, "更新后的课件名称");
    }
}