package com.yuedu.teaching.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.dto.CourseAddDTO;
import com.yuedu.teaching.dto.CourseQueryDTO;
import com.yuedu.teaching.dto.CourseRemoveDTO;
import com.yuedu.teaching.dto.CourseUpdateDTO;
import com.yuedu.teaching.service.CourseService;
import com.yuedu.teaching.vo.CourseVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2024/10/25
 **/
class CourseControllerTest {
    private MockMvc mockMvc;

    @Mock
    private CourseService courseService;

    @InjectMocks
    private CourseController courseController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(courseController).build();
    }

    @Test
    void getCoursePage() throws Exception {
        new CourseQueryDTO();

        when(courseService.listCourses(any(Page.class), any(CourseQueryDTO.class))).thenReturn(new Page<>());

        mockMvc.perform(get("/course/page?size=10&current=1"))
                .andExpect(status().isOk());
    }

    @Test
    void save() throws Exception {
        CourseAddDTO courseAddDTO = new CourseAddDTO();
        courseAddDTO.setCourseName("2024秋季读书会LV1");
        courseAddDTO.setStageId(1);

        when(courseService.saveCourse(any(CourseAddDTO.class))).thenReturn(true);

        mockMvc.perform(post("/course/add")
                        .contentType("application/json")
                        .content("{\"courseName\": \"2024秋季读书会LV1\", \"stageId\": 1}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    void updateById() throws Exception {
        CourseUpdateDTO courseUpdateDTO = new CourseUpdateDTO();
        courseUpdateDTO.setCourseCode("R202410007");
        courseUpdateDTO.setCourseName("2024秋季读书会LV1");
        courseUpdateDTO.setStageId(2);

        when(courseService.updateCourse(any(CourseUpdateDTO.class))).thenReturn(true);

        mockMvc.perform(post("/course/edit")
                        .contentType("application/json")
                        .content("{\"courseCode\": \"R202410007\", \"courseName\": \"2024秋季读书会LV1\", \"stageId\": 2}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    void removeById() throws Exception {
        CourseRemoveDTO courseRemoveDTO = new CourseRemoveDTO();
        courseRemoveDTO.setCourseCode("R202410007");

        when(courseService.deleteCourse(any(CourseRemoveDTO.class))).thenReturn(true);

        mockMvc.perform(post("/course/delete")
                        .contentType("application/json")
                        .content("{\"courseCode\": \"R202410007\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    void getCourseList() throws Exception{
        List<CourseVO> courseVOList = new ArrayList<>();
        when(courseService.getCourseList()).thenReturn(courseVOList);

        mockMvc.perform(get("/course/list"))
                .andExpect(status().isOk());
    }
}