package com.yuedu.teaching.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.dto.BookVersionDTO;
import com.yuedu.teaching.entity.BookVersion;
import com.yuedu.teaching.service.BookVersionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;


/**
 * @ClassName BookVersionControllerTest
 * @Description 书籍版本测试类
 * <AUTHOR>
 * @Date 2024/10/28 16:02
 * @Version v0.0.1
 */

class BookVersionControllerTest {
    private MockMvc mockMvc;

    @Mock
    private BookVersionService bookVersionService;

    @InjectMocks
    private BookVersionController bookVersionController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(bookVersionController).build();
    }

    @Test
    void getBookVersionPage() throws Exception {
        // 创建一个模拟的分页对象
        Page<BookVersion> page = new Page<>();
        page.setRecords(new ArrayList<>()); // 设置 records 为空列表
        page.setTotal(0L);
        page.setSize(10);
        page.setCurrent(1);
        page.setPages(0);

        // 模拟 bookVersionService.page 方法的返回值
        when(bookVersionService.page(any(IPage.class), any())).thenReturn(page);

        // 执行请求并进行断言
        mockMvc.perform(get("/bookVersion/page")
                        .param("current", "1")
                        .param("size", "10")
                        .param("bookId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.records").isEmpty())
                .andExpect(jsonPath("$.data.total").value(0))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.current").value(1))
                .andExpect(jsonPath("$.data.pages").value(0));

        // 验证 bookVersionService.page 方法被调用了一次
        verify(bookVersionService, times(1)).page(any(IPage.class), any());
    }

    @Test
    void save() throws Exception {
        BookVersionDTO bookVersionDTO = new BookVersionDTO();
        bookVersionDTO.setBookId(1);
        bookVersionDTO.setIsbn("123456789");
        bookVersionDTO.setSource(1); // 设置 source 字段
        bookVersionDTO.setStageId(Arrays.asList(2)); // 设置 stageId 字段
        bookVersionDTO.setCover("http://example.com/cover.jpg"); // 设置 cover 字段
        bookVersionDTO.setPress("人民出版社"); // 设置 press 字段

        when(bookVersionService.addBookVersionSeries(any(BookVersionDTO.class))).thenReturn(true);

        mockMvc.perform(post("/bookVersion/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSONUtil.toJsonStr(bookVersionDTO))) // 使用 Hutool 的 JSONUtil 将 DTO 转换为 JSON 字符串
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":true,\"ok\":true}"));

        verify(bookVersionService, times(1)).addBookVersionSeries(any(BookVersionDTO.class));
    }

    @Test
    void updateById() throws Exception {
        BookVersionDTO bookVersionDTO = new BookVersionDTO();
        bookVersionDTO.setBookId(1);
        bookVersionDTO.setIsbn("123456789");
        bookVersionDTO.setSource(1); // 设置 source 字段
        bookVersionDTO.setStageId(Arrays.asList(2)); // 设置 stageId 字段
        bookVersionDTO.setCover("http://example.com/cover.jpg"); // 设置 cover 字段
        bookVersionDTO.setPress("人民出版社"); // 设置 press 字段

        when(bookVersionService.updateVersionSeries(any(BookVersionDTO.class))).thenReturn(true);

        mockMvc.perform(put("/bookVersion/edit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSONUtil.toJsonStr(bookVersionDTO)))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":true,\"ok\":true}"));

        verify(bookVersionService, times(1)).updateVersionSeries(any(BookVersionDTO.class));
    }

    @Test
    void removeById() throws Exception {
        BookVersionDTO bookVersionDTO = new BookVersionDTO();
        bookVersionDTO.setBookId(1);
        bookVersionDTO.setBookVersionId(1);
        bookVersionDTO.setIsbn("123456789");

        when(bookVersionService.subtractVersionSeries(any(BookVersionDTO.class))).thenReturn(true);

        mockMvc.perform(delete("/bookVersion/delete")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSONUtil.toJsonStr(bookVersionDTO))) // 使用 Hutool 的 JSONUtil 将 DTO 转换为 JSON 字符串
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":true,\"ok\":true}"));

        verify(bookVersionService, times(1)).subtractVersionSeries(any(BookVersionDTO.class));
    }
}
