package com.yuedu.teaching.service.impl;

import com.yuedu.teaching.dto.PictureBookRoleDTO;
import com.yuedu.teaching.entity.PictureBookRole;
import com.yuedu.teaching.mapper.PictureBookRoleMapper;
import com.yuedu.teaching.vo.PictureBookRoleVO;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2025/1/3 15:21
 */
@ExtendWith(MockitoExtension.class)
class PictureBookRoleServiceImplTest {

    @Mock
    private PictureBookRoleMapper pictureBookRoleMapper;

    @InjectMocks
    private PictureBookRoleServiceImpl pictureBookRoleService;


    private PictureBookRoleDTO createRoleDTO(Integer id, Integer bookId, String roleName, String url) {
        PictureBookRoleDTO dto = new PictureBookRoleDTO();
        dto.setId(id);
        dto.setBookId(bookId);
        dto.setRoleName(roleName);
        dto.setUrl(url);
        return dto;
    }

    private PictureBookRole createRole(Integer id, Integer bookId, String roleName, String url) {
        PictureBookRole role = new PictureBookRole();
        role.setId(id);
        role.setBookId(bookId);
        role.setRoleName(roleName);
        role.setUrl(url);
        return role;
    }

    @BeforeEach
    void setUp() {
        // 任何必要的设置
    }

    // ----------------- addRole 测试用例 -----------------
    @Test
    void addRole_Success() {
        // 添加角色成功
        // 准备测试数据
        PictureBookRoleDTO roleDTO = createRoleDTO(null, 1, "TestRole", "test.jpg");
        when(pictureBookRoleMapper.selectCount(any())).thenReturn(0L);
        when(pictureBookRoleMapper.exists(any())).thenReturn(false);
        when(pictureBookRoleMapper.insert(any(PictureBookRole.class))).thenReturn(1);

        // 执行测试
        Boolean result = pictureBookRoleService.addRole(roleDTO);

        // 验证结果
        assertTrue(result);
        verify(pictureBookRoleMapper, times(1)).insert(any(PictureBookRole.class));
    }

    @Test
    void addRole_ExceedMaxCount_ThrowsException() {
        // 添加角色超过最大计数抛出异常
        // 准备测试数据
        PictureBookRoleDTO roleDTO = createRoleDTO(null, 1, "TestRole", "test.jpg");
        when(pictureBookRoleMapper.selectCount(any())).thenReturn(6L);

        // 执行测试并验证异常
        assertThrows(CheckedException.class, () -> pictureBookRoleService.addRole(roleDTO));
        verify(pictureBookRoleMapper, never()).insert(any(PictureBookRole.class));
    }

    @Test
    void addRole_DuplicateName_ThrowsException() {
        // 添加角色重复名称引发异常
        // 准备测试数据
        PictureBookRoleDTO roleDTO = createRoleDTO(null, 1, "TestRole", "test.jpg");
        when(pictureBookRoleMapper.selectCount(any())).thenReturn(1L);
        when(pictureBookRoleMapper.exists(any())).thenReturn(true);

        // 执行测试并验证异常
        assertThrows(CheckedException.class, () -> pictureBookRoleService.addRole(roleDTO));
        verify(pictureBookRoleMapper, never()).insert(any(PictureBookRole.class));
    }

    // ----------------- getRoles 测试用例 -----------------
    @Test
    void getRoles_Success() {
        // 获得角色成功
        // 准备测试数据
        Integer bookId = 1;
        List<PictureBookRole> roles = List.of(
                createRole(1, bookId, "Role1", "url1.jpg"),
                createRole(2, bookId, "Role2", "url2.jpg")
        );
        when(pictureBookRoleMapper.selectList(any())).thenReturn(roles);

        // 执行测试
        List<PictureBookRoleVO> result = pictureBookRoleService.getRoles(bookId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Role1", result.get(0).getRoleName());
        assertEquals("Role2", result.get(1).getRoleName());
        verify(pictureBookRoleMapper, times(1)).selectList(any());
    }

    @Test
    void getRoles_EmptyResult() {
        // 获取角色空结果
        // 准备测试数据
        Integer bookId = 1;
        when(pictureBookRoleMapper.selectList(any())).thenReturn(Collections.emptyList());

        // 执行测试
        List<PictureBookRoleVO> result = pictureBookRoleService.getRoles(bookId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(pictureBookRoleMapper, times(1)).selectList(any());
    }

    @Test
    void getRoles_NullBookId_ThrowsException() {
        // book id 为 null 抛出异常
        assertThrows(CheckedException.class, () -> pictureBookRoleService.getRoles(null));
        verify(pictureBookRoleMapper, never()).selectList(any());
    }

    // ----------------- updateRoles 测试用例 -----------------
    @Test
    void updateRoles_Success() {
        // 更新角色列表成功
        // 准备测试数据
        List<PictureBookRoleDTO> updateList = List.of(
                createRoleDTO(1, 1, "UpdatedRole1", "updatedUrl1"),
                createRoleDTO(2, 1, "UpdatedRole2", "updatedUrl2")
        );
        List<PictureBookRole> existingRoles = List.of(
                createRole(1, 1, "Role1", "url1.jpg"),
                createRole(2, 1, "Role2", "url2.jpg")
        );

        // Mock 方法调用
        when(pictureBookRoleMapper.selectList(any())).thenReturn(existingRoles);

        // 执行测试
        Boolean result = pictureBookRoleService.updateRoles(updateList);

        // 验证结果
        assertTrue(result);
        verify(pictureBookRoleMapper, times(1)).selectList(any());
        verify(pictureBookRoleMapper, times(1)).updateById((Collection<PictureBookRole>) any());
    }

    @Test
    void updateRoles_EmptyList_ThrowsException() {
        // 更新角色空列表引发异常
        assertThrows(CheckedException.class, () -> pictureBookRoleService.updateRoles(Collections.emptyList()));
        verify(pictureBookRoleMapper, never()).updateById((PictureBookRole) any());
    }

    // ----------------- updateRole 测试用例 -----------------
    @Test
    void updateRole_Success() {
        // 更新角色成功
        // 准备测试数据
        PictureBookRoleDTO updateDTO = createRoleDTO(1, 1, "UpdatedRole", "url.jpg");
        PictureBookRole existingRole = createRole(1, 1, "Role", "url.jpg");

        when(pictureBookRoleMapper.selectById(1)).thenReturn(existingRole);
        when(pictureBookRoleMapper.exists(any())).thenReturn(false);
        when(pictureBookRoleMapper.updateById(any(PictureBookRole.class))).thenReturn(1);

        // 执行测试
        Boolean result = pictureBookRoleService.updateRole(updateDTO);

        // 验证结果
        assertTrue(result);
        verify(pictureBookRoleMapper, times(1)).updateById(any(PictureBookRole.class));
    }

    @Test
    void updateRole_NonExistent_ThrowsException() {
        // 更新角色不存在引发异常
        // 准备测试数据
        PictureBookRoleDTO updateDTO = createRoleDTO(1, 1, "UpdatedRole", "url.jpg");
        when(pictureBookRoleMapper.selectById(1)).thenReturn(null);

        // 执行测试并验证异常
        assertThrows(CheckedException.class, () -> pictureBookRoleService.updateRole(updateDTO));
        verify(pictureBookRoleMapper, never()).updateById(any(PictureBookRole.class));
    }

    @Test
    void updateRole_DuplicateName_ThrowsException() {
        // 更新角色重复名称引发异常
        // 准备测试数据
        PictureBookRoleDTO updateDTO = createRoleDTO(1, 1, "UpdatedRole", "url.jpg");
        PictureBookRole existingRole = createRole(1, 1, "Role", "url.jpg");

        when(pictureBookRoleMapper.selectById(1)).thenReturn(existingRole);
        when(pictureBookRoleMapper.exists(any())).thenReturn(true);

        // 执行测试并验证异常
        assertThrows(CheckedException.class, () -> pictureBookRoleService.updateRole(updateDTO));
        verify(pictureBookRoleMapper, never()).updateById(any(PictureBookRole.class));
    }

    // ----------------- batchSaveOrUpdateRoles 测试用例 -----------------
    @Test
    void batchSaveOrUpdateRoles_Success() {
        // 批量新增更新角色成功
        // 准备测试数据
        List<PictureBookRoleDTO> roleList = List.of(
                createRoleDTO(1, 1, "UpdatedRole1", "url1.jpg"),  // 更新已存在角色
                createRoleDTO(null, 1, "NewRole2", "url2.jpg")    // 新增角色
        );

        List<PictureBookRole> existingRoles = List.of(
                createRole(1, 1, "Role1", "url1.jpg")
        );

        // Mock 方法调用
        when(pictureBookRoleMapper.selectList(any())).thenReturn(existingRoles);
        when(pictureBookRoleMapper.insert(any(List.class))).thenReturn(Collections.singletonList(1));
        when(pictureBookRoleMapper.updateById(any(List.class))).thenReturn(Collections.singletonList(1));

        // 执行测试
        Boolean result = pictureBookRoleService.batchSaveOrUpdateRoles(roleList);

        // 验证结果
        assertTrue(result);
        verify(pictureBookRoleMapper, times(1)).insert(any(List.class));
        verify(pictureBookRoleMapper, times(1)).updateById(any(List.class));
    }

    @Test
    void batchSaveOrUpdateRoles_EmptyList_ThrowsException() {
        // 批量新增更新角色空列表引发异常
        // 准备测试数据
        List<PictureBookRoleDTO> emptyList = Collections.emptyList();

        // 执行测试并验证异常
        assertThrows(CheckedException.class, () -> pictureBookRoleService.batchSaveOrUpdateRoles(emptyList));
        verify(pictureBookRoleMapper, never()).insert(any(List.class));
        verify(pictureBookRoleMapper, never()).updateById(any(List.class));
    }

    @Test
    void batchSaveOrUpdateRoles_ExceedMaxCount_ThrowsException() {
        // 批量新增更新角色超过最大计数引发异常
        // 准备测试数据：5个现有角色 + 3个新增角色 > 6个最大限制
        List<PictureBookRoleDTO> roleList = List.of(
                createRoleDTO(1, 1, "UpdatedRole1", "url1.jpg"),
                createRoleDTO(null, 1, "NewRole1", "url2.jpg"),
                createRoleDTO(null, 1, "NewRole2", "url3.jpg"),
                createRoleDTO(null, 1, "NewRole3", "url4.jpg")
        );

        List<PictureBookRole> existingRoles = List.of(
                createRole(1, 1, "Role1", "url1.jpg"),
                createRole(2, 1, "Role2", "url2.jpg"),
                createRole(3, 1, "Role3", "url3.jpg"),
                createRole(4, 1, "Role4", "url4.jpg"),
                createRole(5, 1, "Role5", "url5.jpg")
        );

        // Mock 方法调用：只需要模拟获取现有角色列表
        when(pictureBookRoleMapper.selectList(any())).thenReturn(existingRoles);

        // 执行测试并验证异常
        assertThrows(CheckedException.class, () -> pictureBookRoleService.batchSaveOrUpdateRoles(roleList));
        verify(pictureBookRoleMapper, never()).insert(any(List.class));
        verify(pictureBookRoleMapper, never()).updateById(any(List.class));
    }

    @Test
    void batchSaveOrUpdateRoles_DuplicateNames_ThrowsException() {
        // 批量新增更新角色重复名称引发异常
        // 准备测试数据
        List<PictureBookRoleDTO> roleList = List.of(
                createRoleDTO(1, 1, "SameName", "url1.jpg"),
                createRoleDTO(null, 1, "SameName", "url2.jpg")
        );

        List<PictureBookRole> existingRoles = List.of(
                createRole(1, 1, "Role1", "url1.jpg")
        );

        // Mock 方法调用
        when(pictureBookRoleMapper.selectList(any())).thenReturn(existingRoles);

        // 执行测试并验证异常
        assertThrows(CheckedException.class, () -> pictureBookRoleService.batchSaveOrUpdateRoles(roleList));
        verify(pictureBookRoleMapper, never()).insert(any(List.class));
        verify(pictureBookRoleMapper, never()).updateById(any(List.class));
    }

    @Test
    void batchSaveOrUpdateRoles_NonExistentRole_ThrowsException() {
        // 批量新增更新角色不存在的角色ID引发异常
        // 准备测试数据
        List<PictureBookRoleDTO> roleList = List.of(
                createRoleDTO(999, 1, "UpdatedRole", "url1.jpg")  // 不存在的角色ID
        );

        List<PictureBookRole> existingRoles = List.of(
                createRole(1, 1, "Role1", "url1.jpg")
        );

        // Mock 方法调用
        when(pictureBookRoleMapper.selectList(any())).thenReturn(existingRoles);

        // 执行测试并验证异常
        assertThrows(CheckedException.class, () -> pictureBookRoleService.batchSaveOrUpdateRoles(roleList));
        verify(pictureBookRoleMapper, never()).insert(any(List.class));
        verify(pictureBookRoleMapper, never()).updateById(any(List.class));
    }
}