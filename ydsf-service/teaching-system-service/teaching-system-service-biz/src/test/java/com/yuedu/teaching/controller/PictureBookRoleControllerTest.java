package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.PictureBookRoleDTO;
import com.yuedu.teaching.query.PictureBookRoleQuery;
import com.yuedu.teaching.service.PictureBookRoleService;
import com.yuedu.teaching.vo.PictureBookRoleVO;
import com.yuedu.ydsf.common.core.util.R;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * PictureBookRoleController 单元测试
 *
 * <AUTHOR>
 * @date 2025/1/6 11:43
 */
@ExtendWith(MockitoExtension.class)
class PictureBookRoleControllerTest {

    @Mock
    private PictureBookRoleService pictureBookRoleService;

    @InjectMocks
    private PictureBookRoleController pictureBookRoleController;

    private List<PictureBookRoleVO> mockRoleVOList;
    private PictureBookRoleDTO mockAddRoleDTO;
    private PictureBookRoleDTO mockUpdateRoleDTO;
    private PictureBookRoleQuery mockRoleQuery;

    /**
     * 测试数据初始化
     */
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockRoleVOList = new ArrayList<>();
        PictureBookRoleVO roleVO = new PictureBookRoleVO();
        roleVO.setId(1);
        roleVO.setBookId(1);
        roleVO.setRoleName("测试角色");

        // 初始化Avatar对象
        PictureBookRoleVO.Avatar avatar = new PictureBookRoleVO.Avatar();
        avatar.setRelativePath("test-role.jpg");
        avatar.setFullPath("http://tdownload.yuedushufang.com/test-role.jpg");
        roleVO.setAvatar(avatar);

        mockRoleVOList.add(roleVO);

        // 初始化用于新增的PictureBookRoleDTO
        mockAddRoleDTO = new PictureBookRoleDTO();
        mockAddRoleDTO.setBookId(1);
        mockAddRoleDTO.setRoleName("测试角色");
        mockAddRoleDTO.setUrl("test-role.jpg");

        // 初始化用于更新的PictureBookRoleDTO
        mockUpdateRoleDTO = new PictureBookRoleDTO();
        mockUpdateRoleDTO.setId(1);
        mockUpdateRoleDTO.setBookId(1);
        mockUpdateRoleDTO.setRoleName("测试角色");
        mockUpdateRoleDTO.setUrl("test-role.jpg");

        // 初始化PictureBookRoleQuery，用于批量更新
        mockRoleQuery = new PictureBookRoleQuery();
        List<PictureBookRoleDTO> roleDTOList = new ArrayList<>();
        roleDTOList.add(mockUpdateRoleDTO);
        mockRoleQuery.setBookRoleList(roleDTOList);
    }

    // ----------------- getDetails 测试用例 -----------------
    @Test
    void getDetails_ShouldReturnRoleList() {
        // 测试根据绘本ID查询角色列表
        Integer bookId = 1;
        when(pictureBookRoleService.getRoles(bookId)).thenReturn(mockRoleVOList);

        // 执行测试
        R<List<PictureBookRoleVO>> result = pictureBookRoleController.getDetails(bookId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isOk());
        assertEquals(mockRoleVOList, result.getData());

        // 验证返回数据的具体内容
        PictureBookRoleVO returnedRole = result.getData().get(0);
        assertEquals(1, returnedRole.getId());
        assertEquals(1, returnedRole.getBookId());
        assertEquals("测试角色", returnedRole.getRoleName());
        assertEquals("test-role.jpg", returnedRole.getAvatar().getRelativePath());
        assertEquals("http://tdownload.yuedushufang.com/test-role.jpg", returnedRole.getAvatar().getFullPath());

        verify(pictureBookRoleService, times(1)).getRoles(bookId);
    }

    @Test
    void getDetails_WithEmptyResult_ShouldReturnEmptyList() {
        // 测试查询结果为空的情况
        Integer bookId = 999;
        when(pictureBookRoleService.getRoles(bookId)).thenReturn(Collections.emptyList());

        R<List<PictureBookRoleVO>> result = pictureBookRoleController.getDetails(bookId);

        assertNotNull(result);
        assertTrue(result.isOk());
        assertTrue(result.getData().isEmpty());
        verify(pictureBookRoleService, times(1)).getRoles(bookId);
    }

    // ----------------- addRole 测试用例 -----------------
    @Test
    void addRole_ShouldReturnTrue() {
        // 测试新增单个绘本角色，使用不带ID的DTO
        when(pictureBookRoleService.addRole(any(PictureBookRoleDTO.class))).thenReturn(true);

        // 执行测试
        R<Boolean> result = pictureBookRoleController.addRole(mockAddRoleDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isOk());
        assertTrue(result.getData());
        verify(pictureBookRoleService, times(1)).addRole(mockAddRoleDTO);
    }

    @Test
    void addRole_WhenServiceReturnsFalse_ShouldReturnFalse() {
        // 测试新增失败的情况，使用不带ID的DTO
        when(pictureBookRoleService.addRole(any(PictureBookRoleDTO.class))).thenReturn(false);

        R<Boolean> result = pictureBookRoleController.addRole(mockAddRoleDTO);

        assertNotNull(result);
        assertTrue(result.isOk());
        assertFalse(result.getData());
        verify(pictureBookRoleService, times(1)).addRole(mockAddRoleDTO);
    }

    // ----------------- updateRoles 测试用例 -----------------
    @Test
    void updateRoles_ShouldReturnTrue() {
        // 测试批量更新绘本角色，使用带ID的DTO列表
        when(pictureBookRoleService.updateRoles(anyList())).thenReturn(true);

        // 执行测试
        R<Boolean> result = pictureBookRoleController.updateRoles(mockRoleQuery);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isOk());
        assertTrue(result.getData());
        verify(pictureBookRoleService, times(1)).updateRoles(mockRoleQuery.getBookRoleList());
    }

    @Test
    void updateRoles_WithEmptyList_ShouldReturnTrue() {
        // 测试空列表更新的情况
        mockRoleQuery.setBookRoleList(Collections.emptyList());
        when(pictureBookRoleService.updateRoles(anyList())).thenReturn(true);

        R<Boolean> result = pictureBookRoleController.updateRoles(mockRoleQuery);

        assertNotNull(result);
        assertTrue(result.isOk());
        assertTrue(result.getData());
        verify(pictureBookRoleService, times(1)).updateRoles(Collections.emptyList());
    }

    // ----------------- updateRole 测试用例 -----------------
    @Test
    void updateRole_ShouldReturnTrue() {
        // 测试更新单个绘本角色，使用带ID的DTO
        when(pictureBookRoleService.updateRole(any(PictureBookRoleDTO.class))).thenReturn(true);

        // 执行测试
        R<Boolean> result = pictureBookRoleController.updateRole(mockUpdateRoleDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isOk());
        assertTrue(result.getData());
        verify(pictureBookRoleService, times(1)).updateRole(mockUpdateRoleDTO);
    }

    @Test
    void updateRole_WhenServiceReturnsFalse_ShouldReturnFalse() {
        // 测试更新失败的情况，使用带ID的DTO
        when(pictureBookRoleService.updateRole(any(PictureBookRoleDTO.class))).thenReturn(false);

        R<Boolean> result = pictureBookRoleController.updateRole(mockUpdateRoleDTO);

        assertNotNull(result);
        assertTrue(result.isOk());
        assertFalse(result.getData());
        verify(pictureBookRoleService, times(1)).updateRole(mockUpdateRoleDTO);
    }

    // ----------------- batchSaveOrUpdateRoles 测试用例 -----------------
    @Test
    void batchSaveOrUpdateRoles_ShouldReturnTrue() {
        // 测试批量保存或更新绘本角色，使用混合的DTO列表（包含新增和更新的数据）
        when(pictureBookRoleService.batchSaveOrUpdateRoles(anyList())).thenReturn(true);

        // 执行测试
        R<Boolean> result = pictureBookRoleController.batchSaveOrUpdateRoles(mockRoleQuery);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isOk());
        assertTrue(result.getData());
        verify(pictureBookRoleService, times(1)).batchSaveOrUpdateRoles(mockRoleQuery.getBookRoleList());
    }

    @Test
    void batchSaveOrUpdateRoles_WithEmptyList_ShouldReturnTrue() {
        // 测试空列表的情况
        mockRoleQuery.setBookRoleList(Collections.emptyList());
        when(pictureBookRoleService.batchSaveOrUpdateRoles(anyList())).thenReturn(true);

        R<Boolean> result = pictureBookRoleController.batchSaveOrUpdateRoles(mockRoleQuery);

        assertNotNull(result);
        assertTrue(result.isOk());
        assertTrue(result.getData());
        verify(pictureBookRoleService, times(1)).batchSaveOrUpdateRoles(Collections.emptyList());
    }

    @Test
    void batchSaveOrUpdateRoles_WhenServiceReturnsFalse_ShouldReturnFalse() {
        // 测试服务层返回失败的情况
        when(pictureBookRoleService.batchSaveOrUpdateRoles(anyList())).thenReturn(false);

        R<Boolean> result = pictureBookRoleController.batchSaveOrUpdateRoles(mockRoleQuery);

        assertNotNull(result);
        assertTrue(result.isOk());
        assertFalse(result.getData());
        verify(pictureBookRoleService, times(1)).batchSaveOrUpdateRoles(mockRoleQuery.getBookRoleList());
    }
}