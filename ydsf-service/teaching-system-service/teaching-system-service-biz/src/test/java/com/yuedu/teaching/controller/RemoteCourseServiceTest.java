package com.yuedu.teaching.controller;

import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.ydsf.common.core.util.R;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName RemoteCourseServiceTest
 * @Description 远程课程服务测试类
 * <AUTHOR>
 * @Date 2024/11/26 08:59
 * @Version v0.0.1
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@EnableFeignClients("com.yuedu.teaching.api.feign")
class RemoteCourseServiceTest {

    @Resource
    private RemoteCourseService remoteCourseService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void getCourseList(){
        log.info("执行teaching服务远程调用接口，获取所有已发布课程列表：{}",remoteCourseService.getCourseList());
    }

    @Test
    void getCourseListByName(){
        CoursePublishQuery coursePublishQuery = new CoursePublishQuery();
        coursePublishQuery.setCourseName("2024");
        log.info("执行teaching服务远程调用接口，根据课程名称获取已发布课程列表：{}",remoteCourseService.getCourseListByName(coursePublishQuery));
    }

    @Test
    void getCourseListByIds(){
        CourseDTO courseDTO = new CourseDTO();
        courseDTO.setCourseIdList(List.of(1,2));
        log.info("执行teaching服务远程调用接口，根据课程id列表获取已发布课程列表：{}",remoteCourseService.getCourseListByIds(courseDTO));
    }

    @Test
    void getCourseMapByIdList(){
        List<Long> longs = new ArrayList<>();
        longs.add(3L);
        longs.add(2L);
        R<Map<Long, CourseVO>> courseMapByIdList = remoteCourseService.getCourseMapByIdList(longs);
        log.info("执行teaching服务远程调用接口，根据课程id列表获取已发布课程列表：{}",courseMapByIdList);
    }

    @Test
    void getCourseListByVersion(){
        CourseDTO courseDTO = new CourseDTO();
        courseDTO.setVersionList(List.of(243, 229));
        log.info("执行teaching服务远程调用接口，根据课程版本获取已发布课程列表：{}",remoteCourseService.getCourseListByVersion(courseDTO));
    }

}
