package com.yuedu.teaching.controller.pc;

import com.yuedu.teaching.service.CourseService;
import com.yuedu.teaching.vo.CourseVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @ClassName PcCourseControllerTest
 * @Description PC课程控制器测试类
 *
 * <AUTHOR>
 * @Date 2025/1/6 10:48
 * @Version v0.0.1
 */

class PcCourseControllerTest {
    private MockMvc mockMvc;

    @Mock
    private CourseService courseService;

    @InjectMocks
    private PcCourseController pcCourseController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(pcCourseController).build();
    }

    @Test
    void getCoursePubList() throws Exception{
        List<CourseVO> list = new ArrayList<>();
        when(courseService.getCourseList()).thenReturn(list);

        mockMvc.perform(get("/pcCourse/pubList"))
                .andExpect(status().isOk());
    }


}