package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.CoursewareStepByTemplateDTO;
import com.yuedu.teaching.dto.CoursewareStepDTO;
import com.yuedu.teaching.dto.CoursewareStepOrderDTO;
import com.yuedu.teaching.query.CoursewareStepQuery;
import com.yuedu.teaching.service.CoursewareStepService;
import com.yuedu.teaching.vo.CoursewareStepVO;
import com.yuedu.teaching.vo.StepVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2024/11/08
 **/
class CoursewareStepControllerTest {
    private MockMvc mockMvc;

    @Mock
    private CoursewareStepService coursewareStepService;

    @InjectMocks
    private CoursewareStepController coursewareStepController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(coursewareStepController).build();
    }


    /**
     * 查询所有教学环节测试
     */
    @Test
    void test_listCoursewareStep() throws Exception {
        List<StepVO> stepVOList = new ArrayList<>();
        StepVO linkStepVO = new StepVO();
        linkStepVO.setId(1);
        linkStepVO.setStepName("开场");
        linkStepVO.setStepParent(0);
        linkStepVO.setStepOrder(1);
        linkStepVO.setType(1);
        linkStepVO.setPageTemplateId(0);

        StepVO pageStepVO = new StepVO();
        pageStepVO.setId(2);
        pageStepVO.setStepName("课前等待");
        pageStepVO.setStepParent(1);
        pageStepVO.setStepOrder(1);
        pageStepVO.setType(2);
        pageStepVO.setPageTemplateId(1);
        List<StepVO> children = new ArrayList<>();
        children.add(pageStepVO);
        linkStepVO.setChildren(children);

        stepVOList.add(linkStepVO);

        when(coursewareStepService.listCoursewareStep(any(CoursewareStepQuery.class)))
                .thenReturn(stepVOList);

        mockMvc.perform(MockMvcRequestBuilders.get("/coursewareStep/list")
                        .param("coursewareDataId", "1")
                        .param("coursewareId", "3")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());

    }

    /**
     * 新增教学环节测试
     */
    @Test
    void test_save_add() throws Exception {
        CoursewareStepDTO stepDTO = new CoursewareStepDTO();
        doNothing().when(coursewareStepService).addCoursewareStep(stepDTO);
        mockMvc.perform(MockMvcRequestBuilders.post("/coursewareStep/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {
                                    "coursewareDataId": 1,
                                    "coursewareId": 28,
                                    "stepName": "课间休息",
                                    "stepParent": 37,
                                    "stepOrder": 1,
                                    "type": 2,
                                    "pageTemplateId": 2
                                }
                                """))
                .andDo(print())
                .andExpect(status().isOk());
    }

    /**
     * 根据模板新增测试
     */
    @Test
    void test_save_addByTemplate() throws Exception {
        when(coursewareStepService.copyCoursewareStep(any(CoursewareStepByTemplateDTO.class)))
                .thenReturn(true);

        mockMvc.perform(MockMvcRequestBuilders.post("/coursewareStep/addByTemplate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {
                                    "coursewareId": 28,
                                    "coursewareDataId": 1,
                                    "dataTemplateId": 63,
                                    "stepTemplateId": 33,
                                    "stepIds": [
                                        34,36,46,23
                                    ]
                                }
                                """)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());
    }

    /**
     * 修改教学环节测试
     */
    @Test
    void test_updateById() throws Exception {
        CoursewareStepVO stepVO = new CoursewareStepVO();
        stepVO.setId(45);
        stepVO.setCoursewareDataId(1);
        stepVO.setCoursewareId(28);
        stepVO.setStepName("课间互动");
        stepVO.setStepParent(37);
        stepVO.setStepOrder(1);
        stepVO.setType(2);
        stepVO.setDataStepDetailsId(2);

        when(coursewareStepService.updateCoursewareStep(any(CoursewareStepDTO.class)))
                .thenReturn(stepVO);

        mockMvc.perform(MockMvcRequestBuilders.put("/coursewareStep/edit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {
                                "id": 9,
                                "coursewareDataId": 1,
                                "coursewareId": 8,
                                "stepName": "课间互动"
                                }
                                """)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());
    }

    /**
     * 修改教学环节顺序测试
     */
    @Test
    void test_updateById_editList() throws Exception {
        CoursewareStepOrderDTO stepOrderDTO = new CoursewareStepOrderDTO();

        doNothing().when(coursewareStepService).updateBatchByList(stepOrderDTO);

        mockMvc.perform(MockMvcRequestBuilders.put("/coursewareStep/editList")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {
                                    "coursewareDataId": 1,
                                    "coursewareId": 8,
                                    "infoList": [
                                        {
                                            "id": 2,
                                            "stepOrder": 3
                                        },
                                        {
                                            "id": 41,
                                            "stepOrder": 1
                                        }
                                    ]
                                }
                                """)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());
    }

    /**
     * 通过id删除教学环节测试
     */
    @Test
    void test_removeById() throws Exception {
        when(coursewareStepService.deleteCoursewareStep(any(CoursewareStepDTO.class)))
                .thenReturn(true);

        mockMvc.perform(MockMvcRequestBuilders.delete("/coursewareStep/delete")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {
                                    "id": 27,
                                    "coursewareId": 40
                                }
                                """)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));
    }
}