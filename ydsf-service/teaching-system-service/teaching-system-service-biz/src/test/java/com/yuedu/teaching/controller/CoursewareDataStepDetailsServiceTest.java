package com.yuedu.teaching.controller;


import com.alibaba.fastjson.JSON;
import com.yuedu.teaching.dto.CoursewareDataStepDetailsDTO;
import com.yuedu.teaching.query.CoursewareDataStepDetailsQuery;
import com.yuedu.teaching.service.CoursewareDataStepDetailsService;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @ClassName CourseWareDataStepDetailsControllerTest
 * @Description 资料数据和教学环节详情Test
 * <AUTHOR>
 * @Date 2024/11/04 9:27
 * @Version v0.0.1
 */
@SpringBootTest
@Slf4j
class CoursewareDataStepDetailsServiceTest {

    @Resource
    private CoursewareDataStepDetailsService detailsService;

    @Test
    void getDetails() {
        CoursewareDataStepDetailsQuery dto = new CoursewareDataStepDetailsQuery();
        dto.setCoursewareId(123);
        dto.setCoursewareDataId(123);
        dto.setStepId(2);
        CoursewareDataStepDetailsVO vo = detailsService.getDetails(dto);
        log.info("课件环节信息:{}", JSON.toJSONString(vo));
    }

    @Test
    void updateDetails() {
        CoursewareDataStepDetailsDTO dto = new CoursewareDataStepDetailsDTO();
        dto.setStepId(540);
        dto.setCoursewareId(4);
        dto.setCoursewareDataId(58);
        dto.setDetails("{\"title\":{\"type\":\"text\",\"content\":\"啦啦啦啦\"},\"subtitle\":{\"type\":\"text\",\"content\":\"小标题怎么写？\"},\"background\":{\"url\":\"teaching/courseware/2024/11/20/bd1e480e841c47808275dc16b0cca02f.jpg \",\"type\":\"image\"}}");
        Boolean i = detailsService.updateDetails(dto);
        log.info("执行结果:{}", JSON.toJSONString(i));
    }
}