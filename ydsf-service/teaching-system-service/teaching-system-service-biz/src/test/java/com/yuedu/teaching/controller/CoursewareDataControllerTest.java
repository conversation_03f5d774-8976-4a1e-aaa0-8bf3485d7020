package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.CoursewareDataDTO;
import com.yuedu.teaching.entity.CoursewareData;
import com.yuedu.teaching.service.CoursewareDataService;
import com.yuedu.teaching.vo.CoursewareDataVO;
import com.yuedu.teaching.vo.DataTemplateVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * @author: 张浩宇
 * @date: 2024/11/08
 **/
@SpringBootTest
public class CoursewareDataControllerTest {

    private MockMvc mockMvc;

    @Mock
    private CoursewareDataService coursewareDataService;

    @InjectMocks
    private CoursewareDataController coursewareDataController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(coursewareDataController).build();
    }

    @Test
    public void testInsert() throws Exception {
        CoursewareDataDTO coursewareDataDTO = new CoursewareDataDTO();
        coursewareDataDTO.setCoursewareId(1);
        coursewareDataDTO.setDataTemplateId(3);

        CoursewareDataVO build = CoursewareDataVO.builder()
                .coursewareId(1)
                .dataTemplateId(3)
                .build();
//        when(coursewareDataService.insert(coursewareDataDTO)).thenReturn(build);

        mockMvc.perform(post("/coursewareData/add")
                        .contentType("application/json")
                        .content("{\"coursewareId\":1,\"dataTemplateId\":3}"))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":{\"coursewareId\":1,\"dataTemplateId\":3},\"ok\":true}"));
    }

    @Test
    public void testList() throws Exception {
        CoursewareData coursewareData = new CoursewareData();
        coursewareData.setCoursewareId(3);

        DataTemplateVO build = DataTemplateVO.builder().build();
        List<DataTemplateVO> list = new ArrayList<>();
        list.add(build);
        when(coursewareDataService.getCoursewareDataList(coursewareData)).thenReturn((Map<String, List<DataTemplateVO>>) list);

        mockMvc.perform(get("/coursewareData/list?coursewareId=3"))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":[],\"ok\":true}"));
    }

    @Test
    public void updateById_ValidRequest_ShouldReturnOk() throws Exception {
//        CoursewareDataDTO coursewareDataDTO = CoursewareDataDTO.builder()
//                .id(16)
//                .path("test.jpg")
//                .fileName("test")
//                .build();
//
//        CoursewareDataVO build = CoursewareDataVO.builder()
//                .id(16)
//                .fileName("test")
//                .build();
//        when(coursewareDataService.updateCoursewareData(coursewareDataDTO)).thenReturn(build);
//
//        mockMvc.perform(put("/coursewareData/edit")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content("{\"id\":16,\"path\":\"test.jpg\",\"fileName\":\"test\"}")) // 使用合适的JSON内容填充
//                .andExpect(status().isOk())
//                .andExpect(content().json("{\"code\":0,\"msg\":null,\"data\":{\"id\":16,\"fileName\":\"test\"},\"ok\":true}")); // 假设R.ok的返回值中code为200
    }

}
