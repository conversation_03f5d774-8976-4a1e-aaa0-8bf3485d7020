package com.yuedu.teaching.controller;

import cn.hutool.json.JSONUtil;
import com.yuedu.teaching.dto.CourseAddDTO;
import com.yuedu.teaching.entity.CourseType;
import com.yuedu.teaching.service.CourseService;
import com.yuedu.teaching.service.CourseTypeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class CourseTypeControllerTest {
    private MockMvc mockMvc;
    @Mock
    private CourseTypeService courseTypeService;
    @InjectMocks
    private CourseTypeController courseTypeController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(courseTypeController).build();
    }

    @Test
    void save() throws Exception {
        CourseType courseType = new CourseType();
        courseType.setName("常规课");
        String jsonStr = JSONUtil.toJsonStr(courseType);

        when(courseTypeService.save(any(CourseType.class))).thenReturn(true);

        mockMvc.perform(post("/courseType/add")
                        .contentType("application/json")
                        .content(jsonStr))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    void updateById() {
    }

    @Test
    void getAllOK() {
    }

    @Test
    void getAllCourseType() {
    }
}