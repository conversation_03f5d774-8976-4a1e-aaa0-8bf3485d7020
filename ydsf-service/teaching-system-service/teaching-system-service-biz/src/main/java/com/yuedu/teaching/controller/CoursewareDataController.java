package com.yuedu.teaching.controller;

import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.dto.CoursewareDataDTO;
import com.yuedu.teaching.entity.CoursewareData;
import com.yuedu.teaching.service.CoursewareDataService;
import com.yuedu.teaching.valid.CoursewareDataValidGroup;
import com.yuedu.teaching.vo.CoursewareDataVO;
import com.yuedu.teaching.vo.DataTemplateVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 资料表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:40:10
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/coursewareData")
@Tag(description = "courseware_data", name = "资料表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CoursewareDataController {

    private final CoursewareDataService coursewareDataService;

    /**
     * 新增资料表
     *
     * @param coursewareDataDTO 资料表
     * @return R<CoursewareDataVO>
     */
    @Operation(summary = "新增资料表", description = "新增资料表")
    @SysLog("新增资料表")
    @PostMapping("/add")
    @HasPermission("teaching_coursewareData_add")
    public R<CoursewareDataVO> save(@Validated({CoursewareDataValidGroup.InsertCoursewareData.class}) @RequestBody CoursewareDataDTO coursewareDataDTO) {
        return R.ok(coursewareDataService.insert(coursewareDataDTO));
    }

    /**
     * 修改资料表
     *
     * @param coursewareDataDTO 资料表
     * @return R<Boolean>
     */
    @Operation(summary = "修改资料表", description = "修改资料表")
    @SysLog("修改资料表")
    @PutMapping("/edit")
    @HasPermission("teaching_coursewareData_edit")
    public R<CoursewareDataVO> updateById(@Validated({CoursewareDataValidGroup.UpdateCoursewareData.class}) @RequestBody CoursewareDataDTO coursewareDataDTO) {
        return R.ok(coursewareDataService.updateCoursewareData(coursewareDataDTO));
    }

    /**
     * 查询资料列表
     * @param coursewareData 查询条件
     * @return <List<CoursewareDataVO>>
     */
    @Operation(summary = "查询资料列表", description = "查询资料列表")
    @GetMapping("/list")
    @HasPermission("teaching_coursewareData_view")
    public R<Map<String, List<DataTemplateVO>>> getCoursewareDataList(@Validated({CoursewareDataValidGroup.ListCoursewareData.class}) @ParameterObject CoursewareData coursewareData) {
        return R.ok(coursewareDataService.getCoursewareDataList(coursewareData));
    }

    /**
     * 设置发布状态
     * @param coursewareDataDTO 主键id
     * @return R<Boolean>
     */
    @Operation(summary = "设置发布状态", description = "设置发布状态")
    @PostMapping("/setPublishStatus")
    public R<Boolean> setPublishStatus(@Validated({CoursewareDataValidGroup.SetPublishStatus.class}) @RequestBody CoursewareDataDTO coursewareDataDTO){
        coursewareDataService.setPubCanPublish(coursewareDataDTO.getId(), coursewareDataDTO.getCanPublish(),coursewareDataDTO.getCoursewareId());
        return R.ok(Boolean.TRUE);
    }

    /**
     * 是否开放给门店
     * @param coursewareDataDTO 主键id
     * @return R<Boolean>
     */
    @Operation(summary = "是否开放给门店", description = "是否开放给门店")
    @PostMapping("/setOpenStore")
    public R<Boolean> setOpenStore(@Validated({CoursewareDataValidGroup.SetStoreStatus.class}) @RequestBody CoursewareDataDTO coursewareDataDTO){
        coursewareDataService.setStoreStatus(coursewareDataDTO, TeachingConstant.COURSEWARE_DATA_OPEN_STORE);
        return R.ok(Boolean.TRUE);
    }

    /**
     * 是否允许下载
     * @param coursewareDataDTO 主键id
     * @return R<Boolean>
     */
    @Operation(summary = "是否允许下载", description = "是否允许下载")
    @PostMapping("/setAllowDownload")
    public R<Boolean> setAllowDownload(@Validated({CoursewareDataValidGroup.SetDownloadStatus.class}) @RequestBody CoursewareDataDTO coursewareDataDTO){
        coursewareDataService.setStoreStatus(coursewareDataDTO,TeachingConstant.COURSEWARE_DATA_DAWNLOAD_STORE);
        return R.ok(Boolean.TRUE);
    }
}
