package com.yuedu.teaching;

import com.yuedu.ydsf.common.feign.annotation.EnableYdsfFeignClients;
import com.yuedu.ydsf.common.security.annotation.EnableYdsfResourceServer;
import com.yuedu.ydsf.common.swagger.annotation.EnableOpenApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR> auto
 * <p>
 * 项目启动类
 */
@Slf4j
@EnableOpenApi("teaching")
@EnableYdsfFeignClients
@EnableDiscoveryClient
@EnableYdsfResourceServer
@SpringBootApplication
public class YdsfTeachingApplication {
    public static void main(String[] args) {
        SpringApplication.run(YdsfTeachingApplication.class, args);
    }
}
