package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.*;
import com.yuedu.teaching.entity.LessonEntity;
import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.service.LessonService;
import com.yuedu.teaching.vo.LessonCoursewareVO;
import com.yuedu.teaching.vo.LessonNameVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName LessonController
 * @Description 课程章节表管理
 * <AUTHOR>
 * @Date 2024/10/24 9:48
 * @Version v0.0.1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/lesson")
@Tag(description = "lesson", name = "课程章节表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LessonController {

    @Resource
    private LessonService lessonService;


    /**
     * 列表查询,通过课程Id查询所有课节
     *
     * @param courseId 课程id
     * @return 列表
     */
    @Operation(summary = "通过课程Id查询所有课节", description = "列表查询")
    @GetMapping("/list")
//    @HasPermission("teaching_lesson_view")
    public R getLessonList(@ParameterObject Integer courseId) {
        return R.ok(lessonService.getLessonList(courseId));
    }


    /**
     * 通过id查询课程章节详情
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
//    @HasPermission("teaching_lesson_view")
    public R getById(@PathVariable("id") Integer id) {
        return R.ok(lessonService.getLessonById(id));
    }

    /**
     * 通过id列表查询课件名列表
     *
     * @param ids id列表
     * @return List<LessonNameVO>
     */
    @Inner
    @Operation(summary = "通过id列表查询课件名列表", description = "通过id列表查询课件名列表")
    @PostMapping("/listLessonNameByIds")
    public R<List<LessonNameVO>> listLessonNameByIds(@RequestBody List<Long> ids) {
        return R.ok(lessonService.listLessonNameByIds(ids));
    }

    /**
     * 通过Id列表查询课件Id和课件名称
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    @Inner
    @Operation(summary = "通过Id列表查询课件Id和课件名称", description = "通过Id列表查询课件Id和课件名称")
    @PostMapping("/listCoursewareByIds")
//    @HasPermission("teaching_lesson_view")
    public R<List<LessonCoursewareVO>> listCoursewareByIds(@RequestBody List<Integer> ids) {
        return R.ok(lessonService.listCoursewareByIds(ids));
    }

    /**
     * 新增课程章节表
     *
     * @param lessonAddDTO 课程章节dto
     * @return R
     */
    @Operation(summary = "新增课程章节表", description = "新增课程章节表")
    @SysLog("新增课程章节表")
    @PostMapping("/saveLesson")
    @HasPermission("teaching_lesson_add")
    public R save(@RequestBody @Valid LessonAddDTO lessonAddDTO) {
        return R.ok(lessonService.saveLesson(lessonAddDTO));
    }

    /**
     * 修改课程章节表
     *
     * @param updateDTO 课程章节表
     * @return R
     */
    @Operation(summary = "修改课程章节表", description = "修改课程章节表")
    @SysLog("修改课程章节表")
    @PutMapping
    @HasPermission("teaching_lesson_edit")
    public R updateById(@RequestBody @Valid LessonUpdateDTO updateDTO) {
        return R.ok(lessonService.updateLessonById(updateDTO));
    }


    /**
     * 修改课程章节表
     *
     * @param updateBatchLessonDto 课程章节表
     * @return R
     */
    @Operation(summary = "调整课节顺序", description = "修改课程章节表")
    @SysLog("调整课节顺序")
    @PostMapping("updateBatchByList")
    @HasPermission("teaching_lesson_edit")
    public R updateBatchByList(@RequestBody @Valid UpdateBatchLessonDto updateBatchLessonDto) {
        return R.ok(lessonService.updateBatchByList(updateBatchLessonDto));
    }


    /**
     * 通过id删除课程章节表
     *
     * @param removeLessonDto id
     * @return R
     */
    @Operation(summary = "通过id删除课程章节表", description = "通过id删除课程章节表")
    @SysLog("通过id删除课程章节表")
    @DeleteMapping("removeLesson")
    @HasPermission("teaching_lesson_del")
    public R removeLesson(@RequestBody @Valid RemoveLessonDto removeLessonDto) {
        return R.ok(lessonService.removeLesson(removeLessonDto));
    }

    /**
     * 根据课程id获取已发布课节列表
     *
     * @param coursePublishQuery 内含课程id
     * @return R<List < LessonVO>>
     */
    @Operation(summary = "根据课程id获取已发布课节列表", description = "根据课程id获取已发布课节列表")
    @PostMapping("/pubList")
    @Inner
    public R<List<LessonVO>> getPublishLessonList(@RequestBody CoursePublishQuery coursePublishQuery) {
        return R.ok(lessonService.getPublishLessonList(coursePublishQuery.getCourseId(), coursePublishQuery.getVersion()));
    }


    /**
     * 根据课程id获取已发布课节列表
     * 飞天使用
     * @return R<List < LessonVO>>
     */
    @Operation(summary = "根据课程id获取已发布课节列表", description = "根据课程id获取已发布课节列表")
    @GetMapping("/ftPubList/{courseId}")
    @NoToken
    public R<List<LessonVO>> getFtPublishLessonList(@PathVariable Long courseId) {
        return R.ok(lessonService.getPublishLessonList(courseId,null));
    }

    /**
     * 根据课节名称获取已发布的课节列表
     *
     * @param coursePublishQuery 课节名称
     * @return R<List < LessonEntity>>
     */
    @Operation(summary = "根据课节名称获取已发布的课节列表",description = "根据课节名称获取已发布的课节列表")
    @PostMapping("/pubListByName")
    @Inner
    public R<List<LessonEntity>> getLessonByName(@RequestBody CoursePublishQuery coursePublishQuery) {
        return R.ok(lessonService.getPublishLessonListByName(null,coursePublishQuery.getLessonName()));
    }

    /**
     * 根据课程id和课节名称获取已发布的课节列表
     *
     * @param coursePublishQuery 课节名称
     * @return R<List < LessonEntity>>
     */
    @Operation(summary = "根据课程id和课节名称获取已发布的课节列表",description = "根据课程id和课节名称获取已发布的课节列表")
    @PostMapping("/pubListByNameAndId")
//    @HasPermission("teaching_lesson_view")
    public R<List<LessonEntity>> getLessonByNameAndCourseId(@Valid @RequestBody CoursePublishQuery coursePublishQuery) {
        return R.ok(lessonService.getPublishLessonListByName(coursePublishQuery.getCourseId(),coursePublishQuery.getLessonName()));
    }

    /**
     * 通过id列表获取已发布课节列表
     *
     * @param coursePublishQuery 课节id列表
     * @return R<List<LessonVO>>
     */
    @Operation(summary = "通过id列表获取已发布课节列表",description = "通过id列表获取已发布课节列表")
    @PostMapping("/pubListById")
    @Inner
    public R<List<LessonVO>> getPublishLessonListById(@RequestBody CoursePublishQuery coursePublishQuery){
        return R.ok(lessonService.getPublishLessonListById(coursePublishQuery.getLessonIdList()));
    }


    /**
     * 获取课节名称列表
     *
     * @param lessonNameDTO 版本id集合
     * @return R<List<LessonNameVO>>
     */
    @Operation(summary = "获取课节名称列表",description = "获取课节名称列表")
    @PostMapping("/lessonNameList")
    @Inner
    public R<List<LessonNameVO>> getLessonNameList(@RequestBody LessonNameDTO lessonNameDTO){
    	return R.ok(lessonService.getLessonNameList(lessonNameDTO.getVersionList()));
    }

    /**
     * 根据课程id和课节排序获取课节列表
     *
     * @param lessonOrderDTOList 根据课程id和课节排序DTO
     * @return R<List<LessonVO>>
     */
    @Operation(summary = "根据课程id和课节排序获取课节列表",description = "根据课程id和课节排序获取课节列表")
    @PostMapping("/lessonListByOrder")
    @Inner
    public R<List<LessonVO>> getLessonListByOrder(@RequestBody List<LessonOrderDTO> lessonOrderDTOList){
        return R.ok(lessonService.getLessonListByOrder(lessonOrderDTOList));
    }
}