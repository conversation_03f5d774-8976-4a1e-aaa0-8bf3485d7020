package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.TeachingPageTemplateDTO;
import com.yuedu.teaching.service.TeachingPageTemplateService;
import com.yuedu.teaching.vo.TeachingPageTemplateVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 教学页模版表
 *
 * <AUTHOR>
 * @date 2024-11-05 16:39:34
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/teachingPageTemplate")
@Tag(description = "teachingPageTemplate", name = "教学页模版表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TeachingPageTemplateController {

    private final TeachingPageTemplateService teachingPageTemplateService;

    /**
     * 查询全部教学模板
     *
     * @return 全部教学模板列表
     */
    @Operation(summary = "查询教学模板", description = "查询教学模板")
    @GetMapping("/list")
    public R list(@RequestParam(name = "type", required = false, defaultValue = "0") Integer type ,@RequestParam(name = "category", required = false, defaultValue = "0") Integer category ,@RequestParam(name = "enabled", required = false, defaultValue = "2") Integer enabled) {
        return R.ok(teachingPageTemplateService.listTemplates(type, category, enabled));
    }

    /**
     * 通过id获取教学模板详情
     *
     * @param id 教学模板id
     * @return R<TeachingPageTemplateVO> 教学模板详情
     */
    @Operation(summary = "通过书籍版本id获取书籍版本详情", description = "通过书籍版本id获取书籍版本详情")
    @GetMapping("/detail")
    public R<TeachingPageTemplateVO> getTeachingPageTemplateById(@RequestParam(value = "id", defaultValue = "0") Integer id) {
        return R.ok(teachingPageTemplateService.getTeachingPageTemplateById(id));
    }

    /**
     * 新增教学页模版表
     *
     * @param teachingPageTemplate 教学页模版表
     * @return R
     */
    @Operation(summary = "新增教学页模版表", description = "新增教学页模版表")
    @SysLog("新增教学页模版表")
    @PostMapping
    @HasPermission("teaching_teachingPageTemplate_add")
    public R save(@Validated(ValidGroup.Insert.class) @RequestBody TeachingPageTemplateDTO teachingPageTemplate) {
        return R.ok(teachingPageTemplateService.saveTemplate(teachingPageTemplate));
    }

    /**
     * 修改教学页模版表
     *
     * @param teachingPageTemplate 教学页模版表
     * @return R
     */
    @Operation(summary = "修改教学页模版表", description = "修改教学页模版表")
    @SysLog("修改教学页模版表")
    @PutMapping
    @HasPermission("teaching_teachingPageTemplate_edit")
    public R updateById(@Validated(ValidGroup.Update.class) @RequestBody TeachingPageTemplateDTO teachingPageTemplate) {
        return R.ok(teachingPageTemplateService.updateTemplate(teachingPageTemplate));
    }

}