package com.yuedu.teaching.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.dto.*;
import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.service.CourseService;
import com.yuedu.teaching.vo.CampusAuthVO;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.cache.annotation.CacheEvict;

import java.util.List;
import java.util.Map;

/**
 * 课程表 控制类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:53:59
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/course")
@Tag(description = "course", name = "课程表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseController {

    private final CourseService courseService;

    /**
     * 分页查询
     *
     * @param page           分页对象
     * @param courseQueryDTO 查询课程表DTO
     * @return R
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("teaching_course_view")
    public R<Page<CourseVO>> getCoursePage(@ParameterObject Page<?> page, @ParameterObject CourseQueryDTO courseQueryDTO) {
        return R.ok(courseService.listCourses(page, courseQueryDTO));
    }

    /**
     * 根据ID查询
     *
     * @param courseQueryDTO 查询课程表DTO
     * @return R
     */
    @Operation(summary = "根据ID查询", description = "根据ID查询")
    @GetMapping("/get")
    @HasPermission("teaching_course_view")
    public R<CourseVO> getCourseById(@ParameterObject @Valid CourseQueryDTO courseQueryDTO) {
        return R.ok(courseService.getCourse(courseQueryDTO));
    }

    /**
     * 新增课程表
     *
     * @param courseAddDTO 新增课程表DTO
     * @return R
     */
    @Operation(summary = "新增课程表", description = "新增课程表")
    @SysLog("新增课程表")
    @PostMapping("/add")
    @Idempotent(key = "'teaching_course_'+#courseAddDTO.courseName", expireTime = 3, info = "请勿重复添加")
    @CacheEvict(value = "courseTypesByStore", allEntries = true)
    @HasPermission("teaching_course_add")
    public R<Boolean> save(@Valid @RequestBody CourseAddDTO courseAddDTO) {
        return R.ok(courseService.saveCourse(courseAddDTO));
    }

    /**
     * 修改课程表
     *
     * @param courseUpdateDTO 更新课程表DTO
     * @return R
     */
    @Operation(summary = "修改课程表", description = "修改课程表")
    @SysLog("修改课程表")
    @PutMapping("/edit")
    @HasPermission("teaching_course_edit")
    @Idempotent(key = "'teaching_course_'+#courseUpdateDTO.id", expireTime = 3, info = "请勿重复提交修改")
    @CacheEvict(value = "courseTypesByStore", allEntries = true)
    public R<Boolean> updateById(@Valid @RequestBody CourseUpdateDTO courseUpdateDTO) {
        return R.ok(courseService.updateCourse(courseUpdateDTO));
    }

    /**
     * 删除课程表
     *
     * @param courseRemoveDTO 课程编码
     * @return R
     */
    @Operation(summary = "删除课程表", description = "删除课程表")
    @SysLog("删除课程表")
    @DeleteMapping("/delete")
    @HasPermission("teaching_course_del")
    public R<Boolean> removeById(@Valid @RequestBody CourseRemoveDTO courseRemoveDTO) {
        return R.ok(courseService.deleteCourse(courseRemoveDTO));
    }

    /**
     * 发布课程
     *
     * @param coursePublishDTO 课程发布dto
     * @return R
     */
    @Operation(summary = "发布课程", description = "发布课程")
    @SysLog("发布课程")
    @PostMapping("coursePublish")
    @HasPermission("teaching_lesson_del")
    public R coursePublish(@Valid @RequestBody CoursePublishDTO coursePublishDTO) {
        return R.ok(courseService.coursePublish(coursePublishDTO));
    }

    /**
     * 获取所有已发布课程列表
     *
     * @return R<List<CourseVO>>
     */
    @Operation(summary = "前端:获取所有已发布课程列表", description = "前端:获取所有已发布课程列表")
    @GetMapping("/pubList")
    @HasPermission("teaching_course_view")
    public R<List<CourseVO>> getCoursePubList() {
        return R.ok(courseService.getCourseList());
    }

    /**
     * 获取课程授权校区
     * <AUTHOR>
     * @date 2025/5/22 10:00
     * @param courseId
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.teaching.vo.CampusAuthVO>>
     */
    @Operation(summary = "获取课程授权校区", description = "获取课程授权校区")
    @GetMapping("/getAuthStore")
    public R<List<CampusAuthVO>> getAuthStore(@RequestParam(required = false) Long courseId) {
        return R.ok(courseService.getAuthStore(courseId));
    }

    /**
     * 获取所有已发布课程列表
     *
     * @return R<List<CourseVO>>
     */
    @Operation(summary = "获取所有已发布课程列表", description = "获取所有已发布课程列表")
    @GetMapping("/list")
    @Inner
    public R<List<CourseVO>> getCourseList() {
        return R.ok(courseService.getCourseList());
    }



    /**
     *  根据门店ID获得课程列表
     *
     * <AUTHOR>
     * @date 2025年06月05日 16时05分
     */
    @GetMapping("/course/listByStoreId")
    @Inner
    R<List<CourseVO>> getCourseListByStoreId(Long id){
        CourseQueryDTO courseQueryDTO = new CourseQueryDTO();
        courseQueryDTO.setStoreId(id);
        return R.ok(courseService.getCourseListByStoreId(courseQueryDTO));
    }



    /**
     * 获取所有已发布课程列表
     * 飞天使用
     * @return R<List<CourseVO>>
     */
    @Operation(summary = "获取所有已发布课程列表", description = "获取所有已发布课程列表")
    @GetMapping("/ftList")
    @NoToken
    public R<List<CourseVO>> getFtCourseList() {
        return R.ok(courseService.getCourseList());
    }

    /**
     * 根据课程名称获取已发布课程列表
     *
     * @param coursePublishQuery   查询参数
     * @return R<List<CourseVO>>
     */
    @Operation(summary = "根据课程名称获取已发布课程列表", description = "根据课程名称获取已发布课程列表")
    @PostMapping("/listByName")
    @Inner
    public R<List<CourseVO>> getCourseListByName(@RequestBody CoursePublishQuery coursePublishQuery){
        return R.ok(courseService.getCourseListByName(coursePublishQuery.getCourseName()));
    }

    /**
     * 根据课程的阶段获取课程信息
     * <AUTHOR>
     * @date 2025/4/22 11:58
     * @param coursePublishQuery
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.teaching.vo.CourseVO>>
     */
    @Operation(summary = "根据课程的阶段获取课程信息", description = "根据课程的阶段获取课程信息")
    @PostMapping("/getCourseListByStageId")
    @Inner
    public R<List<CourseVO>> getCourseListByStageId(@RequestBody CourseDTO CourseDTO){
        return R.ok(courseService.getCourseListByStageId(CourseDTO));
    }

    /**
     * 根据课程名称获取已发布课程列表
     *
     * @param coursePublishQuery   查询参数
     * @return R<List<CourseVO>>
     */
    @Operation(summary = "根据课程名称获取已发布课程列表", description = "根据课程名称获取已发布课程列表")
    @PostMapping("/listByCourseName")
    @HasPermission("teaching_lesson_view")
    public R<List<CourseVO>> courseListByName(@RequestBody CoursePublishQuery coursePublishQuery){
        return R.ok(courseService.getCourseListByName(coursePublishQuery.getCourseName()));
    }

    /**
     * 根据课程名称获取已发布课程列表
     *
     * @param coursePublishQuery   查询参数
     * @return R<List<CourseVO>>
     */
    @Operation(summary = "根据课程名称获取已发布课程列表", description = "根据课程名称获取已发布课程列表")
    @PostMapping("/findByCourseName")
    @StorePermission
    public R<List<CourseVO>> findByCourseName(@RequestBody CoursePublishQuery coursePublishQuery){
        return R.ok(courseService.getCourseListByName(coursePublishQuery.getCourseName()));
    }

    /**
     * 根据课程id获取已发布课程列表
     *
     * @param courseDTO 课程id列表
     * @return R<List<CourseVO>>
     */
    @Operation(summary = "根据课程id获取已发布课程列表", description = "根据课程id获取已发布课程列表")
    @PostMapping("/listById")
    @Inner
    public R<List<CourseVO>> getCourseListByIds(@RequestBody CourseDTO courseDTO){
        return R.ok(courseService.getCourseListByIds(courseDTO.getCourseIdList()));
    }

    /**
     * 根据课程id获取最新发布课程Map
     * @param courseIdList 课程id列表
     * @return <Map<Long,CourseVO>
     */
    @Operation(summary = "根据课程id获取最新发布课程Map",description = "根据课程id获取最新发布课程Map")
    @PostMapping("/getMapById")
    @Inner
    R<Map<Long,CourseVO>> getCourseMapByIdList(@RequestBody List<Long> courseIdList){
        return R.ok(courseService.getCourseMapByIdList(courseIdList));
    }

    /**
     * 根据课程id停用(启用)课程
     *
     * @param courseDisableDTO 课程id,是否停用
     * @return R<Boolean>
     */
    @Operation(summary = "根据课程id停用(启用)课程",description = "根据课程id停用(启用)课程")
    @PutMapping("/disable")
    @HasPermission("teaching_course_edit")
    public R<Boolean> updateCourseDisable(@RequestBody CourseDisableDTO courseDisableDTO) {
        return R.ok(courseService.updateCourseDisable(courseDisableDTO.getId(),courseDisableDTO.getDisable()));
    }

    /**
     * 根据课程版本获取课程信息列表
     *
     * @param courseDTO 课程DTO
     * @return R<List<CourseVO>>
     */
    @Operation(summary = "根据课程版本获取课程信息列表",description = "根据课程版本获取课程信息列表")
    @PutMapping("/listByVersion")
    @Inner
    public R<List<CourseVO>> getCourseListByVersion(@RequestBody CourseDTO courseDTO){
        return R.ok(courseService.getCourseListByVersion(courseDTO.getVersionList()));
    }

    /**
     * 根据课程类型查询课程ID
     */
    @Operation(summary = "根据课程类型查询课程ID", description = "根据课程类型查询课程ID")
    @GetMapping("/getCourseIdByType")
    @Inner
    public R<List<Integer>> getCourseIdByType(@RequestParam("courseTypeId") Long courseTypeId) {
        return R.ok(courseService.getCourseIdByType(courseTypeId));
    }
}
