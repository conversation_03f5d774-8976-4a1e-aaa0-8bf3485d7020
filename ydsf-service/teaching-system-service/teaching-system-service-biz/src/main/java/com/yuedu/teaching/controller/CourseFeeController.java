package com.yuedu.teaching.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.dto.CourseFeeDTO;
import com.yuedu.teaching.query.CourseAuthStoreQuery;
import com.yuedu.teaching.service.CourseAuthStoreService;
import com.yuedu.teaching.service.CourseFeeService;
import com.yuedu.teaching.vo.CourseAuthStoreVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Objects;

/**
* 控制层
*
* <AUTHOR>
* @date  2025/05/22
*/

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/courseFee")
@Tag(description = "门店课时费" , name = "门店课时费" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseFeeController  {


    private final CourseFeeService courseFeeService;

    private final CourseAuthStoreService courseAuthStoreService;


    /**
     *  获取门店课程课消标准
     *
     * <AUTHOR>
     * @date 2025年05月22日 10时23分
     */
    @GetMapping("/pageByStoreId/{id}")
    @HasPermission("teaching_course_fee_view" )
    @Operation(summary = "门店课时费分页查询" , description = "门店课时费分页查询" )
    public R<IPage<CourseAuthStoreVO>> pageByStoreId(@PathVariable Long id, @ParameterObject Page page, @ParameterObject CourseAuthStoreQuery courseAuthStoreQuery){
        if(Objects.isNull(id)){
            return R.failed("门店ID不能为空");
        }
        courseAuthStoreQuery.setStoreId(id);
        return R.ok(courseAuthStoreService.pageByStoreId(page, courseAuthStoreQuery));
    }

    /**
     *  根据门店修改自定义课时费
     *
     * <AUTHOR>
     * @date 2025年05月27日 14时18分
     */
    @PutMapping("/updateCustomizeFeeByStoreId/{id}")
    @HasPermission("teaching_customize_fee_edit" )
    @Operation(summary = "根据门店修改自定义课时费" , description = "根据门店修改自定义课时费" )
    public R updateCustomizeFeeByStoreId(@PathVariable Long id,  @RequestBody CourseFeeDTO courseFeeDTO){
        if(Objects.isNull(id)){
            return R.failed("门店ID不能为空");
        }

        if(Objects.isNull(courseFeeDTO.getCourseId())){
            return R.failed("课程ID不能为空");
        }

        if(Objects.isNull(courseFeeDTO.getStandardPrice()) || courseFeeDTO.getStandardPrice().compareTo(BigDecimal.ZERO) < 0){
            return R.failed("标准价格不能为空并且价格不能小于0");
        }

        if(Objects.isNull(courseFeeDTO.getEffectiveDate())){
            return R.failed("生效日期不能为空");
        }

        courseFeeDTO.setStoreId(id);
        courseFeeService.updateFeeByStoreId(courseFeeDTO,true);
        return R.ok("修改成功！");
    }


    /**
     *  根据门店修改课时费
     *
     * <AUTHOR>
     * @date 2025年05月27日 14时18分
     */
    @PutMapping("/updateCourseFeeByStoreId/{id}")
    @HasPermission("teaching_course_fee_edit" )
    @Operation(summary = "根据门店修改课时费" , description = "根据门店修改课时费" )
    public R updateCourseFeeByStoreId(@PathVariable Long id,  @RequestBody CourseFeeDTO courseFeeDTO){
        if(Objects.isNull(id)){
            return R.failed("门店ID不能为空");
        }

        if(Objects.isNull(courseFeeDTO.getStandardPrice()) || courseFeeDTO.getStandardPrice().compareTo(BigDecimal.ZERO) < 0){
            return R.failed("标准价格不能为空并且价格不能小于0");
        }

        if(Objects.isNull(courseFeeDTO.getEffectiveDate())){
            return R.failed("生效日期不能为空");
        }

        courseFeeDTO.setStoreId(id);
        courseFeeService.updateFeeByStoreId(courseFeeDTO,false);
        return R.ok("修改成功！");
    }



    /**
     *  根据门店修改课时费
     *
     * <AUTHOR>
     * @date 2025年05月27日 14时18分
     */
    @PostMapping("/addCourseFeeByStore")
    @Inner
    public R addCourseFeeByStore(@RequestBody CourseFeeDTO courseFeeDTO){
        if(Objects.isNull(courseFeeDTO.getStoreId())){
            return R.failed("门店ID不能为空");
        }

        if(Objects.isNull(courseFeeDTO.getStandardPrice()) || courseFeeDTO.getStandardPrice().compareTo(BigDecimal.ZERO) < 0){
            return R.failed("标准价格不能为空并且价格不能小于0");
        }

        if(Objects.isNull(courseFeeDTO.getEffectiveDate())){
            courseFeeDTO.setEffectiveDate(LocalDate.now(ZoneId.systemDefault()));
        }

        courseFeeService.updateFeeByStoreId(courseFeeDTO,false);
        return R.ok("修改成功！");
    }


    /**
     *  根据门店查询当前执行列表
     *
     * <AUTHOR>
     * @date 2025年05月27日 14时39分
     */
    @GetMapping("/getCourseFeeListByStore/{storeId}/{courseId}")
    @HasPermission("teaching_course_fee_list_info" )
    @Operation(summary = "根据门店查询当前执行列表" , description = "根据门店查询当前执行列表" )
    public R getCourseFeeByStoreId(@PathVariable Long storeId,@PathVariable Long courseId){
        return R.ok(courseFeeService.getCourseFeeListByStore(storeId,courseId));
    }



    /**
     *  根据门店获得当前执行信息
     *
     * <AUTHOR>
     * @date 2025年05月27日 14时39分
     */
    @GetMapping("/getCurrentCourseFeeByStore/{storeId}")
    @Operation(summary = "根据门店获得当前执行信息" , description = "根据门店获得当前执行信息" )
   // @HasPermission("teaching_course_fee_list_info" )
    public R getCurrentCourseFeeByStore(@PathVariable Long storeId){
        return R.ok(courseFeeService.getCurrentCourseFeeByStore(storeId));
    }
}
