package com.yuedu.teaching.controller.web;

import com.yuedu.teaching.entity.CoursewareStepPub;
import com.yuedu.teaching.query.CoursewareStepQuery;
import com.yuedu.teaching.service.CoursewareStepPubService;
import com.yuedu.teaching.service.CoursewareStepService;
import com.yuedu.teaching.valid.CoursewareStepValidGroup;
import com.yuedu.teaching.vo.ClientStepDetailsVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("web/coursewareStep")
@Tag(description = "webCoursewareStep", name = "指导师端教学环节")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class WebCoursewareStepController {

    private final CoursewareStepPubService coursewareStepPubService;

    /**
     * 教学环节页预览
     *
     * @param coursewareStepQuery 教学环节表
     * @return List  对象列表
     */
    @Operation(summary = "教学环节页预览", description = "教学环节页预览")
    @GetMapping("/view")
    public R<List<ClientStepDetailsVO>> viewCoursewareStep(@Validated({ CoursewareStepValidGroup.ListCoursewareStep.class })
            @ParameterObject CoursewareStepQuery coursewareStepQuery) {
        return R.ok(coursewareStepPubService.viewCoursewareStep(coursewareStepQuery));
    }
}
