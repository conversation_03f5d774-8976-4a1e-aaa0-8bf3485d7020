package com.yuedu.teaching.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.entity.StageEntity;
import com.yuedu.teaching.service.StageService;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;


/**
 * 阶段/书单
 *
 * <AUTHOR>
 * @date 2024-10-24 10:45:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/stage")
@Tag(description = "stage", name = "阶段/书单管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class StageController {

    private final StageService stageService;

    /**
     * 分页查询
     *
     * @param page      分页对象
     * @param productId 类型id
     * @param stageName 阶段/书单
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("teaching_stage_view")
    public R getStagePage(@ParameterObject Page page, @RequestParam(name = "product_id", required = false, defaultValue = "1") Integer productId, @RequestParam(required = false) String stageName) {
        return R.ok(stageService.getPage(page, productId, stageName));
    }


    /**
     * 查询全部书单
     *
     * @return 全部书单列表
     */
    @Operation(summary = "查询全部书单", description = "查询全部书单")
    @GetMapping("/list")
    public R list(@RequestParam(name = "product_id", required = false, defaultValue = "1") Integer productId) {
        return R.ok(stageService.listBooks(productId));
    }

    /**
     * 小程序:查询书单/阶段信息
     * @param productId 产品id
     * @return 书单/阶段列表
     */
    @Operation(summary = "小程序:查询书单/阶段信息",description = "小程序:查询书单/阶段信息")
    @GetMapping("/info")
    @StorePermission
    public R<List<StageVO>> stageInfo(@RequestParam(name = "product_id", required = false, defaultValue = "1") Integer productId){
        return R.ok(stageService.listBooks(productId));
    }

    /**
     * 查询双师阶段
     * @param productId
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.teaching.entity.StageEntity>>
     * <AUTHOR>
     * @date 2024/12/12 15:40
     */
    @GetMapping("/getStageList")
    @Inner
    public R<List<StageEntity>> getStageList(@Parameter(description = "类型")
                                                 @NotNull(message = "类型不能为空") Integer productId){

        List<StageEntity> stageEntityList = stageService.list(Wrappers.lambdaQuery(StageEntity.class)
                .eq(StageEntity::getProductId, productId)
        );
        return R.ok(stageEntityList);
    }


}