package com.yuedu.teaching.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.yuedu.teaching.entity.LessonPubEntity;
import com.yuedu.teaching.query.LessonPubQuery;
import com.yuedu.teaching.service.LessonPubService;
import com.yuedu.teaching.vo.LessonPubVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @ClassName LessonPubController
 * @Description 课程章节发布表管理
 * <AUTHOR>
 * @Date 2024/12/05 9:48
 * @Version v0.0.1
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/lessonPub")
@Tag(description = "lessonPub", name = "课程章节发布表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LessonPubController {

    @Resource
    private LessonPubService lessonPubService;

    /**
     * 根据课节名称获取已发布的课节列表
     *
     * @param lessonPubQuery 课节Id列表
     * @return 结果
     */
    @Operation(summary = "根据课节id列表获取已发布的课节信息")
    @PostMapping("/getLessonByIds")
    @Inner
    public R<List<LessonPubVO>> getLessonByIds(@RequestBody LessonPubQuery lessonPubQuery) {
        log.info("根据课节id列表获取已发布的课节信息的入参:{}", JSON.toJSONString(lessonPubQuery));
        List<LessonPubEntity> lessonPubEntityList = lessonPubService.lessonPubQueryList(lessonPubQuery);
        log.info("已发布的课节信息:{}", JSON.toJSONString(lessonPubEntityList));
        List<LessonPubVO> lessonPubVoList = new ArrayList<>();
        if (!lessonPubEntityList.isEmpty()) {
            lessonPubEntityList.forEach(lessonPubEntity -> {
                LessonPubVO lessonPubVO = new LessonPubVO();
                BeanUtil.copyProperties(lessonPubEntity, lessonPubVO);
                lessonPubVoList.add(lessonPubVO);
            });
        }
        return R.ok(lessonPubVoList);
    }

    /**
     * 根据课程ID查询课程所有课节信息
     */
    @Operation(summary = "根据课程ID查询课程所有课节信息")
    @PostMapping("/getLessonByCourseIdList")
    @Inner
    public R<Map<Long, List<LessonPubVO>>> getLessonByCourseIdList(@RequestBody List<Long> courseIdList) {
        log.info("根据课程ID查询课程所有课节信息的入参:{}", JSON.toJSONString(courseIdList));
        Map<Long, List<LessonPubEntity>> lessonPubQueryMap = lessonPubService.lessonPubQueryMap(courseIdList);
        Map<Long, List<LessonPubVO>> lessonPubVoMap = lessonPubQueryMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                    List<LessonPubVO> lessonPubVoList = new ArrayList<>();
                    entry.getValue().forEach(lessonPubEntity -> {
                        LessonPubVO lessonPubVO = new LessonPubVO();
                        BeanUtil.copyProperties(lessonPubEntity, lessonPubVO);
                        lessonPubVoList.add(lessonPubVO);
                    });
                    return lessonPubVoList;
                }));

        return R.ok(lessonPubVoMap);
    }

}