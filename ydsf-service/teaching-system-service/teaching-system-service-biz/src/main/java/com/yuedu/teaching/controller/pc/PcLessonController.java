package com.yuedu.teaching.controller.pc;

import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.service.LessonService;
import com.yuedu.teaching.vo.LessonPracticeVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.PcPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName PcLessonController
 * @Description PC课节控制类
 * <AUTHOR>
 * @Date 2025/1/6 09:32
 * @Version v0.0.1
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/pcLesson")
@Tag(description = "pcLesson", name = "PC端课节表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Inner(value = false)
public class PcLessonController {

    private final LessonService lessonService;

    /**
     * 根据课程id获取已发布练课课节列表
     *
     * @param coursePublishQuery 课程id，课程版本
     * @return R<List<LessonPracticeVO>>
     */
    @Operation(summary = "根据课程id获取已发布练课课节列表", description = "根据课程id获取已发布练课课节列表")
    @GetMapping("/lessonPracticeList")
    @PcPermission
    public R<List<LessonPracticeVO>> getLessonPracticeList(@ParameterObject CoursePublishQuery coursePublishQuery) {
        return R.ok(lessonService.getLessonPracticeList(coursePublishQuery.getCourseId(),coursePublishQuery.getVersion()));
    }
}
