package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.CoursewareStepByTemplateDTO;
import com.yuedu.teaching.dto.CoursewareStepDTO;
import com.yuedu.teaching.dto.CoursewareStepOrderDTO;
import com.yuedu.teaching.query.CoursewareStepQuery;
import com.yuedu.teaching.service.CoursewareStepService;
import com.yuedu.teaching.valid.CoursewareStepValidGroup;
import com.yuedu.teaching.vo.*;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教学环节表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-05 17:30:36
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/coursewareStep")
@Tag(description = "courseware_step", name = "教学环节表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CoursewareStepController {

    private final CoursewareStepService coursewareStepService;

    /**
     * 查询所有教学环节
     *
     * @param coursewareStepQuery 教学环节表
     * @return List  对象列表
     */
    @Operation(summary = "查询所有教学环节", description = "查询所有教学环节")
    @GetMapping("/list")
    @HasPermission("teaching_coursewareStep_view")
    public R<List<StepVO>> listCoursewareStep(
            @Validated({ CoursewareStepValidGroup.ListCoursewareStep.class })
            @ParameterObject CoursewareStepQuery coursewareStepQuery) {
        return R.ok(coursewareStepService.listCoursewareStep(coursewareStepQuery));
    }

    /**
     * 新增教学环节
     *
     * @param coursewareStepAddDTO 教学环节新增DTO
     * @return R
     */
    @Operation(summary = "新增教学环节", description = "新增教学环节")
    @SysLog("新增教学环节表")
    @PostMapping("/add")
    @HasPermission("teaching_coursewareStep_add")
    public R<CoursewareStepVO> save(@Validated({ ValidGroup.Insert.class }) @RequestBody CoursewareStepDTO coursewareStepAddDTO) {
        return R.ok(coursewareStepService.addCoursewareStep(coursewareStepAddDTO));
    }


    /**
     * 根据模板新增
     *
     * @param coursewareStepByTemplate 教学环节模版新增DTO列表
     * @return R
     */
    @Operation(summary = "根据模板新增", description = "根据模板新增")
    @SysLog("根据模板新增")
    @PostMapping("/addByTemplate")
//    @HasPermission("teaching_coursewareStep_add")
    public R<Boolean> save(@Valid @RequestBody CoursewareStepByTemplateDTO coursewareStepByTemplate) {
        return R.ok(coursewareStepService.copyCoursewareStep(coursewareStepByTemplate));
    }


    /**
     * 修改教学环节
     *
     * @param coursewareStepDTO 教学环节更新DTO
     * @return R
     */
    @Operation(summary = "修改教学环节", description = "修改教学环节")
    @SysLog("修改教学环节")
    @PutMapping("/edit")
    @HasPermission("teaching_coursewareStep_edit")
    public R<CoursewareStepVO> updateById(@Validated({ ValidGroup.Update.class }) @RequestBody CoursewareStepDTO coursewareStepDTO) {
        return R.ok(coursewareStepService.updateCoursewareStep(coursewareStepDTO));
    }


    /**
     * 调整教学环节顺序
     *
     * @param coursewareStepOrderDTO 教学环节更新DTOList
     * @return R
     */
    @Operation(summary = "调整教学环节顺序", description = "调整教学环节顺序")
    @SysLog("调整教学环节顺序")
    @PutMapping("/editList")
    @HasPermission("teaching_coursewareStep_edit")
    public R updateById(@Valid @RequestBody CoursewareStepOrderDTO coursewareStepOrderDTO) {
        coursewareStepService.updateBatchByList(coursewareStepOrderDTO);
        return R.ok();
    }

    /**
     * 通过id删除教学环节
     *
     * @param coursewareStepRemoveDTO id列表
     * @return R
     */
    @Operation(summary = "通过id删除教学环节", description = "通过id删除教学环节")
    @SysLog("通过id删除教学环节")
    @DeleteMapping("/delete")
    @HasPermission("teaching_coursewareStep_del")
    public R<Boolean> removeById(@Validated({ CoursewareStepValidGroup.DeleteStep.class }) @RequestBody CoursewareStepDTO coursewareStepRemoveDTO) {
        return R.ok(coursewareStepService.deleteCoursewareStep(coursewareStepRemoveDTO));
    }

    /**
     * 教学环节页预览
     *
     * @param coursewareStepQuery 教学环节表
     * @return List  对象列表
     */
    @Operation(summary = "教学环节页预览", description = "教学环节页预览")
    @GetMapping("/view")
    public R<List<ClientStepDetailsVO>> viewCoursewareStep(@Validated({ CoursewareStepValidGroup.ListCoursewareStep.class })
            @ParameterObject CoursewareStepQuery coursewareStepQuery) {
        return R.ok(coursewareStepService.viewCoursewareStep(coursewareStepQuery));
    }
}
