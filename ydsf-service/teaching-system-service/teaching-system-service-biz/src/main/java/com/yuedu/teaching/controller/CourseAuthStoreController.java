package com.yuedu.teaching.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.teaching.service.CourseAuthStoreService;
import com.yuedu.teaching.query.CourseAuthStoreQuery;
import com.yuedu.teaching.dto.CourseAuthStoreDTO;
import com.yuedu.teaching.vo.CourseAuthStoreVO;
import com.yuedu.ydsf.common.security.annotation.Inner;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
* 课程授权门店表控制层
*
* <AUTHOR>
* @date  2025/05/21
*/

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/courseAuthStore")
@Tag(description = "course_auth_store" , name = "课程授权门店表" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseAuthStoreController  {


    private final CourseAuthStoreService ourseAuthStoreService;


    /**
    * 课程授权门店表分页查询
    * @param page 分页对象
    * @param courseAuthStoreQuery 课程授权门店表
    * @return R
    */
    @GetMapping("/page" )
    @HasPermission("courseAuthStore_view")
    @Operation(summary = "分页查询" , description = "课程授权门店表分页查询" )
    public R page(@ParameterObject Page page, @ParameterObject CourseAuthStoreQuery courseAuthStoreQuery) {
        return R.ok(ourseAuthStoreService.page(page, courseAuthStoreQuery));
    }


    /**
    * 通过id查询课程授权门店表
    * @param id id
    * @return R
    */
    @Operation(summary = "通过id查询" , description = "通过id查询课程授权门店表" )
    @GetMapping("/{id}" )
    @HasPermission("courseAuthStore_view")
    public R getById(@PathVariable Serializable id) {
        return R.ok(ourseAuthStoreService.getById(id));
    }



    /**
    * 新增课程授权门店表
    * @param courseAuthStoreDTO 课程授权门店表
    * @return R
    */
    @PostMapping
    @SysLog ("新增课程授权门店表" )
    @HasPermission("courseAuthStore_view" )
    @Operation(summary = "新增课程授权门店表" , description = "新增课程授权门店表" )
    public R add(@Validated(V_A.class) @RequestBody CourseAuthStoreDTO courseAuthStoreDTO) {
         return R.ok(ourseAuthStoreService.add(courseAuthStoreDTO));
    }


    /**
    * 修改课程授权门店表
    * @param courseAuthStoreDTO 课程授权门店表
    * @return R
    */
    @PutMapping
    @SysLog("修改课程授权门店表" )
    @HasPermission("courseAuthStore_edit" )
    @Operation(summary = "修改课程授权门店表" , description = "修改课程授权门店表" )
    public R edit(@Validated(V_E.class) @RequestBody CourseAuthStoreDTO courseAuthStoreDTO) {
         return R.ok(ourseAuthStoreService.edit(courseAuthStoreDTO));
    }



    /**
    * 通过id删除课程授权门店表
    * @param ids id列表
    * @return R
    */
    @DeleteMapping
    @SysLog("通过id删除课程授权门店表" )
    @HasPermission("courseAuthStore_del" )
    @Operation(summary = "删除课程授权门店表" , description = "删除课程授权门店表" )
    public R delete(@RequestBody  Long[] ids){
         return R.ok(ourseAuthStoreService.removeBatchByIds(CollUtil.toList(ids)));
    }



   /**
   * 导出excel 课程授权门店表表格
   * @param  courseAuthStoreQuery 查询条件
   * @param ids 导出指定ID
   * @return excel 文件流
   */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("courseAuthStore_export" )
    @Operation(summary = "导出课程授权门店表表格" , description = "导出课程授权门店表表格" )
    public List<CourseAuthStoreVO> export(CourseAuthStoreQuery courseAuthStoreQuery, Long[] ids) {
        return ourseAuthStoreService.export(courseAuthStoreQuery, ids);
    }

    /**
     * 根据课程类型ID和门店ID查询该门店下所有授权的课程ID列表（包含历史记录）
     *
     * @param courseTypeId 课程类型ID（可选）
     * @param storeId 门店ID
     * @return 课程ID列表
     */
    @GetMapping("/inner/listAuthCourseIds")
    @Inner
    @Operation(summary = "查询授权课程ID列表", description = "根据课程类型ID和门店ID查询该门店下所有授权的课程ID列表")
    public R<List<Long>> listAuthCourseIds(@RequestParam(value = "courseTypeId", required = false) Long courseTypeId, @RequestParam("storeId") Long storeId) {
        return R.ok(ourseAuthStoreService.listAuthCourseIds(courseTypeId, storeId));
    }

    /**
     * 根据课程类型ID和门店ID查询该门店下当前生效的授权课程ID列表
     *
     * @param courseTypeId 课程类型ID（可选）
     * @param storeId 门店ID
     * @return 课程ID列表
     */
    @GetMapping("/inner/listCurrentAuthCourseIds")
    @Inner
    @Operation(summary = "查询当前生效的授权课程ID列表", description = "根据课程类型ID和门店ID查询该门店下当前生效的授权课程ID列表")
    public R<List<Long>> listCurrentAuthCourseIds(@RequestParam(value = "courseTypeId", required = false) Long courseTypeId, @RequestParam("storeId") Long storeId) {
        return R.ok(ourseAuthStoreService.listCurrentAuthCourseIds(courseTypeId, storeId));
    }

}
