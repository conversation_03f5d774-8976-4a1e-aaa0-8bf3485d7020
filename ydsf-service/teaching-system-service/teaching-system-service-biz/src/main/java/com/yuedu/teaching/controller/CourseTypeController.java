package com.yuedu.teaching.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.constant.enums.CourseTypeStatusEnum;
import com.yuedu.teaching.dto.CourseTypeDTO;
import com.yuedu.teaching.entity.CourseType;
import com.yuedu.teaching.query.CourseTypeQuery;
import com.yuedu.teaching.service.CourseTypeService;
import com.yuedu.teaching.vo.CourseTypeVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 课程类型 控制类
 *
 * <AUTHOR>
 * @date 2025-05-21 08:57:32
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/courseType")
@Tag(description = "course_type", name = "课程类型管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseTypeController {

    private final CourseTypeService courseTypeService;

    /**
     * 分页查询
     *
     * @param page       分页对象
     * @param courseTypeQuery 课程类型
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("teaching_courseType_view")
    public R getCourseTypePage(@ParameterObject Page page, @ParameterObject CourseTypeQuery courseTypeQuery) {
        return R.ok(courseTypeService.getCourseTypePage(page, courseTypeQuery));
    }


    /**
     * 通过条件查询课程类型
     *
     * @param courseType 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询", description = "通过条件查询对象")
    @GetMapping("/details")
    @HasPermission("teaching_courseType_view")
    public R getDetails(@ParameterObject CourseType courseType) {
        return R.ok(courseTypeService.list(Wrappers.query(courseType)));
    }

    /**
     * 新增课程类型
     *
     * @param courseType 课程类型
     * @return R
     */
    @Operation(summary = "新增课程类型", description = "新增课程类型")
    @SysLog("新增课程类型")
    @PostMapping("/add")
    @HasPermission("teaching_courseType_add")
    @Idempotent(key = "'create_course_type'", expireTime = 3)
    @CacheEvict(value = "courseTypeList", allEntries = true)
    public R save(@RequestBody CourseType courseType) {
        log.info("新增课程类型,参数:{}", courseType);
        if (Objects.isNull(courseType) ||
                StringUtils.isEmpty(courseType.getName())) {
            return R.failed("课程类型名称不能为空");
        }
        String pinyin = PinyinUtil.getPinyin(courseType.getName(), CharSequenceUtil.EMPTY);
        log.info("课程类型名称拼音:{}", pinyin);
        courseType.setCode(pinyin);
        long count = courseTypeService.count(Wrappers.<CourseType>lambdaQuery().eq(CourseType::getName, courseType.getName())
                .or().eq(CourseType::getCode, courseType.getCode()));
        if (count > 0) {
            return R.failed("课程类型名称已存在");
        }
        return R.ok(courseTypeService.save(courseType));
    }

    /**
     * 修改课程类型
     *
     * @param courseType 课程类型
     * @return R
     */
    @Operation(summary = "修改课程类型", description = "修改课程类型")
    @SysLog("修改课程类型")
    @PutMapping("/edit")
    @HasPermission("teaching_courseType_edit")
    @Idempotent(key = "'create_course_type'", expireTime = 3)
    @CacheEvict(value = "courseTypeList", allEntries = true)
    public R updateById(@RequestBody CourseType courseType) {
        return R.ok(courseTypeService.updateById(courseType));
    }

    /**
     * 通过id删除课程类型
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除课程类型", description = "通过id删除课程类型")
    @SysLog("通过id删除课程类型")
    @DeleteMapping("/delete")
    @HasPermission("teaching_courseType_del")
    @CacheEvict(value = "courseTypeList", allEntries = true)
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(courseTypeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @param courseType 查询条件
     * @param ids        导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("teaching_courseType_export")
    public List<CourseType> exportExcel(CourseType courseType, Integer[] ids) {
        return courseTypeService.list(Wrappers.lambdaQuery(courseType).in(ArrayUtil.isNotEmpty(ids), CourseType::getId, ids));
    }

    /**
     * 导入excel 表
     *
     * @param courseTypeList 对象实体列表
     * @param bindingResult  错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("teaching_courseType_export")
    public R importExcel(@RequestExcel List<CourseType> courseTypeList, BindingResult bindingResult) {
        return R.ok(courseTypeService.saveBatch(courseTypeList));
    }

    /**
     * 查询所有可用课程类型
     */
    @Operation(summary = "查询所有可用课程类型", description = "查询所有可用课程类型")
    @GetMapping("/allOK")
    @Cacheable(value = "courseTypeList", key = "'allOK'")
    public R<List<CourseTypeDTO>> getAllOK() {
        List<CourseTypeDTO> courseTypeDTOList = courseTypeService.list(Wrappers.<CourseType>lambdaQuery()
                        .eq(CourseType::getStatus, CourseTypeStatusEnum.ENABLE.getCode()))
                .stream()
                .map(courseType -> BeanUtil.copyProperties(courseType, CourseTypeDTO.class))
                .toList();
        return R.ok(courseTypeDTOList);
    }

    /**
     * 查询所有课程类型
     */
    @Operation(summary = "查询所有课程类型", description = "查询所有课程类型")
    @GetMapping("/all")
    @Cacheable(value = "courseTypeList", key = "'all'")
    public R<List<CourseTypeDTO>> getAllCourseType() {
        List<CourseTypeDTO> courseTypeDTOList = courseTypeService.list()
                .stream()
                .map(courseType -> BeanUtil.copyProperties(courseType, CourseTypeDTO.class))
                .toList();
        return R.ok(courseTypeDTOList);
    }

    /**
     * 查询所有可用课程类型
     */
    @Operation(summary = "查询所有可用课程类型", description = "查询所有可用课程类型")
    @GetMapping("/inner/allOK")
    @Inner
    @Cacheable(value = "courseTypeList", key = "'allOK'")
    public R<List<CourseTypeDTO>> getInnerAllOK() {
        List<CourseTypeDTO> courseTypeDTOList = courseTypeService.list(Wrappers.<CourseType>lambdaQuery()
                        .eq(CourseType::getStatus, CourseTypeStatusEnum.ENABLE))
                .stream()
                .map(courseType -> BeanUtil.copyProperties(courseType, CourseTypeDTO.class))
                .toList();
        return R.ok(courseTypeDTOList);
    }

    /**
     * 查询所有课程类型
     */
    @Operation(summary = "查询所有课程类型", description = "查询所有课程类型")
    @GetMapping("/inner/all")
    @Inner
    @Cacheable(value = "courseTypeList", key = "'all'")
    public R<List<CourseTypeDTO>> getInnerAllCourseType() {
        List<CourseTypeDTO> courseTypeDTOList = courseTypeService.list()
                .stream()
                .map(courseType -> BeanUtil.copyProperties(courseType, CourseTypeDTO.class))
                .toList();
        return R.ok(courseTypeDTOList);
    }

    /**
     * 根据门店ID查询该门店授权过的课程类型列表
     *
     * @param storeId 门店ID
     * @return 课程类型DTO列表
     */
    @Operation(summary = "查询门店授权的课程类型", description = "根据门店ID查询该门店授权过的课程类型列表")
    @GetMapping("/courseTypeByStore/{storeId}")
    @StorePermission
    public R<List<CourseTypeVO>> getCourseTypesByStoreAuth(@PathVariable Long storeId) {
        log.info("查询门店授权的课程类型, storeId: {}", storeId);

        if (Objects.isNull(storeId) || storeId <= 0) {
            log.warn("门店ID参数无效: {}", storeId);
            return R.failed("门店ID参数无效");
        }

        List<CourseTypeVO> courseTypes = courseTypeService.getCourseTypesByStoreAuth(storeId);
        return R.ok(courseTypes);
    }

}
