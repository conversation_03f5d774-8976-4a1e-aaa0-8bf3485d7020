package com.yuedu.teaching.controller;

import com.aliyun.oss.common.utils.HttpHeaders;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.teaching.dto.CoursewareDTO;
import com.yuedu.teaching.dto.CoursewareInfoDTO;
import com.yuedu.teaching.dto.CoursewareVersionDTO;
import com.yuedu.teaching.entity.Courseware;
import com.yuedu.teaching.query.CoursewareQuery;
import com.yuedu.teaching.service.CoursewareService;
import com.yuedu.teaching.valid.CoursewareValidGroup;
import com.yuedu.teaching.vo.CoursewareInfoVO;
import com.yuedu.teaching.vo.CoursewareVO;
import com.yuedu.teaching.vo.CoursewareVersionVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 课件表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-05 15:05:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/courseware")
@Tag(description = "courseware", name = "课件表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CoursewareController {

    private final CoursewareService coursewareService;

    /**
     * 课件列表查询
     *
     * @param bookId 书籍id
     * @return R<List < Courseware>> 课件列表
     */
    @Operation(summary = "课件列表查询", description = "课件列表查询")
    @GetMapping("/page/{bookId}")
    @HasPermission("teaching_courseware_view")
    public R<List<CoursewareVO>> getCoursewarePage(@PathVariable Integer bookId) {
        return R.ok(coursewareService.getCoursewareListByBookId(bookId));
    }

    /**
     * 根据课件id查询课件详情
     *
     * @param coursewareQuery 课件id
     * @return 课件详情
     */
    @Operation(summary = "根据课件id查询课件详情", description = "根据课件id查询课件详情")
    @GetMapping("/detail")
    @HasPermission("teaching_courseware_view")
    public R<Courseware> getCoursewareById(@Valid @ParameterObject CoursewareQuery coursewareQuery) {
        return R.ok(coursewareService.getOne(Wrappers.lambdaQuery(Courseware.class)
                .eq(Courseware::getId, coursewareQuery.getId())));
    }

    /**
     * 新增课件表
     *
     * @param coursewareDTO 课件表
     * @return R
     */
    @Operation(summary = "新增课件表", description = "新增课件表")
    @SysLog("新增课件表")
    @PostMapping("/add")
    @HasPermission("teaching_courseware_add")
    public R<Boolean> save(@Validated(CoursewareValidGroup.AddCoursewareGroup.class) @RequestBody CoursewareDTO coursewareDTO) {
        return R.ok(coursewareService.addCoursewareSeries(coursewareDTO));
    }

    /**
     * 根据书籍id和课件名称模糊查询课件
     *
     * @param coursewareDTO 课件对象
     * @return R<List < CoursewareVO>> 课件列表
     */
    @Operation(summary = "根据书籍id和课件名称模糊查询课件", description = "根据书籍id和课件名称模糊查询课件")
    @PostMapping("/listByName")
    public R<List<CoursewareVO>> getCoursewareListByName(@Validated(CoursewareValidGroup.ListByNameGroup.class) @RequestBody CoursewareDTO coursewareDTO) {
        return R.ok(coursewareService.getCoursewareList(coursewareDTO.getBookId(), coursewareDTO.getCoursewareName()));
    }

    /**
     * 根据书籍id修改课件名称
     *
     * @param coursewareDTO 课件对象
     * @return R<Boolean>
     */
    @Operation(summary = "修改课件名称", description = "修改课件名称")
    @SysLog("修改课件名称")
    @PutMapping("/edit")
    @HasPermission("teaching_courseware_edit")
    public R<Boolean> updateById(@Validated(CoursewareValidGroup.UpdateCoursewareGroup.class) @RequestBody CoursewareDTO coursewareDTO) {
        return R.ok(coursewareService.updateCourseware(coursewareDTO.getId(), coursewareDTO.getCoursewareName()));
    }

    /**
     * 根据版本id查询课件版本列表
     *
     * @param coursewareVersionDTO 版本id列表
     * @return R<List < CoursewareVersionVO>>
     */
    @Operation(summary = "根据版本id查询课件版本列表", description = "根据版本id查询课件版本列表")
    @PostMapping("/versionList")
    @Inner
    public R<List<CoursewareVersionVO>> getCoursewareVersionList(@RequestBody CoursewareVersionDTO coursewareVersionDTO) {
        return R.ok(coursewareService.getCoursewareVersionList(coursewareVersionDTO.getVersionList(),coursewareVersionDTO.getCoursewareIdList()));
    }

    /**
     * 根据课件id查询课件信息
     *
     * @param coursewareInfoDTO 课件id列表
     * @return R<List < CoursewareInfoVO>>
     */
    @Operation(summary = "根据课件id查询课件信息", description = "根据课件id查询课件信息")
    @PostMapping("/infoList")
    @Inner
    public R<List<CoursewareInfoVO>> getCoursewareInfoList(@RequestBody CoursewareInfoDTO coursewareInfoDTO) {
        return R.ok(coursewareService.getCoursewareInfoList(coursewareInfoDTO.getVersionList()));
    }

    /**
     * 根据id查询课件
     *
     * @param id id
     * @return Courseware
     */
    @Operation(summary = "根据id查询课件", description = "根据id查询课件")
    @GetMapping("/getById")
    @Inner
    public R<CoursewareVO> getById(@RequestParam Long id) {
        CoursewareVO coursewareVO = new CoursewareVO();
        Courseware courseware = coursewareService.getById(id);
        BeanUtils.copyProperties(courseware, coursewareVO);
        return R.ok(coursewareVO);
    }
}
