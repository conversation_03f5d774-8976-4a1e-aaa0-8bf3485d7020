package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.CoursewareStepTemplateAddDTO;
import com.yuedu.teaching.service.CoursewareStepTemplateService;
import com.yuedu.teaching.vo.CoursewareStepTemplateVO;
import com.yuedu.teaching.vo.StepVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教学环节模版表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-05 17:32:03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/coursewareStepTemplate")
@Tag(description = "courseware_step_template", name = "教学环节模版表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CoursewareStepTemplateController {

    private final CoursewareStepTemplateService coursewareStepTemplateService;

    /**
     * 查询所有教学环节模版
     *
     * @return 对象列表
     */
    @Operation(summary = "查询所有教学环节模版", description = "查询所有教学环节模版")
    @GetMapping("/list")
    public R<List<CoursewareStepTemplateVO>> getCoursewareStepTemplatePage() {
        return R.ok(coursewareStepTemplateService.listCoursewareStepTemplate());
    }


    /**
     * 通过id查询教学环节模版
     *
     * @param templateId 模版ID
     * @return List<StepVO>
     */
    @Operation(summary = "通过id查询", description = "通过查询对象")
    @GetMapping("/details")
    public R<List<StepVO>> getDetails(@RequestParam(value = "id", defaultValue = "0") Integer templateId) {
        return R.ok(coursewareStepTemplateService.getCoursewareStepByIdTemplate(templateId));
    }

    /**
     * 新增教学环节模版
     *
     * @param coursewareStepTemplateAddDTO 教学环节模版新增DTO
     * @return boolean
     */
    @Operation(summary = "新增教学环节模版", description = "新增教学环节模版")
    @SysLog("新增教学环节模版")
    @PostMapping("/add")
    @HasPermission("teaching_coursewareStepTemplate_add")
    public R<Boolean> save(@Valid @RequestBody CoursewareStepTemplateAddDTO coursewareStepTemplateAddDTO) {
        return R.ok(coursewareStepTemplateService.addCoursewareStepTemplate(coursewareStepTemplateAddDTO));
    }
}
