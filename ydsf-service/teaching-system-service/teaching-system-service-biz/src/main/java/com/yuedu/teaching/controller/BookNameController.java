package com.yuedu.teaching.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.dto.BookNameDTO;
import com.yuedu.teaching.dto.BookNameQueryDTO;
import com.yuedu.teaching.entity.BookName;
import com.yuedu.teaching.service.BookNameService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 书名表 控制类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:57:33
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/bookName")
@Tag(description = "book_name", name = "书名表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BookNameController {

    private final BookNameService bookNameService;

    /**
     * 分页查询
     *
     * @param page             分页对象
     * @param bookNameQueryDTO 书名表请求实体类
     * @return R  分页对象
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("teaching_bookName_view")
    public R<Page<BookName>> getBookNamePage(@ParameterObject Page<BookName> page, @ParameterObject BookNameQueryDTO bookNameQueryDTO) {
        return R.ok(bookNameService.pageQuery(page, bookNameQueryDTO));
    }


    /**
     * 通过条件查询书名表
     *
     * @param bookNameDTO 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询", description = "通过条件查询对象")
    @GetMapping("/details")
    public R<List<BookName>> getDetails(@Validated({ValidGroup.Update.class}) @ParameterObject BookNameQueryDTO bookNameDTO) {
        return R.ok(bookNameService.queryByName(bookNameDTO));
    }

    /**
     * 新增书名表
     *
     * @param bookNameDTO 书名表
     * @return R
     */
    @Operation(summary = "新增书名表", description = "新增书名表")
    @SysLog("新增书名表")
    @PostMapping("/add")
    @HasPermission("teaching_bookName_add")
    public R<BookName> save(@Validated({ValidGroup.Insert.class}) @RequestBody BookNameDTO bookNameDTO) {
        return R.ok(bookNameService.insert(bookNameDTO));
    }

    /**
     * 修改书名表
     *
     * @param bookName 书名表
     * @return R
     */
    @Operation(summary = "修改书名表", description = "修改书名表")
    @SysLog("修改书名表")
    @PutMapping("/edit")
    @HasPermission("teaching_bookName_edit")
    public R<Boolean> updateById(@Validated({ValidGroup.Update.class}) @RequestBody BookName bookName) {
        return R.ok(bookNameService.updateBook(bookName));
    }


    /**
     * 通过id删除书名
     *
     * @param bookNameDTO 书名请求实体类
     * @return R
     */
    @Operation(summary = "通过id删除书名", description = "通过id删除书名")
    @SysLog("通过id删除书名")
    @DeleteMapping("/delete")
    @HasPermission("teaching_bookName_del")
    public R<Boolean> removeById(@Validated({ValidGroup.Update.class}) @RequestBody BookNameDTO bookNameDTO) {
        return R.ok(bookNameService.removeBook(bookNameDTO));
    }


}
