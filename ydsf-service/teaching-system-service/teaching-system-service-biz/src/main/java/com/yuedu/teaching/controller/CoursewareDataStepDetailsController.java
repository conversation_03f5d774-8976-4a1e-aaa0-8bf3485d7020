package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.CoursewareDataStepDetailsDTO;
import com.yuedu.teaching.query.CoursewareDataStepDetailsQuery;
import com.yuedu.teaching.service.CoursewareDataStepDetailsPubService;
import com.yuedu.teaching.service.CoursewareDataStepDetailsService;
import com.yuedu.teaching.valid.DataStepDetailsValidGroup;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.xss.core.XssCleanIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName CoursewareDataStepDetailsController
 * @Description 资料数据和教学环节详情表管理
 * <AUTHOR>
 * @Date 2024/10/29 9:48
 * @Version v0.0.1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/coursewareDataStepDetails")
@Tag(description = "coursewareDataStepDetails", name = "资料数据和教学环节详情表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CoursewareDataStepDetailsController {

    private final CoursewareDataStepDetailsService courseWareDataStepDetailsService;

    private final CoursewareDataStepDetailsPubService coursewareDataStepDetailsPubService;


    /**
     * 查询资料数据和教学环节详情表
     *
     * @param coursewareDataStepDetailsQuery id
     * @return R
     */
    @Operation(summary = "查询资料数据和教学环节详情", description = "查询资料数据和教学环节详情")
    @GetMapping
    public R<CoursewareDataStepDetailsVO> details(
            @Validated(DataStepDetailsValidGroup.DataStepDetails.class)
            @ParameterObject CoursewareDataStepDetailsQuery coursewareDataStepDetailsQuery) {
        return R.ok(courseWareDataStepDetailsService.getDetails(coursewareDataStepDetailsQuery));
    }


    /**
     * 修改资料数据和教学环节详情表
     *
     * @param coursewareDataStepDetails 资料数据和教学环节详情表
     * @return R
     */
    @Operation(summary = "修改资料数据和教学环节详情表", description = "修改资料数据和教学环节详情表")
    @SysLog("修改资料数据和教学环节详情/置为可发布")
    @PutMapping
    @HasPermission("teaching_coursewareDataStepDetails_edit")
    @XssCleanIgnore
    public R<Boolean> updateById(@RequestBody @Valid CoursewareDataStepDetailsDTO coursewareDataStepDetails) {
        return R.ok(courseWareDataStepDetailsService.updateDetails(coursewareDataStepDetails));
    }

    /**
     * 获取互动页选项参数
     * @param coursewareDataStepDetailsQuery coursewareId stepId
     * @return R
     */
    @Operation(summary = "获取互动页选项参数", description = "获取互动页选项参数")
    @PostMapping("/details")
    @Inner
    public R<CoursewareDataStepDetailsVO> getDetails(@Validated(DataStepDetailsValidGroup.InnerGetDataStepDetails.class) @RequestBody CoursewareDataStepDetailsQuery coursewareDataStepDetailsQuery) {
        return R.ok(coursewareDataStepDetailsPubService.queryDetails(coursewareDataStepDetailsQuery));
    }

}