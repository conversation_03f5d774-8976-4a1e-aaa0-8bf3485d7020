package com.yuedu.teaching.controller.inner;

import com.yuedu.teaching.service.CourseTypeService;
import com.yuedu.teaching.vo.CourseTypeVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.cache.annotation.Cacheable;
import com.yuedu.ydsf.common.log.annotation.SysLog;

import java.util.List;
import java.util.Objects;

/**
 * 课程类型内部接口 控制类
 *
 * <AUTHOR>
 * @date 2025/01/17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/courseType/inner")
@Tag(description = "innerCourseType", name = "课程类型内部接口")
@Slf4j
public class InnerCourseTypeController {

    private final CourseTypeService courseTypeService;

    /**
     * 根据门店ID查询该门店授权过的课程类型列表
     *
     * @param storeId 门店ID
     * @return 课程类型VO列表
     */
    @GetMapping("/courseTypeByStore/{storeId}")
    @Operation(summary = "查询门店授权的课程类型", description = "根据门店ID查询该门店授权过的课程类型列表")
    @Inner
    @Cacheable(value = "courseTypesByStore", key = "#storeId")
    public R<List<CourseTypeVO>> getCourseTypesByStoreAuth(@PathVariable("storeId") Long storeId) {
        log.info("内部接口查询门店授权的课程类型, storeId: {}", storeId);

        if (Objects.isNull(storeId) || storeId <= 0) {
            log.warn("门店ID参数无效: {}", storeId);
            return R.failed("门店ID参数无效");
        }

        List<CourseTypeVO> courseTypes = courseTypeService.getCourseTypesByStoreAuth(storeId);
        return R.ok(courseTypes);
    }
} 