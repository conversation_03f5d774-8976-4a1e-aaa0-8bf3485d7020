package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.CoursewareDTO;
import com.yuedu.teaching.entity.Courseware;
import com.yuedu.teaching.entity.CoursewareData;
import com.yuedu.teaching.service.CoursewareDataPubService;
import com.yuedu.teaching.valid.CoursewareDataPubValidGroup;
import com.yuedu.teaching.vo.CoursewareVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 发布资料表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:41:10
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/coursewareDataPub")
@Tag(description = "courseware_data_pub", name = "发布资料表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CoursewareDataPubController {

    private final CoursewareDataPubService coursewareDataPubService;

    /**
     * 发布资料
     * @param coursewareData 请求实体类
     * @return Boolean
     */
    @Operation(summary = "发布资料", description = "发布资料")
    @SysLog("发布资料")
    @PostMapping("/publishData")
    public R<CoursewareVO> publish(@Validated({CoursewareDataPubValidGroup.PublishData.class}) @RequestBody CoursewareData coursewareData) {
        return R.ok(coursewareDataPubService.publish(coursewareData));
    }

  /**
   * 更新课件是否生成录课状态
   *
   * <AUTHOR>
   * @date 2025/3/12 13:53
   * @param courseware
   * @return com.yuedu.ydsf.common.core.util.R<java.lang.Boolean>
   */
  @Operation(summary = "更新课件是否生成录课状态", description = "更新课件是否生成录课状态")
  @SysLog("更新课件是否生成录课状态")
  @PutMapping("/updateRecordTaskStatus")
  public R updateRecordTaskStatus(@RequestBody CoursewareDTO courseware) {
    coursewareDataPubService.updateRecordTaskStatus(courseware);
    return R.ok();
  }
}
