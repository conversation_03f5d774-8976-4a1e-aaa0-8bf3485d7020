package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.FileSubmitDTO;
import com.yuedu.teaching.dto.FileUploadDTO;
import com.yuedu.teaching.dto.OssSts;
import com.yuedu.teaching.service.FileService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

/**
 * 文件 控制类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:57:33
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/file")
@Tag(description = "file", name = "文件上传")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class FileController {

    private final FileService fileService;

    /**
     * 文件上传
     *
     * @param fileUpload 文件上传参数
     * @return R
     */
    @Operation(summary = "获取文件上传凭证", description = "获取文件上传凭证")
    @GetMapping("/getUploadSign")
    public R<OssSts> update(@Valid @ParameterObject FileUploadDTO fileUpload) {

        return R.ok(fileService.sign(fileUpload));
    }

    /**
     * md5提报
     *
     * @param fileSubmit 文件上传参数
     * @return R
     */
    @Inner(value = false)
    @Operation(summary = "md5提报", description = "md5提报")
    @PostMapping("/submit")
    public R submit(@Valid @RequestBody FileSubmitDTO fileSubmit) {
        fileService.submit(fileSubmit);
        return R.ok();
    }
}
