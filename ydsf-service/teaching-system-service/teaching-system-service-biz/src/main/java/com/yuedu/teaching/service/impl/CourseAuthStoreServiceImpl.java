package com.yuedu.teaching.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.manager.CourseAuthStoreManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.mapper.CourseAuthStoreMapper;
import com.yuedu.teaching.service.CourseAuthStoreService;
import com.yuedu.teaching.query.CourseAuthStoreQuery;
import com.yuedu.teaching.dto.CourseAuthStoreDTO;
import com.yuedu.teaching.vo.CourseAuthStoreVO;
import com.yuedu.teaching.entity.CourseAuthStore;

import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.ArrayList;
import java.util.Objects;


/**
* 课程授权门店表服务层
*
* <AUTHOR>
* @date  2025/05/21
*/
@Service
public class CourseAuthStoreServiceImpl extends ServiceImpl<CourseAuthStoreMapper,CourseAuthStore>
    implements CourseAuthStoreService{


    @Autowired
    private  CourseAuthStoreManager courseAuthStoreManager;

    /**
    * 课程授权门店表分页查询
    * @param page 分页对象
    * @param courseAuthStoreQuery 课程授权门店表
    * @return IPage 分页结果
    */
    @Override
    public IPage page(Page page,CourseAuthStoreQuery courseAuthStoreQuery) {
        return page(page, Wrappers.<CourseAuthStore>lambdaQuery());
    }

    /**
    * 新增课程授权门店表
    * @param courseAuthStoreDTO 课程授权门店表
    * @return boolean 执行结果
    */
    @Override
    public boolean add(CourseAuthStoreDTO courseAuthStoreDTO) {
        CourseAuthStore courseAuthStore = new CourseAuthStore();
        BeanUtils.copyProperties(courseAuthStoreDTO, courseAuthStore);
        return save(courseAuthStore);
    }


    /**
    * 修改课程授权门店表
    * @param courseAuthStoreDTO 课程授权门店表
    * @return boolean 执行结果
    */
    @Override
    public boolean edit(CourseAuthStoreDTO courseAuthStoreDTO) {
        CourseAuthStore courseAuthStore = new CourseAuthStore();
        BeanUtils.copyProperties(courseAuthStoreDTO, courseAuthStore);
        return updateById(courseAuthStore);
    }



    /**
    * 导出excel 课程授权门店表表格
    * @param courseAuthStoreQuery 查询条件
    * @param ids 导出指定ID
    * @return List<CourseAuthStoreVO> 结果集合
    */
    @Override
    public List<CourseAuthStoreVO> export(CourseAuthStoreQuery courseAuthStoreQuery, Long[] ids) {
        return list(Wrappers.<CourseAuthStore>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), CourseAuthStore::getId, ids))
            .stream()
            .map(entity -> {
                CourseAuthStoreVO courseAuthStoreVO = new CourseAuthStoreVO();
                BeanUtils.copyProperties(entity, courseAuthStoreVO);
                return courseAuthStoreVO;
            }).toList();
    }

    @Override
    public IPage pageByStoreId(Page page, CourseAuthStoreQuery courseAuthStoreQuery) {
        return courseAuthStoreManager.fillData(courseAuthStoreQuery.getStoreId(),  baseMapper.pageByStoreId(page, courseAuthStoreQuery));
    }

    /**
     * 包含历史的授权的课程id
     * <AUTHOR>
     * @date 2025/5/29 16:05
     * @param courseTypeId 课程类型ID（可选）
     * @param storeId 门店ID
     * @return java.util.List<java.lang.Long>
     */
    @Override
    public List<Long> listAuthCourseIds(Long courseTypeId, Long storeId) {
        // 获取历史授权记录
        List<Long> historyAuthCourseIds = baseMapper.listHistoryAuthCourseIds(courseTypeId, storeId);
        return new ArrayList<>(historyAuthCourseIds);
    }

    /**
     * 当前有效的授权的课程id
     * <AUTHOR>
     * @date 2025/5/29 16:05
     * @param courseTypeId 课程类型ID（可选）
     * @param storeId 门店ID
     * @return java.util.List<java.lang.Long>
     */
    @Override
    public List<Long> listCurrentAuthCourseIds(Long courseTypeId, Long storeId) {
        return baseMapper.listCurrentAuthCourseIds(courseTypeId, storeId);
    }

}
