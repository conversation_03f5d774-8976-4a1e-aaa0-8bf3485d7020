package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.dto.CoursewareVersionDTO;
import com.yuedu.teaching.entity.CourseVersion;
import com.yuedu.teaching.entity.CoursewareVersion;
import com.yuedu.teaching.mapper.CourseVersionMapper;
import com.yuedu.teaching.mapper.CoursewareVersionMapper;
import com.yuedu.teaching.service.CourseVersionService;
import com.yuedu.teaching.vo.CourseVersionVO;
import com.yuedu.teaching.vo.CoursewareVersionVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 课程发布记录表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-24 09:04:30
 */
@Slf4j
@Service
public class CourseVersionServiceImpl extends ServiceImpl<CourseVersionMapper, CourseVersion> implements CourseVersionService {

    @Resource
    private CoursewareVersionMapper coursewareVersionMapper;


    @Resource
    private CourseVersionMapper courseVersionMapper;

    /**
     * 通过Id列表查询课件Id和课件名称
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    @Override
    public List<CoursewareVersionVO> listCoursewareByIds(List<Long> ids) {
        return coursewareVersionMapper.getCourseWareList(ids);
    }

    /**
     * 通过Id列表查询课件Id和课件名称
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    @Override
    public List<CourseVersionVO> listCourseByIds(List<Long> ids) {
        List<CourseVersion> courseVersionList = courseVersionMapper.selectList(Wrappers.lambdaQuery(CourseVersion.class).in(CourseVersion::getId, ids).select(CourseVersion::getId, CourseVersion::getVersion));

        List<CourseVersionVO> courseVersionVOList = new ArrayList<>();

        courseVersionList.forEach(courseVersion -> {
            CourseVersionVO courseVersionVO = new CourseVersionVO();
            BeanUtil.copyProperties(courseVersion, courseVersionVO);
            courseVersionVOList.add(courseVersionVO);
        });
        return courseVersionVOList;
    }

  /**
   * 通过Id列表查询课件Id和课件名称
   *
   * <AUTHOR>
   * @date 2025/3/13 16:17
   * @param coursewareVersionDTO
   * @return java.util.List<com.yuedu.teaching.vo.CoursewareVersionVO>
   */
  @Override
  public List<CoursewareVersionVO> listCoursewareVersionHis(
      CoursewareVersionDTO coursewareVersionDTO) {
    log.info("开始查询课件版本历史记录, 入参: {}", coursewareVersionDTO);

    if (coursewareVersionDTO == null || coursewareVersionDTO.getCoursewareId() == null) {
      log.warn("查询课件版本历史记录参数无效");
      return new ArrayList<>();
    }

    try {
      // 构建查询条件
      LambdaQueryWrapper<CoursewareVersion> wrapper =
          Wrappers.lambdaQuery(CoursewareVersion.class)
              .eq(CoursewareVersion::getCoursewareId, coursewareVersionDTO.getCoursewareId())
              .ge(
                  coursewareVersionDTO.getLatestRecordTaskTime() != null,
                  CoursewareVersion::getCreateTime,
                  coursewareVersionDTO.getLatestRecordTaskTime())
              .orderByDesc(CoursewareVersion::getCreateTime);

      // 执行查询
      List<CoursewareVersion> coursewareVersions = coursewareVersionMapper.selectList(wrapper);

      if (CollectionUtils.isEmpty(coursewareVersions)) {
        log.info("未查询到课件[{}]的版本历史记录", coursewareVersionDTO.getCoursewareId());
        return new ArrayList<>();
      }

      // 转换结果
      List<CoursewareVersionVO> result =
          coursewareVersions.stream()
              .map(
                  version -> {
                    CoursewareVersionVO vo = new CoursewareVersionVO();
                    BeanUtil.copyProperties(version, vo);
                    return vo;
                  })
              .collect(Collectors.toList());

      log.info(
          "查询课件版本历史记录成功, 课件ID: {}, 记录数: {}", coursewareVersionDTO.getCoursewareId(), result.size());

      return result;

    } catch (Exception e) {
      log.error(
          "查询课件版本历史记录异常, 课件ID: {}, 异常信息: {}",
          coursewareVersionDTO.getCoursewareId(),
          e.getMessage(),
          e);
      throw new RuntimeException("查询课件版本历史记录失败", e);
    }
  }
}
