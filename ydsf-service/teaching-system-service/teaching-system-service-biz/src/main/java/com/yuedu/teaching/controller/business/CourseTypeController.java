package com.yuedu.teaching.controller.business;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.teaching.constant.enums.CourseTypeStatusEnum;
import com.yuedu.teaching.dto.CourseTypeDTO;
import com.yuedu.teaching.entity.CourseType;
import com.yuedu.teaching.service.CourseTypeService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 课程类型 前端使用
 *
 * <AUTHOR>
 */
@Slf4j
@RestController("businessCourseType")
@RequiredArgsConstructor
@RequestMapping("/business/courseType")
@Tag(description = "businessCourseType", name = "课程类型")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class CourseTypeController {

    private final CourseTypeService courseTypeService;

    /**
     * 查询所有可用课程类型
     */
    @Operation(summary = "查询所有课程类型", description = "查询所有课程类型")
    @GetMapping("/all")
    @Cacheable(value = "courseTypeList", key = "'all'")
    public R<List<CourseTypeDTO>> getAll() {
        List<CourseTypeDTO> courseTypeDTOList = courseTypeService.list(Wrappers.lambdaQuery())
                .stream()
                .map(courseType -> BeanUtil.copyProperties(courseType, CourseTypeDTO.class))
                .toList();
        return R.ok(courseTypeDTOList);
    }
}
