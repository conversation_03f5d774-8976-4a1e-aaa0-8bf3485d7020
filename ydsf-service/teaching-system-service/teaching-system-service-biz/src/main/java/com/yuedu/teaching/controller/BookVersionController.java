package com.yuedu.teaching.controller;

import com.aliyun.oss.common.utils.HttpHeaders;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.dto.BookVersionDTO;
import com.yuedu.teaching.entity.BookVersion;
import com.yuedu.teaching.service.BookVersionService;
import com.yuedu.teaching.vo.BookVersionVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 书籍版本 控制类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:43:58
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/bookVersion")
@Tag(description = "book_version", name = "书籍版本管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BookVersionController {

    private final BookVersionService bookVersionService;

    /**
     * 分页查询
     *
     * @param page        分页对象
     * @param bookVersion 书籍版本
     * @return R<Page < BookVersion>> 根据书籍id查询的数据
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("teaching_bookVersion_view")
    public R<Page<BookVersionVO>> getBookVersionPage(@ParameterObject Page<BookVersion> page, @ParameterObject BookVersion bookVersion) {
        return R.ok(bookVersionService.selectBookVersionList(page, bookVersion));
    }

    /**
     * 通过书籍版本id获取书籍版本详情
     *
     * @param id 书籍版本id
     * @return R<BookVersion> 书籍版本详情
     */
    @Operation(summary = "通过书籍版本id获取书籍版本详情", description = "通过书籍版本id获取书籍版本详情")
    @GetMapping("/get/{id}")
    public R<BookVersionVO> getBookVersionById(@PathVariable Integer id) {
        return R.ok(bookVersionService.getBookVersionById(id));
    }

    /**
     * 通过条件查询书籍版本
     *
     * @param bookVersion 查询条件
     * @return R<List < BookVersion>>  对象列表
     */
    @Operation(summary = "通过条件查询", description = "通过条件查询对象")
    @GetMapping("/details")
    @HasPermission("teaching_bookVersion_view")
    public R<List<BookVersionVO>> getDetails(@ParameterObject BookVersion bookVersion) {
        return R.ok(bookVersionService.getBookVersion(bookVersion));
    }

    /**
     * 新增书籍版本
     *
     * @param bookVersionDTO 书籍版本
     * @return R
     */
    @Operation(summary = "新增书籍版本", description = "新增书籍版本")
    @SysLog("新增书籍版本")
    @PostMapping("/add")
    @HasPermission("teaching_bookVersion_add")
    public R<Boolean> save(@Validated({V_A_E.class}) @RequestBody BookVersionDTO bookVersionDTO) {
        return R.ok(bookVersionService.addBookVersionSeries(bookVersionDTO));
    }

    /**
     * 修改书籍版本
     *
     * @param bookVersionDTO 书籍版本
     * @return R
     */
    @Operation(summary = "修改书籍版本", description = "修改书籍版本")
    @SysLog("修改书籍版本")
    @PutMapping("/edit")
    @HasPermission("teaching_bookVersion_edit")
    public R<Boolean> updateById(@Validated(V_A_E.class) @RequestBody BookVersionDTO bookVersionDTO) {
        return R.ok(bookVersionService.updateVersionSeries(bookVersionDTO));
    }

    /**
     * 通过id删除书籍版本
     *
     * @param bookVersionDTO 书籍id(bookId)、书籍版本id(bookVersionId)、书籍版本isbn(isbn)
     * @return R
     */
    @Operation(summary = "通过id删除书籍版本", description = "通过id删除书籍版本")
    @SysLog("通过id删除书籍版本")
    @DeleteMapping("/delete")
    @HasPermission("teaching_bookVersion_del")
    public R<Boolean> removeById(@Validated({ValidGroup.Update.class}) @RequestBody BookVersionDTO bookVersionDTO) {
        return R.ok(bookVersionService.subtractVersionSeries(bookVersionDTO));
    }
}
