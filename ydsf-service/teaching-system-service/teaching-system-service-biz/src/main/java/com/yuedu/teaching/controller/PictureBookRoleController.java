package com.yuedu.teaching.controller;

import com.yuedu.teaching.dto.PictureBookRoleDTO;
import com.yuedu.teaching.query.PictureBookRoleQuery;
import com.yuedu.teaching.service.PictureBookRoleService;
import com.yuedu.teaching.valid.PictureBookRoleValidGroup;
import com.yuedu.teaching.vo.PictureBookRoleVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 绘本角色表 控制类
 *
 * <AUTHOR>
 * @date 2025-01-03 14:49:28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/pictureBookRole")
@Tag(description = "picture_book_role", name = "绘本角色表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PictureBookRoleController {

    private final PictureBookRoleService pictureBookRoleService;


    /**
     * 查询绘本角色表
     *
     * @param bookId 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询", description = "通过条件查询对象")
    @GetMapping("/details")
    public R<List<PictureBookRoleVO>> getDetails(@ParameterObject Integer bookId) {
        return R.ok(pictureBookRoleService.getRoles(bookId));
    }

    /**
     * 新增绘本角色表
     *
     * @param pictureBookRoleDTO 绘本角色表
     * @return R
     */
    @Operation(summary = "新增绘本角色表", description = "新增绘本角色表")
    @SysLog("新增绘本角色表")
    @PostMapping("/add")
    public R<Boolean> addRole(@RequestBody
                              @Validated({ PictureBookRoleValidGroup.AddRoles.class })
                              PictureBookRoleDTO pictureBookRoleDTO) {
        return R.ok(pictureBookRoleService.addRole(pictureBookRoleDTO));
    }

    /**
     * 批量修改绘本角色
     *
     * @param pictureBookRoleQuery 绘本角色表
     * @return R
     */
    @Operation(summary = "批量修改绘本角色", description = "批量修改绘本角色")
    @SysLog("批量修改绘本角色")
    @PutMapping("/edits")
    public R<Boolean> updateRoles(@RequestBody
                                  @Validated({ PictureBookRoleValidGroup.UpdateRoles.class })
                                  PictureBookRoleQuery pictureBookRoleQuery) {
        return R.ok(pictureBookRoleService.updateRoles(pictureBookRoleQuery.getBookRoleList()));
    }

    /**
     * 更新单个绘本角色
     *
     * @param pictureBookRole 角色信息
     * @return 更新结果
     */
    @Operation(summary = "修改单个绘本角色", description = "修改单个绘本角色")
    @SysLog("修改单个绘本角色")
    @PutMapping("/edit")
    public R<Boolean> updateRole(@RequestBody
                                 @Validated({ PictureBookRoleValidGroup.UpdateRoles.class })
                                 PictureBookRoleDTO pictureBookRole) {
        return R.ok(pictureBookRoleService.updateRole(pictureBookRole));
    }

    /**
     * 批量新增更新角色
     *
     * @param pictureBookRoleQuery 角色列表
     * @return R
     */
    @Operation(summary = "批量新增更新角色", description = "批量新增更新角色")
    @SysLog("批量新增更新角色")
    @PostMapping("/batchSave")
    public R<Boolean> batchSaveOrUpdateRoles(@RequestBody
                                             @Validated({ PictureBookRoleValidGroup.AddRoles.class })
                                             PictureBookRoleQuery pictureBookRoleQuery) {
        return R.ok(pictureBookRoleService.batchSaveOrUpdateRoles(pictureBookRoleQuery.getBookRoleList()));
    }
}
