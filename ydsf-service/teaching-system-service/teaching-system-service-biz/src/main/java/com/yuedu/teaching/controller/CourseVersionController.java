package com.yuedu.teaching.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.dto.CoursewareVersionDTO;
import com.yuedu.teaching.entity.CourseVersion;
import com.yuedu.teaching.service.CourseVersionService;
import com.yuedu.teaching.vo.CourseVersionVO;
import com.yuedu.teaching.vo.CoursewareVersionVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 课程发布记录表 控制类
 *
 * <AUTHOR>
 * @date 2024-10-24 09:04:30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/courseVersion")
@Tag(description = "course_version", name = "课程发布记录表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseVersionController {

    private final CourseVersionService courseVersionService;

    /**
     * 分页查询
     *
     * @param page          分页对象
     * @param courseVersion 课程发布记录表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("teaching_courseVersion_view")
    public R getCourseVersionPage(@ParameterObject Page page, @ParameterObject CourseVersion courseVersion) {
        LambdaQueryWrapper<CourseVersion> wrapper = Wrappers.lambdaQuery();
        return R.ok(courseVersionService.page(page, wrapper));
    }


    /**
     * 通过条件查询课程发布记录表
     *
     * @param courseVersion 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询", description = "通过条件查询对象")
    @GetMapping("/details")
    @HasPermission("teaching_courseVersion_view")
    public R getDetails(@ParameterObject CourseVersion courseVersion) {
        return R.ok(courseVersionService.list(Wrappers.query(courseVersion)));
    }

    /**
     * 新增课程发布记录表
     *
     * @param courseVersion 课程发布记录表
     * @return R
     */
    @Operation(summary = "新增课程发布记录表", description = "新增课程发布记录表")
    @SysLog("新增课程发布记录表")
    @PostMapping("/add")
    @HasPermission("teaching_courseVersion_add")
    public R save(@RequestBody CourseVersion courseVersion) {
        return R.ok(courseVersionService.save(courseVersion));
    }

    /**
     * 修改课程发布记录表
     *
     * @param courseVersion 课程发布记录表
     * @return R
     */
    @Operation(summary = "修改课程发布记录表", description = "修改课程发布记录表")
    @SysLog("修改课程发布记录表")
    @PutMapping("/edit")
    @HasPermission("teaching_courseVersion_edit")
    public R updateById(@RequestBody CourseVersion courseVersion) {
        return R.ok(courseVersionService.updateById(courseVersion));
    }

    /**
     * 通过id删除课程发布记录表
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除课程发布记录表", description = "通过id删除课程发布记录表")
    @SysLog("通过id删除课程发布记录表")
    @DeleteMapping("/delete")
    @HasPermission("teaching_courseVersion_del")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(courseVersionService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 通过Id列表查询课件Id和课件名称
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    @Inner(value = false)
    @Operation(summary = "通过Id列表查询课件Id和课件名称", description = "通过Id列表查询课件Id和课件名称")
    @PostMapping("/listCoursewareByIds")
//    @HasPermission("teaching_lesson_view")
    public R<List<CoursewareVersionVO>> listCoursewareByIds(@RequestBody List<Long> ids) {
        return R.ok(courseVersionService.listCoursewareByIds(ids));
    }

  /**
   * 获取时间段内的课件版本修改历史
   *
   * <AUTHOR>
   * @date 2025/3/13 16:07
   * @param coursewareVersionDTO
   * @return
   *     com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.teaching.vo.CoursewareVersionVO>>
   */
  @Inner(value = false)
  @Operation(summary = "获取时间段内的课件版本修改历史", description = "获取时间段内的课件版本修改历史")
  @PostMapping("/listCoursewareVersionHis")
  public R<List<CoursewareVersionVO>> listCoursewareVersionHis(
      @RequestBody CoursewareVersionDTO coursewareVersionDTO) {
    return R.ok(courseVersionService.listCoursewareVersionHis(coursewareVersionDTO));
  }

    /**
     * 根据课程名称获取已发布课程列表
     *
     * @param ids 查询参数
     * @return R<List < CourseVO>>
     */
    @Operation(summary = "根据课程名称获取已发布课程列表", description = "根据课程名称获取已发布课程列表")
    @PostMapping("/listCourseByIds")
    @Inner(value = false)
    public R<List<CourseVersionVO>> listCourseByIds(@RequestBody List<Long> ids) {
        return R.ok(courseVersionService.listCourseByIds(ids));
    }
}
