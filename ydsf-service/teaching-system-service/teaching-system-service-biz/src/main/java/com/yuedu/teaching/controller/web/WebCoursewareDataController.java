package com.yuedu.teaching.controller.web;

import com.yuedu.teaching.entity.CoursewareData;
import com.yuedu.teaching.service.CoursewareDataPubService;
import com.yuedu.teaching.valid.CoursewareDataValidGroup;
import com.yuedu.teaching.vo.DataTemplateVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 资料（课包）
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("web/coursewareData")
@Tag(description = "webCoursewareData", name = "指导师端资料管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class WebCoursewareDataController {

    private final CoursewareDataPubService coursewareDataPubService;

    /**
     * 查询资料列表
     * @param coursewareData 查询条件
     * @return <List<CoursewareDataVO>>
     */
    @Operation(summary = "查询资料列表", description = "查询资料列表")
    @GetMapping("/list")
    public R<Map<String, List<DataTemplateVO>>> getCoursewareDataList(@Validated({CoursewareDataValidGroup.ListCoursewareData.class}) @ParameterObject CoursewareData coursewareData) {
        return R.ok(coursewareDataPubService.getWebCoursewareDataList(coursewareData));
    }
}
