package com.yuedu.teaching.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.teaching.service.CourseAuthStoreHisService;
import com.yuedu.teaching.query.CourseAuthStoreHisQuery;
import com.yuedu.teaching.dto.CourseAuthStoreHisDTO;
import com.yuedu.teaching.vo.CourseAuthStoreHisVO;

import java.io.Serializable;
import java.util.List;

/**
* 课程授权门店历史表控制层
*
* <AUTHOR>
* @date  2025/05/21
*/

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/courseAuthStoreHis")
@Tag(description = "course_auth_store_his" , name = "课程授权门店历史表" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseAuthStoreHisController  {


    private final CourseAuthStoreHisService ourseAuthStoreHisService;


    /**
    * 课程授权门店历史表分页查询
    * @param page 分页对象
    * @param courseAuthStoreHisQuery 课程授权门店历史表
    * @return R
    */
    @GetMapping("/page" )
    @HasPermission("courseAuthStoreHis_view")
    @Operation(summary = "分页查询" , description = "课程授权门店历史表分页查询" )
    public R page(@ParameterObject Page page, @ParameterObject CourseAuthStoreHisQuery courseAuthStoreHisQuery) {
        return R.ok(ourseAuthStoreHisService.page(page, courseAuthStoreHisQuery));
    }


    /**
    * 通过id查询课程授权门店历史表
    * @param id id
    * @return R
    */
    @Operation(summary = "通过id查询" , description = "通过id查询课程授权门店历史表" )
    @GetMapping("/{id}" )
    @HasPermission("courseAuthStoreHis_view")
    public R getById(@PathVariable Serializable id) {
        return R.ok(ourseAuthStoreHisService.getById(id));
    }



    /**
    * 新增课程授权门店历史表
    * @param courseAuthStoreHisDTO 课程授权门店历史表
    * @return R
    */
    @PostMapping
    @SysLog ("新增课程授权门店历史表" )
    @HasPermission("courseAuthStoreHis_view" )
    @Operation(summary = "新增课程授权门店历史表" , description = "新增课程授权门店历史表" )
    public R add(@Validated(V_A.class) @RequestBody CourseAuthStoreHisDTO courseAuthStoreHisDTO) {
         return R.ok(ourseAuthStoreHisService.add(courseAuthStoreHisDTO));
    }


    /**
    * 修改课程授权门店历史表
    * @param courseAuthStoreHisDTO 课程授权门店历史表
    * @return R
    */
    @PutMapping
    @SysLog("修改课程授权门店历史表" )
    @HasPermission("courseAuthStoreHis_edit" )
    @Operation(summary = "修改课程授权门店历史表" , description = "修改课程授权门店历史表" )
    public R edit(@Validated(V_E.class) @RequestBody CourseAuthStoreHisDTO courseAuthStoreHisDTO) {
         return R.ok(ourseAuthStoreHisService.edit(courseAuthStoreHisDTO));
    }



    /**
    * 通过id删除课程授权门店历史表
    * @param ids id列表
    * @return R
    */
    @DeleteMapping
    @SysLog("通过id删除课程授权门店历史表" )
    @HasPermission("courseAuthStoreHis_del" )
    @Operation(summary = "删除课程授权门店历史表" , description = "删除课程授权门店历史表" )
    public R delete(@RequestBody  Long[] ids){
         return R.ok(ourseAuthStoreHisService.removeBatchByIds(CollUtil.toList(ids)));
    }



   /**
   * 导出excel 课程授权门店历史表表格
   * @param  courseAuthStoreHisQuery 查询条件
   * @param ids 导出指定ID
   * @return excel 文件流
   */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("courseAuthStoreHis_export" )
    @Operation(summary = "导出课程授权门店历史表表格" , description = "导出课程授权门店历史表表格" )
    public List<CourseAuthStoreHisVO> export(CourseAuthStoreHisQuery courseAuthStoreHisQuery, Long[] ids) {
        return ourseAuthStoreHisService.export(courseAuthStoreHisQuery, ids);
    }


}
