package com.yuedu.teaching.controller.pc;

import com.yuedu.teaching.service.PlayerService;
import com.yuedu.teaching.vo.ClientStepDetailsVO;
import com.yuedu.teaching.vo.PlayerVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.teaching.query.PlayerQuery;
import com.yuedu.ydsf.common.security.annotation.PcPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 播放器
 *
 * <AUTHOR>
 * @date 2024-12-11 10:04:33
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/player")
@Tag(description = "player", name = "播放器")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Inner(value = false)
public class PlayerController {

    private final PlayerService playerService;

    /**
     * 获取教学环节
     * @param playerQuery  playerQuery
     * @return
     */
    @GetMapping(value = "/step")
    @Operation(summary = "获取教学环节", description = "获取教学环节")
    @SysLog("获取教学环节")
    @PcPermission
    public R<List<ClientStepDetailsVO>> step(PlayerQuery playerQuery) {
        return R.ok( playerService.getStepDetails(playerQuery));
    }

    /**
     * 时间校准
     * @return LocalDateTime
     */
    @GetMapping(value = "/getTime")
    @Operation(summary = "获取当前时间", description = "获取当前时间")
    @SysLog("获取当前时间")
    @PcPermission
    public R<PlayerVO> getTime() {
        return R.ok( playerService.getTime());
    }

}
