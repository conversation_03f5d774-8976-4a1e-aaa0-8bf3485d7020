package com.yuedu.teaching.controller.inner;

import com.yuedu.teaching.dto.CourseFeeDTO;
import com.yuedu.teaching.dto.TimetableCourseFeeDTO;
import com.yuedu.teaching.query.TimetableCourseFeeQuery;
import com.yuedu.teaching.service.CourseFeeService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/courseFee/inner")
@Tag(description = "门店课次课时费（服务内部调用）", name = "门店课次课时费")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class InnerCourseFeeController {
    private final CourseFeeService courseFeeService;


    /**
     * 查询课次费列表
     */
    @PostMapping("/timetable/courseFeeList")
    @Inner
    public R<Map<Long, CourseFeeDTO>> getCourseFeeList(@Validated @NotNull @RequestBody TimetableCourseFeeQuery timetableCourseFeeQuery) {
        if (Objects.isNull(timetableCourseFeeQuery)) {
            return R.ok(Map.of());
        }
        return R.ok(courseFeeService.getCourseFeeListByTimetable(timetableCourseFeeQuery));
    }

    /**
     * 查询课次费列表
     */
    @PostMapping("/timetable/batchCourseFeeList")
    @Inner
    public R<Map<Long, Map<Long, CourseFeeDTO>>> getBatchCourseFeeList(@Validated @NotNull @RequestBody List<TimetableCourseFeeQuery> timetableCourseFeeQueryList) {
        if (CollectionUtils.isEmpty(timetableCourseFeeQueryList)) {
            return R.ok(Map.of());
        }
        return R.ok(courseFeeService.listCourseFeeListByTimetable(timetableCourseFeeQueryList));
    }
}
