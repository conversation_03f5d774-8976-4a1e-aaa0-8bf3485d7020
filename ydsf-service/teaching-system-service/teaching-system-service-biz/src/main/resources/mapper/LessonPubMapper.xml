<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.teaching.mapper.LessonPubMapper">

    <resultMap id="lessonPubMap" type="com.yuedu.teaching.entity.LessonPubEntity">
        <id property="id" column="id"/>
        <result property="lessonId" column="lesson_id"/>
        <result property="courseId" column="course_id"/>
        <result property="version" column="version"/>
        <result property="lessonOrder" column="lesson_order"/>
        <result property="lessonName" column="lesson_name"/>
        <result property="bookId" column="book_id"/>
        <result property="coursewareId" column="courseware_id"/>
        <result property="type" column="type"/>
        <result property="publishStatus" column="public_status"/>
        <result property="canPublish" column="can_publish"/>
        <result property="createBy" column="created_by"/>
        <result property="createTime" column="created_time"/>
        <result property="updateBy" column="updated_by"/>
        <result property="updateTime" column="updated_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

</mapper>