<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.teaching.mapper.CourseTypeMapper">

  <resultMap id="courseTypeMap" type="com.yuedu.teaching.entity.CourseType">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>

  <!-- 根据课程类型ID列表批量查询课程类型 -->
  <select id="selectCourseTypesByIds" resultType="com.yuedu.teaching.vo.CourseTypeVO">
        SELECT
            id,
            name,
            code,
            status,
            create_by createBy,
            create_time createTime,
            update_by updateBy,
            update_time updateTime
        FROM course_type
        WHERE id IN
        <foreach collection="courseTypeIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY create_time DESC
  </select>

</mapper>
