<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.teaching.mapper.CoursewareVersionMapper">

    <resultMap id="coursewareMap" type="com.yuedu.teaching.entity.CoursewareVersion">
        <id property="id" column="id"/>
        <result property="bookId" column="book_id"/>
        <result property="bookVersionId" column="book_version_id"/>
        <result property="coursewareId" column="courseware_id"/>
        <result property="createBy" column="created_by"/>
        <result property="createTime" column="created_time"/>
        <result property="updateBy" column="updated_by"/>
        <result property="updateTime" column="updated_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <!--通过课节获取课件信息-->
    <select id="getCourseWareList" parameterType="java.util.List" resultType="com.yuedu.teaching.vo.CoursewareVersionVO">
        SELECT DISTINCT
        lessonPub.lesson_id as lessonId,
        coursewareVersion.courseware_id as courseWareId,
        courseware.version as courseWareVersion
        FROM
        lesson_pub lessonPub
        LEFT JOIN courseware courseware ON lessonPub.courseware_id = courseware.id
        LEFT JOIN courseware_version coursewareVersion ON coursewareVersion.courseware_id = courseware.id
        WHERE
        lessonPub.lesson_id IN
        <foreach collection="list" item="lessonId" index="index" open="(" close=")"
                 separator=",">
            #{lessonId}
        </foreach>
    </select>

</mapper>