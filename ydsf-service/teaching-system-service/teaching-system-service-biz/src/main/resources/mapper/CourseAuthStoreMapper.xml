<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.teaching.mapper.CourseAuthStoreMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.teaching.entity.CourseAuthStore">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="courseId" column="course_id" jdbcType="BIGINT"/>
            <result property="storeId" column="store_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="pageByStoreId" resultType="com.yuedu.teaching.vo.CourseAuthStoreVO">
        select
            t1.id,
            t1.course_id  courseId,
            t1.store_id  storeId,
            t1.create_by  createBy,
            t1.create_time  createTime,
            t1.update_by  updateBy,
            t1.update_time  updateTime,
            t2.charge_method chargeMethod,
            t2.customize_fee customizeFee,
            t2.course_type_id courseTypeId,
            t2.version version
        from
            (
                select
                    t.id,
                    t.course_id,
                    t.store_id,
                    t.create_by,
                    t.create_time,
                    t.update_by,
                    t.update_time,
                    ROW_NUMBER() OVER ( PARTITION BY t.course_id ORDER BY t.create_time DESC ) AS rn
                 from
                    course_auth_store_his t
                 where
                    t.del_flag = 0 and t.auth_status = 0 and t.store_id = #{query.storeId}
                 <if test="query.courseId != null">
                     and t.course_id = #{query.courseId}
                 </if>
            ) t1
        left join course t2 on t1.course_id = t2.id
        <if test="query.courseName != null and  query.courseName != ''">
            left join course_pub t3 on t2.id = t3.course_id and t3.version = t2.version
        </if>
        <where>
            t1.rn = 1 and t2.version is not null
            <if test="query.courseTypeId != null">
                and t2.course_type_id = #{query.courseTypeId}
            </if>
            <if test="query.courseName != null and  query.courseName != ''">
                and t3.course_name  like CONCAT('%', #{query.courseName},'%')
            </if>
        </where>

        order by t1.create_time desc
    </select>



    <select id="pageByStore" resultType="com.yuedu.teaching.vo.CourseAuthStoreVO">
        SELECT
        t1.id,
        t1.course_id  courseId,
        t1.store_id  storeId,
        t1.create_by  createBy,
        t1.create_time  createTime,
        t1.update_by  updateBy,
        t1.update_time  updateTime,
        t2.charge_method chargeMethod,
        t2.customize_fee customizeFee,
        t2.course_type_id courseTypeId,
        t2.version version
        FROM
        course_auth_store t1
        left join course t2 on t1.course_id = t2.id
        <if test="query.courseName != null and  query.courseName != ''">
            left join course_pub t3 on t2.id = t3.course_id and t3.version = t2.version
        </if>
        <where>
            t1.store_id = #{query.storeId} and t2.version is not null
            <if test="query.courseId != null">
                and t1.course_id = #{query.courseId}
            </if>

            <if test="query.courseName != null and  query.courseName != ''">
                and t3.course_name  like CONCAT('%', #{query.courseName},'%')
            </if>

            <if test="query.courseTypeId != null">
                and t2.course_type_id = #{query.courseTypeId}
            </if>
        </where>

        order by t1.create_time desc
    </select>

    <!-- 获取当前有效的授权课程ID列表 -->
    <select id="listCurrentAuthCourseIds" resultType="java.lang.Long">
        SELECT DISTINCT cas.course_id
        FROM course_auth_store cas
        INNER JOIN course c ON cas.course_id = c.id
        WHERE cas.store_id = #{storeId}
        <if test="courseTypeId != null">
            AND c.course_type_id = #{courseTypeId}
        </if>
    </select>

    <!-- 获取历史授权课程ID列表 -->
    <select id="listHistoryAuthCourseIds" resultType="java.lang.Long">
        SELECT DISTINCT cah.course_id
        FROM course_auth_store_his cah
        INNER JOIN course c ON cah.course_id = c.id
        WHERE cah.store_id = #{storeId}
        <if test="courseTypeId != null">
            AND c.course_type_id = #{courseTypeId}
        </if>
        AND cah.auth_status = 0  -- 只查询正常授权的记录
        AND cah.del_flag = 0     -- 未删除的记录
    </select>

</mapper>
