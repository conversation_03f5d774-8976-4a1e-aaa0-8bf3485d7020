<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.teaching.mapper.CourseFeeMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.teaching.entity.CourseFee">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="standardPrice" column="standard_price" jdbcType="DECIMAL"/>
            <result property="effectiveDate" column="effective_date" jdbcType="DATE"/>
            <result property="storeId" column="store_id" jdbcType="BIGINT"/>
            <result property="courseTypeId" column="course_type_id" jdbcType="BIGINT"/>
            <result property="courseId" column="course_id" jdbcType="BIGINT"/>
            <result property="optType" column="opt_type" jdbcType="INTEGER"/>
    </resultMap>
    <select id="selectListBySotreIdAndTypeIds" resultMap="BaseResultMap">
        select
            t.id,
            t.del_flag,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.standard_price,
            t.effective_date,
            t.store_id,
            t.course_type_id,
            t.course_id
        from course_fee t
        where t.id in (
              select
                  max(t1.id)
              from
                  course_fee t1
              where
                t1.store_id = #{storeId}
                and t1.opt_type = 0
                and t1.effective_date &lt;= now()
                and t1.course_type_id in
                <foreach item="item" collection="types" separator="," open="(" close=")">
                    #{item}
                </foreach>

              group by t1.store_id,t1.course_type_id
            )
    </select>


    <select id="selectListBySotreIdAndCourseIds" resultMap="BaseResultMap">
        select
        t.id,
        t.del_flag,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.standard_price,
        t.effective_date,
        t.store_id,
        t.course_type_id,
        t.course_id
        from (
          select
             t1.id,
             t1.del_flag,
             t1.create_by,
             t1.create_time,
             t1.update_by,
             t1.update_time,
             t1.standard_price,
             t1.effective_date,
             t1.store_id,
             t1.course_type_id,
             t1.course_id,
             ROW_NUMBER() OVER(PARTITION BY t1.course_id ORDER BY t1.effective_date DESC) AS rn
          from
             course_fee t1
          where
             t1.store_id = #{storeId}
             and t1.effective_date &lt;= now()
             and t1.opt_type = 1
             and t1.course_id IN
             <foreach item="id" collection="ids" separator="," open="(" close=")">
               #{id}
             </foreach>
        ) t
        where t.rn = 1
    </select>

</mapper>
