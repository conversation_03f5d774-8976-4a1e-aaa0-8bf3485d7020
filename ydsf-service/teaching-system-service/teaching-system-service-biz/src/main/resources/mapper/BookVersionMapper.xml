<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.teaching.mapper.BookVersionMapper">

    <resultMap id="bookVersionMap" type="com.yuedu.teaching.entity.BookVersion">
        <id property="id" column="id"/>
        <result property="bookId" column="book_id"/>
        <result property="isbn" column="isbn"/>
        <result property="press" column="press"/>
        <result property="translator" column="translator"/>
        <result property="source" column="source"/>
        <result property="cover" column="cover"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <select id="selectBookVersionByIsbn" resultType="com.yuedu.teaching.entity.BookVersion">
        select *
        from book_version
        where isbn = #{isbn}
    </select>

    <update id="updateBookVersionByIsbn">
        update book_version
        set book_id     = #{bookId},
            press       = #{press},
            translator  = #{translator},
            source      = #{source},
            cover       = #{cover},
            update_by   = #{updateBy},
            update_time = #{updateTime},
            del_flag    = 0
        where isbn = #{isbn}
    </update>
</mapper>
