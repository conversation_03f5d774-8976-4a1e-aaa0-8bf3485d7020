package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 教学环节/页面表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-06 10:38:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("courseware_step")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "教学环节/页面表实体类")
public class CoursewareStep extends Model<CoursewareStep> {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 课件内容Id
     */
    @Schema(description = "课件内容Id")
    private Integer coursewareDataId;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    private Integer coursewareId;

    /**
     * 环节名称
     */
    @Schema(description = "环节名称")
    private String stepName;

    /**
     * 父ID
     */
    @Schema(description = "父ID")
    private Integer stepParent;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer stepOrder;

    /**
     * 环节类型，1是环节，2是页面
     */
    @Schema(description = "环节类型，1是环节，2是页面")
    private Integer type;

    /**
     * 页面模板ID
     */
    @Schema(description = "页面模板ID")
    private Integer pageTemplateId;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;
}
