package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 书籍版本适配阶段 实体类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:57:30
 */
@Data
@TableName("book_version_stage")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "书籍版本适配阶段实体类")
public class BookVersionStage extends Model<BookVersionStage> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 阶段ID
     */
    @Schema(description = "阶段ID")
    private Integer stageId;

    /**
     * 书籍ID
     */
    @Schema(description = "书籍ID")
    private Integer bookId;

    /**
     * 书籍版本id
     */
    @Schema(description = "书籍版本id")
    private Integer bookVersionId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @Schema(description = "是否删除")
    private Integer delFlag;
}
