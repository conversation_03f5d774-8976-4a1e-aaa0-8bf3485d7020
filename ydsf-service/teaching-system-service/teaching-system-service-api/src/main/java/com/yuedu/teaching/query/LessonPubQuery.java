package com.yuedu.teaching.query;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @ClassName LessonPubQuery
 * @Description 课程发布查询类
 * <AUTHOR>
 * @Date 2024/12/27 11:29
 * @Version v0.0.1
 */
@Data
public class LessonPubQuery {

    /**
     * 课节顺序
     */
    @Schema(description = "课节顺序")
    private List<Integer> lessonOrderList;


    /**
     * 课程Id
     */
    @Schema(description = "课程Id")
    private List<Long> courseIdList;

    /**
     * 课程版本Id
     */
    @Schema(description = "课程版本Id")
    private List<Integer> courseVersionList;
}
